package cn.htdt.middlewareprocess.biz.sms.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 短信内容处理工具类
 *
 * <AUTHOR>
 * @date 2025/07/31
 **/
@Slf4j
public class SmsUtil {

    /**
     * 从短信内容中提取卡密
     * 提取"卡密："后面到"，"前面的内容
     * 
     * 示例：
     * 输入："测试店铺：您的礼品卡已发放，微信小程序可查询记录。祝您生活愉快！卡密：QFTWQUZ6VVP3MCTC，拒收请回复R"
     * 输出："QFTWQUZ6VVP3MCTC"
     *
     * @param smsContent 短信内容
     * @return 卡密字符串，如果未找到则返回null
     */
    public static String extractCardPassword(String smsContent) {
        if (StringUtils.isBlank(smsContent)) {
            log.warn("短信内容为空，无法提取卡密");
            return null;
        }

        try {
            // 使用正则表达式提取卡密：后面到逗号前面的内容
            // 支持中文逗号和英文逗号
            Pattern pattern = Pattern.compile("卡密：([^，,]+)");
            Matcher matcher = pattern.matcher(smsContent);
            
            if (matcher.find()) {
                String cardPassword = matcher.group(1).trim();
                log.info("成功提取卡密: {}", cardPassword);
                return cardPassword;
            } else {
                log.warn("未在短信内容中找到卡密信息: {}", smsContent);
                return null;
            }
        } catch (Exception e) {
            log.error("提取卡密时发生异常，短信内容: {}", smsContent, e);
            return null;
        }
    }

    /**
     * 从短信内容中提取卡密（支持自定义分隔符）
     * 
     * @param smsContent 短信内容
     * @param prefix 卡密前缀，如"卡密："
     * @param suffix 卡密后缀分隔符，如"，"或","
     * @return 卡密字符串，如果未找到则返回null
     */
    public static String extractCardPassword(String smsContent, String prefix, String suffix) {
        if (StringUtils.isBlank(smsContent) || StringUtils.isBlank(prefix)) {
            log.warn("短信内容或前缀为空，无法提取卡密");
            return null;
        }

        try {
            int startIndex = smsContent.indexOf(prefix);
            if (startIndex == -1) {
                log.warn("未在短信内容中找到前缀: {}", prefix);
                return null;
            }

            startIndex += prefix.length();
            int endIndex;
            
            if (StringUtils.isNotBlank(suffix)) {
                endIndex = smsContent.indexOf(suffix, startIndex);
                if (endIndex == -1) {
                    // 如果没有找到后缀，取到字符串末尾
                    endIndex = smsContent.length();
                }
            } else {
                endIndex = smsContent.length();
            }

            String cardPassword = smsContent.substring(startIndex, endIndex).trim();
            log.info("成功提取卡密: {}", cardPassword);
            return cardPassword;
        } catch (Exception e) {
            log.error("提取卡密时发生异常，短信内容: {}, 前缀: {}, 后缀: {}", smsContent, prefix, suffix, e);
            return null;
        }
    }

    /**
     * 使用正则表达式从短信内容中提取指定模式的内容
     * 
     * @param smsContent 短信内容
     * @param regex 正则表达式，需要包含一个捕获组
     * @return 提取的内容，如果未找到则返回null
     */
    public static String extractByRegex(String smsContent, String regex) {
        if (StringUtils.isBlank(smsContent) || StringUtils.isBlank(regex)) {
            log.warn("短信内容或正则表达式为空");
            return null;
        }

        try {
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(smsContent);
            
            if (matcher.find()) {
                String result = matcher.group(1).trim();
                log.info("正则提取成功: {}", result);
                return result;
            } else {
                log.warn("正则表达式未匹配到内容，regex: {}, content: {}", regex, smsContent);
                return null;
            }
        } catch (Exception e) {
            log.error("正则提取时发生异常，regex: {}, content: {}", regex, smsContent, e);
            return null;
        }
    }

    /**
     * 验证卡密格式是否有效
     * 默认卡密应该是大写字母和数字的组合，长度在8-20位之间
     * 
     * @param cardPassword 卡密
     * @return true表示格式有效，false表示格式无效
     */
    public static boolean isValidCardPassword(String cardPassword) {
        if (StringUtils.isBlank(cardPassword)) {
            return false;
        }

        // 卡密格式：8-20位大写字母和数字组合
        Pattern pattern = Pattern.compile("^[A-Z0-9]{8,20}$");
        return pattern.matcher(cardPassword.trim()).matches();
    }

    /**
     * 从短信内容中安全提取卡密（带格式验证）
     * 
     * @param smsContent 短信内容
     * @return 有效的卡密字符串，如果未找到或格式无效则返回null
     */
    public static String extractValidCardPassword(String smsContent) {
        String cardPassword = extractCardPassword(smsContent);
        
        if (cardPassword != null && isValidCardPassword(cardPassword)) {
            return cardPassword;
        } else {
            log.warn("提取的卡密格式无效: {}", cardPassword);
            return null;
        }
    }
}
