package cn.htdt.middlewareprocess.biz.sms.client;

import cn.htdt.middlewareprocess.biz.sms.dto.AliYunSmsConfig;
import cn.htdt.middlewareprocess.biz.sms.dto.BaseAliyunResultDTO;
import cn.htdt.sms.client.AbstractSmsClient;
import cn.htdt.sms.dao.SmsLogDao;
import cn.htdt.sms.domain.SmsLogDomain;
import cn.htdt.sms.dto.*;
import cn.htdt.sms.enums.SmsChannelEnum;
import cn.htdt.sms.enums.SmsCommonCode;
import cn.htdt.sms.enums.SmsSendStatuslEnum;
import com.alibaba.fastjson.JSON;
import com.aliyun.dysmsapi20170525.models.*;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URLDecoder;
import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.aliyun.teaopenapi.models.Config;
import com.aliyun.dysmsapi20170525.Client;
import org.springframework.stereotype.Service;

@Service
public class AliyunSmsClient extends AbstractSmsClient {
    private static final Logger log = LoggerFactory.getLogger(AliyunSmsClient.class);
    private static final Logger LOGGER = LoggerFactory.getLogger(AliyunSmsClient.class);
    private static final int NUM_ZERO = 0;
    private static final int NUM_ONE = 1;
    private static final int NUM_TWO = 2;

    @Resource
    private SmsLogDao smsLogDao;

    @Resource
    private AliYunSmsConfig aliYunSmsConfig;


    private Client client = null;

    @PostConstruct
    public void init() {
        client = createClient();
    }

    public Client createClient() {
        Config config = new Config()
                .setAccessKeyId(aliYunSmsConfig.getAccessKey())
                .setAccessKeySecret(aliYunSmsConfig.getSecretKey());
        config.endpoint = aliYunSmsConfig.getEndPoint();

        log.info("[阿里云短信]:短信服务注册成功,当前配置为:{}",config);
        log.info("[阿里云短信]:短信配置注入成功,当前配置为:{}",aliYunSmsConfig);
        try {
            return new Client(config);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public UnExecuteDTO<BaseMengWangResultDTO> sendSms(ReqSingleSendSmsDTO reqDTO) {
        LOGGER.info("sendSms-reqDTO:{}", JSON.toJSONString(reqDTO));
        try {
            String currentTime = LocalDateTime.now().format(DateTimeFormatter.ofPattern("MMddHHmmss"));
            SendSmsRequest request = new SendSmsRequest()
                    .setPhoneNumbers(reqDTO.getMobile())
                    .setSignName(aliYunSmsConfig.getSignName())
                    .setTemplateCode(aliYunSmsConfig.getTemplateCode())
                    // TemplateParam 为序列化后的 JSON 字符串。其中\"表示转义后的双引号。
                    .setTemplateParam("{\"code\":\""+reqDTO.getContent()+"\"}");
            SendSmsResponse sendSmsResponse = client.sendSms(request);
            SendSmsResponseBody body = sendSmsResponse.getBody();
            BaseAliyunResultDTO dto = new BaseAliyunResultDTO();
            BeanUtils.copyProperties(body, dto);
            log.info("sendSms-respDTO:{}", dto);
            if (reqDTO.getUseSmsLog() == null || reqDTO.getUseSmsLog() == 1) {
                this.saveSmsLog(reqDTO);
            }

            if ("OK".equals(dto.getCode())) {
                BaseMengWangResultDTO resultDTO = new BaseMengWangResultDTO();
                resultDTO.setResult(0);
                resultDTO.setDesc(dto.getMessage());
                resultDTO.setCustid(dto.getBizId());
                return UnExecuteDTO.success(resultDTO);
            } else {
//                String errorMsg = MengWangErrorEnum.getByCode(String.valueOf(resultDTO.getResult())).getMessage();
//                return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), errorMsg);
                return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), dto.getMessage());
            }
        } catch (Exception e) {
            LOGGER.error("阿里云短信发送异常", e);
            return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), e.getMessage());
        }
    }

    /**
     * TODO: 模板匹配
     *
     * @param reqDTO
     * @return
     */
    @Override
    public UnExecuteDTO<BaseMengWangResultDTO> batchSendSms(ReqBatchSendSmsDTO reqDTO) {
        LOGGER.info("batchSendSms-reqDTO:{}", JSON.toJSONString(reqDTO));

        try {
            String mobileNoListStr = StringUtils.join(reqDTO.getMobileNoList().iterator(), ",");
            String signNameJson = JSON.toJSONString(Arrays.asList(aliYunSmsConfig.getSignName()));
            SendBatchSmsRequest request = new SendBatchSmsRequest()
                    .setPhoneNumberJson(mobileNoListStr)
                    .setSignNameJson(signNameJson)
                    .setTemplateCode(aliYunSmsConfig.getTemplateCode())
                    .setTemplateParamJson("{\"code\":\""+reqDTO.getContent()+"\"}");
            SendBatchSmsResponse response = this.client.sendBatchSms(request);
            BaseAliyunResultDTO dto = new BaseAliyunResultDTO();
            BeanUtils.copyProperties(response.getBody(), dto);
            log.info("batchSendSms-respDTO:{}", dto);

            if (reqDTO.getUseSmsLog() == null || reqDTO.getUseSmsLog() == 1) {
                this.saveBatchSmsLog(reqDTO);
            }

            if ("OK".equals(dto.getCode())) {
                BaseMengWangResultDTO resultDTO = new BaseMengWangResultDTO();
                resultDTO.setResult(0);
                resultDTO.setDesc(dto.getMessage());
                resultDTO.setCustid(dto.getBizId());
                return UnExecuteDTO.success(resultDTO);
            } else {
                return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), dto.getMessage());
            }
        } catch (Exception e) {
            LOGGER.error("阿里云批量短信发送异常", e);
            return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), e.getMessage());
        }
    }


    // 梦网批量发送
//    @Override
//    public UnExecuteDTO<BaseMengWangResultDTO> batchSendSms(ReqBatchSendSmsDTO reqDTO) {
//        LOGGER.info("batchSendSms-reqDTO:{}", JSON.toJSONString(reqDTO));
//
//        try {
//            String mobileNoListStr = StringUtils.join(reqDTO.getMobileNoList().iterator(), ",");
//            String signNameJson = JSON.toJSONString(Arrays.asList(aliYunSmsConfig.getSignName()));
//            SendBatchSmsRequest request = new SendBatchSmsRequest()
//                    .setPhoneNumberJson(mobileNoListStr)
//                    .setSignNameJson(signNameJson)
//                    .setTemplateCode(aliYunSmsConfig.getTemplateCode())
//                    .setTemplateParamJson("{\"code\":\""+reqDTO.getContent()+"\"}");
//            SendBatchSmsResponse response = this.client.sendBatchSms(request);
//            BaseAliyunResultDTO dto = new BaseAliyunResultDTO();
//            BeanUtils.copyProperties(response.getBody(), dto);
//            log.info("batchSendSms-respDTO:{}", dto);
//
//            if (reqDTO.getUseSmsLog() == null || reqDTO.getUseSmsLog() == 1) {
//                this.saveBatchSmsLog(reqDTO);
//            }
//
//            if ("OK".equals(dto.getCode())) {
//                BaseMengWangResultDTO resultDTO = new BaseMengWangResultDTO();
//                resultDTO.setResult(0);
//                resultDTO.setDesc(dto.getMessage());
//                resultDTO.setCustid(dto.getBizId());
//                return UnExecuteDTO.success(resultDTO);
//            } else {
//                return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), dto.getMessage());
//            }
//        } catch (Exception e) {
//            LOGGER.error("阿里云批量短信发送异常", e);
//            return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), e.getMessage());
//        }
//    }

    @Override
    public UnExecuteDTO<List<MengWangRptDTO>> querySendSmsResult(ReqQuerySendSmsResultDTO reqDTO) {

        try {
            String currentDate = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            QuerySendStatisticsRequest request = new QuerySendStatisticsRequest();
            request.setIsGlobe(1);
            request.setStartDate(currentDate);
            request.setEndDate(currentDate);
            request.setPageIndex(1);
            request.setPageSize(10);
            QuerySendStatisticsResponse response = this.client.querySendStatistics(request);


            if (response.getStatusCode() == 0) {
//                String rpts = resultDTO.getRpts();
//                List<MengWangRptDTO> mengWangRptDTOList = null;
//                if (StringUtils.isNotBlank(rpts)) {
//                    mengWangRptDTOList = JSON.parseArray(rpts, MengWangRptDTO.class);
//                }
//
//                return UnExecuteDTO.success(mengWangRptDTOList);
                return UnExecuteDTO.success(null);
            } else {
                return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), response.getBody().getMessage());
            }
        } catch (Exception e) {
            LOGGER.error("查询阿里云短信回执异常", e);
            return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), e.getMessage());
        }
    }

    public UnExecuteDTO<List<MengWangRptDTO>> sendSmsLogResultHandle(ReqQuerySendSmsResultDTO reqDTO) {
        try {
            UnExecuteDTO<List<MengWangRptDTO>> unExecuteDTO = this.querySendSmsResult(reqDTO);
            if (!unExecuteDTO.successFlag()) {
                return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), "查询阿里云短信回执结果异常");
            } else {
                List<MengWangRptDTO> mengWangRptDTOList = (List)unExecuteDTO.getData();
                if (CollectionUtils.isEmpty(mengWangRptDTOList)) {
                    return UnExecuteDTO.success();
                } else {
                    List<String> smsSendSerialNoList = (List)mengWangRptDTOList.stream().map((mengWangRptDTOx) -> mengWangRptDTOx.getCustid()).collect(Collectors.toList());
                    LambdaQueryWrapper<SmsLogDomain> queryWrapper = new LambdaQueryWrapper();
                    queryWrapper.in(SmsLogDomain::getSmsSendSerialNo, smsSendSerialNoList);
                    queryWrapper.eq(SmsLogDomain::getSmsSendStatus, SmsSendStatuslEnum.SENDING.getCode());
                    queryWrapper.eq(SmsLogDomain::getChannelCode, "ALIYUN");
                    List<SmsLogDomain> smsLogDomainList = this.smsLogDao.selectList(queryWrapper);
                    if (CollectionUtils.isEmpty(smsLogDomainList)) {
                        return UnExecuteDTO.success();
                    } else {
                        for(MengWangRptDTO mengWangRptDTO : mengWangRptDTOList) {
                            Optional<SmsLogDomain> optionalSmsLogDomain = smsLogDomainList.stream().filter((smsLogDomainx) -> StringUtils.equals(smsLogDomainx.getSmsSendSerialNo(), mengWangRptDTO.getCustid())).findFirst();
                            if (optionalSmsLogDomain.isPresent()) {
                                SmsLogDomain smsLogDomain = (SmsLogDomain)optionalSmsLogDomain.get();
                                if (mengWangRptDTO.getStatus() == 0) {
                                    LambdaUpdateWrapper<SmsLogDomain> updateWrapper = new LambdaUpdateWrapper();
                                    updateWrapper.eq(SmsLogDomain::getSmsSendSerialNo, smsLogDomain.getSmsSendSerialNo())
                                            .set(SmsLogDomain::getSmsSendStatus, SmsSendStatuslEnum.SUCCESS.getCode());
                                    this.smsLogDao.update((SmsLogDomain) null, updateWrapper);
                                } else {
                                    LambdaUpdateWrapper<SmsLogDomain> updateWrapper = new LambdaUpdateWrapper();
                                    String errMsg = URLDecoder.decode(mengWangRptDTO.getErrdesc(), "GBK");
                                    updateWrapper.eq(SmsLogDomain::getSmsSendSerialNo, smsLogDomain.getSmsSendSerialNo())
                                            .set(SmsLogDomain::getSmsSendStatus, SmsSendStatuslEnum.FAIL.getCode())
                                            .set(SmsLogDomain::getSmsSendMsg, errMsg);
                                    this.smsLogDao.update((SmsLogDomain) null, updateWrapper);
                                }
                            }
                        }

                        return UnExecuteDTO.success(mengWangRptDTOList);
                    }
                }
            }
        } catch (Exception e) {
            LOGGER.error("短信发送结果处理异常", e);
            return UnExecuteDTO.error(SmsCommonCode.CODE_10000016.getCode(), e.getMessage());
        }
    }

    @Override
    public UnExecuteDTO<List<ResStatBatchSensSmsDTO>> statBatchSendSms(ReqStatBatchSendSmsDTO reqDTO) {
        List<ResStatBatchSensSmsDTO> resStatBatchSensSmsDTOList = new ArrayList();

        for(String batchNo : reqDTO.getBatchNoList()) {
            ResStatBatchSensSmsDTO resStatBatchSensSmsDTO = this.statSingleBatch(batchNo);
            resStatBatchSensSmsDTOList.add(resStatBatchSensSmsDTO);
        }

        return UnExecuteDTO.success(resStatBatchSensSmsDTOList);
    }

    private ResStatBatchSensSmsDTO statSingleBatch(String batchNo) {
        ResStatBatchSensSmsDTO resStatBatchSensSmsDTO = new ResStatBatchSensSmsDTO();
        LambdaQueryWrapper<SmsLogDomain> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.eq(SmsLogDomain::getBatchNo, batchNo);
        List<SmsLogDomain> smsLogDomainList = this.smsLogDao.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(smsLogDomainList)) {
            return resStatBatchSensSmsDTO;
        } else {
            long successCount = smsLogDomainList.stream().filter((smsLogDomain) -> smsLogDomain.getSmsSendStatus() == SmsSendStatuslEnum.SUCCESS.getCode()).count();
            long failCount = smsLogDomainList.stream().filter((smsLogDomain) -> smsLogDomain.getSmsSendStatus() == SmsSendStatuslEnum.FAIL.getCode()).count();
            resStatBatchSensSmsDTO.setBatchNo(batchNo);
            resStatBatchSensSmsDTO.setMessageCount(smsLogDomainList.size());
            resStatBatchSensSmsDTO.setSendSuccess(Integer.parseInt(String.valueOf(successCount)));
            resStatBatchSensSmsDTO.setSendFailed(Integer.parseInt(String.valueOf(failCount)));
            resStatBatchSensSmsDTO.setNotSendCount(0);
            resStatBatchSensSmsDTO.setSendCount(smsLogDomainList.size());
            return resStatBatchSensSmsDTO;
        }
    }

//    private String mengWangPostRequest(String requestUrl, String jsonString) throws Exception {
//        String result = null;
//        HttpClient httpClient = new HttpClient(new HttpClientParams(), new SimpleHttpConnectionManager(true));
//        httpClient.getParams().setContentCharset("GBK");
//        PostMethod postMethod = new PostMethod(requestUrl);
//        log.info("阿里云接口请求体-requestUrl:{}，requestJson:{}", requestUrl, jsonString);
//
//        try {
//            RequestEntity requestEntity = new StringRequestEntity(jsonString, "application/json", "UTF-8");
//            postMethod.setRequestEntity(requestEntity);
//            postMethod.setRequestHeader("Content-Type", "application/json");
//            int status = httpClient.executeMethod(postMethod);
//            if (status != 200) {
//                throw new RuntimeException("HttpClient error status code :" + status);
//            }
//
//            result = postMethod.getResponseBodyAsString();
//            log.info("阿里云接口结果返回体-result:{}", result);
//        } catch (Exception e) {
//            LOGGER.error("阿里云短信发送异常", e);
//            throw e;
//        } finally {
//            postMethod.releaseConnection();
//        }
//
//        return result;
//    }

    public String pwdMd5(String userId, String pwd, String currentTime) {
        String pwsString = userId.toUpperCase() + "00000000" + pwd + currentTime;
        MessageDigest messageDigest = null;

        try {
            messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.reset();
            messageDigest.update(pwsString.getBytes("UTF-8"));
        } catch (Exception e) {
            LOGGER.error("MD5加密异常", e);
        }

        byte[] byteArray = messageDigest.digest();
        StringBuffer md5StrBuff = new StringBuffer();

        for(int i = 0; i < byteArray.length; ++i) {
            if (Integer.toHexString(255 & byteArray[i]).length() == 1) {
                md5StrBuff.append("0").append(Integer.toHexString(255 & byteArray[i]));
            } else {
                md5StrBuff.append(Integer.toHexString(255 & byteArray[i]));
            }
        }

        return md5StrBuff.toString();
    }

    @Override
    public boolean matchSmsChannel(SmsChannelEnum smsChannelEnum) {
        return !(smsChannelEnum == SmsChannelEnum.MENG_WANG);
    }

    private void saveSmsLog(ReqSingleSendSmsDTO reqSendSmsDTO) {
        SmsLogDomain smsLogDomain = new SmsLogDomain();
        BeanUtils.copyProperties(reqSendSmsDTO, smsLogDomain);
        smsLogDomain.setMobile(reqSendSmsDTO.getMobile());
        smsLogDomain.setSmsSendTime(LocalDateTime.now());
        smsLogDomain.setChannelCode("ALIYUN");
        smsLogDomain.setChannelName("阿里云");
        smsLogDomain.setSmsSendStatus(SmsSendStatuslEnum.SENDING.getCode());
        this.smsLogDao.insert(smsLogDomain);
    }

    private void saveBatchSmsLog(ReqBatchSendSmsDTO reqBatchSendSmsDTO) {
        for(String mobile : reqBatchSendSmsDTO.getMobileNoList()) {
            SmsLogDomain smsLogDomain = new SmsLogDomain();
            BeanUtils.copyProperties(reqBatchSendSmsDTO, smsLogDomain);
            smsLogDomain.setMobile(mobile);
            smsLogDomain.setSmsSendTime(LocalDateTime.now());
            smsLogDomain.setChannelCode("ALIYUN");
            smsLogDomain.setChannelName("阿里云");
            smsLogDomain.setSmsSendStatus(SmsSendStatuslEnum.SENDING.getCode());
            this.smsLogDao.insert(smsLogDomain);
        }

    }
}
