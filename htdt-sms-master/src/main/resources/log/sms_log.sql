DROP TABLE IF EXISTS `sms_log`;
CREATE TABLE `sms_log` (
`id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键Id',
`batch_no` varchar(32) DEFAULT NULL COMMENT '群发批次号',
`sms_send_serial_no` varchar(32) DEFAULT NULL COMMENT '短信发送流水号',
`sms_type` int(12) DEFAULT 1 COMMENT '短信类型{1：单发，2：群发}',
`client_system` varchar(32) DEFAULT NULL COMMENT '系统标识',
`business_scene` varchar(32) DEFAULT NULL COMMENT '业务发送场景',
`channel_code` varchar(32) DEFAULT NULL COMMENT '短信渠道编号',
`channel_name` varchar(32) DEFAULT NULL COMMENT '短信渠道名称',
`ds_mobile` varchar(32) DEFAULT NULL COMMENT '手机号密文',
`mobile` varchar(32) DEFAULT NULL COMMENT '手机号码',
`content` varchar(512) DEFAULT NULL COMMENT '短信内容',
`sms_send_status` int(12) DEFAULT 1 COMMENT '短信发送状态{{1:未发送,2：发送中,3：发送成功,4:发送失败}}',
`sms_send_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '短信发送时间',
`sms_send_msg` varchar(512) DEFAULT NULL COMMENT '回执结果',
`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`update_time`  datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8 COMMENT='短信日志记录';