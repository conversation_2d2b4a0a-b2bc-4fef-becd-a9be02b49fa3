<properties>
    <!--
        创建goods的索引表结构
    -->
    <property name="createGoodsIndice">
        <![CDATA[
          {
            "settings": {
                "number_of_shards": 3,
                "number_of_replicas": 1
            },
            "mappings": {
                    "properties": {
                        "goods_no": {
                            "type": "keyword"
                        },
                        "goods_help_code": {
                            "type": "keyword"
                        },
                        "third_goods_no": {
                            "type": "keyword"
                        },
                        "goods_form": {
                            "type": "keyword"
                        },
                        "goods_type": {
                            "type": "keyword"
                        },
                        "category_no": {
                            "type": "keyword"
                        },
                        "brand_no": {
                            "type": "keyword"
                        },
                        "category_name": {
                            "type": "text"
                        },
                        "brand_name": {
                            "type": "text"
                        },
                        "goods_name": {
                            "type": "text"
                        },
                        "warehouse_flag": {
                            "type": "integer"
                        },
                        "parent_goods_no": {
                            "type": "keyword"
                        },
                        "cloud_pool_goods_no": {
                            "type": "keyword"
                        },
                        "freight_template_no": {
                            "type": "keyword"
                        },
                        "sales_area_no": {
                            "type": "keyword"
                        },
                        "goods_source_type": {
                            "type": "keyword"
                        },
                        "goods_model": {
                            "type": "keyword"
                        },
                        "goods_sale_description": {
                            "type": "text"
                        },
                        "calculation_unit_no": {
                            "type": "keyword"
                        },
                        "standard_flag": {
                            "type": "integer"
                        },

                        "assist_calculation_unit_no": {
                            "type": "keyword"
                        },
                        "conversion_rate": {
                            "type": "double"
                        },
                        "guarantee_days": {
                            "type": "integer"
                        },
                        "barcode": {
                            "type": "text"
                        },
                        "payment_method": {
                            "type": "text"
                        },
                        "delivery_way": {
                            "type": "keyword"
                        },
                        "first_shelf_time": {
                            "type": "date",
                            "format": "yyyy-MM-dd'T'HH:mm:ss.SSSZ||yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
                        },
                        "audit_status": {
                            "type": "text"
                        },
                        "audit_data_modify_flag": {
                            "type": "text"
                        },
                        "imei_flag": {
                            "type": "integer"
                        },
                        "audit_message": {
                            "type": "text"
                        },
                        "main_data_flag": {
                            "type": "integer"
                        },
                        "merchant_no": {
                            "type": "keyword"
                        },
                        "store_no": {
                            "type": "keyword"
                        },
                        "merchant_name": {
                            "type": "text"
                        },
                        "store_name": {
                            "type": "text"
                        },
                        "company_no": {
                            "type": "keyword"
                        },
                        "company_name": {
                            "type": "text"
                        },
                        "branch_no": {
                            "type": "keyword"
                        },
                        "branch_name": {
                            "type": "text"
                        },
                        "create_no": {
                            "type": "keyword"
                        },
                        "create_name": {
                            "type": "text"
                        },
                        "create_time": {
                            "type": "date",
                            "format": "yyyy-MM-dd'T'HH:mm:ss.SSSZ||yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
                        },
                        "modify_no": {
                            "type": "keyword"
                        },
                        "modify_name": {
                            "type": "text"
                        },
                        "modify_time": {
                            "type": "date",
                            "format": "yyyy-MM-dd'T'HH:mm:ss.SSSZ||yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
                        },
                        "supplier_code": {
                            "type": "keyword"
                        },
                        "supplier_telphone": {
                            "type": "keyword"
                        },
                        "supplier_name": {
                            "type": "text"
                        },
                        "disable_flag": {
                            "type": "integer"
                        },
                        "delete_flag": {
                            "type": "integer"
                        },
                        "first_attribute_name": {
                            "type": "text"
                        },
                        "first_attribute_value_name": {
                            "type": "text"
                        },
                        "second_attribute_name": {
                            "type": "text"
                        },
                        "second_attribute_value_name": {
                            "type": "text"
                        },
                        "third_attribute_name": {
                            "type": "text"
                        },
                        "third_attribute_value_name": {
                            "type": "text"
                        },
                        "original_goods_no": {
                            "type": "keyword"
                        },
                        "series_type": {
                            "type": "text"
                        },
                        "goods_status": {
                            "type": "text"
                        },
                        "limit_price": {
                            "type": "double"
                        },
                        "market_price": {
                            "type": "double"
                        },
                        "purchase_price": {
                            "type": "double"
                        },
                        "retail_price": {
                            "type": "double"
                        },
                        "sales_volume": {
                            "type": "double"
                        },
                        "browse_amount": {
                            "type": "double"
                        },
                        "goods_video": {
                            "type": "integer"
                        },
                        "distribution_status": {
                            "type": "text"
                        },
                        "distribute_goods_flag": {
                            "type": "integer"
                        },
                        "audit_type": {
                            "type": "text"
                        },
                        "audit_record_no": {
                            "type": "text"
                        },
                        "down_message": {
                            "type": "text"
                        },
                        "cloud_pool_supply_price": {
                            "type": "double"
                        },
                        "latest_modify_time": {
                            "type": "date",
                            "format": "yyyy-MM-dd'T'HH:mm:ss.SSSZ||yyyy-MM-dd HH:mm:ss||yyyy-MM-dd||epoch_millis"
                        },
                        "supply_store_no": {
                            "type": "keyword"
                        },
                        "main_picture_url": {
                            "type": "text"
                        },
                        "can_sale_stock_num": {
                            "type": "double"
                        },
                        "calculation_unit_name": {
                            "type": "text"
                        },
                        "distribution_store_names": {
                            "type": "text"
                        },
                        "min_market_price": {
                            "type": "double"
                        },
                        "max_market_price": {
                            "type": "double"
                        },
                        "min_retail_price": {
                            "type": "double"
                        },
                        "max_retail_price": {
                            "type": "double"
                        },
                        "video_duration": {
                            "type": "text"
                        },
                        "superscript_picture_url": {
                            "type": "text"
                        },
                        "service_tag_no": {
                            "type": "text"
                        },
                        "service_tag_name": {
                            "type": "text"
                        },
                        "reward": {
                            "type": "text"
                        }
                    }
            }
          }
        ]]>
    </property>
    <!--
        创建brand的索引表结构
    -->
    <property name="createBrandIndice">
        <![CDATA[
          {
            "settings": {
                "number_of_shards": 3,
                "number_of_replicas": 1
            },
            "mappings": {
                    "properties": {
                        "goods_no": {
                            "type": "text"
                        },
                        "brand_no": {
                            "type": "text"
                        },
                        "brand_name": {
                            "type": "text"
                        }
                    }
            }
          }
        ]]>
    </property>
    <!--
        创建category的索引表结构
    -->
    <property name="createCategoryIndice">
        <![CDATA[
          {
            "settings": {
                "number_of_shards": 3,
                "number_of_replicas": 1
            },
            "mappings": {
                    "properties": {
                        "goods_no": {
                            "type": "text"
                        },
                        "category_no": {
                            "type": "text"
                        },
                        "category_name": {
                            "type": "text"
                        }
                    }
            }
          }
        ]]>
    </property>
    <!--
        创建coupon的索引表结构
    -->
    <property name="createCouponIndice">
        <![CDATA[
          {
            "settings": {
                "number_of_shards": 3,
                "number_of_replicas": 1
            },
            "mappings": {
                    "properties": {
                        "goods_no": {
                            "type": "text"
                        },
                        "promotion_no": {
                            "type": "text"
                        },
                        "promotion_name": {
                            "type": "text"
                        },
                        "coupon_no": {
                            "type": "text"
                        },
                        "coupon_name": {
                            "type": "text"
                        },
                        "coupon_type": {
                            "type": "text"
                        },
                        "discount_threshold": {
                            "type": "text"
                        },
                        "coupon_value": {
                            "type": "text"
                        },
                        "coupon_identity": {
                            "type": "text"
                        },
                        "coupon_use_channel": {
                            "type": "text"
                        },
                        "coupon_use_scope": {
                            "type": "text"
                        },
                        "coupon_use_explain": {
                            "type": "text"
                        }
                    }
            }
          }
        ]]>
    </property>

</properties>