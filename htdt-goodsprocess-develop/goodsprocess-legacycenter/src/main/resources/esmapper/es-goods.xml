<properties>
    <!--根据商品名称，检索商品信息-->
    <!--
        "has_parent": {
              "parent_type": "goods",
              "query": {
                "match": {
                  "goods_name": #[keyword]
                }
              }
            }
                -->
    <property name="searchGoodsByGoodsName">
        <![CDATA[
            {
              "query": {
                "match_all": {}
              }
            }
        ]]>
    </property>

    <!--根据商品名称检索商品信息，同时返回品牌、分类、优惠券信息-->
    <property name="searchGoodsAndSubInfoByGoodsName">
        <![CDATA[
            {
              "query": {
                "bool": {
                    "must": [
                        {
                            "has_parent": {
                              "parent_type": "goods",
                              "query": {
                                "match": {
                                   "goods_name": #[keyword]
                                }
                              },
                              "inner_hits": {}
                            }
                        }
                    ],
                    "filter": [
                      {
                        "term": {
                          "audit_status": {
                            "value": #[auditStatus]
                          }
                        }
                      }
                    ]
                }
              }
            }
        ]]>
    </property>

    <!-- 商品管理列表查询 -->
    <property name="searchEsGoodsPage">
        <![CDATA[
            {
                "size":#[size],
                "query": {
                    "has_parent": {
                      "parent_type": "goods",
                      "query": {
                        "match": {
                          "goods_name": #[keyword]
                        }
                      }
                    }
                }
            }
        ]]>
    </property>

</properties>