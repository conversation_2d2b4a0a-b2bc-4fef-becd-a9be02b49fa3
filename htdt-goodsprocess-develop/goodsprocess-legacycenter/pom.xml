<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>goodsprocess</artifactId>
        <groupId>cn.htdt.goodsprocess</groupId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>goodsprocess-legacycenter</artifactId>

    <dependencies>

        <dependency>
            <groupId>cn.htdt.goodsprocess</groupId>
            <artifactId>goodsprocess-dao</artifactId>
            <version>${project.parent.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.htdt.goodsprocess</groupId>
            <artifactId>goodsprocess-api</artifactId>
            <version>${htdt.goodsprocess-api.version}</version>
        </dependency>

        <dependency>
            <groupId>cn.htdt.common</groupId>
            <artifactId>common-base</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.htdt.common</groupId>
            <artifactId>common-enums</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.htdt.common</groupId>
            <artifactId>common-encry</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
        </dependency>
        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-dependencies-zookeeper</artifactId>
            <version>${org.apache.dubbo.version}</version>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-amqp</artifactId>
        </dependency>
    </dependencies>
</project>
