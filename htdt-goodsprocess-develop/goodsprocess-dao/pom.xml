<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>goodsprocess</artifactId>
        <groupId>cn.htdt.goodsprocess</groupId>
        <version>3.0.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>goodsprocess-dao</artifactId>

    <dependencies>
        <dependency>
            <groupId>cn.htdt.common</groupId>
            <artifactId>common-dao</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.htdt.common</groupId>
            <artifactId>common-encry</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.htdt.common</groupId>
            <artifactId>common-es</artifactId>
        </dependency>

        <dependency>
            <groupId>cn.htdt.goodsprocess</groupId>
            <artifactId>goodsprocess-api</artifactId>
            <version>${htdt.goodsprocess-api.version}</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
        </dependency>

        <!--Lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
</project>
