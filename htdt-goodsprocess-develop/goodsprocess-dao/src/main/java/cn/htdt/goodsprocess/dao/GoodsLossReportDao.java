package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsLossReportDomain;
import cn.htdt.goodsprocess.vo.AtomGoodsLossReportVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品报损单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Mapper
public interface GoodsLossReportDao extends BaseMapper<GoodsLossReportDomain> {

    /**
     * 根据条件获取报损单列表-list-pc
     * @param atomGoodsLossReportVo
     * @return
     */
    public List<AtomGoodsLossReportVo> selectGoodsLossReportByPage(AtomGoodsLossReportVo atomGoodsLossReportVo);

    /**
     * 根据条件获取报损单列表-list-app
     * @param atomGoodsLossReportVo
     * @return
     */
    public List<AtomGoodsLossReportVo> selectAppGoodsLossReportByPage(AtomGoodsLossReportVo atomGoodsLossReportVo);

    /**
     * 根据条件获取单条报损单-有关联
     * @param atomGoodsLossReportVo
     * @return
     */
    public AtomGoodsLossReportVo selectOneGoodsLossReport(AtomGoodsLossReportVo atomGoodsLossReportVo);

    /**
     * 根据条件获取单条报损单信息-未关联
     * @param atomGoodsLossReportVo
     * @return
     */
    public AtomGoodsLossReportVo selectOneGoodsLossReportInfo(AtomGoodsLossReportVo atomGoodsLossReportVo);

    /**
     * 根据条件更新报损单
     * @param atomGoodsLossReportVo
     * @return
     */
    public Integer updateGoodsLossReportByTerm(AtomGoodsLossReportVo atomGoodsLossReportVo);

}
