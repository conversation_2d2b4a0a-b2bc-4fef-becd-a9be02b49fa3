package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.SaleCategoryDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 销售类目查询自定义VO
 *
 * <AUTHOR>
 * @date 2020/11/10
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomReqSaleCategoryVo extends SaleCategoryDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 类目编号集合
     */
    private List<String> categoryNoList;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 商品状态1001-未上架;1002:已上架;
     */
    private String goodsStatus;

    /**
     * 商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发;1006:云池分发;
     */
    private String goodsSourceType;

    /**
     * 分销商品标记 1:否 2:是
     */
    private Integer distributeGoodsFlag;

    /**
     * 查询商品类型 1006:店铺查询(店铺自建+商家分发)
     */
    private String queryType;

    /**
     * 是否可用:默认2，1：不可用，2：可用
     */
    private Integer disableFlag;

    /**
     * 商品结构样式 1001-经典瀑布流;1002-经典左右结构  对应HxgGoodsTypeEnum
     */
    private String goodsStyleType;

    /**
     * 商品审核状态 2001:等待审核;2002:审核中;2003:审核成功; 2004:审核失败
     */
    private String auditStatus;

    /**
     * 商品类型 1001-实物商品;1004-称重商品
     */
    private String goodsType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
