package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.CloudPoolGoodsDistributionRelationDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 云池分销店铺关系表
 *
 * <AUTHOR>
 * @date 2021/2/24
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomCloudPoolGoodsDistributionRelationVo extends CloudPoolGoodsDistributionRelationDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 商品编码
     */
    private List<String> goodsNos;
    /**
     * 店铺编码集合
     * */
    private List<String> storeNos;
    /**
     * 分销店铺数量
     */
    private Integer distributionStoreTotal;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
