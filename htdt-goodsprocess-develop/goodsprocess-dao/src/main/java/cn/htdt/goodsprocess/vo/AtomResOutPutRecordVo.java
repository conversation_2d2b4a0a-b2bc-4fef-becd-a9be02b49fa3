package cn.htdt.goodsprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 一体机-经营报表-出入库记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-19
 */
@Data
public class AtomResOutPutRecordVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 来源单据类型(1001:订单下单 1002:销售退货单 1003:调拨出库 1004:调拨入库 1005:采购入库 1006:采购退货出库 1007:盘点入库 1008:盘点出库 1009:发货出库 1010:手动调整出库 1011:无仓转有仓出库 1012:有仓转无仓出库 1013有仓转无仓并继承原库存入库)
     */
    private String billType;

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 操作库存数量
     */
    private String stockNum;

    /**
     * 操作时间
     */
    private LocalDateTime createTime;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

}
