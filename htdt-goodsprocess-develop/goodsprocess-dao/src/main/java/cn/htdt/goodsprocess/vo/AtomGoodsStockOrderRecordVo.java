package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsStockOrderRecordDomain;
import lombok.Data;

/**
 * <p>
 * 下单库存操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-02-05
 */
@Data
public class AtomGoodsStockOrderRecordVo extends GoodsStockOrderRecordDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private String startCreateTime;
    /**
     * 结束时间
     */
    private String endCreateTime;

}
