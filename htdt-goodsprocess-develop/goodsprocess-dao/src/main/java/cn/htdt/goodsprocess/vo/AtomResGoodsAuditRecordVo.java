package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品审核列表查询 自定义DTO
 *
 * <AUTHOR>
 * @date 2020/12/10
 **/
@Data
public class AtomResGoodsAuditRecordVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 基础信息审核单号
     */
    private String auditRecordNo;

    /**
     * 商品审核状态 2001:等待审核;2002:审核中;2003:审核成功; 2004:审核失败
     */
    private String auditStatus;

    /**
     * 审核原因
     */
    private String auditMessage;

    /**
     * 审核类型(1001:分发审核 1002:上架审核 1003:云池审核)';
     */
    private String auditType;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */

    private String goodsNo;

    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品
     */
    private String goodsType;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类目ID
     */
    private String categoryNo;

    /**
     * 商品类目名称
     */
    private String categoryName;

    /**
     * 商品品牌ID
     */
    private String brandNo;

    /**
     * 商品品牌名称
     */
    private String brandName;

    /**
     * 计量单位ID
     */
    private String calculationUnitNo;

    /**
     * 计量单位名称
     */
    private String calculationUnitName;

    /**
     * 采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 零售价
     */
    private BigDecimal retailPrice;

    /**
     * 创建时间（审核提交时间）
     */
    private LocalDateTime createTime;

    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

    /**
     * 审核人ID
     */
    private String auditUserNo;

    /**
     * 审核人名称
     */
    private String auditUserName;

    /**
     * 商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发;1006:云池分发
     */
    private String goodsSourceType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
