package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsImeiDomain;
import cn.htdt.goodsprocess.vo.AtomCustomGoodsImeVo;
import cn.htdt.goodsprocess.vo.AtomGoodsImeiVo;
import cn.htdt.goodsprocess.vo.AtomGoodsVo;
import cn.htdt.goodsprocess.vo.AtomResGoodsImeiAndWarehouseVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 商品串码表 Mapper 接口
 **/
@Mapper
public interface GoodsImeiDao extends BaseMapper<GoodsImeiDomain> {

    /**
     * 根据参数查询集合
     *
     * @param domain
     * @return
     */
    List<GoodsImeiDomain> selectByParams(GoodsImeiDomain domain);

    /**
     * 根据商品编码和串码获取某一商品串码
     *
     * @param domain
     * @return
     */
    GoodsImeiDomain selectOneGoodsImeiByGoodsNoAndImei(GoodsImeiDomain domain);

    /**
     * 根据商品编码和串码List获取商品串码是否存在
     *
     * @param atomGoodsImeiVo
     * @return
     */
    List<GoodsImeiDomain> selectListGoodsImeiByTerm(AtomGoodsImeiVo atomGoodsImeiVo);

    /**
     * 获取串码商品信息-list
     *
     * @param vo
     * @return
     */
    List<AtomGoodsImeiVo> selectAllGoodsImeiByParams(AtomGoodsImeiVo vo);

    /**
     * 获取串码商品信息数量-count
     *
     * @param vo
     * @return
     */
    Integer selectAllGoodsImeiCountByParams(AtomGoodsImeiVo vo);

    /**
     * 根据商品获取串码信息
     *
     * @param vo
     * @return
     */
    List<AtomGoodsImeiVo> selectGoodsImeiByParams(AtomGoodsImeiVo vo);

    /**
     * 获取串码商品列表信息-串码查询统计
     *
     * @param vo
     * @return
     */
    List<AtomGoodsImeiVo> selectGoodsImeiStatisticsByParams(AtomGoodsImeiVo vo);

    /**
     * 根据批量商品获取串码数量-商品维度
     *
     * @param vo
     * @return
     */
    List<AtomGoodsImeiVo> batchSelectGoodsImeiCountByGoods(AtomGoodsImeiVo vo);

    /**
     * 根据批量商品和仓库获取串码数量-商品和仓库维度
     *
     * @param list
     * @return
     */
    List<AtomGoodsImeiVo> batchSelectGoodsImeiCountByTerm(@Param("list") List<AtomGoodsImeiVo> list);

    /**
     * 根据参数更新
     *
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsImeiDomain domain);

    /**
     * 根据参数逻辑删除
     *
     * @param domain
     * @return
     */
    int logicDeleteByPrimaryKey(GoodsImeiDomain domain);

    /**
     * 批量insert商品串码
     *
     * @param list
     * @return
     */
    int batchInsertGoodsImei(@Param("list") List<GoodsImeiDomain> list);

    /**
     * 根据商品编号查询编码集合
     *
     * @param domain
     * @return
     */
    List<GoodsImeiDomain> selectByParamsByGoodNo(GoodsImeiDomain domain);

    /**
     * 逻辑删除
     *
     * @param domain
     * @return
     */
    int logicDelete(GoodsImeiDomain domain);

    /**
     * 批量逻辑删除
     *
     * @param vo
     * @return
     */
    int batchLogicDelete(AtomGoodsImeiVo vo);

    /**
     * 根据商品编码和仓库编码批量逻辑删除
     *
     * @param imeiVoList
     * @return
     */
    int batchLogicDeleteByNos(@Param("list") List<AtomGoodsImeiVo> imeiVoList);

    /**
     * 批量更新商品串码为已售
     *
     * @param imeiVoList goodsNo 商品编码
     *                   list     商品串码
     *                   warehouseNo 仓库编码
     *                   身份信息等
     * @return
     */
    int updateGoodsImeisSaleByList(@Param("list") List<AtomGoodsImeiVo> imeiVoList);

    /**
     * 批量变更仓库-转仓用
     * @param imeiVoList goodsNo 商品编码
     *                   warehouseNo 仓库编码
     *                   warehouseName 仓库名称
     * @return
     */
    int updateGoodsImeisWarehouseByList(@Param("list") List<AtomGoodsImeiVo> imeiVoList);

    /**
     * 调拨查询商品所在仓库的编码
     *
     * @param atomGoodsImeiVo
     * @return
     */
    List<AtomGoodsImeiVo> getAllGoodsImeiPage(AtomGoodsImeiVo atomGoodsImeiVo);

    /**
     * 根据串码集合返回相同仓库的串码信息和个数
     *
     * @param atomGoodsImeiVo
     * @return
     * <AUTHOR>
     */
    List<AtomResGoodsImeiAndWarehouseVo> getImeiWarehouseInfo(AtomGoodsImeiVo atomGoodsImeiVo);

    /**
     * 根据商品编码、仓库编码以及采购单编码统计串码数
     *
     * @param list
     * @return
     * <AUTHOR>
     * @date 2021-08-26
     */
    List<AtomResGoodsImeiAndWarehouseVo> selectAlreadyStorageGoodsImeiCount(List<AtomGoodsImeiVo> list);

    /**
     * 获取串码的商品列表，并统计串码数
     *
     * @param vo
     * @return
     */
    List<AtomGoodsImeiVo> selectGoodsImeiListByParams(AtomGoodsImeiVo vo);
}
