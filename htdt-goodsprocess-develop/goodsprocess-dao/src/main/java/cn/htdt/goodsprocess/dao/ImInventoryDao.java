package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.ImInventoryDomain;
import cn.htdt.goodsprocess.vo.AtomInventoryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点表 Mapper 接口
 **/
@Mapper
public interface ImInventoryDao extends BaseMapper<ImInventoryDomain> {

    /**
     * 根据参数查询集合
     * @param inventoryVo
     * @return
     */
    List<AtomInventoryVo> selectByParams(AtomInventoryVo inventoryVo);

    /**
     * 根据参数查询盘点详细信息
     * @param inventoryCode
     * @return
     */
    ImInventoryDomain selectByCode(@Param("inventoryCode") String inventoryCode);

    /**
     * 查盘点下的类目
     * @param inventoryVo
     * @return
     */
    List<AtomInventoryVo> selectInventoryCategoryCode(AtomInventoryVo inventoryVo);

    /**
     * 根据参数逻辑删除
     * @param list
     * @return
     */
    int batchLogicDelete(@Param("list") List<String> list);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByCode(ImInventoryDomain domain);
}
