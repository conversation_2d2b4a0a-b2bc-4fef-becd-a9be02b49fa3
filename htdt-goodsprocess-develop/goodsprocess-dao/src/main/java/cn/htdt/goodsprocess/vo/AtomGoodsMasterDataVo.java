package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsMasterDataDomain;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2020/12/16 16:12
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class AtomGoodsMasterDataVo extends GoodsMasterDataDomain {

    /**
     * 商品名称关键词，支持名称、名称拼音、名称拼音首字母模糊查询
     */
    private String goodsNameKeyWord;

    /**
     * 商品品牌名称
     */
    private String brandName;

    /**
     * 商品品牌数据来源类型(默认1002。1001:同步MDM、1002:平台自创、1003:商家自创、1004:店铺自创)
     */
    private String brandDataSourceType;

    /**
     * 商品类目名称
     */
    private String categoryName;

    /**
     * 商品一级销售类目
     */
    private String firstCategoryNo;

    /**
     * 商品二级销售类目
     */
    private String secondCategoryNo;

    /**
     * 商品三级销售类目
     */
    private String thirdCategoryNo;

    /**
     * 类目名称全路径
     */
    private String fullNamePath;

    /**
     * 商品编号集合
     */
    private List<String> goodsNoList;

    /**
     * 是否列表查询 1：否 2：是
     */
    private Integer listFlag;

    /**
     * 商品类目id全路径集合
     */
    private List<String> fullIdPaths;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
