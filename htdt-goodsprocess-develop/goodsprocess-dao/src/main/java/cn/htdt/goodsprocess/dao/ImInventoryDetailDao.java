package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.ImInventoryDetailDomain;
import cn.htdt.goodsprocess.vo.AtomInventoryDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点明细表 Mapper 接口
 **/
@Mapper
public interface ImInventoryDetailDao extends BaseMapper<ImInventoryDetailDomain> {

    /**
     * 根据参数查询集合-查询快照
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectByDetailCode(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 根据参数查询集合-查询快照
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectGoodGroupsByDetailCode(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 查询盘点中已转无仓的商品信息
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectInventoryChangeWarehouseGoods(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 查询盘点中已转无仓的商品信息
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectInventoryChangeWarehouseGoodsGroups(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 查询盘点明细中的商品信息
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectInventoryDetailWarehouseGoods(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 根据参数查询集合
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectByCode(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 根据参数查询动态集合
     * @param inventoryCode
     * @return
     */
    List<AtomInventoryDetailVo> selectGoodsByCode(@Param("inventoryCode") String inventoryCode, @Param("warehouseNo") String warehouseNo);

    /**
     * 根据参数查询动态集合
     * @param inventoryCode
     * @return
     */
    List<AtomInventoryDetailVo> selectGoodsGroupsByCode(@Param("inventoryCode") String inventoryCode, @Param("warehouseNo") String warehouseNo);

    /**
     * 查盘点下明细的类目
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectInventoryDetailCategoryCode(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 判断是否存在商品正在盘点中-添加用
     * @param atomInventoryDetailVo
     * @return
     */
    int selectExistInventoryGoods(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 判断是否存在商品正在盘点中-修改用
     * @param atomInventoryDetailVo
     * @return
     */
    int selectExistInventory(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 查询盘点的商品的数量
     * @param atomInventoryDetailVo
     * @return
     */
    int selectInventoryGoodsCount(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 查询盘点的商品的数量-批量
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectBatchInventoryGoodsCount(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 判断是否存在商品正在盘点中，存在则返回对应的商品信息-添加用
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectExistInventoryGoodsList(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 判断是否存在商品正在盘点中，存在则返回对应的商品信息-修改用
     * @param atomInventoryDetailVo
     * @return
     */
    List<AtomInventoryDetailVo> selectExistInventoryList(AtomInventoryDetailVo atomInventoryDetailVo);

    /**
     * 根据参数逻辑删除
     * @param inventoryCode
     * @return
     */
    int logicDelete(@Param("inventoryCode") String inventoryCode);

    /**
     * 根据参数批量逻辑删除
     * @param list
     * @return
     */
    int batchLogicDelete(@Param("list") List<String> list);
}
