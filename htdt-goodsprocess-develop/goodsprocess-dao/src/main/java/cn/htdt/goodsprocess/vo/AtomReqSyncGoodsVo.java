package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @ClassName AtomReqSyncGoodsVo
 * @Description 同步商品信息到es库
 * @date 2021/11/08 15:24
 */
@Data
public class AtomReqSyncGoodsVo {

    private List<String> goodsNos;

    private LocalDateTime startTime;

    private LocalDateTime endTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
