package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.SaleCategoryDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 销售类目递归查询返回结果集 自定义DTO
 *
 * <AUTHOR>
 * @date 2020/10/19
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomResSaleCategoryVo extends SaleCategoryDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 选择标志（是否被选择）
     */
    private String selectFlag;

    /**
     * 四级类目总数
     */
    private Integer total;

    /**
     * 已选四级类目数量
     */
    private Integer selNum;

    /**
     * 上架商品数量
     */
    private Integer goodsNum;

    /**
     * 一体机排序值
     */
    private Integer scSortNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
