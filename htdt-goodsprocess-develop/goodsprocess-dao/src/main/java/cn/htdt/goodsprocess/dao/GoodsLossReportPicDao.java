package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsLossReportPicDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品报损单图片表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Mapper
public interface GoodsLossReportPicDao extends BaseMapper<GoodsLossReportPicDomain> {

    /**
     * 根据条件获取报损单商品图片list
     * @param goodsLossReportPicDomain
     * @return
     */
    public List<GoodsLossReportPicDomain> selectGoodsLossReportPics(GoodsLossReportPicDomain goodsLossReportPicDomain);

}
