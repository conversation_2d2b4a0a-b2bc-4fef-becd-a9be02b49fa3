package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.PurchaseOrderGoodsDomain;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 采购单商品信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-16
 */
@Data
public class AtomPurchaseOrderGoodsVo extends PurchaseOrderGoodsDomain implements Serializable {

    private static final long serialVersionUID = -3535779464950326269L;
    /**
     * 是否入库标识
     */
    private Integer warehouseFlag;
    /**
     * 商品删除状态
     */
    private String goodsDeleteFlag;

    /**
     * 采购单状态：订单状态21:待签收 22:已签收 23:待入库 24:部分入库 25:已结案 26:完成
     */
    private String purchaseOrderStatus;

    /**
     * 供应商编码和返回
     */
    private String supplierCode;
    /**
     * 供应商名称/编码-条件和返回
     */
    private String supplierName;
    /**
     * 商品ID/条码-条件
     */
    private String barcode;
    /**
     * 采购日期-返回
     */
    private LocalDateTime purchaseDate;
    /**
     * 计量单位ID-返回
     */
    private String calculationUnitNo;
    /**
     * 计量单位ID-返回
     */
    private String calculationUnitName;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 转换率(辅转主)-返回
     */
    private BigDecimal conversionRate;

    /**
     * 约定到货日期-开始-条件
     */
    private LocalDateTime startExpectReceiveDate;

    /**
     * 约定到货日期-结束-条件
     */
    private LocalDateTime endExpectReceiveDate;

    /**
     * 采购日期-开始-条件
     */
    private LocalDateTime startPurchaseDate;

    /**
     * 采购日期-结束-条件
     */
    private LocalDateTime endPurchaseDate;

    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 第一、二、三属性值编码，格式：first-second-third
     */
    private String attributeValueName;
    /**
     * 采购编码集合
     */
    private List<String> purchaseCodeList;

    /**
     * 采购单商品行编码集合
     */
    private List<String> purchaseGoodsCodeList;

    /**
     * 商品当前计量单位id
     */
    private String curCalculationUnitNo;

    /**
     * 商品当前辅助计量单位id
     */
    private String curAssistCalculationUnitNo;

    /**
     * 商品当前主计量单位对应关系数值
     */
    private String curMainUnitNum;

    /**
     * 商品当前辅计量单位对应关系数值
     */
    private String curAssistUnitNum;

    /**
     * 商品名称(不包含属性值拼接)
     */
    private String goodsBaseName;

    /**
     * 是否启动效期管理标识(1:否 2:是)
     */
    private Integer validityPeriodManageFlag;

    /**
     * 保质期,正整形
     */
    private Integer qualityGuaranteePeriod;

    /**
     * 保质期单位,day:日 month:月 year:年,枚举:PlanCycleTypeEnum
     */
    private String shelfLifeUnit;

    /**
     * 自定义商品编号
     */
    private String customGoodsNo;

    /**
     * 20230825蛋品-genghao-采购管理-商品列表
     * 多单位商品类型 1001:主单位商品 1002:子
     */
    private String multiUnitType;

    /**
     * 20230825蛋品-genghao-采购管理-商品列表
     * 多单位主品编号
     */
    private String multiUnitGoodsNo;

    public String getAttributeValueName() {
        attributeValueName = "";
        if (StringUtils.isNotEmpty(getFirstAttributeValueName())) {
            attributeValueName += "-" + getFirstAttributeValueName();
        }
        if (StringUtils.isNotEmpty(getSecondAttributeValueName())) {
            attributeValueName += "-" + getSecondAttributeValueName();
        }
        if (StringUtils.isNotEmpty(getThirdAttributeValueName())) {
            attributeValueName += "-" + getThirdAttributeValueName();
        }
        if (StringUtils.isNotEmpty(attributeValueName)) {
            attributeValueName = attributeValueName.substring(1);
        }
        return attributeValueName;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}