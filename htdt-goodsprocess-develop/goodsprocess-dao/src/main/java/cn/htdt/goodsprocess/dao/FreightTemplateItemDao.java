package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.FreightTemplateItemDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 运费模板条目 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-07
 */
@Mapper
public interface FreightTemplateItemDao extends BaseMapper<FreightTemplateItemDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<FreightTemplateItemDomain> selectByParams(FreightTemplateItemDomain domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(FreightTemplateItemDomain domain);
    
    /**
     * 批量更新
     * @param list
     * @return
     */
    int batchUpdateFreightTemplateItem(@Param("list") List<FreightTemplateItemDomain> list);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(FreightTemplateItemDomain domain);
    
    /**
     * 分页查询运费模板详情列表
     * @param domain
     * @return
     */
    List<FreightTemplateItemDomain> selectFreightTemplateItemListPage(FreightTemplateItemDomain domain);
    
    /**
     * 根据模板编号查询详情列表
     * @param freightTemplateNos
     * @return
     */
    List<FreightTemplateItemDomain> selectItemList(@Param("freightTemplateNos") List<String> freightTemplateNos);
    
    
    
}
