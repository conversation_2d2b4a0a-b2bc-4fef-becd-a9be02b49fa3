package cn.htdt.goodsprocess.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购单待入库Vo
 *
 * <AUTHOR>
 * @date 2020年9月15日
 */
@Data
public class AtomPurchaseOrderStorageVo implements Serializable {

    private static final long serialVersionUID = -2183266402894644967L;
    /**
     * 采购单编码
     */
    private String purchaseCode;
    /**
     * 采购单商品行编码
     */
    private String purchaseGoodsCode;
    /**
     * 商品编码
     */
    private String goodsNo;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品名称 展示用带属性
     */
    private String showGoodsName;
    /**
     * 计量单位ID
     */
    private String calculationUnitNo;
    /**
     * 计量单位名称
     */
    private String calculationUnitName;
    /**
     * 采购单位
     */
    private String purchaseUnit;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;
    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;
    /**
     * 商品当前是否入仓:1-否;2-是;
     */
    private Integer goodsWarehouseFlag;

    /**
     * 采购单商品行记录中商品是否入仓
     */
    private Integer warehouseFlag;
    /**
     * 采购单价
     */
    private BigDecimal purchaseUnitPrice;
    /**
     * 采购数量
     */
    private BigDecimal purchaseNum;
    /**
     * 已入库数量
     */
    private BigDecimal storageCount;
    /**
     * 入库总数数量
     */
    private BigDecimal storageCountAll;

    /**
     * 订单状态 1001待入库 1002部分入库 1003完成
     */
    private String orderStatus;

    /**
     * 是否启用串码标识（1:否，2:是）
     */
    private Integer imeiFlag;

    /**
     * 第一属性值名称
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值名称
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值名称
     */
    private String thirdAttributeValueName;

    /**
     * 是否启动效期管理标识(1:否 2:是)
     */
    private Integer validityPeriodManageFlag;

    /**
     * 保质期,正整形
     */
    private Integer qualityGuaranteePeriod;

    /**
     * 保质期单位,day:日 month:月 year:年,枚举:PlanCycleTypeEnum
     */
    private String shelfLifeUnit;

    /**
     * 自定义商品编号
     */
    private String customGoodsNo;

    private BigDecimal realStockNum;

    /**
     * 主单位商品编号
     */
    private String rawGoodsNo;

    /**
     * 主单位商品购买数量
     */
    private BigDecimal rawGoodsNum;

    /**
     * 已出库数量
     */
    private BigDecimal outStockNum;

    /**
     * 待出库数量
     */
    private BigDecimal waitOutStockNum;

    /**
     * 20230825蛋品-genghao-采购管理-商品列表
     * 多单位商品类型 1001:主单位商品 1002:子
     */
    private String multiUnitType;

    /**
     * 20230825蛋品-genghao-采购管理-商品列表
     * 多单位主品编号
     */
    private String multiUnitGoodsNo;

    /**
     * 采购单类型，0 商品；1 商品组
     */
    private Integer purchaseType;

    /**
     * 采购单商品对应的商品组记录中商品是否入仓
     */
    private Integer warehouseFlagOfGoodsGroups;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}