package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsDescribeDomain;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2020/11/20 16:49
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class AtomGoodsDescribeVo extends GoodsDescribeDomain implements Serializable {
    private static final long serialVersionUID = 8460361225355050958L;

    /**
     * 商品编码列表集合
     */
    private List<String> goodsNos;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
