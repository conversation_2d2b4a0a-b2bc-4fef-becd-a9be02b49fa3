package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.TradeSignDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 对接接口接入控制表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Mapper
public interface TradeSignDao extends BaseMapper<TradeSignDomain> {

    /**
     * 根据dataSource获取Sign信息
     * @param domain 查询参数
     * @return TradeSignDomain
     */
    TradeSignDomain selectSignByParams(TradeSignDomain domain);

}
