package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsBarDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 69码商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-19
 */
@Mapper
public interface GoodsBarDao extends BaseMapper<GoodsBarDomain> {

    /**
     * @description 分页查询69码商品列表
     * <AUTHOR>
     * @date 2021-11-19 10:19:29
     * @param goodsBarDomain
     * @return
     */
    List<GoodsBarDomain> selectGoodsBarByPage(GoodsBarDomain goodsBarDomain);
}