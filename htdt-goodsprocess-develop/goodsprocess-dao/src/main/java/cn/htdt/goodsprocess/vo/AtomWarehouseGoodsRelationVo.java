package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 *  仓库商品关联
 */
@Data
public class AtomWarehouseGoodsRelationVo extends ImWarehouseGoodsRelationDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 辅计量单位No
     */
    private String assistCalculationUnitNo;

    /**
     * 辅计量单位符号
     */
    private String assistCalculationUnitSymbol;

    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 实体库存区间开始
     */
    private Integer wareHouseStockBegin;

    /**
     * 实体库存区间结束
     */
    private Integer wareHouseStockEnd;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 是否启用串码(1:否 2:是)
     */
    private Integer imeiFlag;

    /**
     * 商品编号List
     */
    private List<String> goodsNos;

    /**
     * 商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发;1006:云池分发
     */
    private String goodsSourceType;

    /**
     * 商品ID、商品名称
     */
    private String goodsStr;

    /**
     * 商品助记码
     */
    private String goodsHelpCode;

    /**
     * 商品类目名称
     */
    private String categoryName;

    /**
     * 类目id
     */
    private String categoryNo;

    /**
     * 分类全路径
     */
    private String fullIdPath;

    /**
     * 商品品牌名称
     */
    private String brandName;

    /**
     * 品牌编码
     */
    private String brandNo;

    /**
     * 商品串码数量
     */
    private Integer imeiCount;

    /**
     * 商品删除标识
     */
    private Integer goodsDeleteFlag;

    /**
     * 是否入仓(1:否 2:是)
     */
    private Integer warehouseFlag;

    /**
     * 是否虚拟仓(1:否 2：是)
     */
    private Integer virtualWarehouseFlag;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     * 仓库类型1001:平台仓 1002：商家仓 1003：店铺仓
     */
    private String warehouseType;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品原始编号（默认是与goods_no一致，分发到店铺下才不一致）
     */
    private String originalGoodsNo;

    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 第一、二、三属性值编码，格式：first-second-third
     */
    private String attributeValueName;

    public String getAttributeValueName() {
        attributeValueName = "";
        if(StringUtils.isNotEmpty(getFirstAttributeValueName())) {
            attributeValueName+= "-" + getFirstAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getSecondAttributeValueName())) {
            attributeValueName+= "-" + getSecondAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getThirdAttributeValueName())) {
            attributeValueName+= "-" + getThirdAttributeValueName();
        }
        if(StringUtils.isNotEmpty(attributeValueName)) {
            attributeValueName = attributeValueName.substring(1);
        }
        return attributeValueName;
    }

    /**
     * 仓库编号List
     */
    private List<String> warehouseNos;

    /**
     * 是否自建仓库 1:否 2:是
     */
    private Integer selfWarehouseFlag;

    /**
     * 商品所属库款数
     */
    private Integer totalGoodsNum;

    /**
     * 商品所属仓库存数量
     */
    private Integer totalStockNum;

    /**
     * 类目名称全路径
     */
    private String fullNamePath;

    /**
     * 零售价
     */
    private BigDecimal retailPrice;

    /**
     * 销量
     */
    private BigDecimal salesVolume;

    /**
     * 可售库存
     */
    private BigDecimal canSaleStockNum;

    /**
     * 汇享购排序值
     */
    private String hxgSortValue;

    /**
     * 汇享购排序升降值 desc:降值排序,asc:升值排序
     */
    private String hxgSortType;

    /**
     * 库存预警数量
     */
    private Integer inventoryWarningNum;

    /**
     * 库存锁定数量(指该商品在所有仓库中已经下单但尚未发货的商品数量)
     */
    private Integer stockNums;

    @ApiModelProperty("每页条数")
    private int pageSize = 10;
    @ApiModelProperty("总页数")
    private int pageNum = 1;

    /**
     * 仓库数量
     */
    private Integer warehouseCount;


    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     */
    private String multiUnitType;

    /**
     * 多单位主品编号
     */
    private String multiUnitGoodsNo;

    /**
     * 是否查询多单位商品:1只查主品（默认）,2查询主品和子品,3主品和子品都不查
     */
    private Integer isAuxiliaryUnit;

    /**
     * 保质期单位（day日,month月,year年）
     */
    private String shelfLifeUnit;

    /**
     * 保质期
     */
    private Integer qualityGuaranteePeriod;

    /**
     * 是否启动效期管理标识。1否，2是
     */
    private Integer validityPeriodManageFlag;

    /**
     * 查询商品时排除的商品编码
     */
    private List<String> notExistGoods;

    /**
     * 只展示不存在商品组的商品
     *
     * */
    @ApiModelProperty(value = "是否只展示商品组中的商品:空/1展示全部，2不展示已存在商品组中的商品")
    private String notExistsGroupsFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
