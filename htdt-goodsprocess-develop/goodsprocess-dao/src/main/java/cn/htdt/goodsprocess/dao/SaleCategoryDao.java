package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.SaleCategoryDomain;
import cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo;
import cn.htdt.goodsprocess.vo.AtomResSaleCategoryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 销售类目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface SaleCategoryDao extends BaseMapper<SaleCategoryDomain> {

    /**
     * 获取同路径层级下某销售类目
     *
     * @param domain 查询参数
     * @return SaleCategoryDomain
     */
    SaleCategoryDomain selectOneSaleCategory(SaleCategoryDomain domain);

    /**
     * 获取销售类目列表
     *
     * @param saleCategoryVo 查询参数
     * @return List<AtomResSaleCategoryVo>
     */
    List<AtomResSaleCategoryVo> selectSaleCategory(AtomReqSaleCategoryVo saleCategoryVo);

    /**
     * 获取同路径层级下上一个销售类目
     *
     * @param domain 查询参数
     * @return SaleCategoryDomain
     */
    SaleCategoryDomain selectPreviousSaleCategory(SaleCategoryDomain domain);

    /**
     * 获取同路径层级下下一个销售类目
     *
     * @param domain 查询参数
     * @return SaleCategoryDomain
     */
    SaleCategoryDomain selectNextSaleCategory(SaleCategoryDomain domain);

    /**
     * 根据销售类目编号获取销售类目全路径
     *
     * @param domain 查询参数
     * @return SaleCategoryDomain
     */
    SaleCategoryDomain selectFullSaleCategoryPath(SaleCategoryDomain domain);

    /**
     * 获取展示销售类目全路径
     * ps：通过店铺或商家未删除的商品反推，去重，获取类目全路径
     *
     * @param saleCategoryVo 查询参数
     * @return List<SaleCategoryDomain>
     */
    List<SaleCategoryDomain> selectExhibitionSaleCategoryFullIdPath(AtomReqSaleCategoryVo saleCategoryVo);

    /**
     * 查询关联商品主数据的四级销售类目对应的二级销售类目编号(未去重)
     *
     * @param saleCategoryVo 查询参数
     * @return 二级销售类目
     */
    List<String> selectLevelTwoSaleCategory(AtomReqSaleCategoryVo saleCategoryVo);


    /**
     * 查询关联商品的四级销售类目对应的二级销售类目编号
     *
     * @param saleCategoryVo 查询参数
     * @return 二级销售类目
     */
    List<String> selectLevelTwoSaleCategoryByGood(AtomReqSaleCategoryVo saleCategoryVo);

    /**
     * 查询关联商品的四级销售类目对应的二级销售类目编号和类目名称, 先按照一类目录排序, 再按照二级类目排序
     *
     * @param saleCategoryVo 查询参数
     * @return List<AtomResSaleCategoryVo>
     */
    List<AtomResSaleCategoryVo> getSortedLevelTwoSaleCategory(AtomReqSaleCategoryVo saleCategoryVo);

    /**
     * 获取父节点下，排序最大值
     *
     * @param parentNo 父节点id
     * @return int
     */
    int selectMaxSortValue(@Param("parentNo") String parentNo);

    /**
     * 根据参数更新
     *
     * @param domain 销售类目更新信息
     * @return int
     */
    int updateByCategoryNo(SaleCategoryDomain domain);

    /**
     * 批量修改参数
     *
     * @param domain         修改参数
     * @param categoryNoList 类目id集合
     * @return int
     */
    int batchUpdateDisableFlagByCategoryNo(@Param("domain") SaleCategoryDomain domain, @Param("list") List<String> categoryNoList);

    /**
     * 根据批量参数逻辑删除
     *
     * @param domain         修改参数
     * @param categoryNoList 类目id集合
     * @return int
     */
    int batchLogicDelete(@Param("domain") SaleCategoryDomain domain, @Param("list") List<String> categoryNoList);

    /**
     * 根据批量参数物理删除已删除的数据
     *
     * @param categoryNo         修改参数
     * @return int
     */
    int deleteDataByTerm(@Param("categoryNo") String categoryNo);

    /**
     * 批量修改销售类目
     *
     * @param list 修改参数集合
     * @return int
     */
    int batchUpdateSaleCategory(List<SaleCategoryDomain> list);

    /**
     * 修改关联的MDM管理类目
     *
     * @param domain 修改参数
     * @return int
     */
    int updateMdmCategoryNo(SaleCategoryDomain domain);

    /**
     * 获取父类目下所有子类目，根据登录权限获取已勾选类目
     *
     * @param saleCategoryVo 查询参数
     * @return List<AtomResSaleCategoryVo>
     */
    List<AtomResSaleCategoryVo> selectSaleCategoryListByFullIdPath(AtomReqSaleCategoryVo saleCategoryVo);

    /**
     * 根据登录权限获取父类目下已勾选子类目
     *
     * @param saleCategoryVo 查询参数
     * @return List<AtomResSaleCategoryVo>
     */
    List<AtomResSaleCategoryVo> selectSaleCategorySelectedListByFullIdPath(AtomReqSaleCategoryVo saleCategoryVo);

    /**
     * 根据登录权限获取父类目下展示的子类目
     * ps：通过店铺未删除的商品反推
     *
     * @param saleCategoryVo 查询参数
     * @return List<AtomResSaleCategoryVo>
     */
    List<AtomResSaleCategoryVo> selectExhibitionSaleCategoryByFullIdPath(AtomReqSaleCategoryVo saleCategoryVo);

    /**
     * 关联商品表，反推商品类目
     * ps：通过店铺或商家未删除的商品反推，去重，获取类目
     *
     * @param saleCategoryVo 查询参数
     * @return List<SaleCategoryDomain>
     */
    List<SaleCategoryDomain> selectSaleCategoryWithGoods(AtomReqSaleCategoryVo saleCategoryVo);

    /**
     * 根据登录权限获取父类目下已勾选的四级类目
     *
     * @param saleCategoryVo 查询参数
     * @return List<AtomResSaleCategoryVo>
     */
    List<AtomResSaleCategoryVo> selectSaleCategorySelectedListByFour(AtomReqSaleCategoryVo saleCategoryVo);

    /**
     * 根据商品主数据反推销售类目
     *
     * @param saleCategoryVo 查询参数
     * @return List<AtomResSaleCategoryVo>
     */
    List<AtomResSaleCategoryVo> selectSaleCategoryListByGoodsMasterData(AtomReqSaleCategoryVo saleCategoryVo);


    /**
     * 查询指定类目的商品数量
     * @param saleCategoryVo
     * @return
     */
    List<AtomResSaleCategoryVo> selectSaleCategoryNums(AtomReqSaleCategoryVo saleCategoryVo);

    List<AtomResSaleCategoryVo> selectSaleCategoryNumsForPlatform(AtomReqSaleCategoryVo saleCategoryVo);

    /*查询店铺的销售类目末级上架商品数量*/
    List<AtomResSaleCategoryVo> selectSaleCategoryGoodsNumForStore(@Param("storeNo") String storeNo);

    /*查询平台销售类目末级上架云池商品数量*/
    List<AtomResSaleCategoryVo> selectSaleCategoryGoodsNumForPlatForm();

    /**
     * 通过商品反推获取二级类目
     *
     * @param saleCategoryVo 查询参数
     * @return List<SaleCategoryDomain>
     */
    List<AtomResSaleCategoryVo> selectSecondCategoryByGoods(AtomReqSaleCategoryVo saleCategoryVo);
}
