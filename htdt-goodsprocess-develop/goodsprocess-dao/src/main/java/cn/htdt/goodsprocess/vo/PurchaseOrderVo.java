package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.PurchaseOrderDomain;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 采购单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-09
 */
@Data
public class PurchaseOrderVo extends PurchaseOrderDomain {

    private static final long serialVersionUID = 5109263881634695845L;

    /**
     * 店铺编号
     */
    private List<String> storeNoList;


    /**
     * 商家ID
     */
    private String merchantNo;
    /**
     * 商家名称
     */
    private String merchantName;
    /**
     * 店铺ID，对应orgId
     */
    private String storeNo;

    /**
     * 采购编码集合
     */
    private List<String> purchaseCodeList;

    /**
     * 创建开始时间
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建结束时间
     */
    private LocalDateTime createTimeEnd;

    /**
     * 修改开始时间
     */
    private LocalDateTime modifyTimeStart;

    /**
     * 修改结束时间
     */
    private LocalDateTime modifyTimeEnd;

    /**
     * 商品id
     */
    private String goodsNo;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品名称关键词，支持名称、名称拼音、名称拼音首字母模糊查询
     */
    private String goodsNameKeyWord;

    /**
     * 采购开始日期
     */
    private LocalDateTime purchaseDateStart;

    /**
     * 采购结束日期
     */
    private LocalDateTime purchaseDateEnd;

    /**
     * 商品数量
     */
    private Integer goodsNum;

    /**
     * 商品名称
     */
    private String goodsNameArr;

    /**
     * 查询搜索内容
     */
    private String searchContent;

    /**
     * 分组时间
     */
    private String groupTime;

    /**
     * 月度订单总数
     */
    private Integer totalOrder;

    /**
     * 月度金额总数
     */
    private BigDecimal totalAmount;

    /**
     * querySenceId = 2时，supplierName不匹配供应商编码
     */
    private Integer querySenceId;

    /**
     * 模糊匹配供应商名称或商品名称
     */
    private String queryStr;

    /**
     * 采购单状态列表
     */
    private List<String> orderStatusList;

    /**
     * 采购下商品总数
     */
    private Integer totalCount;

    /**
     * 采购商品是否超20条标识：1=无；2=超
     */
    private Integer moreFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}