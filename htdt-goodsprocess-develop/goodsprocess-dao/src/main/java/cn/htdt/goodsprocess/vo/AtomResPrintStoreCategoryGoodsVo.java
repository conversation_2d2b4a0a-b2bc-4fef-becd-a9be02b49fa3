package cn.htdt.goodsprocess.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 店铺类目商品查询返回结果集 自定义DTO
 *
 * <AUTHOR>
 * @date 2022/11/1
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomResPrintStoreCategoryGoodsVo extends AtomResStoreCategoryGoodsVo {

    /**
     * 查询商品类型 1001:全部 1002:平台商品 1003:商家/店铺商品 1004:商家创建商品  1005:店铺创建商品 1006:店铺查询 1007:云池商品查询 1010:平台自建+商家自建+店铺自建+中台同步 1011:中台同步
     */
    private String queryType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
