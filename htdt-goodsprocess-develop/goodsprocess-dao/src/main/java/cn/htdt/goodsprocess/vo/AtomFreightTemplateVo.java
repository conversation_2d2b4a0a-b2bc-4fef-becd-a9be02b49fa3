package cn.htdt.goodsprocess.vo;

import cn.htdt.common.domain.BaseDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 运费模板
 *
 * <AUTHOR>
 * @date 2020年12月2日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomFreightTemplateVo extends BaseDomain {

	private static final long serialVersionUID = 1L;

	/**
	 * 模板编号
	 */
	private String templateNo;

	/**
	 * 商家编号
	 */
	private String merchantNo;

	/**
	 * 归属平台编号
	 */
	private String companyNo;

	/**
	 * 运费模板名称
	 */
	private String name;

	/**
	 * 配送方式编码
	 */
	private String distributionCode;

	/**
	 * 配送区域类型 0:默认全国; -1:非默认，自定义区域
	 */
	private String distributionType;

	/**
	 * 模板类型 {1000: 基本模板类型; 1001:O+O模板类型; }
	 */
	private String templateType;

	/**
	 * 类型区分 {1010:自定义运费; 1011:卖家承担运费; }
	 */
	private String type;

	/**
	 * 计费方式 包邮条件 { 计费方式 1010:按件数; 1030:一口价;}
	 */
	private String chargeWay;

	/**
	 * 是否默认 1:默认; 0:非默认
	 */
	private String defaultFlag;

	/**
	 * 渠道模式，保存格式(BBC,S2B,POS,B2B,O+O)
	 */
	private String channelMode;

	/**
	 * 配送区域
	 */
	private String distributionRegion;

	/**
	 * 是否已经删除，默认1未删除，其余已删除
	 */
	private Integer disableFlag;

	/**
	 * 归属平台名称
	 */
	private String companyName;

	/**
	 * 归属分部编号
	 */
	private String branchNo;

	/**
	 * 归属分部名称
	 */
	private String branchName;

	/**
	 * 首件
	 */
	private Integer templateFirstPiece;

	/**
	 * 首费
	 */
	private BigDecimal templateFirstAmount;

	/**
	 * 续件
	 */
	private Integer templateNextPiece;

	/**
	 * 续费
	 */
	private BigDecimal templateNextAmount;

	/**
	 * 一口价
	 */
	private BigDecimal templateFixedAmount;
	
    /**
     * 门店编号
     */
    private String storeNo;
    
    /**
     * 模板来源 1001-平台自建;1002-商家自建;1003-店铺自建
     */
    private String templateSourceType;
    
    /**
     * 是否默认   1001:默认;  1002:非默认;
     */
    private String templateDefaultFlag;

	/**
	 * 全部模板个数
	 */
	private Integer allTemplateCount;

	/**
	 * 商家模板个数
	 */
	private Integer merchantTemplateCount;

	/**
	 * 店铺模板个数
	 */
	private Integer storeTemplateCount;

	/**
	 * 是否满额免运费，默认1否，2是
	 */
	private Integer templateFreeFlag;

	/**
	 * 免运费金额限制
	 */
	private BigDecimal templateFreeAmount;

	@Override
	public String toString() {
		return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
	}

}
