package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsTagDomain;
import cn.htdt.goodsprocess.vo.AtomGoodsTagVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsTagDao extends BaseMapper<GoodsTagDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<GoodsTagDomain> selectByParams(GoodsTagDomain domain);

    /**
     * 根据参数插入
     * @param domain
     * @return
     */
    int insertSelective(GoodsTagDomain domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsTagDomain domain);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(GoodsTagDomain domain);
    
    /**
     * 根据参数分页查询商品标签列表
     * @param codeDomain
     * @return
     */
    List<GoodsTagDomain> selectGoodsTagListPage(GoodsTagDomain codeDomain);
    
    
    /**
     * 批量删除商品标签
     * @param spxxNos
     * @return
     */
    int batchDelGoodsTag(@Param("tagNos") List<String> tagNos);
    
    /**
     * 获取单个商品标签详情
     * @param domain
     * @return
     */
    GoodsTagDomain getGoodsTagByParams(GoodsTagDomain domain);
    
    /**
     * 连接查询，根据商品编号查询商品标签
     * @param atomGoodsTagVo
     * @return
     */
    List<AtomGoodsTagVo>  getTagNamesByGoodsNo(AtomGoodsTagVo atomGoodsTagVo); 
    
}
