package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.SaleCategoryRelationDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 销售类目关联表查询自定义VO
 *
 * <AUTHOR>
 * @date 2020/11/10
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomReqSaleCategoryRelationVo extends SaleCategoryRelationDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 类目编号集合
     */
    private List<String> categoryNoList;

    /**
     * 类目id全路径
     */
    private String fullIdPath;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
