package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 各状态采购单/采购退单数量统计
 * <AUTHOR>
 * @since 2022-05-07
 */
@Data
public class AtomResOrderStatusCountVo implements Serializable {

    /**
     * orderStatus
     */
    private String orderStatus;
    /**
     * orderCount
     */
    private long orderCount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}