package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsAuditWhiteDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品审核白名单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsAuditWhiteDao extends BaseMapper<GoodsAuditWhiteDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<GoodsAuditWhiteDomain> selectByParams(GoodsAuditWhiteDomain domain);

    /**
     * 根据参数插入
     * @param domain
     * @return
     */
    int insertSelective(GoodsAuditWhiteDomain domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsAuditWhiteDomain domain);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(GoodsAuditWhiteDomain domain);
}
