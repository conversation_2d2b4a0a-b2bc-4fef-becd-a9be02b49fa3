package cn.htdt.goodsprocess.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import cn.htdt.goodsprocess.domain.GoodsDomain;
import cn.htdt.goodsprocess.vo.*;

@Mapper
public interface GoodsDao extends BaseMapper<GoodsDomain> {

    /**
     * 分页查询所有商品信息同步到es
     *
     * @param reqSyncGoodsVo
     * @return
     */
    List<AtomGoodsVo> selectAllGoodsPage(AtomReqSyncGoodsVo reqSyncGoodsVo);

    /**
     * 根据参数查询集合
     *
     * @param vo
     * @return
     */
    List<AtomGoodsVo> selectByParams(AtomGoodsVo vo);

    /**
     * 根据参数查询商品集合--根据商家商品编号查询关联的店铺商品、根根店铺商品编号查询商家商品信息此类场景使用
     *
     * @param vo
     *            请求参数
     * @return List<AtomGoodsAsyncVo>
     */
    List<AtomGoodsAsyncVo> getGoodsByParam(AtomGoodsAsyncVo vo);

    /**
     * 根据参数查询集合-商品选择用
     *
     * @param vo
     * @return
     */
    List<AtomGoodsVo> selectGoodsByParams(AtomGoodsVo vo);

    /**
     * 根据params查询list商品信息 商品全部信息
     *
     * @param vo
     * @return
     * @author: 卜金隆
     */
    List<AtomGoodsVo> selectGoodsInfoByParams(AtomGoodsVo vo);

    /**
     * 根据goodsNo查询某个商品信息
     *
     * @param goodsNo
     * @return
     */
    AtomGoodsVo selectGoodsByNo(@Param("goodsNo") String goodsNo);

    /**
     * 根据thirdGoodsNo查询某个商品信息-目前虚拟商品用
     *
     * @param thirdGoodsNo
     * @return
     */
    AtomGoodsVo selectGoodsByThirdGoodsNo(@Param("thirdGoodsNo") String thirdGoodsNo);

    /**
     * 根据thirdGoodsNos查询商品信息list-目前虚拟商品用
     *
     * @param thirdGoodsNos
     * @return
     */
    List<AtomGoodsVo> selectGoodsListByThirdGoodsNos(@Param("thirdGoodsNos") List<String> thirdGoodsNos);

    /**
     * 根据customGoodsNos查询商品信息list-目前虚拟商品用
     *
     * @param customGoodsNos
     * @return
     */
    List<AtomGoodsVo> selectGoodsListByCustomGoodsNos(@Param("merchantNo") String merchantNo, @Param("storeNo") String storeNo,
                                                      @Param("customGoodsNos") List<String> customGoodsNos);

    /**
     * 根据goodsNos查询一批商品信息
     *
     * @Date 2021/10/21
     * <AUTHOR>
     * @param goodsNos
     * @return List<AtomGoodsVo>
     **/
    List<AtomGoodsVo> selectGoodsByNoList(@Param("goodsNos") List<String> goodsNos);

    /**
     * 根据条件查询商品列表
     * @Date 2023/7/27
     * <AUTHOR>
     * @param atomGoodsVo
     * @return List<AtomGoodsVo>
     **/
    List<AtomGoodsVo> selectGoodsByParam(AtomGoodsVo atomGoodsVo);

    /**
     * <AUTHOR>
     * @Description 通过商品编号获取商品记录
     * @Date 2021/7/2
     * @Param [goodsDomain]
     * @return cn.htdt.goodsprocess.domain.GoodsDomain
     **/
    GoodsDomain selectDetailByGoodsNo(GoodsDomain goodsDomain);

    /**
     * <AUTHOR>
     * @Description 通过系列商品主品编号获取系列商品记录
     * @Date 2021/7/2
     * @Param [goodsDomain]
     * @return java.util.List<cn.htdt.goodsprocess.domain.GoodsDomain>
     **/
    List<GoodsDomain> selectByParentGoodsNo(GoodsDomain goodsDomain);

    /**
     * 通过系列商品主品编号获取系列商品编号
     *
     * @param atomGoodsVo
     *            请求参数
     * @return 子品的商品编号集合
     */
    List<AtomHxgParentAndSubGoodsNoVo> selectGoodsNoByParentGoodsNo(AtomGoodsVo atomGoodsVo);

    /**
     * <AUTHOR>
     * @Description 通过系列商品主品编号获取系列商品最高价和最低价
     * @Date 2021/7/2
     * @Param [goodsDomain]
     * @return cn.htdt.goodsprocess.vo.AtomMaxMinPriceVo
     **/
    AtomMaxMinPriceVo selectRetailPriceByParentGoodsNo(GoodsDomain goodsDomain);

    /**
     * 根据goodsNos查询list商品信息
     *
     * @param vo
     * @return
     */
    List<AtomGoodsVo> selectGoodsByNos(AtomGoodsVo vo);

    /**
     * 查询参数条件下存在的商品个数
     *
     * @param vo
     * @return
     */
    int selectDuplicateCount(AtomGoodsVo vo);

    /**
     * 查询参数条件下存在的商品个数
     * @Date 2023/7/21
     * <AUTHOR>
     * @param goodsDomain
     * @return int
     **/
    String selectCountByMerchantGoodsNo(GoodsDomain goodsDomain);

    /**
     * 根据参数更新
     *
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsDomain domain);

    /**
     * 更新商品状态
     *
     * @param domain
     * @return
     */
    int updateGoodsStatus(GoodsDomain domain);

    /**
     * 根据参数逻辑删除
     *
     * @param goodsVo
     * @return
     */
    int logicDelete(AtomGoodsVo goodsVo);

    /**
     * 根据goodsNos查询list商品（调拨）
     *
     * @param goodsVo
     * @return
     */
    List<AtomGoodsVo> selectWarehouseAllocationOrderParams(AtomGoodsVo goodsVo);

    /**
     * 功能描述:批量删除商品
     *
     * @param vo
     * @return
     * @author: 张宇
     * @date: 2020/10/15 17:04
     */
    int batchLogicDelete(AtomGoodsVo vo);

    /**
     * 功能描述:批量更新商品信息
     *
     * @param vo
     * @return
     * @author: 张宇
     * @date: 2020/10/15 17:04
     */
    int batchUpdateGoods(AtomGoodsVo vo);

    /**
     * 功能描述:批量更新商品信息-更新为默认类目
     *
     * @param vo
     * @return
     * @author: 张宇
     * @date: 2020/10/15 17:04
     */
    int updateGoodsDefaultCategory(AtomGoodsVo vo);

    /**
     * 功能描述:批量更新商品信息-更新为默认无品牌
     *
     * @param vo
     * @return
     * @author: 张宇
     * @date: 2020/10/15 17:04
     */
    int updateGoodsDefaultBrand(AtomGoodsVo vo);

    /**
     * 根据参数查询集合
     *
     * @param vo
     * @return
     * @author: 陈飞
     */
    List<AtomGoodsVo> getHxgGoodsPageList(AtomGoodsVo vo);

    /**
     * 汇享购, 查询店铺是否存在无销售类目的商品
     *
     * @param vo
     *            查询参数
     * @return 商品数量
     */
    int getNonSaleCategoryHxgGoodsCount(AtomGoodsVo vo);

    /**
     * 根据参数查询集合(店铺类目)
     *
     * @param vo
     * @return
     * @author: 陈飞
     */
    List<AtomGoodsVo> getHxgGoodsStoreCategoryPageList(AtomGoodsVo vo);

    /**
     * 汇享购分页查询无类目商品
     *
     * @param vo
     *            查询参数
     * @return 结果
     */
    List<AtomGoodsVo> getHxgNonStoreCategoryGoodsPageList(AtomGoodsVo vo);

    /**
     * 获取商品审核列表
     *
     * @param vo
     * @return
     */
    List<AtomGoodsVo> getGoodsAuditPageList(AtomGoodsVo vo);

    /**
     * 更新商品审核状态
     *
     * @param domain
     * @return
     * @author: 陈飞
     */
    int updateGoodsAuditStatus(AtomGoodsVo domain);

    /**
     * 根据参数查询系列商品
     *
     * @param vo
     * @return
     */
    List<AtomGoodsVo> selectSeriesGoodsByParams(AtomGoodsVo vo);

    /**
     * 更新商品销量-新增
     *
     * @param vo
     * @return
     * @author: wl
     */
    int updateGoodsSales(AtomGoodsVo vo);

    /**
     * 更新商品销量-扣减
     *
     * @param vo
     * @return
     * @author: wl
     */
    int updateGoodsSalesDel(AtomGoodsVo vo);

    /**
     * 批量修改商品信息
     *
     * @param domainList
     *            商品信息
     * @return int
     * <AUTHOR>
     */
    int batchUpdateGoodsInfo(@Param("list") List<GoodsDomain> domainList);

    /**
     * 修改商品可用状态
     *
     * @param vo
     *            修改信息
     * @return int
     * <AUTHOR>
     */
    int updateCloudPoolGoodsDisableFlag(AtomGoodsVo vo);

    /**
     * 查询云池申请商品列表
     *
     * @param vo
     *            查询参数
     * @return List<AtomGoodsVo>
     * <AUTHOR>
     */
    List<AtomGoodsVo> selectCloudPoolApplyGoodsByParam(AtomGoodsVo vo);

    /**
     * 查询云池商品列表
     *
     * @param vo
     *            查询参数
     * @return List<AtomGoodsVo>
     * <AUTHOR>
     */
    List<AtomGoodsVo> selectCloudPoolGoodsByParam(AtomGoodsVo vo);

    /**
     * 根据参数查询分销商品类表
     *
     * @param vo
     * @return
     * @author: 陈飞
     */
    List<AtomGoodsVo> getDistributionGoodsPageList(AtomGoodsVo vo);

    /**
     * 根据会员店编码和云池原型商品编码查出所有的商品
     *
     * @param vo
     * @return
     * @author: 卜金隆
     */
    List<AtomGoodsVo> getCloudPoolGoodsList(AtomGoodsVo vo);

    /**
     * 每日必推查询列表
     *
     * @param vo
     * @return
     * @author: 陈飞
     */
    List<AtomGoodsVo> getEveryDayGoodsPageList(AtomGoodsVo vo);

    /**
     * 获取云池商品四级销售类目
     *
     * @param vo
     * @return
     * @author: 陈飞
     */
    List<AtomGoodsVo> getHxgGoodsCategoryList(AtomGoodsVo vo);

    /**
     * 获取店铺前3商品
     *
     * @param vo
     * @return
     * @author: 陈飞
     */
    List<AtomGoodsVo> getStoreGoodsList(AtomGoodsVo vo);

    /**
     * 批量修改商品云池佣金信息
     *
     * @param domainList
     *            商品信息
     * @return int
     * <AUTHOR>
     */
    int batchUpdateCoolPoolGoodsInfo(@Param("list") List<AtomGoodsVo> domainList);

    /**
     * 根据参数查询分销商品类表
     *
     * @param vo
     * @return
     * @author: 王磊
     */
    List<AtomGoodsVo> selectGoodsSelectList(AtomGoodsVo vo);

    /**
     * 根据会员店编码和云池原型商品编码查出所有的商品
     *
     * @param vo
     * @return
     * @author: 王磊
     */
    List<AtomGoodsVo> selectCloudGoodsSelectList(AtomGoodsVo vo);

    /**
     * 查看商品数量
     *
     * @param goodsVo
     * @return
     */
    Integer countByParams(AtomGoodsVo goodsVo);

    /**
     * <AUTHOR>
     * @Description 查询店铺是否存在分销商品(做入口)
     * @Date 2021/6/29
     * @Param []
     * @return java.lang.Integer
     **/
    int countDistributionGoodsByStoreNo(GoodsDomain goodsDomain);

    /**
     * <AUTHOR>
     * @Description 查询 3条 新上架的三款商品
     * @Date 2021/7/1
     * @Param [goodsDomain]
     * @return java.util.List<cn.htdt.goodsprocess.vo.AtomGoodsNameNoVo>
     **/
    List<AtomGoodsNameNoVo> selectGoodsNameNoByCreatTime(GoodsDomain goodsDomain);

    /**
     * <AUTHOR>
     * @Description 分销商品列表 (app消息推送-手动选择)
     * @Date 2021/7/2
     * @Param []
     * @return java.util.List<cn.htdt.goodsprocess.vo.AtomGoodsManualMessagePushVo>
     **/
    List<AtomGoodsManualMessagePushVo> manualMessagePush(GoodsDomain goodsDomain);

    /**
     * bossApp -> 优惠券创建 选择商品页-> 分页-供选择页用 排序：1、价格 2、库存 3、销量 4、默认 即按商品更新时间倒序排序。 数据来源：显示店铺中自主创建和商家分发的商品，且审核通过 优惠券：只展示主品
     * 抽奖：只展示子品
     *
     * @author: 卜金隆
     * @param vo
     * @return List<AtomGoodsVo>
     */
    List<AtomGoodsVo> getBossAppGoodsPage(AtomGoodsVo vo);

    /**
     * 获取店铺下是否有 无品牌商品/无类目商品 ps：通过店铺商品反推，去重，获取商品品牌 bossApp展示无分类用
     *
     * @return ExecuteDTO<Integer>
     * <AUTHOR>
     */
    Integer countNoBrandGoods(AtomGoodsVo vo);

    /**
     * 获取店铺下商品名称首字母 ps：通过店铺商品反推 入参：storeNo
     *
     * @return ExecuteDTO<List<String>>
     * <AUTHOR>
     */
    List<String> selectGoodsInitialByStoreGoods(AtomGoodsVo vo);

    /**
     * 根据参数查询集合
     *
     * @param vo
     * @return
     */
    List<AtomGoodsVo> getGoodsStoreCategory(DecoAtomGoodsVo vo);

    /**
     * pc-店铺装修, 单独查询商品
     *
     * @param vo
     *            查询参数
     * @return 商品信息
     */
    List<AtomGoodsVo> getDecoGoodsPage(AtomGoodsVo vo);

    /**
     * pc-店铺装修, 根据店铺类目和销售类目查询商品
     *
     * @param vo
     *            查询参数
     * @return 商品信息
     */
    List<AtomGoodsVo> getDecoGoodsPageByCategory(DecoAtomGoodsVo vo);

    /**
     * 获取商品库存列表
     *
     * @param vo
     *            查询参数
     * @return List<AtomGoodsVo>
     */
    List<AtomGoodsVo> selectGoodsRealStock(AtomGoodsVo vo);

    /**
     * 编辑库存预警开关
     *
     * @param vo
     *            修改内容
     * @return int
     */
    int updateGoodsInventoryWarningFlag(AtomGoodsVo vo);

    /**
     * 查询参数条件下商品的个数
     *
     * @param vo
     *            参数
     * @return
     */
    int selectGoodsCountOfParam(AtomGoodsVo vo);

    /**
     * 查询商品店铺类目
     *
     * @param vo
     *            参数
     * @return List<String> 店铺类目编号集合
     */
    List<String> selectGoodsStoreCategory(AtomGoodsVo vo);

    /**
     * 查询没有店铺自建类目的商品数量
     *
     * @param atomGoodsVo
     *            参数
     * @return 数量
     */
    int selectNoStoreCategoryGoodsCount(AtomGoodsVo atomGoodsVo);

    /**
     * 查询汇享购是否存在无店铺类目的商品
     *
     * @param atomGoodsVo
     *            查询参数
     * @return 结果
     */
    int selectHxgNoStoreCategoryGoodsCount(AtomGoodsVo atomGoodsVo);

    /**
     * 千橙掌柜App, 打印商品标签, 查询子品和普通商品列表
     *
     * @param goodsDomain
     *            请求参数
     * @return 结果
     */
    List<AtomGoodsVo> getBossAppSubGoodsListPage(PrintAtomGoodsVo goodsDomain);

    /**
     * 获取商品采购数据
     *
     * @param vo
     * @return
     */
    List<AtomGoodsStockInfoVo> selectGoodsStockInfoList(AtomGoodsSaleInfoVo vo);

    /**
     * 获取商品采购单价信息
     *
     * @param vo
     * @return
     */
    List<AtomGoodsStockInfoVo> selectGoodsPriceList(AtomGoodsSaleInfoVo vo);

    /**
     * 获取商品库存信息
     *
     * @param vo
     * @return
     */
    List<AtomGoodsStockInfoVo> selectGoodsStockNumList(AtomGoodsSaleInfoVo vo);

    /**
     * 按商品类型分组获取商品数量
     *
     * @param vo
     * @return
     */
    List<AtomGoodsVo> selectGoodsCountByGoodsType(AtomGoodsVo vo);

    /**
     * 查询店铺或者商家的商品sku数
     *
     * @param vo
     *            查询参数
     * @return 结果
     */
    int selectGoodsSkuCount(AtomGoodsVo vo);

    /**
     * 获取类目排序值
     *
     * @param atomSortNumVo
     *            查询参数
     * @return int 排序值
     */
    int selectGoodsSortNum(AtomSortNumVo atomSortNumVo);

    /**
     * 查询商品是有仓商品还是无仓商品
     *
     * @param atomGoodsVo 查询参数
     * @return 商品信息
     */
    List<AtomGoodsVo> selectGoodsWarehouseFlag(AtomGoodsVo atomGoodsVo);

    /**
     * 商品向上或向下移动1
     *
     * @param atomSortNumVo
     */
    void moveBetweenSortNum(AtomSortNumVo atomSortNumVo);

    /**
     * 修改商品排序值
     *
     * @param atomSortNumVo
     *            请求参数
     * @return
     */
    int updateSortNum(AtomSortNumVo atomSortNumVo);

    /**
     * 修改商品同步状态
     *
     * @param domain
     * @return
     */
    int updateGoodsDistributionStatus(GoodsDomain domain);

    /**
     * 批量修改商品同步状态
     *
     * @param vo
     * @return
     */
    int batchUpdateGoodsDistributionStatus(AtomGoodsVo vo);
    /**
     * 查询参数条件下的商品个数
     * <AUTHOR>
     * @param goodsDomain
     * @return
     **/
    List<GoodsDomain> selectListByMerchantGoodsNo(GoodsDomain goodsDomain);
}
