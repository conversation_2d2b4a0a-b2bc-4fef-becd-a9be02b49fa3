package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsShelfBatchDomain;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商品效期批次
 *
 * <AUTHOR>
 */
@Data
public class GoodsShelfBatchVO extends GoodsShelfBatchDomain {

    private static final long serialVersionUID = -1527522833107310157L;

    /**
     * 临期预警设置剩余百分比，例如：50%
     */
    private BigDecimal temporaryWarningSettings;

    /**
     * 临期商品数
     */
    private Integer expiringSoonGoodsNum;

    /**
     * 过期商品数
     */
    private Integer expiringGoodsNum;

    /**
     * 操作类型(1001:加库 1002：减库)
     */
    private String operType;

    /**
     * 商品编码集合
     */
    private List<String> goodsNos;

}
