package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class RealStockNumByParentGoodsNoVo implements Serializable {

    /**
     * 商品库存数量
     * */
    private BigDecimal realStockNum;
    /**
     * 系列商品主品编号
     * */
    private String parentGoodsNo;
    /**
     * 非系列商品编号/系列商品子品编号
     */
    private String GoodsNo;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
