package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsDescribeDomain;
import cn.htdt.goodsprocess.vo.AtomGoodsDescribeVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品文描表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsDescribeDao extends BaseMapper<GoodsDescribeDomain> {

    int deleteByGoodsNo(AtomGoodsDescribeVo vo);

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<GoodsDescribeDomain> selectByParams(AtomGoodsDescribeVo domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsDescribeDomain domain);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(GoodsDescribeDomain domain);
}
