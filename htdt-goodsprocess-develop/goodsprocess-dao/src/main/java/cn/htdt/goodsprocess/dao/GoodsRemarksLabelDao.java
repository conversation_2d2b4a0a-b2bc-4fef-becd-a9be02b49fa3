package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsRemarksLabelDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品备注标签表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-18
 */
@Mapper
public interface GoodsRemarksLabelDao extends BaseMapper<GoodsRemarksLabelDomain> {

    /**
     * 根据参数查询
     *
     * @param domain 查询参数
     * @return List<GoodsRemarksLabelDomain>
     */
    List<GoodsRemarksLabelDomain> selectByParams(GoodsRemarksLabelDomain domain);

    /**
     * 根据参数批量修改
     *
     * @param list 请求参数
     * @return int
     */
    int batchUpdateByParam(List<GoodsRemarksLabelDomain> list);

    /**
     * 根据参数逻辑删除
     *
     * @param domain 请求参数
     * @return int
     */
    int logicDelete(GoodsRemarksLabelDomain domain);
}
