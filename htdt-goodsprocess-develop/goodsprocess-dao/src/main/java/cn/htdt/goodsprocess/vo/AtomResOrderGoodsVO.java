package cn.htdt.goodsprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 采购单返回结果Dto
 *
 * <AUTHOR>
 * @date 2020年9月15日
 */
@Data
public class AtomResOrderGoodsVO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *采购单编码
     */
    private String purchaseCode;
    /**
     *采购单商品行编码
     */
    private String purchaseGoodsCode;
    /**
     *商品编码
     */
    private String goodsNo;
    /**
     *商品名称
     */
    private String goodsName;
    /**
     *采购单价
     */
    private BigDecimal purchaseUnitPrice;
    /**
     *采购数量
     */
    private BigDecimal purchaseNum;
    /**
     *交易金额
     */
    private BigDecimal purchasePrice;
}
