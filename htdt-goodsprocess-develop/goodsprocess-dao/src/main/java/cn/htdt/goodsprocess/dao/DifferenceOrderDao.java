package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.vo.AtomDifferenceOrderVo;
import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import cn.htdt.goodsprocess.domain.DifferenceOrderDomain;

import java.util.List;

/**
 * <AUTHOR>
 * @description 差异单dao
 * @date 2023/5/23 15:19
 **/
@Mapper
public interface DifferenceOrderDao extends BaseMapper<DifferenceOrderDomain> {

    /**
     * 查询差异单
     *
     * @param atomDifferenceOrderVo 查询参数
     * @return 差异单列表
     */
    List<DifferenceOrderDomain> getDifferenceOrderList (AtomDifferenceOrderVo atomDifferenceOrderVo);

    /**
     * 查询差异单详情
     *
     * @param atomDifferenceOrderVo 查询参数
     * @return 差异单
     */
    DifferenceOrderDomain getDifferenceOrderDetail(AtomDifferenceOrderVo atomDifferenceOrderVo);

    /**
     * 更新差异单
     *
     * @param differenceOrderDomain 请求参数
     * @return 更新结果
     */
    int updateDifferenceOrder(DifferenceOrderDomain differenceOrderDomain);
}
