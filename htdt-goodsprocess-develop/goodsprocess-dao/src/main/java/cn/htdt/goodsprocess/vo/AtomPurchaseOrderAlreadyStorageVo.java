package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *  采购单已入库Vo
 *
 * <AUTHOR>
 * @date 2020年9月15日
 */
@Data
public class AtomPurchaseOrderAlreadyStorageVo implements Serializable {

    private static final long serialVersionUID = 6605874684991208877L;
    /**
     * 入库时间
     */
    private LocalDateTime createTime;
    /**
     * 采购单编码
     */
    private String purchaseCode;
    /**
     * 采购单商品行编码
     */
    private String purchaseGoodsCode;
    /**
     * 仓库编码
     */
    private String warehouseNo;
    /**
     * 仓库名称
     */
    private String warehouseName;
    /**
     * 商品编码
     */
    private String goodsNo;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 计量单位名称
     */
    private String calculationUnitName;
    /**
     * 采购单位
     */
    private String purchaseUnit;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 计量单位id
     */
    private String calculationUnitNo;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;
    /**
     * 是否入仓:1-否;2-是;
     */
    private Integer warehouseFlag;
    /**
     * 采购单价
     */
    private BigDecimal purchaseUnitPrice;
    /**
     * 采购数量
     */
    private BigDecimal purchaseNum;

    /**
     * 已入库数量（按主计量单位计算）
     */
    private BigDecimal stockNum;

    /**
     * 已入库数量（按采购单位算）
     */
    private BigDecimal storageCount;

    /**
     * 商品是否已经删除 默认1未删除，2已删除
     */
    private Integer goodsDelFlag;
    /**
     * 商品是否已经删除
     */
    private String goodsDelFlagStr;
    /**
     * 是否启用串码(1:否 2:是)
     */
    private Integer imeiFlag;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 商品名称(不包含属性值拼接)
     */
    private String goodsBaseName;

    /**
     * 2023-09-14蛋品-genghao-采购管理-获取已入库商品列表
     * 多单位商品类型 1001:主单位商品 1002:子
     */
    private String multiUnitType;

    /**
     * 多单位主品编号
     */
    private String multiUnitGoodsNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
