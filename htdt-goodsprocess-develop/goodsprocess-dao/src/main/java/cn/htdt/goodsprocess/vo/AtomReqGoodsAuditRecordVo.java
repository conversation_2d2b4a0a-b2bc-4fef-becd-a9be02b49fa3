package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsAuditRecordDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 商品审核操作 自定义VO
 *
 * <AUTHOR>
 * @date 2020/12/8
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomReqGoodsAuditRecordVo extends GoodsAuditRecordDomain {
    private static final long serialVersionUID = 1L;
    
    /**
     * 基础信息审核单号集合
     */
    private List<String> auditRecordNoList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
