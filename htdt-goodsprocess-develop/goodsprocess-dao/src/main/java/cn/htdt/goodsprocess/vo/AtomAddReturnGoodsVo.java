package cn.htdt.goodsprocess.vo;
/**
 * <AUTHOR>
 * @Date 2020-10-20
 * @Description  请求DTO
 **/

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020-10-26
 * @Description  采购单下所有的商品已退货和退货中的数量 请求DTO
 **/
@Data
public class AtomAddReturnGoodsVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *采购单商品行编码
     */
    private String purchaseGoodsCode;
    /**
     * 商品id
     * */
    private String goodsNo;
    /**
     * 商品名称
     * */
    private String goodsName;
    /**
     * 商品删除状态
     * */
    private String goodsDeleteFlag;
    /**
     *单价
     */
    private String purchaseUnit;
    /**
     * 入库数量
     * */
    private BigDecimal purchaseNum;
    /**
     *单价
     */
    private BigDecimal purchaseUnitPrice;

    /**
     *采购价
     */
    private BigDecimal purchasePrice;
    /**
     * 是否入库
     * */
    private Integer warehouseType;
    /**
     * 商品当前是否入库 1否2是
     */
    private Integer goodsWarehouseFlag;
    /**
     * 仓库编码
     * */
    private String warehouseNo;
    /**
     * 仓库名称
     * */
    private String warehouseName;
    /**
     * 总入库数量
     * */
    private BigDecimal storageCount;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 计量单位ID-返回
     */
    private String calculationUnitNo;

    /**
     * 计量单位名称
     */
    private String calculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 实体库存
     */
    private BigDecimal realStockNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
