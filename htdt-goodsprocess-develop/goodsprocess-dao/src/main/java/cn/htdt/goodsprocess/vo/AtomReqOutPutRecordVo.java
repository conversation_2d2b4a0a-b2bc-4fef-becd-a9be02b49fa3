package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 一体机-经营报表-出入库记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-19
 */
@Data
public class AtomReqOutPutRecordVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 出入库类型 1001:入库 1002：出库
     */
    private String operType;

    /**
     * 商品名称、条形码 条件查询
     */
    private String goodsStr;

    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 是否启用串码(1:否 2:是)
     */
    private Integer imeiFlag;

    /**
     * 商品品牌ID
     */
    private String brandNo;

    /**
     * 商品类目ID
     */
    private String categoryNo;

    /**
     * 类目全路径
     */
    private String fullIdPath;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 门店编号
     */
    private String storeNo;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
