package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.TradeGoodsMediaDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品图信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Mapper
public interface TradeGoodsMediaDao extends BaseMapper<TradeGoodsMediaDomain> {

    /**
     * 根据商品编号获取商品图片信息List
     * @param domain
     * @return
     */
    List<TradeGoodsMediaDomain> selectTradeGoodsMediaBySkuCode(TradeGoodsMediaDomain domain);

    /**
     * 根据商品编号及图片地址获取商品图片信息
     * @param domain
     * @return
     */
    int selectCountByPicUrlAndSkuCode(TradeGoodsMediaDomain domain);

}
