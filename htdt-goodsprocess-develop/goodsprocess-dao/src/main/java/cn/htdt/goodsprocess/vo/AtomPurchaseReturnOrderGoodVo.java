package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 采购退货单商品
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AtomPurchaseReturnOrderGoodVo extends PurchaseReturnOrderGoodsDomain implements Serializable {

    private static final long serialVersionUID = -4751919176121163219L;

    /**
     *已出库数量
     */
    private BigDecimal warehouseOutCount;

    /**
     * 待出库数量
     */
    private BigDecimal waitingWarehouseOut;

    /**
     * 实体库存
     */
    private BigDecimal realStockNum;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;

    /**
     * 计量单位ID
     */
    private String calculationUnitNo;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 最新一次出入库时间
     */
    private LocalDateTime lastStockTime;

    /**
     * 采退单商品行编码列表
     */
    private List<String> returnGoodsCodeList;

    /**
     * 商品的入库标识
     */
    private Integer goodsWarehouseFlag;
}
