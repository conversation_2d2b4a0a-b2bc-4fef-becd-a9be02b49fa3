package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsGroupsDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomGoodsGroupsVo extends GoodsGroupsDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 商品组编码list
     */
    private List<String> goodsGroupsNoList;

    /**
     * 已选择过的采购商品组编码
     */
    private List<String> notExistGoodsGroups;

    /**
     * 权限内区域集合
     */
    private List<String> ucRegionList;
    private String ucRegionListStr;

    /**
     * 查询商品类型 1001：全部 1002:平台商品 1003:商家/店铺商品 1004:商家创建商品 1005:店铺创建商品 1006:店铺查询 1007:云池商品查询 1010:平台自建+商家自建+店铺自建+中台同步 1011:中台同步
     */
    private String queryType;

    /**
     * app根据关键字搜索
     */
    private String bossAppGoodsSearchKey;

    /**
     * 商品
     */
    private String goodsNo;

    /**
     * 商品编码列表
     */
    private List<String> goodsNos;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
