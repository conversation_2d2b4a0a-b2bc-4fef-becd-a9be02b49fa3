package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.TradeOrdersDomain;
import cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO;
import cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderNoDTO;
import cn.htdt.goodsprocess.dto.response.tradeorder.ResPurchaseTradeOrderDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@Mapper
public interface TradeOrdersDao extends BaseMapper<TradeOrdersDomain> {

    /**
     * @description 分页查询采购订单
     * <AUTHOR>
     * @date 2022-08-23 15:10:05
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    List<ResPurchaseTradeOrderDTO> selectPurchaseTradeOrderByPage(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    ResPurchaseTradeOrderDTO selectPurchaseTradeOrderInfo(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    List<TradeOrdersDomain> selectByOrderNoList(ReqPurchaseTradeOrderNoDTO reqPurchaseTradeOrderNoDTO);

    int batchUpdateOrder(@Param("list") List<TradeOrdersDomain> domainList);

    /**
     * @description 查询采购交易总金额和订单量
     * <AUTHOR>
     * @date 2022-08-22 15:37:57
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    ResPurchaseTradeOrderDTO selectTradeOrdersCount(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    /**
     * @description 查询最近一次采购交易单信息
     * <AUTHOR>
     * @date 2022-08-22 15:38:07
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    ResPurchaseTradeOrderDTO selectLastTradeOrders(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    /**
     * 蛋品分页查询B2B采购报表
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    List<ResPurchaseTradeOrderDTO> selectB2BTradeOrderByPage(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

}
