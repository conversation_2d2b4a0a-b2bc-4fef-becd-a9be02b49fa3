package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsServiceDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品服务表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsServiceDao extends BaseMapper<GoodsServiceDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<GoodsServiceDomain> selectByParams(GoodsServiceDomain domain);

    /**
     * 根据参数插入
     * @param domain
     * @return
     */
    int insertSelective(GoodsServiceDomain domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsServiceDomain domain);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(GoodsServiceDomain domain);
}
