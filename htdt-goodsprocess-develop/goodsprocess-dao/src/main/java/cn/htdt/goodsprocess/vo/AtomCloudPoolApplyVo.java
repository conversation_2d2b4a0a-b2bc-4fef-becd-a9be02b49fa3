package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.CloudPoolApplyDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品云池申请记录
 *
 * <AUTHOR>
 * @date 2021/2/1
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomCloudPoolApplyVo extends CloudPoolApplyDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 商品编码集合
     */
    private List<String> goodsNoList;

    /**
     * 商品最后更新时间
     */
    private LocalDateTime goodsUpdateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
