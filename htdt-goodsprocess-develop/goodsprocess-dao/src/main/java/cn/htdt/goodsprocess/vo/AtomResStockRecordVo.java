package cn.htdt.goodsprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 一体机-经营报表-库存统计
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-20
 */
@Data
public class AtomResStockRecordVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *商品编码
     */
    private String goodsNo;

    /**
     *商品名称
     */
    private String goodsName;

    /**
     *条形码
     */
    private String barcode;

    /**
     *仓库编号
     */
    private String warehouseNo;

    /**
     *仓库名称
     */
    private String warehouseName;

    /**
     *库存数量
     */
    private BigDecimal stockNum;

    /**
     *最新进价
     */
    private BigDecimal purchasePrice;

    /**
     *品牌编码
     */
    private String brandNo;

    /**
     *品牌编码
     */
    private String brandName;
    /**
     *类目编码
     */
    private String categoryNo;

    /**
     *库存预估价
     */
    private BigDecimal stockPrice;

    /**
     *第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     *第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 商品型号
     */
    private String goodsForm;

    /**
     * 父商品编号
     */
    private String parentGoodsNo;

    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     * //20230809蛋品-wxb
     */
    private String multiUnitType;

    /**
     * 多单位主品编号
     * //20230809蛋品-wxb
     */
    private String multiUnitGoodsNo;

}
