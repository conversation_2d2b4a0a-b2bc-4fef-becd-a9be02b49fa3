package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 采购单拥有的商品详情接口
 *
 * <AUTHOR>
 * @date 2020年9月9日
 */
@Data
public class AtomReqPurchaseGoodsDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *采购单编码
     */
    private String purchaseCode;
    /**
     *采购单编码
     */
    private String returnCode;
    /** 退货单状态
     */
    private String orderStatus;
    /**
     *商家ID
     */
    private String merchantNo;
    /**
     *店铺ID，对应orgId
     */
    private String storeNo;
    /**
     * 平台类型
     * */
    private String companyNo;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
