package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.StoreCategoryDomain;
import cn.htdt.goodscenter.dto.request.AtomReqStoreCategoryDTO;
import cn.htdt.goodsprocess.vo.AtomResStoreCategoryNumVo;
import cn.htdt.goodsprocess.vo.AtomResStoreCategoryVo;
import cn.htdt.goodsprocess.vo.AtomSortNumVo;
import cn.htdt.goodsprocess.vo.AtomStoreCategoryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 店铺类目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface StoreCategoryDao extends BaseMapper<StoreCategoryDomain> {

    /**
     * 根据参数查询集合
     *
     * @param vo 查询参数
     * @return List<StoreCategoryDomain>
     */
    List<StoreCategoryDomain> selectByParams(AtomStoreCategoryVo vo);

    /**
     * 查询店铺自建类目列表（递归查询）
     *
     * @param vo 查询参数
     * @return List<AtomResStoreCategoryVo>
     */
    List<AtomResStoreCategoryVo> selectStoreCategory(AtomStoreCategoryVo vo);

    /**
     * 根据商品编号集合查询店铺类目与父类目
     *
     * @param vo 查询参数
     * @return List<AtomStoreCategoryVo>
     */
    List<AtomStoreCategoryVo> selectStoreCatagoryByGoodsNoList(AtomStoreCategoryVo vo);

    /**
     * 根据参数更新
     *
     * @param domain 查询参数
     * @return int
     */
    int updateStoreCategory(StoreCategoryDomain domain);

    /**
     * 根据参数逻辑删除(批量删除)
     *
     * @param domain              编辑参数
     * @param storeCategoryNoList 店铺类目编号集合
     * @return int
     */
    int batchLogicDelete(@Param("domain") StoreCategoryDomain domain, @Param("list") List<String> storeCategoryNoList);

    /**
     * 获取同路径层级下上一个销售类目
     *
     * @param domain 查询参数
     * @return SaleCategoryDomain
     */
    StoreCategoryDomain selectPreviousStoreCategory(StoreCategoryDomain domain);

    /**
     * 获取同路径层级下下一个销售类目
     *
     * @param domain 查询参数
     * @return SaleCategoryDomain
     */
    StoreCategoryDomain selectNextStoreCategory(StoreCategoryDomain domain);

    /**
     * 获取有效的店铺类目全路径
     *
     * @param vo 查询参数
     * @return List<StoreCategoryDomain>
     */
    List<StoreCategoryDomain> selectEffectiveStoreCategoryFullIdPath(AtomStoreCategoryVo vo);

    /**
     * 批量修改店铺类目
     *
     * @param domainList 编辑参数
     * @return int
     */
    int batchUpdateStoreCategory(@Param("list") List<StoreCategoryDomain> domainList);

    /**
     * 获取父节点下，排序最大值
     *
     * @param reqDTO 查询参数
     * @return int
     */
    int selectMaxSortValue(StoreCategoryDomain reqDTO);

    /**
     * 查询店铺自建类目列表（某一级）
     * @param domain
     * @return
     */
    List<AtomResStoreCategoryVo> selectStoreCategoryFloor(StoreCategoryDomain domain);

    /**
     * 目前只是给导入商品用
     * 特殊的查询，只查类目编码和类目名称
     * @param dto
     * @return
     */
    List<StoreCategoryDomain> selectCategoryNameByStoreNo(AtomReqStoreCategoryDTO dto);


    /**
     * 查询指定类目的商品数量
     * @param saleCategoryVo 请求参数
     * @return 结果
     */
    List<AtomResStoreCategoryNumVo> selectStoreCategoryNums(AtomReqStoreCategoryDTO saleCategoryVo);

    /**
     * 获取末级店铺类目列表（只有一级时获取一级，有二级获取二级）
     *
     * @param vo 请求参数
     * @return List<StoreCategoryDomain>
     */
    List<StoreCategoryDomain> selectLastLevelStoreCategoryList(AtomStoreCategoryVo vo);


    /**
     * 获取末级店铺类目列表（只有一级时获取一级，有二级获取二级）
     *
     * @param vo 请求参数
     * @return List<StoreCategoryDomain>
     */
    List<StoreCategoryDomain> selectLastLevelStoreCategoryListByGood(AtomStoreCategoryVo vo);

    /**
     * 查指定类目的排序值
     * @param atomSortNumVo 指定类目
     * @return 指定类目的排序值
     */
    int selectStoreCategorySortNum(AtomSortNumVo atomSortNumVo);

    void moveBetweenSortNum(AtomSortNumVo atomSortNumVo);

    int updateSortNum(AtomSortNumVo atomSortNumVo);

    /**
     * 查询指定店铺的最大一体机排序值
     *
     * @param storeNo 店铺编号
     * @return 最大的排序值
     */
    int selectStoreMaxSortNum(@Param("storeNo") String storeNo);
}
