package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.AttributeNameDomain;
import cn.htdt.goodsprocess.domain.AttributeValueDomain;
import cn.htdt.goodsprocess.vo.AtomAttributeVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 属性名称表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface AttributeNameDao extends BaseMapper<AttributeNameDomain> {

    /**
     * 获取属性列表 支持属性名称模糊查询
     *
     * @param domain 查询参数
     * @return List<AtomAttributeVo>
     */
    List<AtomAttributeVo> selectListByAttributeName(@Param("domain") AttributeNameDomain domain, @Param("orderByColumn") String orderByColumn);

    /**
     * 根据属性编码列表查询属性值列表
     * @param attributeCodeList 属性编码列表
     * @return 属性值列表
     */
    List<AttributeValueDomain> selectAttributeValueListByAttributeCode(@Param("attributeCodeList") List<String> attributeCodeList);

    /**
     * 获取属性列详情
     *
     * @param domain 查询参数
     * @return List<AtomAttributeVo>
     */
    List<AtomAttributeVo> selectInfoByAttributeCode(AttributeNameDomain domain);

    /**
     * 根据参数查询集合
     *
     * @param domain            查询参数
     * @return List<AttributeNameDomain>
     */
    List<AttributeNameDomain> selectByParams(AttributeNameDomain domain);

    /**
     * 根据参数更新
     *
     * @param domain 修改参数
     * @return int
     */
    int updateByAttributeCode(AttributeNameDomain domain);

    /**
     * 逻辑删除
     *
     * @param domain            修改参数
     * @param attributeCodeList 属性code集合
     * @return int
     */
    int batchLogicDelete(@Param("domain") AttributeNameDomain domain, @Param("list") List<String> attributeCodeList);
}
