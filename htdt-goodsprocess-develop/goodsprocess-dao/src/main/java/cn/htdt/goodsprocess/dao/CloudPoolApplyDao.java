package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.CloudPoolApplyDomain;
import cn.htdt.goodsprocess.vo.AtomCloudPoolApplyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 云池申请记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2021/1/9
 **/
@Mapper
public interface CloudPoolApplyDao extends BaseMapper<CloudPoolApplyDomain> {

    /**
     * 查询商品云池申请信息
     *
     * @param vo 查询参数
     * @return List<CloudPoolApplyDomain>
     */
    List<CloudPoolApplyDomain> selectByGoodsNo(AtomCloudPoolApplyVo vo);

    /**
     * 查询需要更新的云池商品
     *
     * @param vo 查询参数
     * @return List<CloudPoolApplyDomain>
     */
    List<CloudPoolApplyDomain> selectLastUpdateGoodsByGoodsNo(AtomCloudPoolApplyVo vo);

    /**
     * 根据参数更新
     *
     * @param vo 请求参数
     * @return int
     */
    int updateByParam(AtomCloudPoolApplyVo vo);

    /**
     * 删除云池申请记录
     *
     * @param domain 请求参数
     * @return int
     */
    int deleteByGoodsNo(CloudPoolApplyDomain domain);

    int selectMerchantCloudPoolGoodsCount(@Param("merchantNo") String merchantNo);
}
