package cn.htdt.goodsprocess.dao;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 *	dao接口
 *  <AUTHOR>
 *  @version 2.0
 */
@Mapper
public interface ConfigsDao{

	/**
	 * 添加记录
	 * @param sparam
	 */
	public void insertConfig(@Param("sparam") String sparam);

	/**
	 * 更新记录
	 * @param sparam
	 */
	public void updateConfig(@Param("sparam") String sparam);

	/**
	 * 删除记录
	 * @param sparam
	 */
	public void deleteConfig(@Param("sparam") String sparam);

}
