package cn.htdt.goodsprocess.vo;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 详细说明.
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PrintAtomGoodsVo extends AtomGoodsVo {
    /**
     * 是否包含无分类商品, 1-否, 2-是
     */
    private Integer noCategoryFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
