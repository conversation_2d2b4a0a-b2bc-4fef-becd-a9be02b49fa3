package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.MdmSaleCategoryDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * MDM销售类目表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-18
 */
@Mapper
public interface MdmSaleCategoryDao extends BaseMapper<MdmSaleCategoryDomain> {

    /**
     * 获取MDM销售类目
     *
     * @param domain 查询参数
     * @return List<AtomResSaleCategoryVo>
     */
    List<MdmSaleCategoryDomain> selectByParams(MdmSaleCategoryDomain domain);

    /**
     * 根据参数更新
     *
     * @param domain MDM销售类目更新信息
     * @return int
     */
    int updateByCategoryNo(MdmSaleCategoryDomain domain);

}
