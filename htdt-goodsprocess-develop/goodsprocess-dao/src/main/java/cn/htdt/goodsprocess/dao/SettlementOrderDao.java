package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.SettlementOrderDomain;
import cn.htdt.goodsprocess.vo.AtomSettlementOrderVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @description 结算单dao
 * 
 * <AUTHOR>
 * @date 2023/5/23 15:07
 **/
@Mapper
public interface SettlementOrderDao extends BaseMapper<SettlementOrderDomain> {

    /**
     * 分页查询结算单
     *
     * @param atomSettlementOrderVo 查询参数
     * @return 结算单集合
     */
    List<SettlementOrderDomain> getSettlementOrderListForPage(AtomSettlementOrderVo atomSettlementOrderVo);

    /**
     * 查询结算单详情
     *
     * @param atomSettlementOrderVo 查询参数
     * @return 结算单详情
     */
    SettlementOrderDomain getSettlementOrderDetail(AtomSettlementOrderVo atomSettlementOrderVo);

    /**
     * 根据参数更新
     *
     * @param domain 查询参数
     * @return int
     */
    int updateSettlementOrder(SettlementOrderDomain domain);

    /**
     * 取消结算
     *
     * @param atomSettlementOrderVo 请求参数
     * @return int
     */
    int cancelSettlementOrder(AtomSettlementOrderVo atomSettlementOrderVo);

    /**
     * 标记结算
     *
     * @param atomSettlementOrderVo 请求参数
     * @return int
     */
    int markSettlementOrder(AtomSettlementOrderVo atomSettlementOrderVo);
}
