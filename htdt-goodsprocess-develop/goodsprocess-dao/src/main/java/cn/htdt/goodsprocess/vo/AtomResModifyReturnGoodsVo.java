package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;

/**
 * <AUTHOR>
 * @Date 2020-11-2
 * @Description  修改页面 商品Vo
 **/
@Data
public class AtomResModifyReturnGoodsVo implements Serializable {
    private static final long serialVersionUID = 1L;
    private BigInteger id;
    /**
     *退货单商品行编码
     */
    private String returnGoodsCode;
    /**
     *退货单编码
     */
    private String returnCode;
    /**
     *采购单编码
     */
    private String purchaseCode;
    /**
     *采购单商品行编码
     */
    private String purchaseGoodsCode;
    /**
     *数据来源 1001:手动添加 1002：采购商城添加
     */
    private String sourceType;
    /**
     *商品编码
     */
    private String goodsNo;
    /**
     *商品名称
     */
    private String goodsName;
    /**
     *采购单位
     */
    private String purchaseUnit;
    /**
     *采购单价
     */
    private BigDecimal purchaseUnitPrice;
    /**
     *采购数量
     */
    private BigDecimal purchaseNum;
    /**
     *退货中数量
     */
    private BigDecimal returningCount;
    /**
     *已退货数量
     */
    private BigDecimal returnedCount;
    /**
     *本次退货数量
     */
    private BigDecimal returnRequestCount;
    /**
     *已入库数量
     */
    private BigDecimal storageCount;
    /**
     *交易金额
     */
    private BigDecimal purchasePrice;
    /**
     *是否入仓 1001-有实体仓;1002-无实体仓
     */
    private Integer warehouseType;

    /**
     *仓库编号
     */
    private String warehouseNo;
    /**
     *仓库名称
     */
    private String warehouseName;
    /**
     *备注
     */
    private String remark;
    /**
     *订单状态1001待出库 1002部分出库 1003完成
     */
    private String orderStatus;
    /**
     *商家ID
     */
    private String merchantNo;
    /**
     *商家名称
     */
    private String merchantName;
    /**
     *店铺ID，对应orgId
     */
    private String storeNo;
    /**
     *店铺名称，对应orgName
     */
    private String storeName;
    /**
     *是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;
    /**
     *归属平台编号
     */
    private String companyNo;
    /**
     *归属平台名称
     */
    private String companyName;
    /**
     *归属分部编号
     */
    private String branchNo;
    /**
     *归属分部名称
     */
    private String branchName;
    /**
     * 归属平台类型(1001.运营 1002.商家 1003.店铺)
     */
    private String platformType;
    /*
     * 商品的入库标识
     * */
    private Integer goodsWarehouseFlag;
    /**
     * 商品删除状态
     * */
    private String goodsDeleteFlag;

    /**
     * 计量单位ID-返回
     */
    private String calculationUnitNo;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 实体库存
     */
    private BigDecimal realStockNum;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
