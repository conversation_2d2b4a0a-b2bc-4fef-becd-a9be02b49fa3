package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsShelfBatchDomain;
import cn.htdt.goodsprocess.dto.request.goodsshelf.ReqGoodsShelfBatchDTO;
import cn.htdt.goodsprocess.vo.GoodsShelfBatchListVO;
import cn.htdt.goodsprocess.vo.GoodsShelfBatchVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023-06-19
 * @Description 商品效期批次表
 **/
@Mapper
public interface GoodsShelfBatchDao extends BaseMapper<GoodsShelfBatchDomain> {

    List<GoodsShelfBatchListVO> selectByParams(ReqGoodsShelfBatchDTO reqDto);

    List<GoodsShelfBatchListVO> selectGoodsShelfBatchList(ReqGoodsShelfBatchDTO reqDto);

    List<GoodsShelfBatchListVO> selectGoodsShelfBatchFlow(ReqGoodsShelfBatchDTO reqDto);

    GoodsShelfBatchDomain selectGoodsShelfByNo(@Param("batchNo") String batchNo);

    /**
     * 统计过期及临期款式
     *
     * @param goodsShelfBatchVO 请求参数
     */
    GoodsShelfBatchVO countShelfLife(GoodsShelfBatchVO goodsShelfBatchVO);

    /**
     * 批量编辑商品效期剩余数量
     *
     * @param list 入参集合
     * @return int
     */
    int batchUpdateLeftStockNum(List<GoodsShelfBatchVO> list);

    /**
     * 逻辑删除
     *
     * @param goodsShelfBatchVO 请求参数
     * @return int
     */
    int logicDelete(GoodsShelfBatchVO goodsShelfBatchVO);
}