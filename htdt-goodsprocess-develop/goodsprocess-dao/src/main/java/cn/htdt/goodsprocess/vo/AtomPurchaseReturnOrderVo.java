package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.PurchaseReturnOrderDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 采购退货单
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AtomPurchaseReturnOrderVo extends PurchaseReturnOrderDomain {

    /**
     * 商品名称组合
     */
    private String goodName;

    /**
     * 商品名称关键词，支持名称、名称拼音、名称拼音首字母模糊查询
     */
    private String goodsNameKeyWord;

    /**
     * 退货单创建开始时间
     */
    private String startTime;


    /**
     * 退货单创建结束时间
     */
    private String endTime;

    /**
     * 退货开始日期
     */
    private String returnDateStartStr;

    /**
     * 退货结束日期
     */
    private String returnDateEndStr;

    /**
     * 采购退货单对应的退货商品
     */
    private List<AtomPurchaseReturnOrderGoodDetailVo> returnOrderGoodList;

    /**
     * 退单状态列表
     */
    private List<String> returnStatusList;

    /**
     * 模糊匹配供应商名称或商品名称
     */
    private String queryStr;

    /**
     * 采购类型类型：0 商品；1 商品组
     */
    private Integer purchaseType;

}
