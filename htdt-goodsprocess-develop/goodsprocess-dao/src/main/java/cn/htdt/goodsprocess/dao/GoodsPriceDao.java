package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsPriceDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品价格表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsPriceDao extends BaseMapper<GoodsPriceDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<GoodsPriceDomain> selectByParams(GoodsPriceDomain domain);

    /**
     * 根据参数插入
     * @param domain
     * @return
     */
    int insertSelective(GoodsPriceDomain domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsPriceDomain domain);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(GoodsPriceDomain domain);
}
