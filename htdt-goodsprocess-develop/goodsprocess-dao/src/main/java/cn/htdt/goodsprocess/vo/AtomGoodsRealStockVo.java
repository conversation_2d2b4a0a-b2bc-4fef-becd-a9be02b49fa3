package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsRealStockDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 商品库存
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AtomGoodsRealStockVo extends GoodsRealStockDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 店铺编号
     */
    private List<String> storeNoList;

    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 是否入仓:(1:否 2:是)
     */
    private Integer warehouseFlag;

    /**
     * 总库存区间开始
     */
    private Integer totalStockBegin;

    /**
     * 总库存区间结束
     */
    private Integer totalStockEnd;


    /**
     * 可售库存区间开始
     */
    private Integer saleStockBegin;

    /**
     * 可售库存区间结束
     */
    private Integer saleStockEnd;

    /**
     * 商品来源 1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发;1006:云池分发
     */
    private String goodsSourceType;
    /**
     * 是否启用串码(1:否 2:是)
     */
    private Integer imeiFlag;
    /**
     * 数据逻辑删除标识，默认1未删除，2已删除
     */
    private Integer deleteFlag;

    /**
     *  商品状态1001-未上架;1002-审核中;1003-审核失败;1004:已上架;1005:未分发;1006:分发成功;1007:已失效;
     */
    private String goodsStatus;
    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;

    /**
     *  调拨在途数量
     */
    private BigDecimal allocationNum;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    /**
     * 计量单位ID
     */
    private String calculationUnitNo;

    /**
     * 商品编码集合
     */
    private List<String> goodsNoList ;

    /**
     * 主品商品编码集合
     */
    private List<String> parentGoodsNoList;
    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 第一、二、三属性值编码，格式：first-second-third
     */
    private String attributeValueName;

    /**
     * 是否是商家同步商品:(1:否 2:是)
     */
    private Integer syncFlag;

    /**
     * 系列商品类型 1001:主品 1002:子品
     */
    private String childFlag;

    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     */
    private String multiUnitType;

    /**
     * 多单位主品编号
     */
    private String multiUnitGoodsNo;

    public String getAttributeValueName() {
        attributeValueName = "";
        if(StringUtils.isNotEmpty(getFirstAttributeValueName())) {
            attributeValueName+= "-" + getFirstAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getSecondAttributeValueName())) {
            attributeValueName+= "-" + getSecondAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getThirdAttributeValueName())) {
            attributeValueName+= "-" + getThirdAttributeValueName();
        }
        if(StringUtils.isNotEmpty(attributeValueName)) {
            attributeValueName = attributeValueName.substring(1);
        }
        return attributeValueName;
    }
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
