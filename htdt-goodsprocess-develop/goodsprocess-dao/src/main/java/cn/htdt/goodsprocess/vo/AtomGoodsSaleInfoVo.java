package cn.htdt.goodsprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/16 16:06
 */
@Data
public class AtomGoodsSaleInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 店铺类目编号
     */
    private String storeCategoryNo;

    /**
     * 店铺类目编号集合
     */
    private List<String> storeCategoryNoList;

    /**
     * 商品编号集合
     */
    private List<String> originalGoodsNoList;

    /**
     * 商品类型 1001-实物商品;1004-称重商品
     */
    private String goodsType;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 店铺编号
     */
    private String storeNo;

    /**
     * 查询商品类型 1001：全部 1002:平台商品  1003:商家/店铺商品 1004:商家创建商品  1005:店铺创建商品 1006:店铺查询 1007:云池商品查询 1008:店铺非分销商品 1009:根据身份选择 1010:店铺商品
     */
    private String queryType;

    /**
     * 计量单位
     */
    private String unit;
}
