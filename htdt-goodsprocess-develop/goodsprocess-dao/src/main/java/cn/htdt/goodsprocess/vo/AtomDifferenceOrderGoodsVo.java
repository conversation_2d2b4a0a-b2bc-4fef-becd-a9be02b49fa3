package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.DifferenceOrderGoodsDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 *
 * 差异单商品行vo
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AtomDifferenceOrderGoodsVo extends DifferenceOrderGoodsDomain implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("差异单号集合")
    private List<String> differenceNos;

    @ApiModelProperty("差异商品行编号集合")
    private List<String> differenceItemNos;

    @ApiModelProperty("商品编号集合")
    private List<String> goodsNos;

    @ApiModelProperty("原始商品编号集合")
    private List<String> merchantGoodsNos;

    @ApiModelProperty("创建开始时间")
    private LocalDate startCreateTime;

    @ApiModelProperty("创建结束时间")
    private LocalDate endCreateTime;
}
