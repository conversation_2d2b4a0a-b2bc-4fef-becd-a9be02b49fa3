package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsImeiChangeLogDomain;
import cn.htdt.goodsprocess.vo.GoodsImeiChangeLogVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品串码流水表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-23
 */
@Mapper
public interface GoodsImeiChangeLogDao extends BaseMapper<GoodsImeiChangeLogDomain> {

    /**
     * 查询流水列表
     * @Date 2023/7/25
     * <AUTHOR>
     * @param goodsImeiChangeLogDomain
     * @return List<GoodsImeiChangeLogDomain>
     **/
    List<GoodsImeiChangeLogDomain> selectGoodsImeiChangeLogList(GoodsImeiChangeLogVo goodsImeiChangeLogVo);
}
