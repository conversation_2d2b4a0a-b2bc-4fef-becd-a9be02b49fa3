package cn.htdt.goodsprocess.dao;


import cn.htdt.goodsprocess.domain.BarCodeScaleFlagDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 条码秤标识 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-02-24
 */
@Mapper
public interface BarCodeScaleFlagDao extends BaseMapper<BarCodeScaleFlagDomain> {

    /**
     * 根据条件查询
     *
     * @param domain 查询条件
     * @return List<BarCodeScaleFlagDomain>
     */
    List<BarCodeScaleFlagDomain> selectByParam(BarCodeScaleFlagDomain domain);

    /**
     * 逻辑删除
     *
     * @param domain 编辑参数
     * @return int
     */
    int logicDelete(BarCodeScaleFlagDomain domain);

}
