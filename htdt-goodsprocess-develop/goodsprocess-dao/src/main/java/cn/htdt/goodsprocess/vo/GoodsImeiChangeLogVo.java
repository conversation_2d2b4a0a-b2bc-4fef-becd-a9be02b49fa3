package cn.htdt.goodsprocess.vo;


import cn.htdt.goodsprocess.domain.GoodsImeiChangeLogDomain;
import cn.htdt.goodsprocess.domain.GoodsImeiHistoryDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 * @since 2023-07-26
 */
@EqualsAndHashCode
@Data
public class GoodsImeiChangeLogVo extends GoodsImeiChangeLogDomain implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 商品id或名称
     */
    private String goodNoOrName;

    /**
     * 交易开始时间
     */
    @ApiModelProperty(value = "备份开始时间")
    private LocalDate startDate;

    /**
     * 交易结束日期
     */
    @ApiModelProperty(value = "备份结束日期")
    private LocalDate endDate;

    /**
     *供应商名称
     */
    private String supplierName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
