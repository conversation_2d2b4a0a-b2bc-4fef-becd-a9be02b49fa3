package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * pc店铺装修, 查询参数
 *
 * <AUTHOR>
 */

@Data
public class DecoAtomGoodsVo extends AtomGoodsVo{

    /**
     * 店铺类目全路径
     */
    private String storeCategoryFullIdPath;

    /**
     * 店铺类目id全路径集合
     */
    private List<String> storeCategoryFullIdPaths;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
