package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.DifferenceOrderDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 *
 * 差异单vo
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AtomDifferenceOrderVo extends DifferenceOrderDomain implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "多店铺编号")
    private List<String> storeNoList;

    @ApiModelProperty("差异单号集合")
    private List<String> differenceNos;

    @ApiModelProperty("要货单号集合")
    private List<String> requisitionNos;

    @ApiModelProperty("创建开始时间")
    private LocalDate startCreateTime;

    @ApiModelProperty("创建结束时间")
    private LocalDate endCreateTime;
}
