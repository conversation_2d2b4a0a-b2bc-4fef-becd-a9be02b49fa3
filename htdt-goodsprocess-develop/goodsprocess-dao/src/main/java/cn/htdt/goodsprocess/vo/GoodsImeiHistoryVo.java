package cn.htdt.goodsprocess.vo;


import cn.htdt.goodsprocess.domain.GoodsImeiHistoryDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 *
 * <AUTHOR>
 * @since 2023-07-21
 */
@EqualsAndHashCode
@Data
public class GoodsImeiHistoryVo extends GoodsImeiHistoryDomain implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 商品id或名称
     */
    private String goodNoOrName;

    /**
     * 备份开始时间
     */
    @ApiModelProperty(value = "备份开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate inventoryDateStart;

    /**
     * 备份结束日期
     */
    @ApiModelProperty(value = "备份结束日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private LocalDate inventoryDateEnd;

    /**
     *供应商名称
     */
    private String supplierName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
