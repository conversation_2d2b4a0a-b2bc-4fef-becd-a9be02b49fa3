package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * 详细说明.
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomGoodsStockVo extends GoodsDomain {
    private static final long serialVersionUID = 1L;
    /********************入参***********************/

    /**
     * 店铺编号
     */
    private List<String> storeNoList;


    /**
     * 入参条件：商品名称或助记码-模糊查询用
     */
    private String goodsStr;

    /**
     * 入参条件：查询商品类型 1001：全部 1002:平台商品  1003:商家/店铺商品 1004:商家创建商品  1005:店铺创建商品 1006:店铺查询 1007:云池商品查询
     */
    private String queryType;

    /**
     * 入参条件：入仓类型:0-全部；1-不入仓；2-入仓;
     */
    private Integer warehouseFlagType;

    /**
     * 入参条件：最低实体库存数
     */
    private BigDecimal minStockNum;

    /**
     * 入参条件：最高实体库存数
     */
    private BigDecimal maxStockNum;

    /**
     * 入参条件：最低可售库存数
     */
    private BigDecimal minAvailableStockNum;

    /**
     * 入参条件：最高可售库存数
     */
    private BigDecimal maxAvailableStockNum;

    /**
     * 只展示子品标识=1002 其它展示全部
     */
    private String childFlag;


    /********************出参***********************/
    /**
     * 总库存数量，即实体库存
     */
    private BigDecimal realStockNum;

    /**
     * 冻结库存数量，即预锁数量
     */
    private BigDecimal freezeStockNum;

    /**
     * 可用库存数量，即可售库存
     */
    private BigDecimal availableStockNum;

    /**
     * 待发货数量
     */
    private BigDecimal deliverStockNum;

    /**
     * 调拨在途数量
     */
    private Integer outStockNum;

    /**
     * 已售数量
     */
    private Integer hasSaleNum;

    /**
     * 主计量单位名称
     */
    private String calculationUnitName;

    /**
     * 是否是商家同步商品:(1:否 2:是)
     */
    private Integer syncFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
