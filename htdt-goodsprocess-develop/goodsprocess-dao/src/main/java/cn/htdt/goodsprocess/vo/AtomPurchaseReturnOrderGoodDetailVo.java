package cn.htdt.goodsprocess.vo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 采购退货单商品详情
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AtomPurchaseReturnOrderGoodDetailVo  implements Serializable {


    private static final long serialVersionUID = -7761729316103194737L;

    /**
     *商品名称
     */
    private String goodsName;

    /**
     *已退货数量
     */
    private BigDecimal returnedCount;

    /**
     *已出库数量
     */
    private BigDecimal warehouseOutCount;

    /**
     *采购单价
     */
    private BigDecimal purchaseUnitPrice;

    /**
     *交易金额
     */
    private BigDecimal purchasePrice;

}
