package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsDistributionShopRelationDomain;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2020/12/5 15:46
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class AtomGoodsDistributionShopRelationVo extends GoodsDistributionShopRelationDomain {

    private static final long serialVersionUID = -2132433305897701554L;
    /**
     * 店铺编码列表集合
     */
    private List<String> storeNoList;

    /**
     * 商品集合
     */
    private List<String> goodsNoList;

    private List<String> goodsGroupsNoList;

    /**
     * 商品状态 1001-未上架;1002-已上架;
     */
    private String goodsStatus;

    /**
     * 商品审核状态 2001:等待审核;2002:审核中;2003:审核成功; 2004:审核失败
     */
    private String auditStatus;

    /**
     * 分发状态,3001:未分发,3002:分发中,3003,已分发,3004:已失效
     */
    private String distributionStatus;

    /**
     * 商品名称
     */
    private String goodsName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
