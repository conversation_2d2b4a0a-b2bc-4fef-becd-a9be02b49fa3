package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsMediaMasterDataDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2020/10/13 19:21
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class AtomGoodsMediaMasterDataVo extends GoodsMediaMasterDataDomain implements Serializable {

    private static final long serialVersionUID = 4662302564613697012L;

    /**
     * 商品编码集合
     */
    private List<String> goodsNoList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
