package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsChangeLogDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品修改日志表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsChangeLogDao extends BaseMapper<GoodsChangeLogDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<GoodsChangeLogDomain> selectByParams(GoodsChangeLogDomain domain);
}
