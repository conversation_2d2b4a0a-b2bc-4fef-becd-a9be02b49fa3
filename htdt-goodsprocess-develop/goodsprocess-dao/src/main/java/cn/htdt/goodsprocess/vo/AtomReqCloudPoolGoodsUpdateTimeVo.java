package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.CloudPoolGoodsUpdateTimeDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.time.LocalDateTime;

/**
 * 店铺云池分销商品更新时间
 *
 * <AUTHOR>
 * @date 2021/9/16
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomReqCloudPoolGoodsUpdateTimeVo extends CloudPoolGoodsUpdateTimeDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 新商品更新时间
     */
    private LocalDateTime newGoodsUpdateTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
