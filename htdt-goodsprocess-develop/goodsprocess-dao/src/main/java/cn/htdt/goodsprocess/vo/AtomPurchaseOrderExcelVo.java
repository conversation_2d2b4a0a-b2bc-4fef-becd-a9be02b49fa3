package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *  采购单信息导出Vo
 *
 * <AUTHOR>
 * @date 2020年9月15日
 */
@Data
public class AtomPurchaseOrderExcelVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *采购单编码
     */
    private String purchaseCode;

    /**
     *供应商编码
     */
    private String supplierCode;
    /**
     *供应商名称
     */
    private String supplierName;

    /**
     *发货联系人
     */
    private String sendContactName;
    /**
     *发货联系人手机号
     */
    private String sendContactMobile;
    /**
     *交易金额
     */
    private BigDecimal totalPurchasePrice;
    /**
     *商品编码
     */
    private String goodsNo;
    /**
     *商品名称
     */
    private String goodsName;
    /**
     *采购单位
     */
    private String purchaseUnit;
    /**
     *采购单价
     */
    private BigDecimal purchaseUnitPrice;
    /**
     *采购数量
     */
    private BigDecimal purchaseNum;
    /**
     * 采购价
     */
    private BigDecimal purchasePrice;
    /**
     *约定到货日期
     */
    private LocalDateTime expectReceiveDate;
    /**
     *备注
     */
    private String remark;
    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
