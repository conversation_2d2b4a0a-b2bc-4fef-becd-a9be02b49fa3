package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @Description 调拨单-查
 **/
@Data
public class AtomWarehouseAllocationOrderVo extends ImWarehouseAllocationOrderGoodsDomain {

    /**
     * 创建时间开始时间
     */
    private String startTime;
    /**
     * 创建时间结束时间
     */
    private String endTime;

    /**
     *调拨商品名称总
     */
    private String goodsNames;

    /**
     *调拨总数量
     */
    private BigDecimal allocationNums;
    /**
     * 商品ID、商品名称
     */
    private String goodsStr;

    /**
     * 本次出库数量
     */
    private BigDecimal stockNum;
    /**
     * 修改时间
     */
    private LocalDateTime modifyTime;
    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 盘点单状态-多选用
     */
    private List<String> orderStatusList;
    /**
     * 调拨编码或商品名称
     */
    private String allocationGoodsStr;

    /**
     * 调拨下商品总数
     */
    private Integer totalCount;

    /**
     * 用户身份(1运营，2商家，4-店铺；8-单店)
     */
    private Integer loginIdentity;

    private List<String> allocationOrderNos;
    /**
     * 盘点商品是否超20条标识：1=无；2=超
     */
    private Integer moreFlag;

    /**
     * 商品编号集合
     */
    private List<String> goodsNos;

}
