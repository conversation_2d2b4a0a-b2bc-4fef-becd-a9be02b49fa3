package cn.htdt.goodsprocess.vo;

import cn.htdt.common.domain.BaseDomain;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.goodsprocess.domain.GoodsLossReportItemDomain;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 商品报损单明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-05
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomGoodsLossReportItemVo extends GoodsLossReportItemDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 报损单编号list
     */
    private List<String> lossReportNos;

    /**
     * 取前多少条,默认前3条
     */
    private Integer preTotal;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
