package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsRealStockDomain;
import cn.htdt.goodsprocess.vo.AtomGoodsRealStockVo;
import cn.htdt.goodsprocess.vo.AtomGoodsStockVo;
import cn.htdt.goodsprocess.vo.RealStockNumByParentGoodsNoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品实体表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsRealStockDao extends BaseMapper<GoodsRealStockDomain> {

    /**
     * 根据参数查询单条
     * @param domain
     * @return
     */
    GoodsRealStockDomain selectByParams(GoodsRealStockDomain domain);


    /**
     * 根据商品集合批量查询
     * @param listGoodsNo
     * @return
     */
    List<GoodsRealStockDomain> selectByListGoods(@Param("listGoodsNo") List<String> listGoodsNo);

    /**
     * 根据商品编码获取调拨在途数量
     * @param goodsNo
     * @return
     */
    int  selectAllocationNum(@Param("goodsNo") String goodsNo);


    /**
     * 获取商品库存信息-list
     * @param vo
     * @return
     */
    List<AtomGoodsRealStockVo> selectAllGoodsRealStockByParams(AtomGoodsRealStockVo vo);

    /**
     * 批量查询库存集合
     * @param voList
     * @return
     */
    List<GoodsRealStockDomain> selectBatchGoodsRealStock(@Param("voList") List<GoodsRealStockDomain> voList);

    /**
     * 通过goodsNoList或者parentGoodsNoList查询库存集合，二者有且仅有一个不为空
     * @param vo
     * @return List
     */
    List<GoodsRealStockDomain> selectBatchGoodsRealStockByNos(AtomGoodsRealStockVo vo);

    /**
     * 根据条件获取商品库存信息
     * @param vo
     * @return
     */
    AtomGoodsRealStockVo selectGoodsRealStockByTerm(AtomGoodsRealStockVo vo);

    /**
     * 根据条件获取店铺或商家的所有商品库存信息（预锁数量和待发货数量）
     * @param vo
     * @return
     */
    AtomGoodsRealStockVo selectGoodsRealStockByPower(AtomGoodsRealStockVo vo);

    /**
     * 根据条件获取店铺或商家的所有商品库存信息-数量
     * @param vo
     * @return
     */
    Integer selectGoodsRealStockByPowerCount(AtomGoodsRealStockVo vo);

    /**
     * 根据条件获取店铺或商家的所有商品库存信息-分页
     * @param vo
     * @return
     */
    List<AtomGoodsRealStockVo> selectGoodsRealStockByPowerPage(AtomGoodsRealStockVo vo);

    /**
     * 根据商品nos获取店铺或商家的所有商品库存信息
     * @param vo
     * @return
     */
    List<AtomGoodsRealStockVo> selectGoodsRealStockByNos(AtomGoodsRealStockVo vo);

    /**
     * 根据参数插入
     * @param domain
     * @return
     */
    int insertSelective(GoodsRealStockDomain domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsRealStockDomain domain);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(GoodsRealStockDomain domain);

    /**
     * 批量更新商品的库存数量(一样的数量)
     * @param dto
     */
    void batchUpdateGoodsRealStock(AtomGoodsRealStockVo dto);

    /**
     * <AUTHOR>
     * @Description 查询 3条 系列主商品总库存排序
     * @Date 2021/7/1
     * @Param [goodsRealStockDomain]
     * @return java.util.List<cn.htdt.goodsprocess.vo.RealStockNumByParentGoodsNoVo>
     **/
    List<RealStockNumByParentGoodsNoVo> selectRealStockNumByParentGoodsNo(GoodsRealStockDomain goodsRealStockDomain);

    /**
     * <AUTHOR>
     * @Description
     * @Date 2021/7/2 根据系列主品的编号 查询系列主品的商品库存
     * @Param [goodsRealStockDomain]
     * @return cn.htdt.goodsprocess.vo.RealStockNumByParentGoodsNoVo
     **/
    RealStockNumByParentGoodsNoVo selectStockNumByParentGoodsNo(GoodsRealStockDomain goodsRealStockDomain);

    /**
     * <AUTHOR>
     * @Description 查询 3条 非系列商品 普通商品库存排序
     * @Date 2021/7/1
     * @Param [goodsRealStockDomain]
     * @return java.util.List<cn.htdt.goodsprocess.vo.RealStockNumByParentGoodsNoVo>
     **/
    List<RealStockNumByParentGoodsNoVo> selectRealStockNumByGoodsNo(GoodsRealStockDomain goodsRealStockDomain);

    /**
     * <AUTHOR>
     * @Description bossapp-查询商品及统计信息（预锁数量、待发货数量、调拨在途数量、可售库存、实体库存）
     * @return java.util.List<AtomGoodsStockVo>
     **/
    List<AtomGoodsStockVo> selectGoodsRealStocksList(AtomGoodsStockVo atomGoodsStockVo);

    GoodsRealStockDomain selectForGoodsGroupByParams(GoodsRealStockDomain domain);

    List<GoodsRealStockDomain> selectByListGoodsGroups(@Param("listGoodsNo") List<String> listGoodsNo);
    List<GoodsRealStockDomain> queryRealStockByGoodsGroupsNo(@Param("goodsGroupsNos") List<String> goodsGroupsNos);

    /**
     * 根据商品组参数查询单条
     * @param domain
     * @return
     */
    GoodsRealStockDomain selectByGoodsGroupsParams(GoodsRealStockDomain domain);
}
