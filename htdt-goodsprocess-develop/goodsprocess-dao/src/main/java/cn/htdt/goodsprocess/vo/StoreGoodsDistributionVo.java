package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.StoreGoodsDistributionDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2021/1/12
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class StoreGoodsDistributionVo extends StoreGoodsDistributionDomain {

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品
     */
    private String goodsType;

    /**
     * 助记码
     */
    private String goodsHelpCode;

    /**
     * 商品状态 1001-未上架;1002-已上架;
     */
    private String goodsStatus;

    /**
     * 商品类目ID
     */
    private String categoryNo;

    /**
     * 商品品牌ID
     */
    private String brandNo;

    /**
     * 判断是否有库存 1：有 2：无
     */
    private String stockFlag;

    /**
     * 商品展示状态1001-未上架;1002-已上架;2002-审核中;2003-审核成功;2004-审核失败;3001:未分发;3003:分发成功;3004:已失效;
     */
    private String goodsShowStatus;

    /**
     * 类目全路径
     */
    private String fullIdPath;
}
