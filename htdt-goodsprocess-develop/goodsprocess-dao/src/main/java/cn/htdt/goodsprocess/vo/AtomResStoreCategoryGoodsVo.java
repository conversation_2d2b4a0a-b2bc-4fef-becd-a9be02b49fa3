package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 店铺类目商品查询返回结果集 自定义DTO
 *
 * <AUTHOR>
 * @date 2020/10/20
 **/
@Data
public class AtomResStoreCategoryGoodsVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 店铺类目id
     */
    private String storeCategoryNo;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 类目名称全路径
     */
    private String fullNamePath;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 零售价
     */
    private BigDecimal retailPrice;

    /**
     * 商品状态1001-未上架;1002-已上架;
     */
    private String goodsStatus;

    /**
     * 店铺类目id集合
     */
    private List<String> storeCategoryNoList;

    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 系列商品类型 1001:主品 1002:子品
     */
    private String seriesType;

    /**
     * 商品原始编号（默认是与goods_no一致，分发到店铺下才不一致）
     */
    private String originalGoodsNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
