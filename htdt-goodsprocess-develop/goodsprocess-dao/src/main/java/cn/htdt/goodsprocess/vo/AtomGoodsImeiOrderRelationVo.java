package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsImeiOrderRelationDomain;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * <p>
 * 订单商品串码关系表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-10
 */
@Data
public class AtomGoodsImeiOrderRelationVo extends GoodsImeiOrderRelationDomain {

    private static final long serialVersionUID = 1L;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
