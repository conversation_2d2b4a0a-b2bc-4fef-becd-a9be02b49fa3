package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.StoreCloudPoolDefaultShelfStatusDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 店铺云池默认上架状态表
 *
 * <AUTHOR>
 * @date 2021/2/26
 **/
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomStoreCloudPoolDefaultShelfStatusVo extends StoreCloudPoolDefaultShelfStatusDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 门店编号集合
     */
    private List<String> storeNos;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
