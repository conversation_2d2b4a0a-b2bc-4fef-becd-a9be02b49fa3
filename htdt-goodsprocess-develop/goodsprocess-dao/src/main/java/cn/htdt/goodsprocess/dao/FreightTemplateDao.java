package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.FreightTemplateDomain;
import cn.htdt.goodsprocess.vo.AtomFreightTemplateVo;
import cn.htdt.goodsprocess.vo.AtomReqFreightTemplateVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 运费模板 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-07
 */
@Mapper
public interface FreightTemplateDao extends BaseMapper<FreightTemplateDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<FreightTemplateDomain> selectByParams(FreightTemplateDomain domain);

    /**
     * 根据名称查询
     * @param domain
     * @return
     */
    List<FreightTemplateDomain> getFreightTemplateByName(FreightTemplateDomain domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(FreightTemplateDomain domain);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(FreightTemplateDomain domain);

    /**
     * @description 根据运费模版类型统计运费模板个数
     * <AUTHOR>
     * @date 2021-05-06 10:43:27
     * @param freightTemplateDomain
     * @return
     */
    AtomFreightTemplateVo selectFreightTemplateCount(FreightTemplateDomain freightTemplateDomain);

    /**
     * 分页查询运费模板
     * @param reqVo
     * @return
     */
    List<AtomFreightTemplateVo> selectFreightTemplateListPage(AtomReqFreightTemplateVo reqVo);

    /**
     * 分页查询运费模板, 未添加系统创建的店铺模板之前的查询逻辑
     * @param reqVo 请求参数
     * @return 结果
     */
    List<AtomFreightTemplateVo> selectFreightTemplateListForPage(AtomReqFreightTemplateVo reqVo);

    /**
     * 查询是否存在默认模板
     * @param reqVo 查询参数
     * @return 默认模板数量
     */
    List<FreightTemplateDomain> getDefaultFreightTemplateList(AtomReqFreightTemplateVo reqVo);

    /**
     * 根据模板编号查询
     * @param domain
     * @return
     */
    FreightTemplateDomain  selectFreightTemplateByNo(FreightTemplateDomain domain);


    /**
     * 根据模板编号列表批量查询
     * @param templateNos
     * @return
     */
    List<FreightTemplateDomain> selectBytemplateNos(@Param("templateNos") List<String> templateNos);

    /**
     * 根据参数查询默认模板
     * @param domain
     * @return
     */
    List<FreightTemplateDomain> selectDefaultByParams(FreightTemplateDomain domain);

    /**
     * 查询系统创建的默认模板
     * @param domain 请求参数
     * @return 结果
     */
    FreightTemplateDomain  getSystemFreightTemplate(FreightTemplateDomain domain);
}
