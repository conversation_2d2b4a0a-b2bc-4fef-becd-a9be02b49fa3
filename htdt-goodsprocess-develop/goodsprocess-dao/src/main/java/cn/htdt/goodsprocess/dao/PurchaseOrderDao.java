package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.PurchaseOrderDomain;
import cn.htdt.goodsprocess.vo.AtomPurchaseOrderExcelVo;
import cn.htdt.goodsprocess.vo.AtomResOrderStatusCountVo;
import cn.htdt.goodsprocess.vo.AtomUpdatePurchaseOrderStatusVo;
import cn.htdt.goodsprocess.vo.PurchaseOrderVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 采购单信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-15
 */
@Mapper
public interface PurchaseOrderDao extends BaseMapper<PurchaseOrderDomain> {

    /**
     * 根据条件分页查询采购单信息表
     *
     * @param purchaseOrderVo
     * @return
     * <AUTHOR>
     */
    List<PurchaseOrderDomain> selectPagePurchaseOrderByParam(PurchaseOrderVo purchaseOrderVo);

    /**
     * 查询采购单的商品的数量-批量
     * @param purchaseOrderVo
     * @return
     */
    List<PurchaseOrderVo> selectBatchPurchaseGoodsCount(PurchaseOrderVo purchaseOrderVo);

    /**
     * 根据条件分页查询采购单信息表
     *
     * @param purchaseOrderVo
     * @return
     * <AUTHOR>
     */
    List<AtomResOrderStatusCountVo> selectOrderCountByStatus(PurchaseOrderVo purchaseOrderVo);

    /**
     * @description 采购订单月度汇总
     * <AUTHOR>
     * @date 2021-12-22 09:59:39
     * @param purchaseOrderVo
     * @return
     */
    List<PurchaseOrderVo> selectPurchaseOrderMonthCount(PurchaseOrderVo purchaseOrderVo);

    /**
     * @description 采购订单月度汇总
     * <AUTHOR>
     * @date 2021-12-22 09:59:39
     * @param purchaseOrderVo
     * @return
     */
    List<PurchaseOrderVo> selectPurchaseReturnOrderMonthCount(PurchaseOrderVo purchaseOrderVo);

    /**
     * @description 首页采购概况
     * <AUTHOR>
     * @date 2023-07-13 17:06:33
     * @param purchaseOrderVo
     * @return
     */
    PurchaseOrderVo selectPurchaseCount(PurchaseOrderVo purchaseOrderVo);

    /**
     * @description 首页采购商品概况
     * <AUTHOR>
     * @date 2023-07-18 17:28:12
     * @param purchaseOrderVo
     * @return
     */
    PurchaseOrderVo selectPurchaseGoodsCount(PurchaseOrderVo purchaseOrderVo);

    /**
     * 根据条件分页查询采购单信息表导出信息
     *
     * @param purchaseCodeList
     * @return List<AtomPurchaseOrderExcelVo>
     * <AUTHOR>
     */
    List<AtomPurchaseOrderExcelVo> selectPurchaseOrderExcel(@Param("purchaseCodeList") List<String> purchaseCodeList);

    /**
     * 单个查询采购单信息详情
     *
     * @param domain
     * @return
     */
    PurchaseOrderDomain selectPurchaseOrderDetailByCode(PurchaseOrderDomain domain);
    /**
     * 根据商品编码 查询采购单信息表 （待入库或部分入库状态）
     *
     * @param purchaseOrderVo
     * @return
     * <AUTHOR>
     */
    List<PurchaseOrderDomain> selectPurchaseOrderByGoodsNo(PurchaseOrderVo purchaseOrderVo);

    /**
     * 新增采购详情信息
     *
     * @param record
     * @return
     * <AUTHOR>
     */
    int insertSelective(PurchaseOrderDomain record);

    /**
     * 修改采购详情信息
     *
     * @param record
     * @return
     * <AUTHOR>
     */
    int updateByPrimaryKeySelective(PurchaseOrderDomain record);

    /**
     * 修改采购单状态 根据采购单编码
     *
     * @param orderDomain
     * @return
     * <AUTHOR>
     */
    int updatePurchaseOrderStatus(AtomUpdatePurchaseOrderStatusVo orderDomain);

    /**
     * 修改可退数量
     * @param orderDomain orderDomain
     * @return int
     */
    int updateCanReturnNum(AtomUpdatePurchaseOrderStatusVo orderDomain);

    /**
     * 根据供应商编码获取是否存在采购单
     *
     * @param supplierCode 供应商编码
     * @return
     * <AUTHOR>
     */
    int selectOrderCountBySupplierCode(@Param("supplierCode") String supplierCode);

    /**
     * 根据采购单 逻辑删除采购单
     *
     * @param orderDomain 采购单编码
     * @return
     * <AUTHOR>
     */
    int logicDeleteByPurchaseCode(PurchaseOrderVo orderDomain);

    /**
     * 根据采购单 逻辑删除采购单
     *
     * @param orderDomain 采购单编码
     * @return
     * <AUTHOR>
     */
    int updateOrderStatusFinishByPurchaseCode(PurchaseOrderVo orderDomain);

    /**
     * 根据条件分页查询一体机采购单信息列表
     *
     * @param purchaseOrderVo
     * @return
     * <AUTHOR>
     * @date 2021-08-12
     */
    List<PurchaseOrderVo> selectPagePurchaseOrderForCashier(PurchaseOrderVo purchaseOrderVo);

    /**
     * 分页查询可退货采购单列表
     *
     * @param purchaseOrderVo purchaseOrderVo
     * @return List<PurchaseOrderDomain>
     * <AUTHOR>
     */
    List<PurchaseOrderDomain> selectPageCanreturnPurchaseOrderList(PurchaseOrderVo purchaseOrderVo);

}