package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsShelfBatchDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2023-06-19
 * @Description 商品效期批次表
 **/
@Data
public class GoodsShelfBatchListVO extends GoodsShelfBatchDomain {

    @ApiModelProperty(value = "过期天数")
    private BigDecimal expiringDayNum;

    /**
     * 商品效期批次流水号
     */
    private String batchFlowNo;

    /**
     * 操作库存数量
     */
    private BigDecimal stockNum;

    /**
     * 单据退货数量
     */
    private BigDecimal billReturnStockNum;

}