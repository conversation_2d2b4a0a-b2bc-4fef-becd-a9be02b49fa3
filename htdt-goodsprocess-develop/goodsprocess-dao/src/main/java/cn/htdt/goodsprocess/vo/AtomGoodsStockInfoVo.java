package cn.htdt.goodsprocess.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2022/11/16 16:05
 */
@Data
public class AtomGoodsStockInfoVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 实际入库数量
     */
    private BigDecimal stockNum;

    /**
     * 采购单价
     */
    private BigDecimal purchasePrice;

    /**
     * 商品原始编号（默认是与goods_no一致，分发到店铺下才不一致）
     */
    private String originalGoodsNo;

    /**
     * 采购单号
     */
    private String billCode;

    private LocalDateTime createTime;
}
