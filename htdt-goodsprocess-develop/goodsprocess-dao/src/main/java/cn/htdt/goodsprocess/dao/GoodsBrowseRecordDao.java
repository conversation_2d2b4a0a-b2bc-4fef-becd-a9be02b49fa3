package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsBrowseRecordDomain;
import cn.htdt.goodsprocess.vo.GoodsBrowseRecordVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品浏览记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-07-19
 */
@Mapper
public interface GoodsBrowseRecordDao extends BaseMapper<GoodsBrowseRecordDomain> {

    /**
     * @description 查询商品浏览记录统计列表
     * <AUTHOR>
     * @date 2021-07-19 14:46:05
     * @param goodsBrowseRecordVO
     * @return
     */
    List<GoodsBrowseRecordVO> selectGoodsBrowseCountList(GoodsBrowseRecordVO goodsBrowseRecordVO);

    /**
     * @description 分页查询商品浏览记录列表
     * <AUTHOR>
     * @date 2021-07-21 13:43:44
     * @param goodsBrowseRecordVO
     * @return
     */
    List<GoodsBrowseRecordVO> selectGoodsBrowseRecordByPage(GoodsBrowseRecordVO goodsBrowseRecordVO);

    /**
     * 查询店铺商品浏览数量
     * @param domain
     * @return
     */
    int selectGoodsBrowseRecordNum(GoodsBrowseRecordVO domain);
}
