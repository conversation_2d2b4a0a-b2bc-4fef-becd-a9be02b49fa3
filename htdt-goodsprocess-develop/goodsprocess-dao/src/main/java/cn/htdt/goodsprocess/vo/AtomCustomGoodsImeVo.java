package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2020/9/21 16:12
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomCustomGoodsImeVo extends GoodsDomain {
    private static final long serialVersionUID = 1L;

    /**
     * 串码
     */
    private String imei;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
