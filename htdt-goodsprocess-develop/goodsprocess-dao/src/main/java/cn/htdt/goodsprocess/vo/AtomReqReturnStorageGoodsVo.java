package cn.htdt.goodsprocess.vo;
/**
 * <AUTHOR>
 * @Date 2020-10-20
 * @Description 请求DTO
 **/

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020-10-26
 * @Description 采购单下所有的商品已退货和退货中的数量 请求DTO
 **/
@Data
public class AtomReqReturnStorageGoodsVo implements Serializable {

    private static final long serialVersionUID = -7890689661167910000L;
    /**
     * 商品id
     */
    private String goodsNo;

    /**
     * 仓库编码
     */
    private String warehouseNo;

    /**
     * 退货中的数量
     */
    private BigDecimal returningCount;
    /**
     * 已出入库数量
     */
    private BigDecimal returnedCount;

    /**
     * 2023-09-13蛋品-genghao-采购退货单-主单位商品编号
     * 主单位商品编号
     */
    private String rawGoodsNo;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
