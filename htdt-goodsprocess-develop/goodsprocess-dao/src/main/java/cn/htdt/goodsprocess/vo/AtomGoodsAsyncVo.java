package cn.htdt.goodsprocess.vo;

import java.io.Serializable;
import java.util.List;

import cn.htdt.goodsprocess.domain.GoodsDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @ClassName AtomGoodsAsyncVo
 * @Description 查询同步相关商品vo
 * @date 2023/06/06 15:32

 *
 * <AUTHOR>
 * @Data
 * @EqualsAndHashCode(callSuper = true)
 * public class AtomReqGoodsDTO extends ReqComPageDTO {
 * <p>
 * /**
 * 渠道销售 1001 零售，1002 普通小批，1003 精品小批，1004 渠道
 * @since 2025-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomGoodsAsyncVo extends GoodsDomain implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 渠道销售 1001 零售，1002 普通小批，1003 精品小批，1004 渠道
     */
    private String channelSales;


    /**
     * 查询标记，1001-根据根据商家商品编号查询关联的店铺商品信息 1002-店铺商品编号反查关联的商家商品信息
     */
    private String queryFlag;

    /**
     * 店铺编号，当根据商家商品编码查询店铺商品时 即queryFlag == 1001 此值必传
     */
    private String storeNo;

    /**
     * 商品编码list
     */
    private List<String> goodsNos;

    /**
     * 商家商品编号(商家同步商品原型商品编号)集合
     */
    private List<String> merchantGoodsNos;

    /**
     * 店铺商品编码
     */
    private String storeGoodsNo;
}
