package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.TradeOrderItemsDomain;
import cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO;
import cn.htdt.goodsprocess.dto.request.tradeorder.ReqTradeOrderItemsDTO;
import cn.htdt.goodsprocess.dto.response.tradeorder.ResPurchaseTradeOrderDTO;
import cn.htdt.goodsprocess.dto.response.tradeorder.ResPurchaseTradeOrderItemDTO;
import cn.htdt.goodsprocess.vo.AtomTradeOrderItemsVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 订单行信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-15
 */
@Mapper
public interface TradeOrderItemsDao extends BaseMapper<TradeOrderItemsDomain> {

    /**
     * 查询采购交易单信息
     *
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    List<ResPurchaseTradeOrderDTO> selectTradeOrderItems(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    /**
     * 查询采购交易单信息-stockFlag不为空
     *
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    List<ResPurchaseTradeOrderDTO> selectTradeOrderItemsStock(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    /**
     * 查询采购交易单信息-商品状态入參不为空时
     *
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    List<ResPurchaseTradeOrderDTO> selectTradeOrderItemsStockFlagNotNull(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    /**
     * 判断是否所有订单下此商品都入库
     *
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    Integer selectExistTradeOrderItemsRelationCount(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    /**
     * 若所有订单下此商品都入库，删除未入库的关联
     *
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    void delNotStockRelation(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    /**
     * 查询采购交易单信息
     *
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    List<ResPurchaseTradeOrderDTO> selectTradeOrderItemsList(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    /**
     * 查询采购交易单信息-count
     * @param reqPurchaseTradeOrderDTO
     * @return
     */
    Integer selectTradeOrderItemsCount(ReqPurchaseTradeOrderDTO reqPurchaseTradeOrderDTO);

    /**
     * 根据中台订单号和商品sku查询采购商品行信息信息
     *
     * @param reqTradeOrderItemsDTO
     * @return
     */
    TradeOrderItemsDomain selectOneTradeOrderItems(ReqTradeOrderItemsDTO reqTradeOrderItemsDTO);

    /**
     * 根据中台订单号和商品sku查询采购商品行信息信息
     *
     * @param reqTradeOrderItemsDTO
     * @return
     */
    List<ResPurchaseTradeOrderItemDTO> selectTradeOrderItemList(ReqTradeOrderItemsDTO reqTradeOrderItemsDTO);

    /**
     * 根据中台订单号和商品sku更新关联千橙商品信息
     *
     * @param domain
     * @return
     */
    int updateOneGoodsByTrade(TradeOrderItemsDomain domain);

    /**
     * 根据千橙商品编号更新关联千橙商品信息
     *
     * @param domain
     * @return
     */
    int updateTradeOrderItems(TradeOrderItemsDomain domain);

    /**
     * 根据批量千橙商品编号更新关联千橙商品信息
     *
     * @param vo
     * @return
     */
    int batchUpdateTradeOrderItems(AtomTradeOrderItemsVo vo);

}
