package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AtomGoodsNameNoVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 商品id
     * */
    private String goodsNo;
    /**
     * 商品名称
     * */
    private String goodsName;
    /**
     * 零售价
     * */
    private BigDecimal retailPrice;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
