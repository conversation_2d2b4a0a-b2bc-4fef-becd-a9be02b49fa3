package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsAuditRecordDomain;
import cn.htdt.goodsprocess.vo.AtomGoodsVo;
import cn.htdt.goodsprocess.vo.AtomReqGoodsAuditRecordVo;
import cn.htdt.goodsprocess.vo.AtomResGoodsAuditRecordVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品审核记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsAuditRecordDao extends BaseMapper<GoodsAuditRecordDomain> {

    /**
     * 获取商品审核列表（分页）
     *
     * @param vo 查询参数
     * @return List<AtomResGoodsAuditRecordVo>
     */
    List<AtomResGoodsAuditRecordVo> getGoodsAuditPageList(AtomGoodsVo vo);

    /**
     * 获取商品审核信息列表
     *
     * @param vo 查询参数
     * @return List<AtomResGoodsAuditRecordVo>
     */
    List<AtomResGoodsAuditRecordVo> getGoodsAuditInfoList(AtomGoodsVo vo);

    /**
     * 根据参数查询集合
     *
     * @param domain 查询参数
     * @return List<GoodsAuditRecordDomain>
     */
    List<GoodsAuditRecordDomain> selectByParams(GoodsAuditRecordDomain domain);

    /**
     * 根据参数更新
     *
     * @param vo 请求参数
     * @return int
     */
    int updateByParam(AtomReqGoodsAuditRecordVo vo);
}
