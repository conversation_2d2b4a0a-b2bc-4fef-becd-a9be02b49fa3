package cn.htdt.goodsprocess.dao;

import cn.htdt.goodscenter.dto.response.AtomResGoodsGroupsDTO;
import cn.htdt.goodsprocess.domain.GoodsGroupsDomain;
import cn.htdt.goodsprocess.dto.request.goodsgroups.ReqGoodsGroupsDTO;
import cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo;
import cn.htdt.goodsprocess.vo.AtomGoodsVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface GoodsGroupsDao extends BaseMapper<GoodsGroupsDomain> {

    /**
     * 查询商品组
     *
     * @param id 商品组
     * @return 商品组
     */
    public GoodsGroupsDomain selectGoodsGroupsById(Long id);

    /**
     * 查询商品组列表
     *
     * @param goodsGroupsDomain 商品组
     * @return 商品组
     */
    public List<AtomGoodsGroupsVo> selectGoodsGroupsList(GoodsGroupsDomain goodsGroupsDomain);


    /**
     * 修改商品组
     *
     * @param goodsGroupsDomain 商品组
     * @return 结果
     */
    public int updateGoodsGroups(GoodsGroupsDomain goodsGroupsDomain);

    /**
     * 删除商品组
     *
     * @param id 商品组主键
     * @return 结果
     */
    public int deleteGoodsGroupsById(Long id);

    /**
     * 批量删除商品组
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGoodsGroupsByIds(Long[] ids);

    /**
     * 根据商品组ID列表查询
     *
     * @param goodsGroupsVo
     * @return
     */
    List<GoodsGroupsDomain> selectGoodsGroupsByNos(AtomGoodsGroupsVo goodsGroupsVo);
    /**
     * 根据商家商品编码。查询
     */
    List<GoodsGroupsDomain> selectGoodsGroupsByMerchantGoodsGroupsNo(AtomGoodsGroupsVo goodsGroupsVo);

    /**
     * 根据商品组
     *
     * @param goodsGroupsVo
     * @return
     */
    AtomResGoodsGroupsDTO getGoodsGroupsDetail(AtomGoodsGroupsVo goodsGroupsVo);

    /**
     * 商家自建排重使用
     * @param reqGoodsGroupsDTO
     * @return
     */
    List<GoodsGroupsDomain> getGoodsGroupsDetailByGoodsGroupsName(ReqGoodsGroupsDTO reqGoodsGroupsDTO);

    /**
     * 店铺自建排重使用
     * @param reqGoodsGroupsDTO
     * @return
     */
    List<GoodsGroupsDomain> getDetailForStoreBuildDistinct(ReqGoodsGroupsDTO reqGoodsGroupsDTO);


    int modifyLogicDeleteGoodsGroups(GoodsGroupsDomain goodsGroupsDomain);

    List<AtomGoodsVo> selectWarehouseAllocationOrderParams(AtomGoodsGroupsVo goodsGroupsVo);

    List<GoodsGroupsDomain> getNoWarehouseGoodsPage(AtomGoodsGroupsVo goodsGroupsVo);

    /**
     * 物理删除同步的商品
     * @param temp
     */
    int modifyDeleteGoodsGroups(GoodsGroupsDomain temp);

    /**
     * 根据商品组ID（子商品组）查询主商品组信息
     * @param vo
     * @return
     */
    GoodsGroupsDomain selectGoodsGroupsBySubGoodsGroupsNo(AtomGoodsGroupsVo vo);

    /**
     * 根据商品编码查询商品组
     * @param vo
     * @return
     */
    List<GoodsGroupsDomain> selectGoodsGroupsByGoodsNo(AtomGoodsGroupsVo vo);

    /**
     * 根据商品编码列表查询商品组
     * @param vo
     * @return
     */
    List<AtomResGoodsGroupsDTO> selectGoodsGroupsByGoodsNos(AtomGoodsGroupsVo vo);
}
