package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.AttributeNameDomain;
import cn.htdt.goodsprocess.domain.FreightTemplateNewDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 运费模板明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-07
 */
@Mapper
public interface FreightTemplateNewDao extends BaseMapper<FreightTemplateNewDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<AttributeNameDomain> selectByParams(FreightTemplateNewDomain domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(FreightTemplateNewDomain domain);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(FreightTemplateNewDomain domain);
}
