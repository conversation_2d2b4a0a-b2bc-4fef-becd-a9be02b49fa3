package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsAttributeModelDomain;
import cn.htdt.goodsprocess.vo.AtomGoodsAttributeModelVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品属性模板表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-26
 */
@Mapper
public interface GoodsAttributeModelDao extends BaseMapper<GoodsAttributeModelDomain> {

    /**
     * 查询商品属性模板
     *
     * @param vo 查询参数
     * @return List<GoodsAttributeModelDomain>
     */
    List<GoodsAttributeModelDomain> selectByParams(AtomGoodsAttributeModelVo vo);

    /**
     * 修改商品属性模板
     *
     * @param domain 请求参数
     * @return int
     */
    int updateGoodsAttributeModel(GoodsAttributeModelDomain domain);
}
