package cn.htdt.goodsprocess.dao;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import cn.htdt.goodsprocess.domain.RequisitionOrderDomain;
import cn.htdt.goodsprocess.dto.request.requisitionorder.ReqAuditRequisitionOrderDTO;
import cn.htdt.goodsprocess.dto.request.requisitionorder.ReqRequisitionOrderDetailDTO;
import cn.htdt.goodsprocess.dto.request.requisitionorder.ReqRequisitionOrderListDTO;
import cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionOrderDetailDTO;
import cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionOrderListDTO;

/**
 * <AUTHOR>
 * @description 要货单dao
 * @date 2023/5/23 15:15
 **/
@Mapper
public interface RequisitionOrderDao extends BaseMapper<RequisitionOrderDomain> {

    /**
     * 查询要货单分页列表
     * 
     * @param reqDto
     *            请求参数
     * @return List<ResRequisitionOrderListDTO>
     */
    List<ResRequisitionOrderListDTO> selectRequisitionOrderPage(ReqRequisitionOrderListDTO reqDto);

    /**
     * 获取要货单详情
     * 
     * @param reqDto
     *            请求参数
     * @return ResRequisitionOrderDetailDTO
     */
    ResRequisitionOrderDetailDTO getRequisitionOrderDetail(ReqRequisitionOrderDetailDTO reqDto);

    /**
     * 更新要货单
     * 
     * @param reqDto
     *            请求参数
     */
    void updateRequisitionOrder(RequisitionOrderDomain reqDto);

    /**
     * 审核要货单
     * 
     * @param reqDto
     *            请求参数
     */
    void auditRequisitionOrder(ReqAuditRequisitionOrderDTO reqDto);

}
