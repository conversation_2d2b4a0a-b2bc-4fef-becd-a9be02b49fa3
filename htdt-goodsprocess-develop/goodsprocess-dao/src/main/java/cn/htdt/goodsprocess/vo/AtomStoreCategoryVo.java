package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.StoreCategoryDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2020/12/28 10:47
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomStoreCategoryVo extends StoreCategoryDomain {

    private static final long serialVersionUID = -4531505045130547102L;
    /**
     * 父类目名称
     */
    private String parentStoreCategoryName;

    /**
     * 商品集合编码
     */
    private List<String> goodsNoList;

    /**
     * 商品编号
     */
    private String goodsNo;

    /**
     * 店铺类目id集合
     */
    private List<String> storeCategoryNoList;

    /**
     * 父类目节点ID集合
     */
    private List<String> parentNoList;

    /**
     * 排序方式
     */
    private String orderBy;

    /**
     * 字段名
     */
    private String sort;

    /**
     * 商品状态1001-未上架;1002:已上架;
     */
    private String goodsStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
