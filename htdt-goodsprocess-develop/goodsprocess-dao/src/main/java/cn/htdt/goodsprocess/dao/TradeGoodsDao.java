package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.TradeGoodsDomain;
import cn.htdt.goodsprocess.dto.request.tradeorder.ReqTradeGoodsDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品sku信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Mapper
public interface TradeGoodsDao extends BaseMapper<TradeGoodsDomain> {

    /**
     * 根据商品编号获取商品信息
     * @param reqTradeGoodsDTO
     * @return
     */
    List<TradeGoodsDomain> selectBySkuCodeList(ReqTradeGoodsDTO reqTradeGoodsDTO);

    /**
     * 批量更新
     * @param domainList
     * @return
     */
    int batchUpdateGoods(@Param("list") List<TradeGoodsDomain> domainList);

}
