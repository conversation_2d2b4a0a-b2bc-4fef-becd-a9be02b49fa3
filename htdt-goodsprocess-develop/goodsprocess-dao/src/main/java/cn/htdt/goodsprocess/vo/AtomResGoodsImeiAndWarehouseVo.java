package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 商品串码对应的仓库信息
 *
 * <AUTHOR>
 * @date 2021年4月2日
 */
@Data
public class AtomResGoodsImeiAndWarehouseVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品串码主键
     */
    private String imeiNo;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 仓库编码
     */
    private String warehouseNo;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 采购单编码
     */
    private String purchaseCode;

    /**
     * 串码个数
     */
    private int imeiNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
