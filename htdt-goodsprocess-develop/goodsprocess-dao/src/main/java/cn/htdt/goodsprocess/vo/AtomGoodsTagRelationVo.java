package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.GoodsTagRelationDomain;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 详细说明.商品标签关系vo
 * <p>
 * Copyright: Copyright (c) 2020/12/11 16:17
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class AtomGoodsTagRelationVo extends GoodsTagRelationDomain {

    private static final long serialVersionUID = -4373600856090574689L;
    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签文案
     */
    private String tagContent;

    /**
     * 标签图片url
     */
    private String tagPictureUrl;

    /**
     * 商品集合
     */
    private List<String> goodsNoList;

    /**
     * 商品编码集合
     * <p>
     * 备注：上面已经有了 goodsNoList，再加一个goodsList的原因是因为AtomReqGoodsTagRelationDTO定义了goodsList， 但不知goodsNoList是否在其他地方有使用
     */
    private List<String> goodsList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
