package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *  采购单待入库Vo
 *
 * <AUTHOR>
 * @date 2020年9月15日
 */
@Data
public class AtomPurchaseOrderTrackDetailVo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 采购单编码
     */
    private String purchaseCode;
    /**
     * 数据来源
     * */
    private String sourceType;
    /**
     *供应商编码
     */
    private String supplierCode;
    /**
     *供应商名称
     */
    private String supplierName;
    /**
     * 商品编码
     */
    private String goodsNo;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     *采购日期
     */
    private LocalDateTime purchaseDate;
    /**
     *订单状态21:待签收 22:已签收 23:待入库 24:部分入库 25:已结案 26:完成
     */
    private String orderStatus;
    /**
     *商品类目编码
     */
    private String categoryNo;
    /**
     *商品类目名称
     */
    private String categoryName;
    /**
     * 计量单位ID
     */
    private String calculationUnitNo;
    /**
     * 计量单位名称
     */
    private String calculationUnitName;
    /**
     * 采购单位
     */
    private String purchaseUnit;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;
    /**
     * 采购金额
     */
    private BigDecimal purchasePrice;
    /**
     *采购单价
     */
    private BigDecimal purchaseUnitPrice;
    /**
     * 采购数量
     */
    private BigDecimal purchaseNum;
    /**
     * 已入库数量
     */
    private BigDecimal storageCount;

    /**
     * 已入库金额
     */
    private BigDecimal putPurchasePrice;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
