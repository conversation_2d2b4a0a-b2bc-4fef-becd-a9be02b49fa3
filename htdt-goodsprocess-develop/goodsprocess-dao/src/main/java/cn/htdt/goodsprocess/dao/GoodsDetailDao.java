package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsDetailDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsDetailDao extends BaseMapper<GoodsDetailDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<GoodsDetailDomain> selectByParams(GoodsDetailDomain domain);

    /**
     * 根据参数插入
     * @param domain
     * @return
     */
    int insertSelective(GoodsDetailDomain domain);

    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsDetailDomain domain);

    /**
     * 根据参数逻辑删除
     * @param domain
     * @return
     */
    int logicDelete(GoodsDetailDomain domain);
}
