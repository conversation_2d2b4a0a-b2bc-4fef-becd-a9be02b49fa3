package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *  采购单信息导出Vo
 *
 * <AUTHOR>
 * @date 2020年9月15日
 */
@Data
public class AtomPurchaseOrderVo  implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     *采购单编码
     */
    private String purchaseCode;
    /**
     *数据来源 1001:手动添加 1002：采购商城添加
     */
    private String sourceType;
    /**
     *供应商编码
     */
    private String supplierCode;
    /**
     *供应商名称
     */
    private String supplierName;
    /**
     *采购日期
     */
    private LocalDateTime purchaseDate;
    /**
     *采购员
     */
    private String purchaserName;
    /**
     *备注
     */
    private String remark;
    /**
     *附件名称
     */
    private String attachName;
    /**
     *附件路径
     */
    private String attachPath;
    /**
     *收货联系人
     */
    private String receiveContactName;
    /**
     *收货联系人手机号
     */
    private String receiveContactMobile;
    /**
     *收货地址省份code
     */
    private String receiveProvinceCode;
    /**
     *收货地址省份名称
     */
    private String receiveProvinceName;
    /**
     *收货地址城市code
     */
    private String receiveCityCode;
    /**
     *收货地址城市名称
     */
    private String receiveCityName;
    /**
     *收货地址地区code
     */
    private String receiveDistrictCode;
    /**
     *收货地址地区名称
     */
    private String receiveDistrictName;
    /**
     *收货地址镇code
     */
    private String receiveTownCode;
    /**
     *收货地址镇名称
     */
    private String receiveTownName;
    /**
     *收货详细地址
     */
    private String receiveDetailAddress;
    /**
     *发货联系人
     */
    private String sendContactName;
    /**
     *发货联系人手机号
     */
    private String sendContactMobile;
    /**
     *发货地址省份code
     */
    private String sendProvinceCode;
    /**
     *发货地址省份名称
     */
    private String sendProvinceName;
    /**
     *发货地址城市code
     */
    private String sendCityCode;
    /**
     *发货地址城市名称
     */
    private String sendCityName;
    /**
     *发货地址地区code
     */
    private String sendDistrictCode;
    /**
     *发货地址地区名称
     */
    private String sendDistrictName;
    /**
     *发货地址镇code
     */
    private String sendTownCode;
    /**
     *发货地址镇名称
     */
    private String sendTownName;
    /**
     *发货详细地址
     */
    private String sendDetailAddress;
    /**
     *订单状态21:待签收 22:已签收 23:待入库 24:部分入库 25:已结案 26:完成
     */
    private Integer orderStatus;
    /**
     *总采购数量
     */
    private BigDecimal totalPurchaseNum;
    /**
     *交易金额
     */
    private BigDecimal totalPurchasePrice;
    /**
     *商家ID
     */
    private String merchantNo;
    /**
     *商家名称
     */
    private String merchantName;
    /**
     *店铺ID，对应orgId
     */
    private String storeNo;
    /**
     *店铺名称，对应orgName
     */
    private String storeName;
    /**
     *是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;
    /**
     *归属平台编号
     */
    private String companyNo;
    /**
     *归属平台名称
     */
    private String companyName;
    /**
     *归属分部编号
     */
    private String branchNo;
    /**
     *归属分部名称
     */
    private String branchName;
    /**
     * 归属平台类型(1001.运营 1002.商家 1003.店铺)
     */
    private String platformType;

    /**
     * 分组时间
     */
    private String groupTime;

    /**
     * 月度订单总数
     */
    private Integer totalOrder;

    /**
     * 月度金额总数
     */
    private BigDecimal totalAmount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
