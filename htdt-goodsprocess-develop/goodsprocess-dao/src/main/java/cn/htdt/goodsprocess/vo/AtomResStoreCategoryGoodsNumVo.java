package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 店铺类目和关联的商品数量查询返回结果集 自定义DTO
 *
 * <AUTHOR>
 * @date 2022/12/6
 **/
@Data
public class AtomResStoreCategoryGoodsNumVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 店铺类目id
     */
    private String storeCategoryNo;

    /**
     * 店铺类目名称
     */
    private String storeCategoryName;

    /**
     * 父类目节点ID
     */
    private String parentNo;

    /**
     * 商品数量
     */
    private Integer goodsNum;

    /**
     * 门店编号
     */
    private String storeNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
