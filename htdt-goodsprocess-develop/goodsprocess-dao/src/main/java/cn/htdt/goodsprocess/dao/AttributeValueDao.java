package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.AttributeValueDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 属性值表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface AttributeValueDao extends BaseMapper<AttributeValueDomain> {

    /**
     * 根据参数查询集合
     *
     * @param domain 查询参数
     * @return List<AttributeValueDomain>
     */
    List<AttributeValueDomain> selectByParams(AttributeValueDomain domain);

    /**
     * 根据参数更新
     *
     * @param domain 查询参数
     * @return int
     */
    int updateByAttributeValueCode(AttributeValueDomain domain);

    /**
     * 逻辑删除
     *
     * @param domain 查询参数
     * @return int
     */
    int logicDelete(AttributeValueDomain domain);

    /**
     * 批量逻辑删除
     *
     * @param domain            查询参数
     * @param attributeCodeList 属性名称code集合
     * @return int
     */
    int batchLogicDelete(@Param("domain") AttributeValueDomain domain, @Param("list") List<String> attributeCodeList);
}
