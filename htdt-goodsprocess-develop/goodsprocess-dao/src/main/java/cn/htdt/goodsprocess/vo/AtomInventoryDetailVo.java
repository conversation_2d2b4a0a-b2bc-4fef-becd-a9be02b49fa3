package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.ImInventoryDetailDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点
 **/
@Data
public class AtomInventoryDetailVo extends ImInventoryDetailDomain {
    /**
     * 计量单位id
     */
    private String calculationUnitNo;
    /**
     * 计量单位符号
     */
    private String calculationUnitSymbol;

    /**
     * 计量单位名称
     */
    private String calculationUnitName;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 辅计量单位符号
     */
    private String assistCalculationUnitSymbol;

    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;

    /**
     * 总库存数量，即实体库存
     */
    private BigDecimal realStockNum;

    /**
     * 冻结库存数量，即预锁数量
     */
    private BigDecimal freezeStockNum;

    /**
     * 是否入仓(1:否 2:是)
     */
    private Integer warehouseFlag;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 助记码
     */
    private String goodsHelpCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品删除标识
     */
    private Integer goodsDeleteFlag;

    /**
     * 批量商品编号
     */
    private List<String> goodsList;

    /**
     * 商品类目名称
     */
    private String categoryName;

    /*****返回参*****/
    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 第一、二、三属性值编码，格式：first-second-third
     */
    private String attributeValueName;

    public String getAttributeValueName() {
        attributeValueName = "";
        if(StringUtils.isNotEmpty(getFirstAttributeValueName())) {
            attributeValueName+= "-" + getFirstAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getSecondAttributeValueName())) {
            attributeValueName+= "-" + getSecondAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getThirdAttributeValueName())) {
            attributeValueName+= "-" + getThirdAttributeValueName();
        }
        if(StringUtils.isNotEmpty(attributeValueName)) {
            attributeValueName = attributeValueName.substring(1);
        }
        return attributeValueName;
    }

    /**
     * 类目名称全路径
     */
    private String fullNamePath;

    /**
     * 销量
     */
    private BigDecimal salesVolume;

    /**
     * 零售价
     */
    private BigDecimal retailPrice;

    /**
     * 可售库存
     */
    private BigDecimal canSaleStockNum;

    /**
     * 盘点下商品总数
     */
    private Integer totalCount;

    private List<String> inventoryCodes;
    /**
     * 盘点商品是否超20条标识：1=无；2=超
     */
    private Integer moreFlag;

    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     * 20230928蛋品-zxy-盘点单-盘点单详情
     */
    private String multiUnitType;

    /**
     * 多单位主品编号
     * 20230928蛋品-zxy-盘点单-盘点单详情
     */
    private String multiUnitGoodsNo;

    /**
     * 保质期单位（day日,month月,year年）
     */
    private String shelfLifeUnit;

    /**
     * 保质期
     */
    private Integer qualityGuaranteePeriod;

    /**
     * 是否启动效期管理标识。1否，2是
     */
    private Integer validityPeriodManageFlag;

    /**
     * 盘点类型：0 商品；1 商品组
     */
    private Integer inventoryType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
