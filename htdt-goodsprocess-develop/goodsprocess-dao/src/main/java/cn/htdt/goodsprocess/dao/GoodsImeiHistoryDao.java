package cn.htdt.goodsprocess.dao;


import cn.htdt.goodsprocess.domain.GoodsImeiHistoryDomain;
import cn.htdt.goodsprocess.vo.GoodsImeiHistoryVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 商品串码历史表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@Mapper
public interface GoodsImeiHistoryDao extends BaseMapper<GoodsImeiHistoryDomain> {



    /**
     * 从商品串码表中将前一日数据记录至商品串码历史表
     */
    void insertGoodsImeiHistoryByYesterday(@Param("merchantNo") String merchantNo);

    /**
     * 删除商家下30天前的数据
     * @param merchantNo
     */
    void deleteGoodsImeiHistoryBefore31Days(@Param("merchantNo") String merchantNo);


    /**
     * 获取串码历史记录列表
     * @param goodsImeiHistoryVo
     * @return
     */
    List<GoodsImeiHistoryVo> selectByParams(GoodsImeiHistoryVo goodsImeiHistoryVo);
}
