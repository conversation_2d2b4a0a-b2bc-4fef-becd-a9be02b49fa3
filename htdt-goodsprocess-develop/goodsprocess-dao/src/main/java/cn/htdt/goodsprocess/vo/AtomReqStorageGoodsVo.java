package cn.htdt.goodsprocess.vo;
/**
 * <AUTHOR>
 * @Date 2020-10-20
 * @Description  请求DTO
 **/

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020-10-20
 * @Description 仓库店铺关联 请求DTO
 **/
@Data
public class AtomReqStorageGoodsVo  implements Serializable {

    private static final long serialVersionUID = -7890689661167910000L;
    /**
     * 采购单号
     * */
    private String purchaseCode;
    /**
     * 采购单商品行编码 对应流水表sub_bill_code
     * */
    private String purchaseGoodsCode;
    /**
     * 出入库数量
     */
    private BigDecimal stockNum;

    /**
     *采购数量
     */
    private BigDecimal purchaseNum;

    /**
     * 修改人编码
     */
    private String  modifyNo;
    /**
     * 修改人名称
     */
    private String  modifyName;
    /**
     * 订单状态
     * */
    private String orderStatus;
    /**
     * 已入库数量
     * */
    private BigDecimal storageCount;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
