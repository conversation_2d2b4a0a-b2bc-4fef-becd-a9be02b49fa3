package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsMasterDataDomain;
import cn.htdt.goodsprocess.vo.AtomGoodsMasterDataVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface GoodsMasterDataDao extends BaseMapper<GoodsMasterDataDomain> {

    /**
     * 根据参数查询集合
     * @param vo
     * @return
     */
    List<AtomGoodsMasterDataVo> selectByParams(AtomGoodsMasterDataVo vo);

    /**
     * 批量更新
     * @param goodsList
     * @return
     */
    int batchUpdate(@Param("goodsList") List<GoodsMasterDataDomain> goodsList);
    /**
     * 根据参数更新
     * @param domain
     * @return
     */
    int updateByPrimaryKeySelective(GoodsMasterDataDomain domain);

    /**
     * 根据参数逻辑删除
     * @param vo
     * @return
     */
    int logicDelete(AtomGoodsMasterDataVo vo);

    /**
     * 查询商品数量
     * @param vo
     * @return
     */
    Integer selectCount(AtomGoodsMasterDataVo vo);
}
