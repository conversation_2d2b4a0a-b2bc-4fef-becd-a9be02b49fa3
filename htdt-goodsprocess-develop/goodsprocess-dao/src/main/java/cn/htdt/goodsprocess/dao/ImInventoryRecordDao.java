package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.ImInventoryRecordDomain;
import cn.htdt.goodsprocess.vo.AtomInventoryRecordVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 盘点人记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
@Mapper
public interface ImInventoryRecordDao extends BaseMapper<ImInventoryRecordDomain> {

    /**
     * 根据参数查询集合
     * @param domain
     * @return
     */
    List<ImInventoryRecordDomain> selectInventoryRecordByParams(ImInventoryRecordDomain domain);

    /**
     * 根据参数查询盘点人（盘点人逗号隔开）
     * @param vo
     * @return
     */
    AtomInventoryRecordVo selectInventoryNameByParams(AtomInventoryRecordVo vo);

    /**
     * 根据参数查询盘点人（盘点人逗号隔开）-批量
     * @param vo
     * @return
     */
    List<AtomInventoryRecordVo> selectInventoryNamesByParams(AtomInventoryRecordVo vo);

    /**
     * 根据参数逻辑删除
     * @param list
     * @return
     */
    int batchLogicDelete(@Param("list") List<String> list);

}
