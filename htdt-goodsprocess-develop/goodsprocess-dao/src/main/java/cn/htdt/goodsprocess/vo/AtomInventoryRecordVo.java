package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.ImInventoryRecordDomain;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <p>
 * 盘点人记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AtomInventoryRecordVo extends ImInventoryRecordDomain {

    private static final long serialVersionUID = 1L;

    /**
     * 盘点人姓名
     */
    private String inventoryName;

    /**
     * 盘点单号list
     */
    private List<String> inventoryCodes;


}
