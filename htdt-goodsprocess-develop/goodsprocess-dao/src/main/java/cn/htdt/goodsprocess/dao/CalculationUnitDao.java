package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.CalculationUnitDomain;
import cn.htdt.goodsprocess.vo.AtomCalculationUnitVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 计量单位表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface CalculationUnitDao extends BaseMapper<CalculationUnitDomain> {

    /**
     * 根据参数查询集合,名称模糊查询
     *
     * @param vo 查询参数
     * @return List<CalculationUnitDomain>
     */
    List<CalculationUnitDomain> selectFuzzyByParams(AtomCalculationUnitVo vo);

    /**
     * 根据参数查询集合
     *
     * @param domain 查询参数
     * @return List<CalculationUnitDomain>
     */
    List<CalculationUnitDomain> selectByParams(CalculationUnitDomain domain);

    /**
     * 查询计量单位信息，根据计量单位编码集合查询
     *
     * @param calculationUnitNoList 计量单位ID集合
     * @return List<CalculationUnitDomain>
     */
    List<CalculationUnitDomain> selectByCalculationUnitNoList(@Param("list") List<String> calculationUnitNoList);

    /**
     * 根据参数更新
     *
     * @param domain 计量单位参数
     * @return int
     */
    int updateByCalculationUnitNo(CalculationUnitDomain domain);

    /**
     * 根据参数逻辑删除
     *
     * @param domain 查询参数
     * @return int
     */
    int logicDelete(CalculationUnitDomain domain);

    /**
     * 批量逻辑删除计量单位
     *
     * @param domain 修改参数
     * @param list   计量单位id集合
     * @return int
     */
    int batchLogicDelete(@Param("domain") CalculationUnitDomain domain, @Param("list") List<String> list);

    /**
     * 特殊的查询，只查计量单位编码和计量单位名称
     * @return
     */
    List<CalculationUnitDomain> selectCalculationUnitNameByNo();

    /**
     * 特殊的查询，只查计量单位编码和计量单位名称
     * @return
     */
    CalculationUnitDomain selectCalculationUnitByUnitNo(CalculationUnitDomain domain);

}