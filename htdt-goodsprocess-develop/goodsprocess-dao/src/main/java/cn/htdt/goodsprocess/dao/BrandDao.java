package cn.htdt.goodsprocess.dao;

import cn.htdt.goodscenter.dto.request.AtomReqBrandComPageDTO;
import cn.htdt.goodsprocess.domain.BrandDomain;
import cn.htdt.goodsprocess.vo.AtomReqBrandVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 品牌表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface BrandDao extends BaseMapper<BrandDomain> {

    /**
     * 根据参数查询集合,名称模糊查询
     *
     * @param vo 品牌参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectByParams(AtomReqBrandVo vo);

    /**
     * 根据参数查询集合,名称模糊查询（平台）
     *
     * @param vo 品牌参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectByParamsPlatform(AtomReqBrandVo vo);

    /**
     * 根据参数查询集合,名称模糊查询（商家）
     *
     * @param vo 品牌参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectByParamsMerchant(AtomReqBrandVo vo);

    /**
     * 获取商品相关的品牌
     * @Date 2021/12/10
     * <AUTHOR>
     * @param vo
     * @return List<BrandDomain>
     **/
    List<BrandDomain> selectBrandsByGoods(AtomReqBrandVo vo);

    /**
     * 获取商品相关的品牌开头字母
     * @Date 2021/12/10
     * <AUTHOR>
     * @param domain
     * @return List<String>
     **/
    List<String> selectInitialssByGoods(AtomReqBrandVo vo);

    /**
     * 根据参数查询集合,名称模糊查询（店铺）
     *
     * @param vo 品牌参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectByParamsStore(AtomReqBrandVo vo);

    /**
     * 根据参数查询集合，名称精确查询（平台）
     *
     * @param domain 品牌参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectByBrandNamePrecisePlatform(BrandDomain domain);

    /**
     * 根据参数查询集合，名称精确查询（商家）
     *
     * @param domain 品牌参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectByBrandNamePreciseMerchant(BrandDomain domain);

    /**
     * 根据参数查询集合，名称精确查询（店铺）
     *
     * @param domain 品牌参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectByBrandNamePreciseStore(BrandDomain domain);

    /**
     * 查询品牌信息，根据品牌编码集合查询
     *
     * @param brandNoList 品牌编码集合
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectByBrandNoList(@Param("list") List<String> brandNoList);

    /**
     * 查询品牌信息，根据品牌编码查询
     *
     * @param domain  BrandDomain 品牌编码brandNo
     * @return BrandDomain
     */
    BrandDomain selectByBrandNo(BrandDomain domain);

    /**
     * 获取品牌首字母列表（平台）
     *
     * @param domain 品牌参数
     * @return List<String>
     */
    List<String> selectBrandInitialsListPlatform(BrandDomain domain);

    /**
     * 获取品牌首字母列表（商家）
     *
     * @param domain 品牌参数
     * @return List<String>
     */
    List<String> selectBrandInitialsListMerchant(BrandDomain domain);

    /**
     * 获取品牌首字母列表（店铺）
     *
     * @param domain 品牌参数
     * @return List<String>
     */
    List<String> selectBrandInitialsListStore(BrandDomain domain);

    /**
     * 根据参数更新
     *
     * @param domain 品牌参数
     * @return int
     */
    int updateByBrandNo(BrandDomain domain);

    /**
     * 根据参数逻辑删除
     *
     * @param domain 品牌参数
     * @return int
     */
    int logicDelete(BrandDomain domain);

    /**
     * 根据参数逻辑删除
     *
     * @param domain      品牌参数
     * @param brandNoList 品牌编号集合
     * @return int
     */
    int batchLogicDelete(@Param("domain") BrandDomain domain, @Param("list") List<String> brandNoList);

    /**
     * 获取所有商品的品牌列表
     * ps：通过店铺商品反推，去重，获取商品品牌
     *
     * @param vo 查询参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectBrandListByAllGoods(AtomReqBrandVo vo);

    /**
     * 获取品牌列表
     * ps：通过商品主数据反推，去重，获取商品品牌
     *
     * @param vo 查询参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectBrandListByGoodsMasterData(AtomReqBrandVo vo);

    /**
     * 商家身份，目前只是给导入商品用
     * 特殊的查询，只查品牌编码和品牌名称
     * @param dto
     * @return
     */
    List<BrandDomain> selectBrandNameByMerchant(AtomReqBrandComPageDTO dto);

    /**
     * 店铺和单店身份，目前只是给导入商品用
     * 特殊的查询，只查品牌编码和品牌名称
     * @param dto
     * @return
     */
    List<BrandDomain> selectBrandNameByStore(AtomReqBrandComPageDTO dto);

    /**
     * 获取所有商品的品牌列表
     * ps：汇享购通过店铺商品反推，去重，首字母排序
     *
     * @param vo 查询参数
     * @return List<BrandDomain>
     */
    List<BrandDomain> selectStoreBrandByGoods(AtomReqBrandVo vo);
}