package cn.htdt.goodsprocess.vo;

import cn.htdt.goodsprocess.domain.TradeOrderItemsRelationDomain;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 订单行信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-20
 */
@Data
public class AtomTradeOrderItemsRelationVo extends TradeOrderItemsRelationDomain {

    private static final long serialVersionUID = 1L;

    private List<String> goodsNos;

    @ApiModelProperty(value = "原关联的商品编号，包含普通商品、套餐商品等所有商品")
    private String relationGoodsNo;

}
