package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 店铺类目商品查询请求 自定义DTO
 *
 * <AUTHOR>
 * @date 2020/11/13
 **/
@Data
public class AtomReqStoreCategoryGoodsVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 店铺类目id
     */
    private String storeCategoryNo;

    /**
     * 店铺类目id集合
     */
    private List<String> storeCategoryNoList;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 商品编号集合，包含普通商品、套餐商品等所有商品
     */
    private List<String> goodsNoList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
