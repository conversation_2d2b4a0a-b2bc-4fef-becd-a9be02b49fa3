package cn.htdt.goodsprocess.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class AtomMaxMinPriceVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 最高价
     * */
    private BigDecimal maxPrice;
    /**
     * 最低价
     * */
    private BigDecimal minPrice;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
