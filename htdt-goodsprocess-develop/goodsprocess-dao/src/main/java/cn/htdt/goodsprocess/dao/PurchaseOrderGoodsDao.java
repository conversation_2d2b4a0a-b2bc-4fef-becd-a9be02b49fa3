package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.PurchaseOrderDomain;
import cn.htdt.goodsprocess.domain.PurchaseOrderGoodsDomain;
import cn.htdt.goodsprocess.dto.request.purchaseorder.ReqPurchaseGoodsDTO;
import cn.htdt.goodsprocess.vo.*;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-09-10
 */
@Mapper
public interface PurchaseOrderGoodsDao extends BaseMapper<PurchaseOrderGoodsDomain> {
    /**
     * 根据采购单编码 查下面待入库的商品
     */
    List<AtomPurchaseOrderGoodsVo> selectPurchaseGoodsByPurchaseCode(@Param("purchaseCode") String purchaseCode);

    /**
     * 根据采购单编码 查询所拥有的商品信息  关联商品表的实时是否入库
     */
    List<AtomPurchaseOrderGoodsVo> selectPurchaseOrderGoods(@Param("storeNo") String storeNo, @Param("merchantNo") String merchantNo, @Param("purchaseCode") String purchaseCode);


    /**
     * 根据采购单编码 查询所拥有的商品组信息  关联商品表的实时是否入库
     */
    List<AtomPurchaseOrderGoodsVo> selectPurchaseOrderGoodsGroups(@Param("storeNo") String storeNo, @Param("merchantNo") String merchantNo, @Param("purchaseCode") String purchaseCode);

    /**
     * 查询所有未到货的采购商品明细
     */
    List<AtomPurchaseOrderGoodsVo> selectPurchaseOrderGoodsNoDelivery(AtomPurchaseOrderGoodsVo vo);

    /**
     * 新增采购单商品
     */
    int insertSelective(PurchaseOrderGoodsDomain record);

    /**
     * 根据采购单编码 查询所有采购商品信息
     */
    List<AtomPurchaseOrderGoodsVo> selectOrderGoodsListByPCodeArr(@Param("purchaseCodeList") List<String> purchaseCodeList);

    /**
     * 根据采购单编码 查询所有采购商品信息
     */
    List<AtomPurchaseOrderGoodsVo> selectOrderGoodsList(@Param("purchaseCodeList") List<String> purchaseCodeList);

    /**
     * 根据采购单编码 删除采购单下商品
     */
    int updateDelGoodsByPurchaseCode(PurchaseOrderDomain record);

    /**
     * 入库 待入库列表页
     * 智慧零售-采购单管理-入库-列表页数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    List<AtomPurchaseOrderStorageVo> selectStorageOrderGoodsList(AtomPurchaseOrderStorageVo vo);

    /**
     * 入库 待入库列表页
     * 智慧零售-采购单管理-入库-列表页数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    List<AtomPurchaseOrderStorageVo> selectStorageOrderGoodsList2(AtomPurchaseOrderStorageVo vo);


    /**
     * 根据采购单编码修改所有订单状态为已完成（强制完成按钮）
     */
    int updateGoodsStatusFinishByPurchaseCode(AtomPurchaseOrderGoodsVo record);

    /**
     * 根据采购单商品行编码修改状态为已完成（强制完成按钮）
     */
    int updateGoodsStatusFinishByPurchaseGoodsCode(AtomPurchaseOrderGoodsVo record);

    /**
     * 入库 已入库列表页
     * 智慧零售-采购单管理-入库-已入库列表页数据
     *
     * @param
     * @return
     * <AUTHOR>
     */
    List<AtomPurchaseOrderAlreadyStorageVo> selectAlreadyStorageOrderGoodsList(@Param("purchaseCode") String purchaseCode);

    /**
     * 入库
     * 智慧零售-采购单管理-入库-详情查看
     *
     * @param
     * @return
     * <AUTHOR>
     */
    List<AtomPurchaseOrderAlreadyStorageVo> selectStorageOrderGoodsDetail(@Param("storeNo") String storeNo, @Param("merchantNo") String merchantNo, @Param("purchaseCode") String purchaseCode);

    /**
     * 入库
     * 智慧零售-采购单管理-入库-详情查看
     *
     * @param
     * @return
     * <AUTHOR>
     */
    int updateStorageOrderGoodsListStatus(@Param("storageGoodsList") List<AtomReqStorageGoodsVo> storageGoodsList);


    List<AtomPurchaseOrderTrackDetailVo> selectPurchaseOrderTrackDetail(AtomPurchaseTrackDetailDTO vo);

    long selectPurchaseOrderTrackDetail_COUNT(AtomPurchaseTrackDetailDTO vo);

    List<AtomPurchaseGoodsTrackDetailVo> selectPurchaseGoodsTrackDetail(@Param("purchaseCodeList") List<String> purchaseCodeList);

    /**
     * 判断商品存在待支付/待发货订单或者存在未入库的采购单/未出库的采购退单用
     * @param reqPurchaseGoodsDTO
     * @return
     */
    Integer selectPurchaseAndReturnGoodsCount(ReqPurchaseGoodsDTO reqPurchaseGoodsDTO);

}
