package cn.htdt.goodsprocess.dao;

import cn.htdt.goodsprocess.domain.GoodsAuditManageDomain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <p>
 * 商品审核配置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-08-25
 */
@Mapper
public interface GoodsAuditManageDao extends BaseMapper<GoodsAuditManageDomain> {

    /**
     * 根据参数查询集合
     *
     * @param domain 查询参数
     * @return List<GoodsAuditManageDomain>
     */
    List<GoodsAuditManageDomain> selectByParams(GoodsAuditManageDomain domain);

    /**
     * 根据参数更新
     *
     * @param domain 编辑参数
     * @return int
     */
    int updateByPrimaryKeySelective(GoodsAuditManageDomain domain);

    /**
     * 根据参数逻辑删除
     *
     * @param domain 编辑参数
     * @return int
     */
    int logicDelete(GoodsAuditManageDomain domain);
}
