<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.RequisitionOrderDao">
    <resultMap id="ResultMap" type="cn.htdt.goodsprocess.domain.RequisitionOrderDomain">
        <result column="id" property="id"/>
        <result column="requisition_no" property="requisitionNo"/>
        <result column="delivery_date" property="deliveryDate"/>
        <result column="remark" property="remark"/>
        <result column="item_count" property="itemCount"/>
        <result column="status" property="status"/>
        <result column="requisition_amount" property="requisitionAmount"/>
        <result column="take_amount" property="takeAmount"/>
        <result column="audit_user" property="auditUser"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_remark" property="auditRemark"/>
        <result column="out_storage_user" property="outStorageUser"/>
        <result column="out_storage_time" property="outStorageTime"/>
        <result column="in_storage_user" property="inStorageUser"/>
        <result column="in_storage_time" property="inStorageTime"/>
        <result column="cancel_user" property="cancelUser"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
        <result column="platform_type" property="platformType"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="create_no" property="createNo"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,requisition_no,delivery_date,remark,item_count,status,requisition_amount,take_amount,audit_user,audit_remark,audit_time,
        out_storage_user,out_storage_time,in_storage_user,in_storage_time,cancel_user,cancel_time,
        company_no,company_name,branch_no,branch_name,platform_type,merchant_no,merchant_name,store_no,store_name,
        create_no,create_name,create_time,modify_no,modify_name,modify_time,delete_flag
    </sql>

    <select id="selectRequisitionOrderPage"
            parameterType="cn.htdt.goodsprocess.dto.request.requisitionorder.ReqRequisitionOrderListDTO"
            resultType="cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionOrderListDTO">
        select requisition_no,
        status,
        store_name,
        item_count,
        create_time,
        create_name,
        modify_time,
        modify_name
        from requisition_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="requisitionNo != null and requisitionNo != ''">
                    and requisition_no = #{requisitionNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>

                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>


                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="status != null and status == 10">
                    and status in(10, 12)
                </if>
                <if test="status != null and status != 10">
                    and status = #{status}
                </if>
                <if test="createTimeStart != null">
                    <![CDATA[
                        and date_format(create_time, '%Y-%m-%d')  >= #{createTimeStart}
                    ]]>
                </if>
                <if test="createTimeEnd != null">
                    <![CDATA[
                        and date_format(create_time, '%Y-%m-%d') <= #{createTimeEnd}
                    ]]>
                </if>
            </trim>
        </where>
        order by create_time desc
    </select>

    <select id="getRequisitionOrderDetail"
            parameterType="cn.htdt.goodsprocess.dto.request.requisitionorder.ReqRequisitionOrderDetailDTO"
            resultType="cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionOrderDetailDTO">
        select
        <include refid="Base_Column_List"/>
        from requisition_order
        where requisition_no = #{requisitionNo}
        <if test="storeNo != null and storeNo != ''">
            and store_no = #{storeNo}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            and merchant_no = #{merchantNo}
        </if>
    </select>


    <update id="updateRequisitionOrder" parameterType="cn.htdt.goodsprocess.domain.RequisitionOrderDomain">
        update requisition_order
        set
        <if test="deliveryDate != null">delivery_date=#{deliveryDate},</if>
        <if test="remark != null">remark=#{remark},</if>
        <if test="itemCount != null">item_count=#{itemCount},</if>
        <if test="status != null">status=#{status},</if>
        <if test="requisitionAmount != null">
            requisition_amount = if(isnull(requisition_amount), 0, requisition_amount) + #{requisitionAmount},
        </if>
        <if test="takeAmount != null">take_amount = if(isnull(take_amount), 0, take_amount) + #{takeAmount},</if>
        <if test="modifyName != null">modify_name=#{modifyName},</if>
        <if test="modifyNo != null">modify_no=#{modifyNo},</if>
        <if test="status != null and (status == 12 or status == 13)">audit_user = #{auditUser},</if>
        <if test="status != null and (status == 12 or status == 13)">audit_remark = #{auditRemark},</if>
        <if test="status != null and (status == 12 or status == 13)">audit_time=NOW(),</if>
        <if test="status != null and status == 14">out_storage_user=#{outStorageUser},</if>
        <if test="status != null and status == 14">out_storage_time=NOW(),</if>
        <if test="status != null and status == 15">in_storage_user=#{inStorageUser},</if>
        <if test="status != null and status == 15">in_storage_time=NOW(),</if>
        <if test="status != null and status == 20">cancel_user=#{modifyName},</if>
        <if test="status != null and status == 20">cancel_time=NOW(),</if>
        modify_time = NOW()
        where requisition_no = #{requisitionNo}
        and merchant_no = #{merchantNo}
        <if test="storeNo != null and storeNo != ''">
            and store_no = #{storeNo}
        </if>
    </update>

    <update id="auditRequisitionOrder"
            parameterType="cn.htdt.goodsprocess.dto.request.requisitionorder.ReqAuditRequisitionOrderDTO">
        update requisition_order set status = #{status}, audit_user = #{modifyName}, audit_remark = #{auditRemark},
        audit_time = NOW(), modify_name=#{modifyName}
        where requisition_no in
        <foreach collection="requisitionNos" index="index" item="requisitionNo" open="(" separator="," close=")">
            #{requisitionNo}
        </foreach>
        <if test="merchantNo != null and merchantNo != ''">
            and merchant_no = #{merchantNo}
        </if>
    </update>


</mapper>