<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsImeiChangeLogDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsImeiChangeLogDomain">
        <result column="id" property="id" />
        <result column="create_no" property="createNo" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_no" property="modifyNo" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="goods_no" property="goodsNo" />
        <result column="imei" property="imei" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="goods_name" property="goodsName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="custom_goods_no" property="customGoodsNo" />
        <result column="custom_store_no" property="customStoreNo" />
        <result column="inventory_state" property="inventoryState" />
        <result column="inventory_type" property="inventoryType" />
        <result column="remark" property="remark" />
        <result column="custom_batch_number" property="customBatchNumber" />
        <result column="supplier_code" property="supplierCode" />
        <result column="img_json" property="imgJson"/>
    </resultMap>

    <!--<resultMap id="VoResultMap" extends="BaseResultMap" type="cn.htdt.goodsprocess.vo.GoodsImeiChangeLogVo">

    </resultMap>-->

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_no,
        create_name,
        create_time,
        modify_no,
        modify_name,
        modify_time,
        delete_flag,
        goods_no, imei, merchant_no, merchant_name, goods_name, store_no, store_name, custom_goods_no, custom_store_no, inventory_state, inventory_type, remark, custom_batch_number, supplier_code,img_json
    </sql>

    <select id="selectGoodsImeiChangeLogList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        goods_imei_change_log gl
        <where>
            <if test="goodNoOrName != null and goodNoOrName != ''">
                and (gl.goods_no = #{goodNoOrName,jdbcType=VARCHAR}
                OR
                gl.goods_name like concat('%',concat(#{goodNoOrName},'%'))
                )
            </if>
            <if test="imei != null and imei != ''">
                and gl.imei = #{imei}
            </if>
            <if test="storeName != null and storeName != ''">
                and gl.store_name = #{storeName}
            </if>
            <if test="storeNo != null">
                and gl.store_no = #{storeNo}
            </if>
            <if test="merchantNo != null and merchantNo != ''">
                and gl.merchant_no = #{merchantNo}
            </if>
            <if test="inventoryType != null and inventoryType != ''">
                and gl.inventory_type = #{inventoryType}
            </if>
            <if test="customGoodsNo != null and customGoodsNo != ''">
                and gl.custom_goods_no = #{customGoodsNo}
            </if>

            <if test="startDate != null">
                <![CDATA[
                        and  date_format(gl.create_time, '%Y-%m-%d') >= #{startDate}
                    ]]>
            </if>
            <if test="endDate != null">
                <![CDATA[
                        and date_format(gl.create_time, '%Y-%m-%d') <= #{endDate}
                    ]]>
            </if>
        </where>
        order by gl.id desc
    </select>
</mapper>
