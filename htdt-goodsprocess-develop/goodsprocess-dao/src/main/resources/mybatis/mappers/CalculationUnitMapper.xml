<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.CalculationUnitDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.CalculationUnitDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="calculation_unit_no" property="calculationUnitNo"/>
        <result column="calculation_unit_name" property="calculationUnitName"/>
        <result column="calculation_unit_symbol" property="calculationUnitSymbol"/>
        <result column="data_source_type" property="dataSourceType"/>
        <result column="disable_flag" property="disableFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        calculation_unit_no, calculation_unit_name, calculation_unit_symbol, data_source_type, disable_flag
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectFuzzyByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomCalculationUnitVo">
        select
        <include refid="Base_Column_List"/>
        from
        calculation_unit
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="calculationUnitName != null and calculationUnitName != ''">
                    and calculation_unit_name LIKE CONCAT (CONCAT('%',#{calculationUnitName}),'%')
                </if>
                <if test="calculationUnitNameList != null and calculationUnitNameList.size() > 0">
                    and calculation_unit_name  IN
                    <foreach item="calculationUnitName" collection="calculationUnitNameList" open="(" close=")" separator=",">
                        #{calculationUnitName}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        ORDER BY
        create_time DESC
    </select>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.CalculationUnitDomain">
        select
        <include refid="Base_Column_List"/>
        from
        calculation_unit
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="calculationUnitNo != null and calculationUnitNo != ''">
                    and calculation_unit_no = #{calculationUnitNo}
                </if>
                <if test="calculationUnitName != null and calculationUnitName != ''">
                    and calculation_unit_name = #{calculationUnitName}
                </if>
                <if test="calculationUnitSymbol != null and calculationUnitSymbol != ''">
                    and calculation_unit_symbol = #{calculationUnitSymbol}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <select id="selectByCalculationUnitNoList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        calculation_unit
        <where>
            calculation_unit_no IN
            <foreach item="calculationUnitNo" collection="list" open="(" close=")" separator=",">
                #{calculationUnitNo}
            </foreach>
        </where>
    </select>

    <update id="updateByCalculationUnitNo" parameterType="cn.htdt.goodsprocess.domain.CalculationUnitDomain">
        update
        calculation_unit
        <set>
            <if test="calculationUnitName != null and calculationUnitName != ''">
                calculation_unit_name = #{calculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitSymbol != null and calculationUnitSymbol != ''">
                calculation_unit_symbol = #{calculationUnitSymbol,jdbcType=VARCHAR},
            </if>
            <if test="dataSourceType != null and dataSourceType != ''">
                data_source_type = #{dataSourceType,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            calculation_unit_no = #{calculationUnitNo}
            <include refid="Base_Column_Where"/>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.CalculationUnitDomain">
        update
        calculation_unit
        set
        <if test="modifyNo != null and modifyNo != ''">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null and modifyName != ''">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            calculation_unit_no = #{calculationUnitNo}
        </where>
    </update>

    <update id="batchLogicDelete">
        update
        calculation_unit
        set
        <if test="domain.modifyNo != null and domain.modifyNo != ''">
            modify_no = #{domain.modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="domain.modifyName != null and domain.modifyName != ''">
            modify_name = #{domain.modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            calculation_unit_no IN
            <foreach item="calculationUnitNo" collection="list" open="(" close=")" separator=",">
                #{calculationUnitNo}
            </foreach>
        </where>
    </update>

    <select id="selectCalculationUnitNameByNo" resultMap="BaseResultMap">
        select
            calculation_unit_no, calculation_unit_name
        from
            calculation_unit
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <select id="selectCalculationUnitByUnitNo" resultMap="BaseResultMap">
        select
        calculation_unit_no, calculation_unit_name
        from
        calculation_unit
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="calculationUnitNo != null and calculationUnitNo != ''">
                    and calculation_unit_no = #{calculationUnitNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>
</mapper>
