<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsMediaDao">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsMediaDomain">
		<result column="id" property="id" />
		<result column="create_name" property="createName" />
		<result column="create_time" property="createTime" />
		<result column="modify_name" property="modifyName" />
		<result column="modify_time" property="modifyTime" />
		<result column="delete_flag" property="deleteFlag" />
		<result column="media_no" property="mediaNo" />
		<result column="goods_no" property="goodsNo" />
		<result column="sort_value" property="sortValue" />
		<result column="media_type" property="mediaType" />
		<result column="picture_url" property="pictureUrl" />
		<result column="main_picture_flag" property="mainPictureFlag" />
		<result column="picture_show_area" property="pictureShowArea" />
		<result column="video_url" property="videoUrl" />
		<result column="file_name" property="fileName" />
		<result column="file_size" property="fileSize" />
		<result column="merchant_no" property="merchantNo" />
		<result column="merchant_name" property="merchantName" />
		<result column="store_no" property="storeNo" />
		<result column="store_name" property="storeName" />
		<result column="disable_flag" property="disableFlag" />
		<result column="create_no" property="createNo" />
		<result column="modify_no" property="modifyNo" />
		<result column="company_no" property="companyNo" />
		<result column="company_name" property="companyName" />
		<result column="branch_no" property="branchNo" />
		<result column="branch_name" property="branchName" />
		<result column="ali_video_id" property="aliVideoId" />
		<result column="video_duration" property="videoDuration" />
		<result column="video_audit_status" property="videoAuditStatus" />
		<result column="video_audit_result" property="videoAuditResult" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		id,
		create_name,
		create_time,
		modify_name,
		modify_time,
		delete_flag,
		company_no,
		company_name,
		branch_no,
		branch_name,
		goods_no,
		sort_value, media_type, picture_url, main_picture_flag, picture_show_area, video_url,
		file_name, file_size, merchant_no, merchant_name, store_no,
		store_name, disable_flag, create_no, modify_no,
		ali_video_id,video_duration,video_audit_status,video_audit_result
	</sql>
	
	<sql id="Base_Column_Goods_List">
		m.id,
		m.create_name,
		m.create_time,
		m.modify_name,
		m.modify_time,
		m.delete_flag,
		m.company_no,
		m.company_name,
		m.branch_no,
		m.branch_name,
		m.goods_no,
		m.sort_value, m.media_type, m.picture_url, m.main_picture_flag, m.video_url,
		m.file_name, m.file_size, m.merchant_no, m.merchant_name, m.store_no,
		m.store_name, m.disable_flag, m.create_no, m.modify_no,
		m.ali_video_id,m.video_duration,m.video_audit_status,m.video_audit_result
	</sql>

	<sql id="Base_Column_Where">
		and delete_flag = 1
		<if test="pictureShowArea != null">
			and picture_show_area = #{pictureShowArea}
		</if>
	</sql>

	<select id="selectByParams" resultMap="BaseResultMap"
		parameterType="cn.htdt.goodsprocess.domain.GoodsMediaDomain">
		select
		<include refid="Base_Column_List" />
		from
		goods_media
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="goodsNo != null and goodsNo != ''">
					and goods_no = #{goodsNo}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</select>

	<select id="selectByGoodsNoList" resultMap="BaseResultMap"
		parameterType="cn.htdt.goodsprocess.vo.AtomGoodsMediaVo">
		select
		<include refid="Base_Column_List" />
		from
		goods_media
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				and goods_no in
				<foreach collection="goodsNoList" item="item" index="index"
					open="(" separator="," close=")">
					#{item}
				</foreach>
				<if test="mainPictureFlag != null">
					and main_picture_flag = #{mainPictureFlag}
				</if>
				<if test="mediaType != null">
					and media_type = #{mediaType}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
		order by sort_value
	</select>

	<!-- 根据goodsNo查询商品主图 -->
	<select id="selectMainPictureByGoodsNoList" resultType="java.lang.String">
		  select picture_url from goods_media where delete_flag = 1 and media_type = '1001' and main_picture_flag = 2
		  and goods_no = #{goodsNo}
		  limit 1
	</select>

	<!-- 根据商品编号批量查询商品主图url -->
	<select id="selectMainPictureByGoodsNos" resultMap="BaseResultMap">
		  select <include refid="Base_Column_List" />
		  from goods_media
		  where delete_flag = 1 and media_type = '1001' and main_picture_flag = 2
		  and goods_no in
			<foreach collection="goods" item="item" index="index" open="(" separator="," close=")" >
				#{item}
			</foreach>
	</select>

	<update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.GoodsMediaDomain">
		update
		goods_media
		set
		<if test="modifyNo != null">
			modify_no = #{modifyNo,jdbcType=VARCHAR},
		</if>
		<if test="modifyName != null">
			modify_name = #{modifyName,jdbcType=VARCHAR},
		</if>
		<if test="modifyTime != null">
			modify_time = #{modifyTime,jdbcType=TIMESTAMP},
		</if>
		delete_flag = 2
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="goodsNo != null and goodsNo != ''">
					and goods_no = #{goodsNo}
				</if>
				<if test="mediaType != null">
					and media_type = #{mediaType}
				</if>
				<if test="pictureShowArea != null">
					and picture_show_area = #{pictureShowArea}
				</if>
			</trim>
		</where>
	</update>


	<select id="selectByMediaStatus" resultMap="BaseResultMap"
		parameterType="cn.htdt.goodsprocess.domain.GoodsMediaDomain">
		select
		<include refid="Base_Column_List" />
		from
		goods_media
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="mediaType != null and mediaType != ''">
					and media_type = #{mediaType}
				</if>
				<if test="videoAuditStatus != null and videoAuditStatus != ''">
					and video_audit_status = #{videoAuditStatus}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</select>
	
	<select id="selectByMediaNo" resultMap="BaseResultMap"
		parameterType="cn.htdt.goodsprocess.domain.GoodsMediaDomain">
		select
		<include refid="Base_Column_List" />
		from
		goods_media
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="mediaNo != null and mediaNo != ''">
					and media_no = #{mediaNo}
				</if>
				<if test="mediaType != null">
					and media_type = #{mediaType}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</select>
	
	<select id="selectGoodsByMediaNo" resultMap="BaseResultMap"
		parameterType="cn.htdt.goodsprocess.domain.GoodsMediaDomain">
		select
		<include refid="Base_Column_Goods_List" />
		from
	 	goods g LEFT JOIN goods_media m
			 on m.goods_no = g.goods_no 
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="mediaNo != null and mediaNo != ''">
					and m.media_no = #{mediaNo}
				</if>
				<if test="mediaType != null">
					and m.media_type = #{mediaType}
				</if>
				and m.delete_flag = 1
				and g.delete_flag = 1
			</trim>
		</where>
	</select>
	
	 <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsTagDomain">
        update
        goods_media
        <set>
            <if test="goodsNo != null">
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="aliVideoId != null">
                ali_video_id = #{aliVideoId,jdbcType=VARCHAR},
            </if>
            <if test="videoDuration != null">
                video_duration = #{videoDuration,jdbcType=VARCHAR},
            </if>
            <if test="videoAuditStatus != null">
                video_audit_status = #{videoAuditStatus,jdbcType=VARCHAR},
            </if>
            <if test="videoAuditResult != null">
                video_audit_result = #{videoAuditResult,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="mediaNo != null and mediaNo != ''">
                    and media_no = #{mediaNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

	<delete id="deleteGoodsMediaByParam">
		delete from goods_media
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				and goods_no IN
				<foreach collection="goodsNos" item="item" index="index"
						 open="(" separator="," close=")">
					#{item}
				</foreach>
				<if test="mediaType != null and mediaType != ''">
					and media_type = #{mediaType}
				</if>
				<if test="pictureShowArea != null">
					and picture_show_area = #{pictureShowArea}
				</if>
			</trim>
		</where>
	</delete>

	<select id="selectGoodsImgsByGoodsNos" resultMap="BaseResultMap"
			parameterType="cn.htdt.goodsprocess.vo.AtomGoodsMediaVo">
		select
		<include refid="Base_Column_Goods_List" />
		from goods_media m
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				and m.goods_no in (select g.original_goods_no from goods g where g.goods_no in
				<foreach collection="goodsNoList" item="item" index="index"
						 open="(" separator="," close=")">
					#{item}
				</foreach>
				)
				and m.media_type = '1001'
				and m.picture_show_area = 0
				and m.delete_flag = 1
				and m.disable_flag = 2
			</trim>
		</where>
		order by sort_value, goods_no
	</select>

</mapper>
