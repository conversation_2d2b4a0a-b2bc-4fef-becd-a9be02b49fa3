<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.ImWarehouseDao">

    <!-- 通用查询映射结果 202503 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.ImWarehouseDomain">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="warehouse_no" property="warehouseNo"/>
        <result column="warehouse_code" property="warehouseCode"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="warehouse_type" property="warehouseType"/>
        <result column="self_warehouse_flag" property="selfWarehouseFlag"/>
        <result column="country_code" property="countryCode"/>
        <result column="country_name" property="countryName"/>
        <result column="province_code" property="provinceCode"/>
        <result column="province_name" property="provinceName"/>
        <result column="city_code" property="cityCode"/>
        <result column="city_name" property="cityName"/>
        <result column="district_code" property="districtCode"/>
        <result column="district_name" property="districtName"/>
        <result column="town_code" property="townCode"/>
        <result column="town_name" property="townName"/>
        <result column="address" property="address"/>
        <result column="warehouse_contacter" property="warehouseContacter"/>
        <result column="warehouse_contacter_mobile" property="warehouseContacterMobile"/>
        <result column="virtual_warehouse_flag" property="virtualWarehouseFlag"/>
        <result column="warehouse_remark" property="warehouseRemark"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
        <result column="login_identity" property="loginIdentity"/>
        <result column="ds_address" property="dsAddress"/>
        <result column="ds_warehouse_contacter_mobile" property="dsWarehouseContacterMobile"/>
    </resultMap>
    <resultMap id="VoBaseResultMap" type="cn.htdt.goodsprocess.vo.AtomWarehouseVo" extends="BaseResultMap">
        <result column="stock_num" property="stockNum"/>
        <result column="calculation_unit_no" property="calculationUnitNo"/>
        <result column="goods_no" property="goodsNo"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        login_identity,
        warehouse_no, warehouse_code, warehouse_name, warehouse_type, self_warehouse_flag, country_code, country_name, province_code, province_name, city_code, city_name, district_code, district_name, town_code, town_name, address, warehouse_contacter, warehouse_contacter_mobile, virtual_warehouse_flag, warehouse_remark, merchant_no, merchant_name, store_no, store_name, disable_flag, create_no, modify_no, company_no, company_name, branch_no, branch_name
    </sql>
    <!-- 通用查询结果列-加解密用字段 -->
    <sql id="Ds_Base_Column_List">
        ds_address, ds_warehouse_contacter_mobile
    </sql>

    <!-- 仓库商品库存列 -->
    <sql id="Wg_Base_Column_List">
        r.stock_num,r.goods_no
    </sql>

    <sql id="Base_Column_List_W">
        w
        .
        id
        ,
        w.create_name,
        w.create_time,
        w.modify_name,
        w.modify_time,
        w.delete_flag,
        w.login_identity,
        w.warehouse_no, w.warehouse_code, w.warehouse_name, w.warehouse_type, w.self_warehouse_flag, w.country_code, w.country_name, w.province_code, w.province_name, w.city_code, w.city_name, w.district_code, w.district_name, w.town_code, w.town_name, w.address, w.warehouse_contacter, w.warehouse_contacter_mobile, w.virtual_warehouse_flag, w.warehouse_remark, w.merchant_no, w.merchant_name, w.store_no, w.store_name, w.disable_flag, w.create_no, w.modify_no, w.company_no, w.company_name, w.branch_no, w.branch_name
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <sql id="Base_Column_Where_W">
        and w.delete_flag = 1
    </sql>

    <select id="selectByParams" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseVo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Ds_Base_Column_List"/>
        from
        im_warehouse
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseName  != null and warehouseName  != ''">
                    and warehouse_name LIKE CONCAT(CONCAT('%',#{warehouseName}),'%')
                </if>
                <if test="warehouseContacter  != null and warehouseContacter  != ''">
                    and warehouse_contacter LIKE CONCAT(CONCAT('%',#{warehouseContacter}),'%')
                </if>
                <if test="disableFlag  != null and disableFlag  != ''">
                    and disable_flag = #{disableFlag}
                </if>
                <if test="provinceCode  != null and provinceCode  != ''">
                    and province_code = #{provinceCode}
                </if>
                <if test="cityCode  != null and cityCode  != ''">
                    and city_code = #{cityCode}
                </if>
                <if test="districtCode  != null and districtCode  != ''">
                    and district_code = #{districtCode}
                </if>
                <if test="townCode  != null and townCode  != ''">
                    and town_code = #{townCode}
                </if>
                <if test="createTimeStart != null">
                    <![CDATA[
                        and  date_format(create_time, '%Y-%m-%d %H:%i:%s')  >= DATE_FORMAT(#{createTimeStart} ,'%Y-%m-%d %H:%i:%s')
                     ]]>
                </if>
                <if test="createTimeEnd != null">
                    <![CDATA[
                        and date_format(create_time, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT(#{createTimeEnd},'%Y-%m-%d %H:%i:%s')
                     ]]>
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="warehouseStr  != null and warehouseStr  != ''">
                    and (warehouse_no = #{warehouseStr,jdbcType=VARCHAR} OR warehouse_name
                    LIKE CONCAT(CONCAT('%',#{warehouseStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="virtualWarehouseFlag != null and virtualWarehouseFlag != ''">
                    and virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="loginIdentity  != null">
                    and login_identity = #{loginIdentity}
                </if>
                <if test="noList != null and noList.size() > 0">
                    and warehouse_no NOT IN
                    <foreach collection="noList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        order by modify_time desc
    </select>

    <select id="selectListByParams" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseVo" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Ds_Base_Column_List"/>
        from
        im_warehouse
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="disableFlag  != null and disableFlag  != ''">
                    and disable_flag = #{disableFlag}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="warehouseType != null and warehouseType != ''">
                    and warehouse_type = #{warehouseType}
                </if>
                <if test="virtualWarehouseFlag != null and virtualWarehouseFlag != ''">
                    and virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="noList != null and noList.size() > 0">
                    and warehouse_no IN
                    <foreach collection="noList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
                <if test="loginIdentity  != null">
                    and login_identity = #{loginIdentity}
                </if>
            </trim>
        </where>
        order by create_time desc
    </select>

    <select id="selectCountByParams" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseVo" resultType="java.lang.Integer">
        select
        count(id)
        from
        im_warehouse
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="disableFlag  != null and disableFlag  != ''">
                    and disable_flag = #{disableFlag}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="warehouseType != null and warehouseType != ''">
                    and warehouse_type = #{warehouseType}
                </if>
                <if test="virtualWarehouseFlag != null and virtualWarehouseFlag != ''">
                    and virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="noList != null and noList.size() > 0">
                    and warehouse_no IN
                    <foreach collection="noList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
                <if test="loginIdentity  != null">
                    and login_identity = #{loginIdentity}
                </if>
            </trim>
        </where>
    </select>

    <select id="selectByParamsDetail" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseDomain"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Ds_Base_Column_List"/>
        from
        im_warehouse
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo  != null and warehouseNo  != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <if test="virtualWarehouseFlag  != null">
                    and virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="warehouseType != null and warehouseType != ''">
                    and warehouse_type = #{warehouseType}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="disableFlag  != null and disableFlag  != ''">
                    and disable_flag = #{disableFlag}
                </if>
                <include refid="Base_Column_Where"/>
                <if test="loginIdentity  != null">
                    and login_identity = #{loginIdentity}
                </if>
            </trim>
        </where>
        order by create_time desc
        limit 1
    </select>

    <select id="selectByParamsDetailByTerm" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseVo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_W"/>,
        <include refid="Ds_Base_Column_List"/>
        from
        im_warehouse w left join im_warehouse_goods_relation r on w.warehouse_no=r.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo  != null and warehouseNo  != ''">
                    and w.warehouse_no = #{warehouseNo}
                </if>
                <if test="virtualWarehouseFlag  != null">
                    and w.virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="warehouseType != null and warehouseType != ''">
                    and r.warehouseType = #{warehouseType}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and w.company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and w.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and w.store_no = #{storeNo}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and r.goods_no = #{goodsNo}
                </if>
                <include refid="Base_Column_Where_W"/>
                <if test="loginIdentity  != null">
                    and w.login_identity = #{loginIdentity}
                </if>
            </trim>
        </where>
        order by create_time desc
        limit 1
    </select>

    <select id="selectWarehouseByGoods" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseVo"
            resultMap="VoBaseResultMap">
        select
        <include refid="Base_Column_List_W"/>,
        <include refid="Ds_Base_Column_List"/>,
        <include refid="Wg_Base_Column_List"/>
        from
        im_warehouse_goods_relation r left join im_warehouse w on w.warehouse_no=r.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and r.delete_flag = 1
                <if test="warehouseNo  != null and warehouseNo  != ''">
                    and w.warehouse_no = #{warehouseNo}
                </if>
                <if test="warehouseName  != null and warehouseName  != ''">
                    and w.warehouse_name LIKE CONCAT(CONCAT('%',#{warehouseName}),'%')
                </if>
                <if test="virtualWarehouseFlag  != null">
                    and w.virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="warehouseType != null and warehouseType != ''">
                    and r.warehouseType = #{warehouseType}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and w.company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and w.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and w.store_no = #{storeNo}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and r.goods_no = #{goodsNo}
                </if>
                <if test="disableFlag  != null and disableFlag  != ''">
                    and w.disable_flag = #{disableFlag}
                </if>
                <include refid="Base_Column_Where_W"/>
                <if test="loginIdentity  != null">
                    and w.login_identity = #{loginIdentity}
                </if>
            </trim>
        </where>
        group by w.warehouse_no
        order by w.create_time desc
    </select>

    <select id="selectTradeOrderWarehouseByGoods" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseVo"
            resultMap="VoBaseResultMap">
        select
        <include refid="Base_Column_List_W"/>,
        <include refid="Ds_Base_Column_List"/>,
        <include refid="Wg_Base_Column_List"/>
        from
        im_warehouse_goods_relation r left join im_warehouse w on w.warehouse_no=r.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and r.delete_flag = 1
                <if test="warehouseNo  != null and warehouseNo  != ''">
                    and w.warehouse_no = #{warehouseNo}
                </if>
                <if test="warehouseName  != null and warehouseName  != ''">
                    and w.warehouse_name LIKE CONCAT(CONCAT('%',#{warehouseName}),'%')
                </if>
                <if test="virtualWarehouseFlag  != null">
                    and w.virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="warehouseType != null and warehouseType != ''">
                    and r.warehouseType = #{warehouseType}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and w.company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and w.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and w.store_no = #{storeNo}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and r.goods_no = #{goodsNo}
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and r.goods_no in
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="disableFlag  != null and disableFlag  != ''">
                    and w.disable_flag = #{disableFlag}
                </if>
                <include refid="Base_Column_Where_W"/>
                <if test="loginIdentity  != null">
                    and w.login_identity = #{loginIdentity}
                </if>
            </trim>
        </where>
        order by r.goods_no,r.id
    </select>

    <select id="selectWarehouseExist" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseDomain"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Ds_Base_Column_List"/>
        from
        im_warehouse
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <if test="loginIdentity  != null">
                    and login_identity = #{loginIdentity}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        order by create_time desc
        limit 1
    </select>

    <!--校验仓库名是否存在-->
    <select id="selectWarehouseNameCount" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseDomain"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Ds_Base_Column_List"/>
        from
        im_warehouse
        where
        warehouse_name = #{warehouseName}
        <if test="warehouseNo != null and warehouseNo != ''">
            and warehouse_no != #{warehouseNo}
        </if>
        <if test="merchantNo  != null and merchantNo != ''">
            and merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo  != null and storeNo != ''">
            and store_no = #{storeNo,jdbcType=VARCHAR}
        </if>
        <include refid="Base_Column_Where"/>
    </select>

    <select id="selectMerchantWareHouseInfo" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseDomain" resultMap="BaseResultMap">
        select
            warehouse_no,
            warehouse_name,
            login_identity,
            warehouse_type,
            virtual_warehouse_flag
        from im_warehouse
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo  != null and merchantNo  != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="disableFlag  != null and disableFlag  != ''">
                    and disable_flag = #{disableFlag}
                </if>
                <if test="warehouseType != null and warehouseType != ''">
                    and warehouse_type = #{warehouseType}
                </if>
                <if test="virtualWarehouseFlag != null and virtualWarehouseFlag != ''">
                    and virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="loginIdentity  != null">
                    and login_identity = #{loginIdentity}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseDomain">
        update
        im_warehouse
        <set>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="warehouseCode != null">
                warehouse_code = #{warehouseCode},
            </if>
            <if test="warehouseName != null">
                warehouse_name = #{warehouseName},
            </if>
            <if test="warehouseType != null">
                warehouse_type = #{warehouseType},
            </if>
            <if test="selfWarehouseFlag != null">
                self_warehouse_flag = #{selfWarehouseFlag},
            </if>
            <if test="countryCode != null">
                country_code = #{countryCode},
            </if>
            <if test="countryName != null">
                country_name = #{countryName},
            </if>
            <if test="provinceCode != null">
                province_code = #{provinceCode},
            </if>
            <if test="provinceName != null">
                province_name = #{provinceName},
            </if>
            <if test="cityCode != null">
                city_code = #{cityCode},
            </if>
            <if test="cityName != null">
                city_name = #{cityName},
            </if>
            <if test="districtCode != null">
                district_code = #{districtCode},
            </if>
            <if test="districtName != null">
                district_name = #{districtName},
            </if>
            <if test="townCode != null">
                town_code = #{townCode},
            </if>
            <if test="townName != null">
                town_name = #{townName},
            </if>
            <if test="address != null">
                address = #{address},
            </if>
            <if test="dsAddress != null">
                ds_address = #{dsAddress},
            </if>
            <if test="warehouseContacter != null">
                warehouse_contacter = #{warehouseContacter},
            </if>
            <if test="warehouseContacterMobile != null">
                warehouse_contacter_mobile = #{warehouseContacterMobile},
            </if>
            <if test="dsWarehouseContacterMobile != null">
                ds_warehouse_contacter_mobile = #{dsWarehouseContacterMobile},
            </if>
            <if test="virtualWarehouseFlag != null">
                virtual_warehouse_flag = #{virtualWarehouseFlag},
            </if>
            <if test="warehouseRemark != null">
                warehouse_remark = #{warehouseRemark},
            </if>
            <if test="companyNo != null">
                company_no = #{companyNo},
            </if>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo},
            </if>
            <if test="merchantName != null">
                merchant_name = #{merchantName},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo},
            </if>
            <if test="storeName != null">
                store_name = #{storeName},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag},
            </if>
            <if test="createNo != null">
                create_no = #{createNo},
            </if>
            <if test="createName != null">
                create_name = #{createName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="companyNo != null">
                company_no = #{companyNo},
            </if>
            <if test="companyName != null">
                company_name = #{companyName},
            </if>
            <if test="branchNo != null">
                branch_no = #{branchNo},
            </if>
            <if test="branchName != null">
                branch_name = #{branchName},
            </if>
            <if test="loginIdentity  != null">
                login_identity = #{loginIdentity},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <if test="loginIdentity  != null">
                    and login_identity = #{loginIdentity}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="updateBySelectiveOpen" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseDomain">
        update
        im_warehouse
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime},
        </if>
        disable_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <if test="loginIdentity  != null">
                    and login_identity = #{loginIdentity}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="updateBySelectiveClose" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseDomain">
        update
        im_warehouse
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime},
        </if>
        disable_flag = 1
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <if test="loginIdentity  != null">
                    and login_identity = #{loginIdentity}
                </if>
            </trim>
        </where>
    </update>
</mapper>
