<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsTagRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.vo.AtomGoodsTagRelationVo">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="tag_no" property="tagNo"/>
        <result column="tag_name" property="tagName"/>
        <result column="tag_picture_url" property="tagPictureUrl"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="source_type" property="sourceType"/>
        <result column="store_name" property="storeName"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="tag_type" property="tagType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        r
        .
        id
        ,
        r.create_name,
        r.create_time,
        r.modify_name,
        r.modify_time,
        r.delete_flag,
        r.tag_no, r.goods_no, r.merchant_no, r.merchant_name,
        r.store_no, r.source_type, r.store_name,
        r.disable_flag, r.tag_type,r.create_no, r.modify_no
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsTagRelationVo">
        select
        <include refid="Base_Column_List"/>,g.tag_name, g.tag_content,g.tag_picture_url
        from
        goods_tag_relation r inner join goods_tag g on r.tag_no = g.tag_no and g.delete_flag = 1
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="tagNo != null and tagNo != ''">
                    and r.tag_no = #{tagNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and r.goods_no = #{goodsNo}
                </if>
                <if test="goodsNoList != null and goodsNoList.size() > 0">
                    and goods_no in
                    <foreach collection="goodsNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsList != null and goodsList.size() > 0">
                    and goods_no in
                    <foreach collection="goodsList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="tagType != null and tagType != ''">
                    and r.tag_type = #{tagType}
                </if>
                and r.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="getGoodsTagRelationByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.GoodsTagRelationDomain">
        select
        <include refid="Base_Column_List"/>
        from
        goods_tag_relation r
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="tagNo != null and tagNo != ''">
                    and tag_no = #{tagNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.GoodsTagRelationDomain">
        insert into
        goods_tag_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="tagNo != null">
                tag_no,
            </if>
            <if test="goodsNo != null">
                goods_no,
            </if>
            <if test="merchantNo != null">
                merchant_no,
            </if>
            <if test="merchantName != null">
                merchant_name,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
            <if test="disableFlag != null">
                disable_flag,
            </if>
            <if test="createNo != null">
                create_no,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyNo != null">
                modify_no,
            </if>
            <if test="modifyName != null">
                modify_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="tagType != null">
                tag_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="tagNo != null">
                #{tagNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsNo != null">
                #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null">
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=TINYINT},
            </if>
            <if test="storeName != null">
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="tagType != null">
                #{tagType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsTagRelationDomain">
        update
        goods_tag_relation
        <set>
            <if test="tagNo != null">
                tag_no = #{tagNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsNo != null">
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=TINYINT},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="tagType != null">
                tag_type = #{tagType,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="tagNo != null and tagNo != ''">
                    and tag_no = #{tagNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="tagType != null and tagType != ''">
                    and tag_type = #{tagType}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.GoodsTagRelationDomain">
        update
        goods_tag_relation
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        </if>
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="tagNo != null and tagNo != ''">
                    and tag_no = #{tagNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="tagType != null and tagType != ''">
                    and tag_type = #{tagType}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <delete id="deleteByParams" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsTagRelationVo">
        delete from goods_tag_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNoList != null and goodsNoList.size() > 0">
                    and goods_no IN
                    <foreach collection="goodsNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
    </delete>

</mapper>
