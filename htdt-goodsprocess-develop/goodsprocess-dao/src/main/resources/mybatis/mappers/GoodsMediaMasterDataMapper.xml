<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsMediaMasterDataDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsMediaMasterDataDomain">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="media_no" property="mediaNo"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="sort_value" property="sortValue"/>
        <result column="media_type" property="mediaType"/>
        <result column="picture_url" property="pictureUrl"/>
        <result column="main_picture_flag" property="mainPictureFlag"/>
        <result column="picture_show_area" property="pictureShowArea"/>
        <result column="video_url" property="videoUrl"/>
        <result column="file_name" property="fileName"/>
        <result column="file_size" property="fileSize"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="video_duration" property="videoDuration"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
		id,
		create_name,
		create_time,
		modify_name,
		modify_time,
		delete_flag,
		goods_no,
		sort_value, media_type, picture_url, main_picture_flag, picture_show_area, video_url,
		file_name, file_size, disable_flag, create_no, modify_no,
		video_duration
	</sql>

    <sql id="Base_Column_Where">
		and delete_flag = 1
	</sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsMediaMasterDataVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods_media_master_data
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and goods_no in
                <foreach collection="goodsNoList" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
                <if test="mainPictureFlag != null">
                    and main_picture_flag = #{mainPictureFlag}
                </if>
                <if test="mediaType != null">
                    and media_type = #{mediaType}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <delete id="deleteByParams" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsMediaMasterDataVo">
        delete from goods_media_master_data
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="goodsNoList != null and goodsNoList.size > 0">
                    and goods_no in
                    <foreach collection="goodsNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="mediaType != null and mediaType != ''">
                    and media_type = #{mediaType}
                </if>
                <if test="pictureShowArea != null">
                    and picture_show_area = #{pictureShowArea}
                </if>
            </trim>
        </where>
    </delete>

</mapper>
