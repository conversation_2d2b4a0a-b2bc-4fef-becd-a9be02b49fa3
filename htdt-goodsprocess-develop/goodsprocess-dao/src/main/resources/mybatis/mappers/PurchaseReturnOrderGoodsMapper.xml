<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htdt.goodsprocess.dao.PurchaseReturnOrderGoodsDao">
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="return_goods_code" property="returnGoodsCode" jdbcType="VARCHAR"/>
        <result column="return_code" property="returnCode" jdbcType="VARCHAR"/>
        <result column="purchase_code" property="purchaseCode" jdbcType="VARCHAR"/>
        <result column="purchase_goods_code" property="purchaseGoodsCode" jdbcType="VARCHAR"/>
        <result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
        <result column="expect_receive_date" property="expectReceiveDate" jdbcType="TIMESTAMP"/>
        <result column="actual_receive_date" property="actualReceiveDate" jdbcType="TIMESTAMP"/>
        <result column="goods_no" property="goodsNo" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="purchase_unit" property="purchaseUnit" jdbcType="VARCHAR"/>
        <result column="category_no" property="categoryNo" jdbcType="VARCHAR"/>
        <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
        <result column="purchase_unit_price" property="purchaseUnitPrice" jdbcType="DECIMAL"/>
        <result column="purchase_num" property="purchaseNum" jdbcType="DECIMAL"/>
        <result column="returning_count" property="returningCount" jdbcType="DECIMAL"/>
        <result column="returned_count" property="returnedCount" jdbcType="DECIMAL"/>
        <result column="return_request_count" property="returnRequestCount" jdbcType="DECIMAL"/>
        <result column="storage_count" property="storageCount" jdbcType="DECIMAL"/>
        <result column="purchase_price" property="purchasePrice" jdbcType="DECIMAL"/>
        <result column="warehouse_type" property="warehouseType" jdbcType="TINYINT"/>
        <result column="warehouse_no" property="warehouseNo" jdbcType="VARCHAR"/>
        <result column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="order_status" property="orderStatus" jdbcType="VARCHAR"/>
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="merchant_name" property="merchantName" jdbcType="VARCHAR"/>
        <result column="store_no" property="storeNo" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="disable_flag" property="disableFlag" jdbcType="TINYINT"/>
        <result column="create_no" property="createNo" jdbcType="VARCHAR"/>
        <result column="create_name" property="createName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_no" property="modifyNo" jdbcType="VARCHAR"/>
        <result column="modify_name" property="modifyName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="company_no" property="companyNo" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="branch_no" property="branchNo" jdbcType="VARCHAR"/>
        <result column="branch_name" property="branchName" jdbcType="VARCHAR"/>
        <result column="platform_type" property="platformType" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="VoBaseResultMap" type="cn.htdt.goodsprocess.vo.AtomResModifyReturnGoodsVo" extends="BaseResultMap">
        <result column="first_attribute_value_name" property="firstAttributeValueName"/>
        <result column="second_attribute_value_name" property="secondAttributeValueName"/>
        <result column="third_attribute_value_name" property="thirdAttributeValueName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, return_goods_code, return_code, purchase_code, purchase_goods_code, source_type,
        goods_no, goods_name, purchase_unit,purchase_unit_price, purchase_num, returning_count, returned_count,
        return_request_count, storage_count, purchase_price, warehouse_type, warehouse_no,
        warehouse_name, remark, order_status, merchant_no, merchant_name, store_no, store_name,
        disable_flag, create_no, create_name, create_time, modify_no, modify_name, modify_time,
        delete_flag, company_no, company_name, branch_no, branch_name, platform_type, raw_goods_no, raw_goods_num
  </sql>

    <sql id="PROG_Base_Column_List">
    prog.id, prog.return_goods_code, prog.return_code, prog.purchase_code, prog.purchase_goods_code, prog.source_type,
    prog.goods_no, prog.goods_name, prog.purchase_unit, prog.purchase_unit_price, prog.purchase_num, prog.returning_count, prog.returned_count,
    prog.return_request_count, prog.storage_count, prog.purchase_price, prog.warehouse_type, prog.warehouse_no,
    prog.warehouse_name, prog.remark, prog.order_status, prog.merchant_no, prog.merchant_name, prog.store_no, prog.store_name,
    prog.disable_flag, prog.create_no, prog.create_name, prog.create_time, prog.modify_no, prog.modify_name, prog.modify_time,
    prog.delete_flag, prog.company_no, prog.company_name, prog.branch_no, prog.branch_name, prog.platform_type
  </sql>


    <resultMap id="WaitingResultMap" type="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo">
        <result column="goods_no" property="goodsNo" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="warehouse_type" property="warehouseType" jdbcType="TINYINT"/>
        <result column="purchase_unit" property="purchaseUnit" jdbcType="VARCHAR"/>
        <result column="purchase_unit_price" property="purchaseUnitPrice" jdbcType="DECIMAL"/>
        <result column="purchase_num" property="purchaseNum" jdbcType="DECIMAL"/>
        <result column="returned_count" property="returnedCount" jdbcType="DECIMAL"/>
        <result column="return_request_count" property="returnRequestCount" jdbcType="DECIMAL"/>
        <result column="warehouse_no" property="warehouseNo" jdbcType="VARCHAR"/>
        <result column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
        <result column="conversion_rate" property="conversionRate" jdbcType="TINYINT"/>
        <result column="calculation_unit_no" property="calculationUnitNo" />
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="warehouse_Out_Count" property="warehouseOutCount" jdbcType="TINYINT"/>
        <result column="company_no" property="companyNo" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="branch_no" property="branchNo" jdbcType="VARCHAR"/>
        <result column="branch_name" property="branchName" jdbcType="VARCHAR"/>
        <result column="store_no" property="storeNo" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="platform_type" property="platformType" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Waiting_Warehouse_Column_List">
        p.goods_name,p.warehouse_type,p.goods_no,p.purchase_unit,p.purchase_unit_price,p.return_code,p.return_goods_code,
        p.purchase_num,p.returned_count,p.returning_count, p.return_request_count,p.warehouse_no, p.warehouse_name,
        p.platform_type,p.merchant_no, p.merchant_name, p.store_no, p.store_name, p.company_no, p.company_name,
        pog.calculation_unit_no,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_calculation_unit_no AS assistCalculationUnitNo,
        pog.assist_unit_num AS assistUnitNum,
        iwg.stock_num as realStockNum,
        g.conversion_rate,g.delete_flag,
        g.first_attribute_value_name AS firstAttributeValueName,g.second_attribute_value_name AS secondAttributeValueName,g.third_attribute_value_name AS thirdAttributeValueName,
        g.warehouse_flag as goodsWarehouseFlag,
        IFNULL((select sum(d.stock_num) from im_warehouse_stock_flow_record d where d.goods_no = p.raw_goods_no and d.oper_type='1002' and d.bill_type='1006' and p.return_goods_code = d.sub_bill_code), 0) as warehouse_Out_Count
  </sql>

  <sql id="Modify_Goods_List">
    prog.id, prog.return_goods_code, prog.return_code, prog.purchase_code, prog.purchase_goods_code, prog.source_type, prog.goods_no, prog.goods_name,
    prog.purchase_unit, prog.purchase_unit_price, prog.purchase_num, prog.returning_count, prog.returned_count,
    prog.return_request_count, prog.storage_count, prog.purchase_price, prog.warehouse_type, prog.warehouse_no,
    prog.warehouse_name, prog.remark, prog.order_status, prog.merchant_no, prog.merchant_name, prog.store_no, prog.store_name,
    prog.disable_flag, prog.create_no, prog.create_name, prog.create_time, prog.modify_no, prog.modify_name, prog.modify_time,
    prog.delete_flag, prog.company_no, prog.company_name, prog.branch_no, prog.branch_name,prog.platform_type,gs.warehouse_flag as goodsWarehouseFlag,gs.delete_flag AS goodsDeleteFlag,
    pog.calculation_unit_no AS calculationUnitNo,pog.main_unit_num AS mainUnitNum,pog.assist_calculation_unit_no AS assistCalculationUnitNo,pog.assist_unit_num AS assistUnitNum,
    iwg.stock_num AS realStockNum,
    gs.first_attribute_value_name,
    gs.second_attribute_value_name,
    gs.third_attribute_value_name
  </sql>

    <sql id="Return_Goods_List">
        sf.goods_no AS goodsNo,
        sf.goods_name AS goodsName,
        pog.purchase_goods_code as purchaseGoodsCode,
        pog.purchase_unit AS purchaseUnit,
        pog.purchase_num AS purchaseNum,
        pog.purchase_price AS purchasePrice,
        pog.purchase_unit_price AS purchaseUnitPrice,
        pog.calculation_unit_no AS calculationUnitNo,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_calculation_unit_no AS assistCalculationUnitNo,
        pog.assist_unit_num AS assistUnitNum,
        sf.warehouse_flag AS warehouseType,
        sf.warehouse_no AS warehouseNo,
        sf.warehouse_name AS warehouseName,
        gs.delete_flag AS goodsDeleteFlag,
        gs.first_attribute_value_name AS firstAttributeValueName,
        gs.second_attribute_value_name AS secondAttributeValueName,
        gs.third_attribute_value_name AS thirdAttributeValueName,
        gs.warehouse_flag AS goodsWarehouseFlag,
        SUM(sf.stock_num) AS storageCount
    </sql>
    <!--状态标识-->
    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long">
        select
        <include refid="Base_Column_List"/>
        from purchase_return_order_goods
        where id = #{id,jdbcType=BIGINT}
    </select>


    <!--查询待出库采购退货商品信息-->
    <select id="getWaitingWarehouseOutPurchaseReturnOrderGoods" resultMap="WaitingResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo">
        select
        <include refid="Waiting_Warehouse_Column_List"/>
        from purchase_return_order_goods p
        LEFT JOIN im_warehouse_goods_relation iwg on iwg.warehouse_no = p.warehouse_no and iwg.goods_no = p.raw_goods_no and iwg.delete_flag = 1
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = p.purchase_goods_code
        LEFT JOIN goods g ON p.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and p.return_code= #{returnCode,jdbcType=VARCHAR} and p.delete_flag = 1 and p.order_status not in ('1003')
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and p.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="storeName != null and storeName != ''">
                    and p.store_name = #{storeName}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and p.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantName != null and merchantName != ''">
                    and p.merchant_name = #{merchantName}
                </if>
            </trim>
        </where>
    </select>

    <!--查询待出库采购退货商品组信息-->
    <select id="getWaitingWarehouseOutPurchaseReturnOrderGoodsGroups" resultMap="WaitingResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo">
        select
        p.goods_name,p.warehouse_type,p.goods_no,p.purchase_unit,p.purchase_unit_price,p.return_code,p.return_goods_code,
        p.purchase_num,p.returned_count,p.returning_count, p.return_request_count,p.warehouse_no, p.warehouse_name,
        p.platform_type,p.merchant_no, p.merchant_name, p.store_no, p.store_name, p.company_no, p.company_name,
        pog.calculation_unit_no,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_calculation_unit_no AS assistCalculationUnitNo,
        pog.assist_unit_num AS assistUnitNum,
        iwg.stock_num as realStockNum,
        g.delete_flag,
        g.warehouse_flag as goodsWarehouseFlag,
        IFNULL((select sum(d.stock_num) from im_warehouse_stock_flow_record d where d.goods_no = p.raw_goods_no and d.oper_type='1002' and d.bill_type='1006' and p.return_goods_code = d.sub_bill_code), 0) as warehouse_Out_Count
        from purchase_return_order_goods p
        LEFT JOIN im_warehouse_goods_relation iwg on iwg.warehouse_no = p.warehouse_no and iwg.goods_no = p.raw_goods_no and iwg.delete_flag = 1
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = p.purchase_goods_code
        LEFT JOIN goods_groups g ON p.goods_no = g.goods_groups_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and p.return_code= #{returnCode,jdbcType=VARCHAR} and p.delete_flag = 1 and p.order_status not in ('1003')
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and p.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="storeName != null and storeName != ''">
                    and p.store_name = #{storeName}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and p.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantName != null and merchantName != ''">
                    and p.merchant_name = #{merchantName}
                </if>
            </trim>
        </where>
    </select>

    <!--查询待出库采购退货商品信息-->
    <select id="getBoolWaitingWarehouseOutPurchaseReturnOrderGoods" resultMap="WaitingResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo">
        select
        <include refid="Waiting_Warehouse_Column_List"/>
        from purchase_return_order_goods p
        LEFT JOIN im_warehouse_goods_relation iwg on iwg.warehouse_no = p.warehouse_no and iwg.goods_no = p.goods_no and iwg.delete_flag = 1
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = p.purchase_goods_code
        LEFT JOIN goods g ON p.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and p.return_code= #{returnCode,jdbcType=VARCHAR} and p.delete_flag = 1
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and p.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="storeName != null and storeName != ''">
                    and p.store_name = #{storeName}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and p.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantName != null and merchantName != ''">
                    and p.merchant_name = #{merchantName}
                </if>
            </trim>
        </where>
    </select>


    <select id="getCompletPurchaseReturnOrderGoodsByReturnCode" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo">
        select
        prog.goods_no,prog.goods_name,
        prog.warehouse_no, prog.warehouse_name,
        gs.first_attribute_value_name as firstAttributeValueName,
        gs.second_attribute_value_name as secondAttributeValueName,
        gs.third_attribute_value_name as thirdAttributeValueName,
        gs.warehouse_flag as goodsWarehouseFlag,
        pog.purchase_unit,
        pog.calculation_unit_no,
        pog.main_unit_num,
        pog.assist_calculation_unit_no,
        pog.assist_unit_num,
        pog.purchase_num,
        sum(prog.return_request_count) as returnRequestCount,
        sum(prog.returned_count) as returnedCount,
        (select max(create_time) from im_warehouse_stock_flow_record where sub_bill_code = prog.return_goods_code) as lastStockTime,
        prog.warehouse_type,
        prog.purchase_unit_price
        FROM purchase_return_order_goods prog
        LEFT JOIN goods gs ON gs.goods_no = prog.goods_no
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = prog.purchase_goods_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                AND prog.return_code =#{returnCode,jdbcType=VARCHAR}
                AND prog.delete_flag = 1
                AND prog.order_status in ('1002', '1003')
                AND prog.returned_count > 0
            </trim>
        </where>
        group by prog.goods_no, prog.warehouse_no
        order by lastStockTime desc
    </select>

    <select id="getCompletPurchaseReturnOrderGoodsGroupsByReturnCode" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo">
        select
        prog.goods_no,prog.goods_name,
        prog.warehouse_no, prog.warehouse_name,
        gs.warehouse_flag as goodsWarehouseFlag,
        pog.purchase_unit,
        pog.calculation_unit_no,
        pog.main_unit_num,
        pog.assist_calculation_unit_no,
        pog.assist_unit_num,
        pog.purchase_num,
        sum(prog.return_request_count) as returnRequestCount,
        sum(prog.returned_count) as returnedCount,
        (select max(create_time) from im_warehouse_stock_flow_record where sub_bill_code = prog.return_goods_code) as lastStockTime,
        prog.warehouse_type,
        prog.purchase_unit_price
        FROM purchase_return_order_goods prog
        LEFT JOIN goods_groups gs ON gs.goods_groups_no = prog.goods_no
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = prog.purchase_goods_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                AND prog.return_code =#{returnCode,jdbcType=VARCHAR}
                AND prog.delete_flag = 1
                AND prog.order_status in ('1002', '1003')
                AND prog.returned_count > 0
            </trim>
        </where>
        group by prog.goods_no, prog.warehouse_no
        order by lastStockTime desc
    </select>

    <!--根据退货单编码查询退货单商品信息-->
    <select id="selectPurchaseReturnOrderGoodsByReturnCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from purchase_return_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and return_code= #{returnCode,jdbcType=VARCHAR} and order_status !='1003'
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <!--根据退货单编码查询退货单商品信息-->
    <select id="selectAllPurchaseReturnOrderGoodsByReturnCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from purchase_return_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and return_code= #{returnCode,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <!--根据退货单编码查询退货单商品信息,按商品聚合-->
    <select id="selectAllPurchaseReturnOrderGoodsByReturnCodes" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo" parameterType="java.util.List">
        select
            prog.return_code as returnCode,
            prog.purchase_code as purchaseCode,
            prog.purchase_goods_code as purchaseGoodsCode,
            prog.goods_no as goodsNo,
            prog.goods_name as goodsName,
            prog.purchase_unit as purchaseUnit,
            prog.purchase_unit_price as purchaseUnitPrice,
            pog.calculation_unit_no as calculationUnitNo,
            pog.assist_calculation_unit_no as assistCalculationUnitNo,
            pog.main_unit_num as mainUnitNum,
            pog.assist_unit_num as assistUnitNum,
            sum(prog.purchase_num) as purchaseNum,
            sum(prog.returning_count) as returningCount,
            sum(prog.returned_count) as returnedCount,
            sum(prog.return_request_count) as returnRequestCount,
            sum(prog.storage_count) as storageCount,
            sum(prog.purchase_price) as purchasePrice,
            prog.warehouse_type as warehouseType,
            gs.first_attribute_value_name as firstAttributeValueName,
            gs.second_attribute_value_name as secondAttributeValueName,
            gs.third_attribute_value_name as thirdAttributeValueName
        from purchase_return_order_goods prog
        inner join goods gs on gs.goods_no = prog.goods_no
        left join purchase_order_goods pog on prog.purchase_code = pog.purchase_code and prog.purchase_goods_code = pog.purchase_goods_code and prog .goods_no =pog.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCodeList != null and returnCodeList.size() > 0">
                    and prog.return_code in
                    <foreach collection="returnCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and prog.delete_flag = 1
            </trim>
        </where>
        group by prog.return_code,prog.goods_no
    </select>

    <!--根据退货单编码查询退货单商品信息,按商品组聚合-->
    <select id="selectAllPurchaseReturnOrderGoodsGroupsByReturnCodes" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo" parameterType="java.util.List">
        select
        prog.return_code as returnCode,
        prog.purchase_code as purchaseCode,
        prog.purchase_goods_code as purchaseGoodsCode,
        prog.goods_no as goodsNo,
        prog.goods_name as goodsName,
        prog.purchase_unit as purchaseUnit,
        prog.purchase_unit_price as purchaseUnitPrice,
        pog.calculation_unit_no as calculationUnitNo,
        pog.assist_calculation_unit_no as assistCalculationUnitNo,
        pog.main_unit_num as mainUnitNum,
        pog.assist_unit_num as assistUnitNum,
        sum(prog.purchase_num) as purchaseNum,
        sum(prog.returning_count) as returningCount,
        sum(prog.returned_count) as returnedCount,
        sum(prog.return_request_count) as returnRequestCount,
        sum(prog.storage_count) as storageCount,
        sum(prog.purchase_price) as purchasePrice,
        prog.warehouse_type as warehouseType
        from purchase_return_order_goods prog
        inner join goods_groups ggs on ggs.goods_groups_no = prog.goods_no
        left join purchase_order_goods pog on prog.purchase_code = pog.purchase_code and prog.purchase_goods_code = pog.purchase_goods_code and prog .goods_no =pog.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCodeList != null and returnCodeList.size() > 0">
                    and prog.return_code in
                    <foreach collection="returnCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and prog.delete_flag = 1
            </trim>
        </where>
        group by prog.return_code,prog.goods_no
    </select>

    <!--批量更新采购退货单商品采购数量以及状态-->
    <update id="updatePurchaseReturnOrderGoodsStockOrStatus" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open=""
                 close="" separator=";">
            update
            purchase_return_order_goods
            <set>
                <if test="item.returnedCount != null">
                    returned_count = returned_count + #{item.returnedCount,jdbcType=DECIMAL},
                </if>
                <if test="item.returningCount != null">
                    returning_count = #{item.returningCount,jdbcType=DECIMAL},
                </if>
<!--                <if test="item.purchasePrice != null">-->
<!--                    purchase_price = purchase_price + #{item.purchasePrice,jdbcType=DECIMAL},-->
<!--                </if>-->
                <if test="item.modifyNo != null">
                    modify_no = #{item.modifyNo,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyName != null">
                    modify_name = #{item.modifyName,jdbcType=VARCHAR},
                </if>
                <if test="item.orderStatus != null">
                    order_status = #{item.orderStatus,jdbcType=VARCHAR},
                </if>
            </set>
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.returnGoodsCode != null and item.returnGoodsCode != ''">
                        and return_goods_code = #{item.returnGoodsCode,jdbcType=VARCHAR}
                    </if>
                </trim>
            </where>
        </foreach>
    </update>



    <!--根据采购单编码和商品编码查询是否存在退货订单-->
    <select id="selectReturnCountByPurchaseCodeOrGoodsNo" resultType="java.lang.Integer" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        select
        count(1)
        from purchase_return_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    and purchase_code= #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <if test="purchaseGoodsCode != null and purchaseGoodsCode != ''">
                    and purchase_goods_code= #{purchaseGoodsCode,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <!--根据采购单编码查询 所有退货单的同种商品的已出库 和 待出库数量  新增和修改通用-->
    <select id="selectReturnGoodsStorageCount" resultType="cn.htdt.goodsprocess.vo.AtomReqReturnStorageGoodsVo"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        SELECT
        prog.goods_no as goodsNo,
        prog.raw_goods_no as rawGoodsNo,
        prog.warehouse_no  as warehouseNo,
        SUM(CASE WHEN prog.order_status = '1003' THEN 0 ELSE prog.returning_count END) AS returningCount,
        SUM(prog.returned_count) AS returnedCount
        FROM
        purchase_return_order pro
        INNER JOIN purchase_return_order_goods prog ON pro.return_code = prog.return_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    AND pro.purchase_code= #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <if test="returnCode != null and returnCode != ''">
                    AND pro.return_code != #{returnCode,jdbcType=VARCHAR}
                </if>
                AND prog.delete_flag = '1'
                AND prog.source_type='1001'
                AND pro.delete_flag = '1'
            </trim>
        </where>
        GROUP BY
        prog.goods_no,prog.warehouse_no
    </select>

    <!--根据采购单编码查询 获取本退货单的同种商品的已出库数量  新增和修改通用-->
    <select id="selectReturnedGoodsStorageCount" resultType="cn.htdt.goodsprocess.vo.AtomReqReturnStorageGoodsVo"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        SELECT
        prog.goods_no as goodsNo,
        IFNULL(SUM(prog.returned_count), 0) AS returnedCount
        FROM
        purchase_return_order pro
        INNER JOIN purchase_return_order_goods prog ON pro.return_code = prog.return_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    AND pro.purchase_code= #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <if test="returnCode != null and returnCode != ''">
                    AND pro.return_code = #{returnCode,jdbcType=VARCHAR}
                </if>
                AND prog.delete_flag = '1'
                AND prog.source_type='1001'
                AND pro.delete_flag = '1'
            </trim>
        </where>
        GROUP BY
        prog.goods_no,prog.warehouse_no
    </select>

    <!--根据采购退货单编码查询 所有退货单下的商品 可退商品-->
    <select id="selectPurchaseReturnGoodsByPurchaseCode" resultType="cn.htdt.goodsprocess.vo.AtomAddReturnGoodsVo"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        SELECT
        <include refid="Return_Goods_List"/>
        ,iwg.stock_num as realStockNum
        FROM
            im_warehouse_stock_flow_record sf
        LEFT JOIN goods gs ON sf.goods_no = gs.goods_no
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = sf.sub_bill_code
        LEFT JOIN im_warehouse_goods_relation iwg on iwg.warehouse_no = sf.warehouse_no and iwg.goods_no = sf.goods_no and iwg.delete_flag = 1
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    AND sf.bill_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                 AND sf.bill_type = '1005'
                AND sf.delete_flag = '1'
                AND sf.warehouse_flag = gs.warehouse_flag
            </trim>
        </where>
        GROUP BY
        sf.goods_no,
        sf.goods_name,
        pog.purchase_goods_code,
        pog.purchase_unit,
        pog.purchase_num,
        pog.purchase_unit_price,
        sf.warehouse_flag,
        sf.warehouse_no,
        sf.warehouse_name,
        gs.first_attribute_value_name,
        gs.second_attribute_value_name,
        gs.third_attribute_value_name,
        gs.delete_flag
    </select>

    <!--根据采购退货单编码查询 所有退货单下的商品 可退商品-->
    <select id="selectPurchaseReturnGoodsGroupsByPurchaseCode" resultType="cn.htdt.goodsprocess.vo.AtomAddReturnGoodsVo"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        SELECT
        sf.goods_no AS goodsNo,
        sf.goods_name AS goodsName,
        pog.purchase_goods_code as purchaseGoodsCode,
        pog.purchase_unit AS purchaseUnit,
        pog.purchase_num AS purchaseNum,
        pog.purchase_price AS purchasePrice,
        pog.purchase_unit_price AS purchaseUnitPrice,
        pog.calculation_unit_no AS calculationUnitNo,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_calculation_unit_no AS assistCalculationUnitNo,
        pog.assist_unit_num AS assistUnitNum,
        sf.warehouse_flag AS warehouseType,
        sf.warehouse_no AS warehouseNo,
        sf.warehouse_name AS warehouseName,
        gs.delete_flag AS goodsDeleteFlag,
        gs.warehouse_flag AS goodsWarehouseFlag,
        SUM(sf.stock_num) AS storageCount,
        iwg.stock_num as realStockNum
        FROM
        im_warehouse_stock_flow_record sf
        LEFT JOIN goods_groups gs ON sf.goods_no = gs.goods_groups_no
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = sf.sub_bill_code
        LEFT JOIN im_warehouse_goods_relation iwg on iwg.warehouse_no = sf.warehouse_no and iwg.goods_no = sf.goods_no and iwg.delete_flag = 1
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    AND sf.bill_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                AND sf.bill_type = '1005'
                AND sf.delete_flag = '1'
                AND sf.warehouse_flag = gs.warehouse_flag
            </trim>
        </where>
        GROUP BY
        sf.goods_no,
        sf.goods_name,
        pog.purchase_goods_code,
        pog.purchase_unit,
        pog.purchase_num,
        pog.purchase_unit_price,
        sf.warehouse_flag,
        sf.warehouse_no,
        sf.warehouse_name,
        gs.delete_flag
    </select>

    <!--根据采购退货单编码查询 所有退货单不可以退的商品-->
    <select id="selectPurchaseNotReturnGoodsByPurchaseCode" resultType="cn.htdt.goodsprocess.vo.AtomAddReturnGoodsVo"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        SELECT
        <include refid="Return_Goods_List"/>
        FROM
        im_warehouse_stock_flow_record sf
        LEFT JOIN goods gs ON sf.goods_no = gs.goods_no
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = sf.sub_bill_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    AND sf.bill_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                AND sf.bill_type = '1005'
                AND sf.delete_flag = '1'
                AND sf.warehouse_flag != gs.warehouse_flag
                AND gs.warehouse_flag = '2'
            </trim>
        </where>
        GROUP BY
        sf.goods_no,
        sf.goods_name,
        pog.purchase_goods_code,
        pog.purchase_unit,
        pog.purchase_num,
        pog.purchase_unit_price,
        sf.warehouse_flag,
        sf.warehouse_no,
        sf.warehouse_name,
        gs.first_attribute_value_name,
        gs.second_attribute_value_name,
        gs.third_attribute_value_name,
        gs.delete_flag
    </select>

    <!--根据采购退货单编码查询 所有退货单不可以退的商品-->
    <select id="selectPurchaseNotReturnGoodsGroupsByPurchaseCode" resultType="cn.htdt.goodsprocess.vo.AtomAddReturnGoodsVo"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        SELECT
        sf.goods_no AS goodsNo,
        sf.goods_name AS goodsName,
        pog.purchase_goods_code as purchaseGoodsCode,
        pog.purchase_unit AS purchaseUnit,
        pog.purchase_num AS purchaseNum,
        pog.purchase_price AS purchasePrice,
        pog.purchase_unit_price AS purchaseUnitPrice,
        pog.calculation_unit_no AS calculationUnitNo,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_calculation_unit_no AS assistCalculationUnitNo,
        pog.assist_unit_num AS assistUnitNum,
        sf.warehouse_flag AS warehouseType,
        sf.warehouse_no AS warehouseNo,
        sf.warehouse_name AS warehouseName,
        gs.delete_flag AS goodsDeleteFlag,
        gs.warehouse_flag AS goodsWarehouseFlag,
        SUM(sf.stock_num) AS storageCount
        FROM
        im_warehouse_stock_flow_record sf
        LEFT JOIN goods_groups gs ON sf.goods_no = gs.goods_groups_no
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = sf.sub_bill_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    AND sf.bill_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                AND sf.bill_type = '1005'
                AND sf.delete_flag = '1'
                AND sf.warehouse_flag != gs.warehouse_flag
                AND gs.warehouse_flag = '2'
            </trim>
        </where>
        GROUP BY
        sf.goods_no,
        sf.goods_name,
        pog.purchase_goods_code,
        pog.purchase_unit,
        pog.purchase_num,
        pog.purchase_unit_price,
        sf.warehouse_flag,
        sf.warehouse_no,
        sf.warehouse_name,
        gs.delete_flag
    </select>


    <!--退货单下 修改页面可退商品-->
    <select id="selectModifyPurchaseReturnGoodsByReturnCode" resultType="cn.htdt.goodsprocess.vo.AtomResModifyReturnGoodsVo"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
       SELECT
        <include refid="Modify_Goods_List"/>
        FROM
            purchase_return_order_goods prog
        LEFT JOIN goods gs ON prog.goods_no = gs.goods_no
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = prog.purchase_goods_code
        LEFT JOIN im_warehouse_goods_relation iwg on iwg.warehouse_no = prog.warehouse_no
        and (iwg.goods_no = prog.goods_no OR iwg.goods_no = prog.raw_goods_no) and iwg.delete_flag = 1
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    AND prog.return_code = #{returnCode,jdbcType=VARCHAR}
                </if>

                AND prog.source_type = '1001'
                AND prog.delete_flag = '1'
            </trim>
        </where>
    </select>
    <!--退货单下 修改页面可退商品-->
    <select id="selectModifyPurchaseReturnGoodsGroupsByReturnCode" resultType="cn.htdt.goodsprocess.vo.AtomResModifyReturnGoodsVo"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        SELECT
        prog.id, prog.return_goods_code, prog.return_code, prog.purchase_code, prog.purchase_goods_code, prog.source_type, prog.goods_no, prog.goods_name,
        prog.purchase_unit, prog.purchase_unit_price, prog.purchase_num, prog.returning_count, prog.returned_count,
        prog.return_request_count, prog.storage_count, prog.purchase_price, prog.warehouse_type, prog.warehouse_no,
        prog.warehouse_name, prog.remark, prog.order_status, prog.merchant_no, prog.merchant_name, prog.store_no, prog.store_name,
        prog.disable_flag, prog.create_no, prog.create_name, prog.create_time, prog.modify_no, prog.modify_name, prog.modify_time,
        prog.delete_flag, prog.company_no, prog.company_name, prog.branch_no, prog.branch_name,prog.platform_type,
        gs.warehouse_flag as goodsWarehouseFlag,gs.delete_flag AS goodsDeleteFlag,
        pog.calculation_unit_no AS calculationUnitNo,pog.main_unit_num AS mainUnitNum,pog.assist_calculation_unit_no AS assistCalculationUnitNo,pog.assist_unit_num AS assistUnitNum,
        iwg.stock_num AS realStockNum
        FROM
        purchase_return_order_goods prog
        LEFT JOIN goods_groups gs ON prog.goods_no = gs.goods_groups_no
        LEFT JOIN purchase_order_goods pog ON pog.purchase_goods_code = prog.purchase_goods_code
        LEFT JOIN im_warehouse_goods_relation iwg on iwg.warehouse_no = prog.warehouse_no
        and (iwg.goods_no = prog.goods_no OR iwg.goods_no = prog.raw_goods_no) and iwg.delete_flag = 1
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    AND prog.return_code = #{returnCode,jdbcType=VARCHAR}
                </if>

                AND prog.source_type = '1001'
                AND prog.delete_flag = '1'
            </trim>
        </where>
    </select>
    <!--退货单下 修改页面不可退商品-->
    <select id="selectModifyPurchaseNotReturnGoodsByReturnCode" resultType="cn.htdt.goodsprocess.vo.AtomResModifyReturnGoodsVo" parameterType="java.lang.String">
        select
        <include refid="PROG_Base_Column_List"/>,
        gs.first_attribute_value_name,
        gs.second_attribute_value_name,
        gs.third_attribute_value_name
        from purchase_return_order_goods prog
        LEFT JOIN goods gs ON prog.goods_no = gs.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and prog.return_code= #{returnCode,jdbcType=VARCHAR}
                </if>
                AND prog.delete_flag = '2'
            </trim>
        </where>
    </select>
    <!--退货单下 修改页面不可退商品-->
    <select id="selectModifyPurchaseNotReturnGoodsGroupsByReturnCode" resultType="cn.htdt.goodsprocess.vo.AtomResModifyReturnGoodsVo" parameterType="java.lang.String">
        select
        <include refid="PROG_Base_Column_List"/>
        from purchase_return_order_goods prog
        LEFT JOIN goods_groups gs ON prog.goods_no = gs.goods_groups_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and prog.return_code= #{returnCode,jdbcType=VARCHAR}
                </if>
                AND prog.delete_flag = '2'
            </trim>
        </where>
    </select>


    <!--更新采购退货单状态-->
    <update id="updatePurchaseReturnOrderGoodsStatus" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        update purchase_return_order_goods
        <set>
            <if test="orderStatus != null and orderStatus!=''">
                order_status = #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="returningCount != null">
                returning_count = #{returningCount},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and return_code = #{returnCode,jdbcType=VARCHAR}
                </if>
            </trim>
        </where>
    </update>

    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        insert into purchase_return_order_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="returnGoodsCode != null">
                return_goods_code,
            </if>
            <if test="returnCode != null">
                return_code,
            </if>
            <if test="purchaseCode != null">
                purchase_code,
            </if>
            <if test="purchaseGoodsCode != null">
                purchase_goods_code,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="expectReceiveDate != null">
                expect_receive_date,
            </if>
            <if test="actualReceiveDate != null">
                actual_receive_date,
            </if>
            <if test="goodsNo != null">
                goods_no,
            </if>
            <if test="goodsName != null">
                goods_name,
            </if>
            <if test="purchaseUnit != null">
                purchase_unit,
            </if>
            <if test="categoryNo != null">
                category_no,
            </if>
            <if test="categoryName != null">
                category_name,
            </if>
            <if test="purchaseUnitPrice != null">
                purchase_unit_price,
            </if>
            <if test="purchaseNum != null">
                purchase_num,
            </if>
            <if test="returningCount != null">
                returning_count,
            </if>
            <if test="returnedCount != null">
                returned_count,
            </if>
            <if test="returnRequestCount != null">
                return_request_count,
            </if>
            <if test="storageCount != null">
                storage_count,
            </if>
            <if test="purchasePrice != null">
                purchase_price,
            </if>
            <if test="warehouseType != null">
                warehouse_type,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="warehouseName != null">
                warehouse_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="merchantNo != null">
                merchant_no,
            </if>
            <if test="merchantName != null">
                merchant_name,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
            <if test="disableFlag != null">
                disable_flag,
            </if>
            <if test="createNo != null">
                create_no,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyNo != null">
                modify_no,
            </if>
            <if test="modifyName != null">
                modify_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="companyNo != null">
                company_no,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="branchNo != null">
                branch_no,
            </if>
            <if test="branchName != null">
                branch_name,
            </if>
            <if test="platformType != null">
                platform_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="returnGoodsCode != null">
                #{returnGoodsCode,jdbcType=VARCHAR},
            </if>
            <if test="returnCode != null">
                #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="purchaseCode != null">
                #{purchaseCode,jdbcType=VARCHAR},
            </if>
            <if test="purchaseGoodsCode != null">
                #{purchaseGoodsCode,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="expectReceiveDate != null">
                #{expectReceiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="actualReceiveDate != null">
                #{actualReceiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="goodsNo != null">
                #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="purchaseUnit != null">
                #{purchaseUnit,jdbcType=VARCHAR},
            </if>
            <if test="categoryNo != null">
                #{categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="categoryName != null">
                #{categoryName,jdbcType=VARCHAR},
            </if>
            <if test="purchaseUnitPrice != null">
                #{purchaseUnitPrice,jdbcType=DECIMAL},
            </if>
            <if test="purchaseNum != null">
                #{purchaseNum,jdbcType=DECIMAL},
            </if>
            <if test="returningCount != null">
                #{returningCount,jdbcType=DECIMAL},
            </if>
            <if test="returnedCount != null">
                #{returnedCount,jdbcType=DECIMAL},
            </if>
            <if test="returnRequestCount != null">
                #{returnRequestCount,jdbcType=DECIMAL},
            </if>
            <if test="storageCount != null">
                #{storageCount,jdbcType=DECIMAL},
            </if>
            <if test="purchasePrice != null">
                #{purchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="warehouseType != null">
                #{warehouseType,jdbcType=TINYINT},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null">
                #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null">
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null">
                #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null">
                #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null">
                #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="platformType != null">
                #{platformType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        update purchase_return_order_goods
        <set>
            <if test="returnGoodsCode != null">
                return_goods_code = #{returnGoodsCode,jdbcType=VARCHAR},
            </if>
            <if test="returnCode != null">
                return_code = #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="purchaseCode != null">
                purchase_code = #{purchaseCode,jdbcType=VARCHAR},
            </if>
            <if test="purchaseGoodsCode != null">
                purchase_goods_code = #{purchaseGoodsCode,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="expectReceiveDate != null">
                expect_receive_date = #{expectReceiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="actualReceiveDate != null">
                actual_receive_date = #{actualReceiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="goodsNo != null">
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="purchaseUnit != null">
                purchase_unit = #{purchaseUnit,jdbcType=VARCHAR},
            </if>
            <if test="categoryNo != null">
                category_no = #{categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="categoryName != null">
                category_name = #{categoryName,jdbcType=VARCHAR},
            </if>
            <if test="purchaseUnitPrice != null">
                purchase_unit_price = #{purchaseUnitPrice,jdbcType=DECIMAL},
            </if>
            <if test="purchaseNum != null">
                purchase_num = #{purchaseNum,jdbcType=DECIMAL},
            </if>
            <if test="returningCount != null">
                returning_count = #{returningCount,jdbcType=DECIMAL},
            </if>
            <if test="returnedCount != null">
                returned_count = #{returnedCount,jdbcType=DECIMAL},
            </if>
            <if test="returnRequestCount != null">
                return_request_count = #{returnRequestCount,jdbcType=DECIMAL},
            </if>
            <if test="storageCount != null">
                storage_count = #{storageCount,jdbcType=DECIMAL},
            </if>
            <if test="purchasePrice != null">
                purchase_price = #{purchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="warehouseType != null">
                warehouse_type = #{warehouseType,jdbcType=TINYINT},
            </if>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null">
                warehouse_name = #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null">
                company_no = #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null">
                branch_no = #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null">
                branch_name = #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="platformType != null">
                platform_type = #{platformType,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <!--根据退货单、商品行编码 修改商品状态-->
    <update id="updatePurchaseReturnOrderGoodsDeleteFlag" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
            update
            purchase_return_order_goods
            <set>
                <if test="deleteFlag != null">
                    delete_flag = #{deleteFlag,jdbcType=TINYINT},
                </if>
                <if test="modifyNo != null">
                    modify_no = #{modifyNo,jdbcType=VARCHAR},
                </if>
                <if test="modifyName != null">
                    modify_name = #{modifyName,jdbcType=VARCHAR}
                </if>
            </set>
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="returnGoodsCode != null and returnGoodsCode != ''">
                        and return_goods_code = #{returnGoodsCode,jdbcType=VARCHAR}
                    </if>
                    <if test="returnCode != null and returnCode != ''">
                        and  return_code = #{returnCode,jdbcType=VARCHAR}
                    </if>
                </trim>
            </where>
    </update>
    <!-- 修改 -->
    <update id="batchUpdatePurchaseReturnOrderGoods" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open=""
                 close="" separator=";">
            update
            purchase_return_order_goods
            <set>
                <if test="item.returnGoodsCode != null">
                    return_goods_code = #{item.returnGoodsCode,jdbcType=VARCHAR},
                </if>
                <if test="item.returnCode != null">
                    return_code = #{item.returnCode,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseCode != null">
                    purchase_code = #{item.purchaseCode,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseGoodsCode != null">
                    purchase_goods_code = #{item.purchaseGoodsCode,jdbcType=VARCHAR},
                </if>
                <if test="item.sourceType != null">
                    source_type = #{item.sourceType,jdbcType=VARCHAR},
                </if>
                <if test="item.expectReceiveDate != null">
                    expect_receive_date = #{item.expectReceiveDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.actualReceiveDate != null">
                    actual_receive_date = #{item.actualReceiveDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.goodsNo != null">
                    goods_no = #{item.goodsNo,jdbcType=VARCHAR},
                </if>
                <if test="item.goodsName != null">
                    goods_name = #{item.goodsName,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseUnit != null">
                    purchase_unit = #{item.purchaseUnit,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryNo != null">
                    category_no = #{item.categoryNo,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryName != null">
                    category_name = #{item.categoryName,jdbcType=VARCHAR},
                </if>
                <if test="item.purchaseUnitPrice != null">
                    purchase_unit_price = #{item.purchaseUnitPrice,jdbcType=DECIMAL},
                </if>
                <if test="item.purchaseNum != null">
                    purchase_num = #{item.purchaseNum,jdbcType=DECIMAL},
                </if>
                <if test="item.returningCount != null">
                    returning_count = #{item.returningCount,jdbcType=DECIMAL},
                </if>
                <if test="item.returnedCount != null">
                    returned_count = #{item.returnedCount,jdbcType=DECIMAL},
                </if>
                <if test="item.returnRequestCount != null">
                    return_request_count = #{item.returnRequestCount,jdbcType=DECIMAL},
                </if>
                <if test="item.storageCount != null">
                    storage_count = #{item.storageCount,jdbcType=DECIMAL},
                </if>
                <if test="item.purchasePrice != null">
                    purchase_price = #{item.purchasePrice,jdbcType=DECIMAL},
                </if>
                <if test="item.warehouseType != null">
                    warehouse_type = #{item.warehouseType,jdbcType=TINYINT},
                </if>
                <if test="item.warehouseNo != null">
                    warehouse_no = #{item.warehouseNo,jdbcType=VARCHAR},
                </if>
                <if test="item.warehouseName != null">
                    warehouse_name = #{item.warehouseName,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null">
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.orderStatus != null">
                    order_status = #{item.orderStatus,jdbcType=VARCHAR},
                </if>
                <if test="item.merchantNo != null">
                    merchant_no = #{item.merchantNo,jdbcType=VARCHAR},
                </if>
                <if test="item.merchantName != null">
                    merchant_name = #{item.merchantName,jdbcType=VARCHAR},
                </if>
                <if test="item.storeNo != null">
                    store_no = #{item.storeNo,jdbcType=VARCHAR},
                </if>
                <if test="item.storeName != null">
                    store_name = #{item.storeName,jdbcType=VARCHAR},
                </if>
                <if test="item.disableFlag != null">
                    disable_flag = #{item.disableFlag,jdbcType=TINYINT},
                </if>
                <if test="item.createNo != null">
                    create_no = #{item.createNo,jdbcType=VARCHAR},
                </if>
                <if test="item.createName != null">
                    create_name = #{item.createName,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifyNo != null">
                    modify_no = #{item.modifyNo,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyName != null">
                    modify_name = #{item.modifyName,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null">
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.deleteFlag != null">
                    delete_flag = #{item.deleteFlag,jdbcType=TINYINT},
                </if>
                <if test="item.companyNo != null">
                    company_no = #{item.companyNo,jdbcType=VARCHAR},
                </if>
                <if test="item.companyName != null">
                    company_name = #{item.companyName,jdbcType=VARCHAR},
                </if>
                <if test="item.branchNo != null">
                    branch_no = #{item.branchNo,jdbcType=VARCHAR},
                </if>
                <if test="item.branchName != null">
                    branch_name = #{item.branchName,jdbcType=VARCHAR},
                </if>
                <if test="item.platformType != null">
                    platform_type = #{item.platformType,jdbcType=VARCHAR},
                </if>
            </set>
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.returnGoodsCode != null">
                        and return_goods_code = #{item.returnGoodsCode}
                    </if>
                </trim>
            </where>
        </foreach>
    </update>

    <!--根据采退单商品行号 强制完成-->
    <update id="updateGoodsStatusFinishByReturnGoodsCode"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodVo">
        update purchase_return_order_goods
        <set>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="returningCount != null">
                returning_count = #{returningCount},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            return_goods_code in
            <foreach collection="returnGoodsCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <select id="getTotalReturningCountByReturnCode" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain" resultType="java.math.BigDecimal">
        select SUM(CASE WHEN order_status = '1003' THEN 0 ELSE returning_count END) AS returningCount
        from purchase_return_order_goods where return_code = #{returnCode} and delete_flag = 1
    </select>

    <!--根据采购单号 逻辑删除下面的采购商品-->
    <update id="updateDelGoodsByReturnCode" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderGoodsDomain">
        update purchase_return_order_goods
        <set>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null">
                    and return_code = #{returnCode,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

</mapper>