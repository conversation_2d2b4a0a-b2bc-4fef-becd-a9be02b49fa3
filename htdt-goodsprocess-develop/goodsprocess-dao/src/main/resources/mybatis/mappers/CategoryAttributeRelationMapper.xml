<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.CategoryAttributeRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.CategoryAttributeRelationDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="attribute_code" property="attributeCode"/>
        <result column="category_no" property="categoryNo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        attribute_code, category_no
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectCategoryAttributeListInfo" resultType="cn.htdt.goodsprocess.vo.AtomAttributeVo"
            parameterType="cn.htdt.goodsprocess.domain.CategoryAttributeRelationDomain">
        select
        an.attribute_code attributeCode,
        an.attribute_name attributeName
        from
        category_attribute_relation car
        INNER JOIN attribute_name an ON an.attribute_code = car.attribute_code and an.delete_flag = 1
        <where>
            car.category_no = #{categoryNo}
            and car.delete_flag = 1
        </where>
        group by
        an.attribute_code,
        an.attribute_name
        ORDER BY
        an.attribute_code DESC
    </select>

    <delete id="deleteCategoryAttribute" parameterType="cn.htdt.goodsprocess.domain.CategoryAttributeRelationDomain">
        DELETE
        FROM
        category_attribute_relation
        WHERE
        <trim suffixOverrides="AND | OR" prefix="1=1">
            <if test="categoryNo != null and categoryNo != ''">
                and category_no = #{categoryNo}
            </if>
            <if test="attributeCode != null and attributeCode != ''">
                and attribute_code = #{attributeCode}
            </if>
        </trim>
    </delete>

</mapper>
