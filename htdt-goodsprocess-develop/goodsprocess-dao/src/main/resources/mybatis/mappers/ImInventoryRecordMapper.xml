<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.ImInventoryRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.ImInventoryRecordDomain">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="create_no" property="createNo" />
        <result column="create_name" property="createName" />
        <result column="modify_no" property="modifyNo" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="inventory_code" property="inventoryCode" />
    </resultMap>

    <resultMap id="VoBaseResultMap" type="cn.htdt.goodsprocess.vo.AtomInventoryRecordVo" extends="BaseResultMap">
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        create_no,
        create_name,
        modify_no,
        modify_name,
        modify_time,
        delete_flag,
        inventory_code
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectInventoryRecordByParams" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.domain.ImInventoryRecordDomain">
        select
        <include refid="Base_Column_List"/>
        from im_inventory_record r
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and r.inventory_code like CONCAT(CONCAT('%', #{inventoryCode}),'%')
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        ORDER BY r.modify_time DESC
    </select>

    <select id="selectInventoryNameByParams" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomInventoryRecordVo">
        select inventory_code, GROUP_CONCAT(distinct r.modify_name) inventoryName from im_inventory_record r
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and r.inventory_code like CONCAT(CONCAT('%', #{inventoryCode}),'%')
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        ORDER BY r.modify_time DESC
    </select>

    <select id="selectInventoryNamesByParams" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomInventoryRecordVo">
        select inventory_code, GROUP_CONCAT(distinct r.modify_name) inventoryName from im_inventory_record r
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and r.inventory_code like CONCAT(CONCAT('%', #{inventoryCode}),'%')
                </if>
                <if test="inventoryCodes != null and inventoryCodes.size() > 0">
                    and r.inventory_code IN
                    <foreach collection="inventoryCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        group by r.inventory_code
        ORDER BY r.modify_time DESC
    </select>

    <update id="batchLogicDelete" parameterType="java.util.List">
        update
        im_inventory_record r
        set
        r.delete_flag = 2
        <where>
            r.delete_flag = 1
            and inventory_code in
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

</mapper>
