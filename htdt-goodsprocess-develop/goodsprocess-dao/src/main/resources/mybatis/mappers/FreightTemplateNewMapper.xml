<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.FreightTemplateNewDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.FreightTemplateNewDomain">
        <result column="id" property="id" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="merchant_no" property="merchantNo" />
        <result column="name" property="name" />
        <result column="template_type" property="templateType" />
        <result column="type" property="type" />
        <result column="charge_way" property="chargeWay" />
        <result column="distribution_code" property="distributionCode" />
        <result column="distribution_type" property="distributionType" />
        <result column="distribution_region" property="distributionRegion" />
        <result column="default_flag" property="defaultFlag" />
        <result column="version_no" property="versionNo" />
        <result column="client_versionno" property="clientVersionno" />
        <result column="company_no" property="companyNo" />
        <result column="channel_mode" property="channelMode" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
        <result column="company_name" property="companyName" />
        <result column="branch_no" property="branchNo" />
        <result column="branch_name" property="branchName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        merchant_no, name, template_type, type, charge_way, distribution_code, distribution_type, distribution_region, default_flag, version_no, client_versionno, company_no, channel_mode, disable_flag, create_no, modify_no, company_name, branch_no, branch_name
    </sql>

    <update id="updateByPrimaryKeySelective">

    </update>

    <update id="logicDelete">

    </update>

    <select id="selectByParams" resultType="cn.htdt.goodsprocess.domain.AttributeNameDomain">

    </select>
</mapper>
