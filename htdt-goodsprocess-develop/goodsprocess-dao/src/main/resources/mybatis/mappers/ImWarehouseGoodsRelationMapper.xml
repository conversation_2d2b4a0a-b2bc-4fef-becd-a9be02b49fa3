<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.ImWarehouseGoodsRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        <result column="id" property="id" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="relation_no" property="relationNo" />
        <result column="warehouse_no" property="warehouseNo" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="goods_no" property="goodsNo" />
        <result column="stock_num" property="stockNum" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="calculation_unit_no" property="calculationUnitNo" />
        <result column="calculation_unit_name" property="calculationUnitName" />
        <result column="calculation_unit_symbol" property="calculationUnitSymbol" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
        <result column="company_no" property="companyNo" />
        <result column="company_name" property="companyName" />
        <result column="branch_no" property="branchNo" />
        <result column="branch_name" property="branchName" />
        <result column="parent_goods_no" property="parentGoodsNo" />
    </resultMap>

    <resultMap id="VoBaseResultMap" type="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo" extends="BaseResultMap">
        <result column="goods_name" property="goodsName" />
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo" />
        <result column="assist_calculation_unit_name" property="assistCalculationUnitName"/>
        <result column="assist_calculation_unit_symbol" property="assistCalculationUnitSymbol"/>
        <result column="calculation_unit_no" property="calculationUnitNo" />
        <result column="imei_flag" property="imeiFlag"/>
        <result column="brand_name" property="brandName"/>
        <result column="category_no" property="categoryNo"/>
        <result column="category_name" property="categoryName"/>
        <result column="goods_help_code" property="goodsHelpCode" />
        <result column="goods_delete_flag" property="goodsDeleteFlag" />
        <result column="warehouse_flag" property="warehouseFlag" />
        <result column="virtual_warehouse_flag" property="virtualWarehouseFlag" />
        <result column="goods_form" property="goodsForm" />
        <result column="first_attribute_value_name" property="firstAttributeValueName"/>
        <result column="second_attribute_value_name" property="secondAttributeValueName"/>
        <result column="third_attribute_value_name" property="thirdAttributeValueName"/>
        <result column="full_id_path" property="fullIdPath"/>
        <result column="full_name_path" property="fullNamePath"/>
        <result column="retail_price" property="retailPrice"/>
        <result column="sales_volume" property="salesVolume"/>
        <result column="multi_unit_type" property="multiUnitType"/>
        <result column="multi_unit_goods_no" property="multiUnitGoodsNo"/>
        <result column="shelf_life_unit" property="shelfLifeUnit"/>
        <result column="validity_period_manage_flag" property="validityPeriodManageFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        relation_no, warehouse_no, warehouse_name, goods_no, stock_num, merchant_no, merchant_name, store_no, store_name, calculation_unit_no, calculation_unit_name, calculation_unit_symbol, disable_flag, create_no, modify_no, company_no, company_name, branch_no, branch_name,
        parent_goods_no
    </sql>
    <sql id="Base_Column_List_Relation">
        r.id,
        r.create_name,
        r.create_time,
        r.modify_name,
        r.modify_time,
        r.relation_no, r.warehouse_no, r.goods_no, r.stock_num, r.merchant_no, r.merchant_name, r.store_no, r.store_name, r.disable_flag, r.create_no, r.modify_no, r.company_no, r.company_name, r.branch_no, r.branch_name,
        r.parent_goods_no,
        w.warehouse_name,w.virtual_warehouse_flag, g.goods_name, g.barcode, g.delete_flag, g.goods_form, g.first_attribute_value_name, g.second_attribute_value_name, g.third_attribute_value_name, c.calculation_unit_no, c.calculation_unit_name, c.calculation_unit_symbol
    </sql>
    <sql id="Base_Column_List_R">
        r.id,
        r.create_name,
        r.create_time,
        r.modify_name,
        r.modify_time,
        r.delete_flag,
        r.relation_no, r.warehouse_no, r.goods_no, r.stock_num, r.stock_num canSaleStockNum, r.merchant_no, r.merchant_name, r.store_no, r.store_name, r.disable_flag, r.create_no, r.modify_no, r.company_no, r.company_name, r.branch_no, r.branch_name,
        r.parent_goods_no
    </sql>
    <sql id="Base_Column_List_O">
        w.warehouse_name, g.goods_name, g.category_no, g.calculation_unit_no, g.retail_price, g.warehouse_flag, g.delete_flag as goods_delete_flag, g.goods_form, g.first_attribute_value_name, g.second_attribute_value_name, g.third_attribute_value_name, g.sales_volume, w.virtual_warehouse_flag,
        g.multi_unit_type,g.multi_unit_goods_no
    </sql>
    <sql id="Base_Column_Goods_Relation">
        r.id,
        r.create_name,
        r.create_time,
        r.modify_name,
        r.modify_time,
        g.delete_flag,
        g.warehouse_flag,
        r.relation_no, r.warehouse_no, r.goods_no, r.stock_num, r.merchant_no, r.merchant_name, r.store_no, r.store_name, r.disable_flag, r.create_no, r.modify_no, r.company_no, r.company_name, r.branch_no, r.branch_name,
        r.parent_goods_no,
        w.warehouse_name, g.original_goods_no, g.goods_name, g.barcode,g.conversion_rate, g.calculation_unit_no,g.assist_calculation_unit_no, g.goods_form, g.first_attribute_value_name, g.second_attribute_value_name, g.third_attribute_value_name,g.multi_unit_type,g.multi_unit_goods_no,
        w.virtual_warehouse_flag
    </sql>
    <sql id="Base_Column_Goods_Relation_C">
        r.id,
        r.create_name,
        r.create_time,
        r.modify_name,
        r.modify_time,
        g.delete_flag,
        r.relation_no, r.warehouse_no, r.goods_no, r.stock_num, r.merchant_no, r.merchant_name, r.store_no, r.store_name, r.disable_flag, r.create_no, r.modify_no, r.company_no, r.company_name, r.branch_no, r.branch_name,
        r.parent_goods_no,
        w.warehouse_name, g.goods_name, g.category_no, g.barcode,g.conversion_rate,g.calculation_unit_no,g.assist_calculation_unit_no, g.goods_form, g.first_attribute_value_name, g.second_attribute_value_name, g.third_attribute_value_name
    </sql>
    <sql id="Base_Column_List_warehouse">
        iwg.warehouse_no, iw.warehouse_name
    </sql>
    <sql id="Base_Column_List_goods">
        r.goods_no, g.goods_name, g.barcode, g.first_attribute_value_name, g.second_attribute_value_name, g.third_attribute_value_name,
        g.multi_unit_type, g.multi_unit_goods_no, g.calculation_unit_no, g.assist_calculation_unit_no
    </sql>


    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>
    <sql id="Base_Column_Where_Good">
        and r.delete_flag = 1
    </sql>
    <sql id="Base_Column_Where_goods">
        and r.delete_flag = 1 and g.delete_flag = 1
    </sql>


    <sql id="Base_Column_Where_imwarehouse_goods">
        <if test="companyNo  != null and companyNo  != ''">
            and g.company_no =  #{companyNo,jdbcType=VARCHAR}
        </if>
        <if test="branchNo != null and branchNo != ''">
            and g.branch_no = #{branchNo}
        </if>
        <if test="merchantNo  != null and merchantNo  != ''">
            and g.merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo  != null and storeNo  != ''">
            and g.store_no = #{storeNo,jdbcType=VARCHAR}
        </if>
        <if test="goodsNo != null and goodsNo != ''">
            and r.goods_no   like CONCAT(CONCAT('%', #{goodsNo}),'%')
        </if>
        <if test="warehouseName  != null and warehouseName  != ''">
            and w.warehouse_name LIKE CONCAT(CONCAT('%',#{warehouseName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="loginIdentity  != null and loginIdentity==1">
            and g.goods_source_type in ('1001','1002','1003')
        </if>
        <if test="loginIdentity  != null and loginIdentity==2">
            and g.goods_source_type in ('1002')
        </if>
        <if test="loginIdentity  != null and loginIdentity==4">
            and g.goods_source_type in ('1003')
        </if>
        <if test="loginIdentity  != null and loginIdentity==8">
            and g.goods_source_type in ('1003')
        </if>
        <if test="deleteFlag != null">
            and g.delete_flag = #{deleteFlag}
        </if>
        <if test="virtualWarehouseFlag != null">
            and w.virtual_warehouse_flag = #{virtualWarehouseFlag}
        </if>
        <if test="wareHouseStockBegin != null">
            <![CDATA[and r.stock_num >= #{wareHouseStockBegin}]]>
        </if>
        <if test="wareHouseStockEnd != null">
            <![CDATA[and r.stock_num <= #{wareHouseStockEnd}]]>
        </if>
<!--        <if test="wareHouseStockBegin != null or wareHouseStockEnd!=null">-->
<!--            <choose>-->
<!--                <when test="wareHouseStockBegin != null and wareHouseStockEnd == null">-->
<!--                    <![CDATA[ and r.stock_num > #{wareHouseStockBegin}]]>-->
<!--                </when>-->
<!--                <when test="wareHouseStockBegin == null and wareHouseStockEnd != null">-->
<!--                    <![CDATA[ and r.stock_num < #{wareHouseStockEnd}]]>-->
<!--                </when>-->
<!--                <otherwise>-->
<!--                    <![CDATA[and r.stock_num  between #{wareHouseStockBegin} and #{wareHouseStockEnd}]]>-->
<!--                </otherwise>-->
<!--            </choose>-->
<!--        </if>-->
        <if test="goodsName  != null and goodsName  != ''">
            and (g.goods_no = #{goodsName,jdbcType=VARCHAR} or g.goods_name LIKE CONCAT(CONCAT('%',#{goodsName,jdbcType=VARCHAR}),'%'))
        </if>

        <!-- 是否展示辅单位 -->
        <if test="null == isAuxiliaryUnit or '1'.toString() == isAuxiliaryUnit">
            and g.multi_unit_type != '1002'
        </if>
        <if test="null != isAuxiliaryUnit and '3'.toString() == isAuxiliaryUnit">
            and g.multi_unit_type = ''
        </if>
    </sql>

    <sql id="Base_Column_Where_power">
        <if test="loginIdentity  != null and loginIdentity==1">
            and g.goods_source_type in ('1001')
        </if>
        <if test="loginIdentity  != null and loginIdentity==2">
            and g.goods_source_type in ('1002')
        </if>
        <if test="loginIdentity  != null and loginIdentity==4">
            and g.goods_source_type in ('1003')
        </if>
        <if test="loginIdentity  != null and loginIdentity==8">
            and g.goods_source_type in ('1003')
        </if>
    </sql>

    <select id="selectByParamsNum" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        select
        <include refid="Base_Column_List" />
        from
        im_warehouse_goods_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="merchantName  != null and merchantName  != ''">
                    and merchant_name = #{merchantName,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="warehouseName  != null and warehouseName  != ''">
                    and warehouse_name LIKE CONCAT(CONCAT('%',#{warehouseName,jdbcType=VARCHAR}),'%')
                </if>
                <if test="deleteFlag == null">
                  <include refid="Base_Column_Where" />
                </if>
                <if test="deleteFlag != null">
                    and delete_flag = #{deleteFlag}
                </if>
            </trim>
        </where>
    </select>


    <!-- 获取串码商品信息数量-count -->
    <select id="selectAllGoodsImeiCountByGoodsNo" resultType="java.lang.Integer" parameterType="java.lang.String" >
        select count(1)
        from goods_imei i
        left join goods g on g.goods_no=i.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and i.goods_no = #{goodsNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and i.warehouse_no = #{warehouseNo}
                </if>
                and i.delete_flag=1
            </trim>
        </where>
    </select>

    <select id="selectOneByParams" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        select
        <include refid="Base_Column_List" />
        from im_warehouse_goods_relation r
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and r.relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and r.goods_no = #{goodsNo,jdbcType=VARCHAR}
                </if>
                and r.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="selectGoodsByParams" resultMap="VoBaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
            g.goods_no,
            g.goods_name,
            g.goods_help_code,
            g.first_attribute_value_name,
            g.second_attribute_value_name,
            g.third_attribute_value_name,
            g.barcode,
            sc.full_id_path,
            sc.full_name_path
        from
            im_warehouse_goods_relation r
        left join goods g on g.goods_no = r.goods_no
        left join goods_groups_related_goods ggrg on g.goods_no = ggrg.goods_no
        LEFT JOIN sale_category sc
        ON g.category_no = sc.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">

                <if test="goodsStr  != null and goodsStr  != ''">
                    and (g.goods_no like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%") OR g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="goodsHelpCode != null and goodsHelpCode != ''">
                    and g.goods_help_code LIKE CONCAT (CONCAT('%',#{goodsHelpCode}),'%')
                </if>
                <if test="categoryNo != null and categoryNo != ''">
                    and g.category_no = #{categoryNo,jdbcType=VARCHAR}
                </if>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                <if test="brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <!-- 排除指定的商品 -->
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="notExistsGroupsFlag != null and notExistsGroupsFlag =='2'.toString()">
                    and NOT EXISTS (
                    SELECT 1 FROM goods_groups_related_goods rg
                    WHERE rg.goods_no = g.goods_no
                    )
                    and NOT EXISTS (
                    SELECT 1 FROM goods_groups_related_goods rg
                    WHERE rg.goods_no = g.merchant_goods_no
                    )
                </if>

                <!-- 排除绑定商品组的商品 -->
                and ggrg.id is null
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
        order by  g.create_time desc
    </select>

    <select id="selectGoodsGroupsByParams" resultMap="VoBaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
            g.goods_groups_no as goods_no,
            g.goods_groups_name as goods_name
        from im_warehouse_goods_relation r
        left join goods_groups g on g.goods_groups_no = r.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsStr  != null and goodsStr  != ''">
                    and (g.goods_groups_no like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%") OR g.goods_groups_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
        order by  g.create_time desc
    </select>

    <!-- 查询某一仓库所有商品-bossapp用 -->
    <select id="selectAppGoodsByParams" resultMap="VoBaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
        r.warehouse_no,
        g.goods_no,
        g.goods_name,
        g.goods_help_code,
        g.first_attribute_value_name,
        g.second_attribute_value_name,
        g.third_attribute_value_name,
        g.barcode,
        g.retail_price,
        g.sales_volume,
        g.calculation_unit_no,
        r.stock_num stockNum,
        r.stock_num canSaleStockNum,
        sc.full_id_path,
        sc.full_name_path,
        g.multi_unit_type,
        g.multi_unit_goods_no,
        g.quality_guarantee_period,
        g.shelf_life_unit,
        g.validity_period_manage_flag
        from
        im_warehouse_goods_relation r
        left join
        goods g
        on
        g.goods_no = r.goods_no
        LEFT JOIN sale_category sc
        ON g.category_no = sc.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">

                <if test="goodsStr  != null and goodsStr  != ''">
                    and (g.goods_help_code like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%") OR g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="categoryNo != null and categoryNo != ''">
                    and g.category_no = #{categoryNo,jdbcType=VARCHAR}
                </if>
                <if test="brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo}
                </if>
                <if test="warehouseFlag != null">
                    and g.warehouse_flag = #{warehouseFlag,jdbcType=TINYINT}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="null == isAuxiliaryUnit or '1'.toString() == isAuxiliaryUnit">
                    and g.multi_unit_type != '1002'
                </if>
                <if test="null != isAuxiliaryUnit and '3'.toString() == isAuxiliaryUnit">
                    and g.multi_unit_type = ''
                </if>
                <if test="notExistsGroupsFlag != null and notExistsGroupsFlag =='2'.toString()">
                    and NOT EXISTS (
                    SELECT 1 FROM goods_groups_related_goods rg
                    WHERE rg.goods_no = g.goods_no
                    )
                    and NOT EXISTS (
                    SELECT 1 FROM goods_groups_related_goods rg
                    WHERE rg.goods_no = g.merchant_goods_no
                    )

                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
        <![CDATA[
            order by ${hxgSortValue} ${hxgSortType}
        ]]>
    </select>

    <!-- 查询某一仓库所有商品-bossapp用 -->
    <select id="selectAppGoodsGroupsByParams" resultMap="VoBaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
            r.warehouse_no,
            g.goods_groups_no as goods_no,
            g.goods_groups_name as goods_name,
            g.calculation_unit_no,
            r.stock_num stockNum,
            r.stock_num canSaleStockNum
        from im_warehouse_goods_relation r
        inner join goods_groups g on g.goods_groups_no = r.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsStr  != null and goodsStr  != ''">
                    and (g.goods_no like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%") OR g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="warehouseFlag != null">
                    and g.warehouse_flag = #{warehouseFlag,jdbcType=TINYINT}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
    </select>

    <select id="selectWarehouseGoodsStockNumPageByParams" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
        <include refid="Base_Column_Goods_Relation" />
        from im_warehouse_goods_relation r
        left join  goods  g on  g.original_goods_no = r.goods_no
--         left join goods_real_stock l on r.goods_no = l.goods_no
        left join im_warehouse w on r.warehouse_no = w.warehouse_no
--         left join calculation_unit c on g.calculation_unit_no = c.calculation_unit_no
--         left join calculation_unit u on g.assist_calculation_unit_no = u.calculation_unit_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.warehouse_flag = 2
                <include refid="Base_Column_Where_goods" />
                <include refid="Base_Column_Where_imwarehouse_goods" />
                <if test="goodsStr  != null and goodsStr  != ''">
                    and (w.warehouse_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%') or g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="warehouseNos != null and warehouseNos.size() > 0">
                    and r.warehouse_no IN
                    <foreach collection="warehouseNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and r.goods_no = #{goodsNo}
                </if>
                <if test="virtualWarehouseFlag != null">
                    and w.virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                order by r.modify_time desc
            </trim>
        </where>
    </select>

    <select id="selectAppWarehouseGoodsStockNumPageByParams" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
        r.warehouse_no, r.goods_no, r.stock_num
        from im_warehouse_goods_relation r
        left join  goods  g on  g.original_goods_no = r.goods_no
        left join im_warehouse w on r.warehouse_no = w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.warehouse_flag = 2
                <include refid="Base_Column_Where_goods" />
                <include refid="Base_Column_Where_imwarehouse_goods" />
                <if test="goodsStr  != null and goodsStr  != ''">
                    and (w.warehouse_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%') or g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                group by r.warehouse_no
                order by sum(r.stock_num) desc
            </trim>
        </where>
    </select>

    <select id="selectByParams" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
        <include refid="Base_Column_List_Relation" />
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no=g.goods_no
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        left join calculation_unit c on g.calculation_unit_no=c.calculation_unit_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where_power"/>
                <if test="relationNo  != null and relationNo  != ''">
                    and r.relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="warehouseName  != null and warehouseName  != ''">
                    and w.warehouse_name LIKE CONCAT(CONCAT('%',#{warehouseName,jdbcType=VARCHAR}),'%')
                </if>
                <if test="goodsStr  != null and goodsStr  != ''">
                    and (g.goods_no LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%') or g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="goodsNos != null and goodsNos.size > 0">
                    r.goods_no in
                    <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="deleteFlag != null">
                    and g.delete_flag = #{deleteFlag}
                </if>
                <if test="warehouseFlag != null">
                    and g.warehouse_flag = #{warehouseFlag,jdbcType=TINYINT}
                </if>
                and r.delete_flag = 1 order by r.create_time desc
            </trim>
        </where>
    </select>

    <select id="selectCountByParams" resultType="java.lang.Integer" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select count(1)
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no=g.goods_no
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        left join calculation_unit c on g.calculation_unit_no=c.calculation_unit_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.warehouse_flag=2
                and g.goods_source_type = #{goodsSourceType}
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNos != null and goodsNos.size > 0">
                    r.goods_no in
                    <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="deleteFlag == null">
                    <include refid="Base_Column_Where" />
                </if>
                <if test="deleteFlag != null">
                    and g.delete_flag = #{deleteFlag}
                </if>
            </trim>
        </where>
    </select>

    <select id="selectWarehouseSetByParams" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
        <include refid="Base_Column_List_Relation" />
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no=g.goods_no
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        left join calculation_unit c on g.calculation_unit_no=c.calculation_unit_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.goods_source_type = #{goodsSourceType}
                <if test="warehouseFlag != null and warehouseFlag != ''">
                    and g.warehouse_flag=#{warehouseFlag}
                </if>
                <if test="goodsSourceType == '1001'">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="goodsSourceType == '1002'">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsSourceType == '1003'">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNos != null and goodsNos.size > 0">
                    r.goods_no in
                    <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
    </select>

    <select id="selectWarehouseSetCountByParams" resultType="java.lang.Integer" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select count(1)
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no=g.goods_no
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        left join calculation_unit c on g.calculation_unit_no=c.calculation_unit_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.goods_source_type = #{goodsSourceType}
                <if test="warehouseFlag != null and warehouseFlag != ''">
                    and g.warehouse_flag=#{warehouseFlag}
                </if>
                <if test="goodsSourceType == '1001'">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="goodsSourceType == '1002'">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsSourceType == '1003'">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNos != null and goodsNos.size > 0">
                    r.goods_no in
                    <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
    </select>

    <select id="selectWarehouseGoodsByNo" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        select
            <include refid="Base_Column_Goods_Relation_C" />,
            IFNULL(l.freeze_stock_num,0) + IFNULL(l.deliver_stock_num,0) stockNums,
            g.multi_unit_type, g.multi_unit_goods_no, g.shelf_life_unit, g.validity_period_manage_flag
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no = g.goods_no
        left join goods_real_stock l on r.goods_no = l.goods_no
        left join im_warehouse w on r.warehouse_no = w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
    </select>

    <select id="selectWarehouseGoodsGroupsByNo" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        select
        r.id, r.create_name, r.create_time, r.modify_name, r.modify_time, r.relation_no, r.warehouse_no, r.goods_no,
        r.stock_num, r.merchant_no, r.merchant_name, r.store_no, r.store_name, r.disable_flag, r.create_no, r.modify_no,
        r.company_no, r.company_name, r.branch_no, r.branch_name, r.parent_goods_no,
        g.delete_flag,g.goods_groups_name as goods_name, g.calculation_unit_no,
        w.warehouse_name,
        IFNULL(l.freeze_stock_num,0) + IFNULL(l.deliver_stock_num,0) stockNums
        from im_warehouse_goods_relation r
        left join goods_groups g on r.goods_no = g.goods_groups_no
        left join goods_real_stock l on r.goods_no = l.goods_no
        left join im_warehouse w on r.warehouse_no = w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
    </select>

    <!-- 根据条件获取商品关联的仓库 -->
    <select id="selectWarehouseByParams" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        select
        DISTINCT <include refid="Base_Column_List_warehouse" />
        from
        im_warehouse_goods_relation iwg
        LEFT JOIN im_warehouse iw on iw.warehouse_no=iwg.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and iwg.relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and iwg.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and iwg.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and iwg.warehouse_no = #{warehouseNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and iwg.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and iwg.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and iwg.goods_no = #{goodsNo,jdbcType=VARCHAR}
                </if>
                and iwg.delete_flag = 1
            </trim>
        </where>
    </select>

    <!-- 根据条件获取仓库关联的商品 -->
    <select id="selectWarehouseGoodsByParams" resultType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        select
        <include refid="Base_Column_List_goods" />
        from goods g left join im_warehouse_goods_relation r on g.goods_no=r.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and r.relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
        group by r.goods_no, g.goods_name, g.barcode
    </select>

    <!-- 根据条件获取仓库关联的商品组 -->
    <select id="selectWarehouseGoodsGroupsByParams" resultType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        select
        r.goods_no, g.goods_groups_name as goods_name, g.calculation_unit_no
        from goods_groups g left join im_warehouse_goods_relation r on g.goods_groups_no=r.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and r.relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
        group by r.goods_no, g.goods_groups_name
    </select>

    <!-- 根据条件获取仓库商品关联的数量 -->
    <select id="selectWarehouseGoodsCountByParams" resultType="Integer" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select count(0)
        from im_warehouse_goods_relation r
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and r.relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and r.goods_no = #{goodsNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsSourceType  != null and goodsSourceType  != ''">
                    and w.warehouse_type = #{goodsSourceType}
                </if>
                and r.delete_flag = 1
            </trim>
        </where>
    </select>

    <!-- 根据商品编号list获取商品关联的仓库 -->
    <select id="selectWarehouseGoodsInfoByParams" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select g.goods_no, g.original_goods_no, g.imei_flag, g.warehouse_flag, r.warehouse_no, w.warehouse_name,w.virtual_warehouse_flag,
        g.first_attribute_value_name,g.second_attribute_value_name,g.third_attribute_value_name
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no=g.original_goods_no
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and r.relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="goodsNo" separator="," open="(" close=")">
                        #{goodsNo,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
    </select>

    <!-- 根据商品仓库维度查询信息-列表 -->
    <select id="selectWarehouseGoods" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
        <include refid="Base_Column_List_R" />,
        <include refid="Base_Column_List_O" />
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no=g.goods_no
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where_power" />
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsStr  != null and goodsStr  != ''">
                    and (g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%') or r.goods_no LIKE #{goodsStr,jdbcType=VARCHAR})
                </if>
                <if test="imeiFlag != null">
                    and g.imei_flag = #{imeiFlag}
                </if>
                <if test="barcode != null and barcode != ''">
                    and g.barcode = #{barcode}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="warehouseName != null and warehouseName  != ''">
                    and w.warehouse_name LIKE CONCAT(CONCAT('%',#{warehouseName,jdbcType=VARCHAR}),'%')
                </if>
                <if test="goodsDeleteFlag == null" >
                    and r.delete_flag = 1
                </if>
                <if test="goodsDeleteFlag != null" >
                    and g.delete_flag = #{goodsDeleteFlag,jdbcType=TINYINT}
                </if>
                <if test="warehouseFlag != null">
                    and g.warehouse_flag = #{warehouseFlag,jdbcType=VARCHAR}
                </if>
                <if test="warehouseType != null and warehouseType  != ''" >
                    and w.warehouse_type = #{warehouseType,jdbcType=VARCHAR}
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and r.goods_no IN
                    <foreach collection="goodsNos" item="goodsNo" separator="," open="(" close=")">
                        #{goodsNo,jdbcType=VARCHAR}
                    </foreach>
                </if>
            </trim>
        </where>
        order by  r.create_time desc
    </select>

    <!-- 根据商品仓库维度查询信息-数量 -->
    <select id="selectWarehouseGoodsCount" resultType="java.lang.Integer" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select count(1)
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no=g.goods_no
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                and r.delete_flag = 1
                <if test="warehouseType != null and warehouseType  != ''" >
                    and w.warehouse_type = #{warehouseType,jdbcType=VARCHAR}
                </if>
            </trim>
        </where>
    </select>

    <select id="selectWarehouseGoodsStoreCount" resultType="java.lang.Integer" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        select
        count(1)
        from im_warehouse_goods_relation r
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and r.relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and r.goods_no = #{goodsNo,jdbcType=VARCHAR}
                </if>
                and r.stock_num > 0
                and r.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="selectWarehouseGoodsRelationCount" resultType="java.lang.Integer" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
        count(1)
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no=g.goods_no
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and r.relation_no =  #{relationNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and r.company_no =  #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and r.branch_no = #{branchNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and r.goods_no = #{goodsNo,jdbcType=VARCHAR}
                </if>
                <if test="virtualWarehouseFlag != null and virtualWarehouseFlag  != ''">
                    and w.virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="warehouseFlag != null and warehouseFlag != ''">
                    and g.warehouse_flag=#{warehouseFlag}
                </if>
                and r.delete_flag = 1
                and g.delete_flag = 1
                and w.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="selectWarehouseGoodsRelationCounts" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select r.goods_no,
        <if test="merchantNo  != null and merchantNo  != ''">
            r.merchant_no,
        </if>
        <if test="storeNo  != null and storeNo  != ''">
            r.store_no,
        </if>
        count(1) as warehouseCount
        from im_warehouse_goods_relation r
        left join goods g on r.goods_no=g.goods_no
        left join im_warehouse w on r.warehouse_no=w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and r.goods_no IN
                    <foreach collection="goodsNos" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="virtualWarehouseFlag != null and virtualWarehouseFlag  != ''">
                    and w.virtual_warehouse_flag = #{virtualWarehouseFlag}
                </if>
                <if test="warehouseFlag != null and warehouseFlag != ''">
                    and g.warehouse_flag=#{warehouseFlag}
                </if>
                and r.delete_flag = 1
                and g.delete_flag = 1
                and w.delete_flag = 1
            </trim>
        </where>
        group by r.goods_no
        <if test="merchantNo  != null and merchantNo  != ''">
            , r.merchant_no
        </if>
        <if test="storeNo  != null and storeNo  != ''">
            , r.store_no
        </if>
    </select>

    <select id="selectWarehouseGoodsList" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
        <include refid="Base_Column_List_R" />
        from
        im_warehouse_goods_relation r
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and r.goods_no IN
                <foreach collection="voList" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item.goodsNo}
                </foreach>
                and r.warehouse_no IN
                <foreach collection="voList" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item.warehouseNo}
                </foreach>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </select>

    <!-- 获取仓库下商品款数和库存数 -->
    <select id="selectWarehouseGoodsNumList" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select r.warehouse_no, IFNULL(count(r.goods_no), 0) totalGoodsNum, sum(r.stock_num) totalStockNum
        from im_warehouse_goods_relation r left join im_warehouse w on w.warehouse_no =r.warehouse_no
        left join goods g on g.goods_no = r.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNos != null and warehouseNos.size() > 0">
                    and r.warehouse_no IN
                    <foreach collection="warehouseNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="selfWarehouseFlag != null">
                    and w.self_warehouse_flag = #{selfWarehouseFlag}
                </if>
                <include refid="Base_Column_Where_goods" />
            </trim>
        </where>
        group by r.warehouse_no
    </select>

    <!--一体机->销售报表->商品库存统计-->
    <select id="selectGoodsStockRecordForPage" resultType="cn.htdt.goodsprocess.vo.AtomResStockRecordVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqStockRecordVo">
        SELECT
            r.goods_no,
            g.goods_form,
            g.parent_goods_no,
            r.warehouse_no,
            r.stock_num,
            (case when iw.virtual_warehouse_flag = 1 then iw.warehouse_name else '' end) as warehouseName,
            g.goods_name,
            g.brand_no,
            b.brand_name,
            g.category_no,
        (select t.purchase_unit_price from purchase_order_goods t where t.goods_no  = g.goods_no and t.order_status in (1002,1003) order by t.create_time desc limit 1) purchase_price,
            g.first_attribute_value_name,
            g.second_attribute_value_name,
            g.third_attribute_value_name,
            g.multi_unit_type,
            g.multi_unit_goods_no,
            CAST(g.purchase_price * r.stock_num as DECIMAL(12,2)) AS stockPrice
        FROM
            im_warehouse_goods_relation r
        LEFT JOIN im_warehouse iw ON r.warehouse_no = iw.warehouse_no
        LEFT JOIN goods g ON g.goods_no = r.goods_no
        LEFT JOIN brand b ON b.brand_no = g.brand_no
        LEFT JOIN sale_category sc ON sc.category_no = g.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="stockNumType != null and stockNumType != '' ">
                    <if test="stockNumType == 1002">
                        and r.stock_num = 0
                    </if>
                </if>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                <if test=" imeiFlag != null and imeiFlag != ''">
                    and g.imei_flag = #{imeiFlag,jdbcType=VARCHAR}
                </if>
                <if test=" brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo,jdbcType=VARCHAR}
                </if>
                <if test=" goodsForm != null and goodsForm != ''">
                    and g.goods_form = #{goodsForm,jdbcType=VARCHAR}
                </if>
                <if test=" categoryNo != null and categoryNo != ''">
                    and sc.parent_no = #{categoryNo,jdbcType=VARCHAR}
                </if>
                <if test=" goodsStr != null and goodsStr != ''">
                    and (
                    g.barcode like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%")
                    or g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%')
                    or g.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsStr}),'%')
                    or g.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsStr}),'%')
                    )
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                and g.delete_flag = 1
                and r.delete_flag = 1
            </trim>
        </where>
    </select>
    <!--一体机->销售报表->商品库存统计->关联店铺自建类目 -->
    <select id="selectStoreGoodsStockRecordForPage" resultType="cn.htdt.goodsprocess.vo.AtomResStockRecordVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqStockRecordVo">
        SELECT
        r.goods_no,
        g.goods_form,
        g.parent_goods_no,
        r.warehouse_no,
        r.stock_num,
        (case when iw.virtual_warehouse_flag = 1 then iw.warehouse_name else '' end) as warehouseName,
        g.goods_name,
        g.brand_no,
        b.brand_name,
        sc.store_category_no categoryNo,
        (select t.purchase_unit_price from purchase_order_goods t where t.goods_no  = g.goods_no and t.order_status in (1002,1003) order by t.create_time desc limit 1) purchase_price,
        g.first_attribute_value_name,
        g.second_attribute_value_name,
        g.third_attribute_value_name,
        g.multi_unit_type,
        g.multi_unit_goods_no,
        CAST(g.purchase_price * r.stock_num as DECIMAL(12,2)) AS stockPrice
        FROM
        im_warehouse_goods_relation r
        LEFT JOIN im_warehouse iw ON r.warehouse_no = iw.warehouse_no
        LEFT JOIN goods g ON g.goods_no = r.parent_goods_no
        LEFT JOIN store_category_goods_relation scg ON g.goods_no = scg.goods_no
        left join store_category sc on scg.store_category_no = sc.store_category_no
        LEFT JOIN brand b ON b.brand_no = g.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="stockNumType != null and stockNumType != '' ">
                    <if test="stockNumType == 1002">
                        and r.stock_num = 0
                    </if>
                </if>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                <if test=" imeiFlag != null and imeiFlag != ''">
                    and g.imei_flag = #{imeiFlag,jdbcType=VARCHAR}
                </if>
                <if test=" brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo,jdbcType=VARCHAR}
                </if>
                <if test=" goodsForm != null and goodsForm != ''">
                    and g.goods_form = #{goodsForm,jdbcType=VARCHAR}
                </if>
                <if test=" categoryNo != null and categoryNo != ''">
                    and sc.parent_no = #{categoryNo,jdbcType=VARCHAR}
                </if>
                <if test=" goodsStr != null and goodsStr != ''">
                    and (
                    g.barcode like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%")
                    or g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%')
                    or g.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsStr}),'%')
                    or g.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsStr}),'%')
                    )
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                and g.delete_flag = 1
                and r.delete_flag = 1
            </trim>
        </where>
    </select>
    <!--一体机->销售报表->商品库存统计 汇总数据-->
    <select id="selectGoodsStockRecordCount" resultType="cn.htdt.goodsprocess.vo.AtomResStockRecordCountVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqStockRecordVo">
        SELECT
            sum(r.stock_num) as stockTotal,
            SUM(
            CAST(g.purchase_price * r.stock_num AS DECIMAL (12, 2))) AS amountTotal
        FROM
            im_warehouse_goods_relation r
        LEFT JOIN goods g ON g.goods_no = r.parent_goods_no
        LEFT JOIN brand b ON b.brand_no = g.brand_no
        LEFT JOIN sale_category sc ON sc.category_no = g.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="stockNumType != null and stockNumType != '' ">
                    <if test="stockNumType == 1002">
                        and r.stock_num = 0
                    </if>
                </if>
                <if test=" imeiFlag != null and imeiFlag != ''">
                    and g.imei_flag = #{imeiFlag,jdbcType=VARCHAR}
                </if>
                <if test=" brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo,jdbcType=VARCHAR}
                </if>
                <if test=" goodsForm != null and goodsForm != ''">
                    and g.goods_form = #{goodsForm,jdbcType=VARCHAR}
                </if>
                <if test=" categoryNo != null and categoryNo != ''">
                    and sc.parent_no = #{categoryNo,jdbcType=VARCHAR}
                </if>
                <if test=" goodsStr != null and goodsStr != ''">
                    and (g.barcode like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%") OR g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                and g.delete_flag = 1
                and r.delete_flag = 1
            </trim>
        </where>
    </select>
    <!--一体机->销售报表->商品库存统计 汇总数据  关联店铺自建类目-->
    <select id="selectStoreGoodsStockRecordCount" resultType="cn.htdt.goodsprocess.vo.AtomResStockRecordCountVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqStockRecordVo">
        SELECT
        sum(r.stock_num) as stockTotal,
        SUM(
        CAST(g.purchase_price * r.stock_num AS DECIMAL (12, 2))) AS amountTotal
        FROM
        im_warehouse_goods_relation r
        LEFT JOIN goods g ON g.goods_no = r.parent_goods_no
        LEFT JOIN brand b ON b.brand_no = g.brand_no
        LEFT JOIN store_category_goods_relation scg ON g.goods_no = scg.goods_no
        left join store_category sc on scg.store_category_no = sc.store_category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
                <if test="stockNumType != null and stockNumType != '' ">
                    <if test="stockNumType == 1002">
                        and r.stock_num = 0
                    </if>
                </if>
                <if test=" imeiFlag != null and imeiFlag != ''">
                    and g.imei_flag = #{imeiFlag,jdbcType=VARCHAR}
                </if>
                <if test=" brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo,jdbcType=VARCHAR}
                </if>
                <if test=" goodsForm != null and goodsForm != ''">
                    and g.goods_form = #{goodsForm,jdbcType=VARCHAR}
                </if>
                <if test=" categoryNo != null and categoryNo != ''">
                    and sc.parent_no = #{categoryNo,jdbcType=VARCHAR}
                </if>
                <if test=" goodsStr != null and goodsStr != ''">
                    and (g.barcode like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%") OR g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and r.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and r.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                and g.delete_flag = 1
                and r.delete_flag = 1
            </trim>
        </where>
    </select>
    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain" >
        insert into
            im_warehouse_goods_relation
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="relationNo  != null">
                relation_no,
            </if>
            <if test="warehouseNo != null" >
                warehouse_no,
            </if>
            <if test="warehouseName != null" >
                warehouse_name,
            </if>
            <if test="goodsNo != null" >
                goods_no,
            </if>
            <if test="stockNum != null" >
                stock_num,
            </if>
            <if test="merchantNo != null" >
                merchant_no,
            </if>
            <if test="merchantName != null" >
                merchant_name,
            </if>
            <if test="storeNo != null" >
                store_no,
            </if>
            <if test="storeName != null" >
                store_name,
            </if>
            <if test="calculationUnitNo != null" >
                calculation_unit_no,
            </if>
            <if test="calculationUnitName != null" >
                calculation_unit_name,
            </if>
            <if test="calculationUnitSymbol != null" >
                calculation_unit_symbol,
            </if>
            <if test="disableFlag != null" >
                disable_flag,
            </if>
            <if test="createNo != null" >
                create_no,
            </if>
            <if test="createName != null" >
                create_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyNo != null" >
                modify_no,
            </if>
            <if test="modifyName != null" >
                modify_name,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="deleteFlag != null" >
                delete_flag,
            </if>
            <if test="companyNo != null" >
                company_no = #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null" >
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null" >
                branch_no = #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null" >
                branch_name = #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="parentGoodsNo != null" >
                parent_goods_no = #{parentGoodsNo,jdbcType=VARCHAR},
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="relationNo  != null">
                #{relationNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null" >
                #{warehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null" >
                #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="goodsNo != null" >
                #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="stockNum != null" >
                #{stockNum,jdbcType=DECIMAL},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null" >
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null" >
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null" >
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null" >
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null" >
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null" >
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null" >
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null" >
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null" >
                #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null" >
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null" >
                #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null" >
                #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="parentGoodsNo != null" >
                #{parentGoodsNo,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <!-- 批量插入 -->
    <insert id="batchInsertWarehouseGoods">
        INSERT INTO im_warehouse_goods_relation(create_name, create_time, modify_name, modify_time, delete_flag, relation_no, warehouse_no, warehouse_name, goods_no, stock_num,
        merchant_no, merchant_name, store_no, store_name, disable_flag, create_no, modify_no, company_no, company_name, branch_no, branch_name, parent_goods_no)
        <foreach collection="list" item="emp" index="index" separator="union all">
            SELECT
                <trim prefix="" suffix="" suffixOverrides=",">
                    <choose>
                        <when test="emp.createName != null">
                            #{emp.createName,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.createTime != null">
                            #{emp.createTime,jdbcType=TIMESTAMP},
                        </when>
                        <otherwise>now(),</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.modifyName != null">
                            #{emp.modifyName,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.modifyTime != null">
                            #{emp.modifyTime,jdbcType=TIMESTAMP},
                        </when>
                        <otherwise>now(),</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.deleteFlag != null">
                            #{emp.deleteFlag,jdbcType=INTEGER},
                        </when>
                        <otherwise>1,</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.relationNo != null">
                            #{emp.relationNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.warehouseNo != null">
                            #{emp.warehouseNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.warehouseName != null">
                            #{emp.warehouseName,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.goodsNo != null">
                            #{emp.goodsNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.stockNum != null">
                            #{emp.stockNum,jdbcType=DECIMAL},
                        </when>
                        <otherwise>0,</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.merchantNo != null">
                            #{emp.merchantNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.merchantName != null">
                            #{emp.merchantName,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.storeNo != null">
                            #{emp.storeNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.storeName != null">
                            #{emp.storeName,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.disableFlag != null">
                            #{emp.disableFlag,jdbcType=INTEGER},
                        </when>
                        <otherwise>1,</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.createNo != null">
                            #{emp.createNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.modifyNo != null">
                            #{emp.modifyNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.companyNo != null">
                            #{emp.companyNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.companyName != null">
                            #{emp.companyName,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.branchNo != null">
                            #{emp.branchNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.branchName != null">
                            #{emp.branchName,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                    <choose>
                        <when test="emp.parentGoodsNo != null">
                            #{emp.parentGoodsNo,jdbcType=VARCHAR},
                        </when>
                        <otherwise>'',</otherwise>
                    </choose>
                </trim>
            FROM DUAL
        </foreach>
    </insert>

    <!-- 根据relation_no批量变更仓库和清0库存 -->
    <update id="batchUpdateWarehouseGoods" parameterType="java.util.List" >
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update im_warehouse_goods_relation
            <trim prefix="SET" suffixOverrides=",">
                <if test="item.modifyNo != null and item.modifyNo  != ''" >
                    modify_no = #{item.modifyNo,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyName != null and item.modifyName  != ''" >
                    modify_name = #{item.modifyName,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyTime != null" >
                    modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.warehouseNo != null and item.warehouseNo != ''">
                    warehouse_no = #{item.warehouseNo},
                </if>
                <if test="item.stockNum != null">
                    stock_num = #{item.stockNum},
                </if>
            </trim>
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.relationNo  != null and item.relationNo  != ''">
                        and relation_no =  #{item.relationNo}
                    </if>
                    <if test="item.warehouseNo  != null and item.warehouseNo  != ''">
                        and warehouse_no =  #{item.warehouseNo}
                    </if>
                    <if test="item.goodsNo  != null and item.goodsNo  != ''">
                        and goods_no =  #{item.goodsNo}
                    </if>
                    <include refid="Base_Column_Where" />
                </trim>
            </where>
        </foreach>
    </update>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain" >
        update
            im_warehouse_goods_relation
        <set >
            <if test="relationNo  != null and relationNo  != ''">
                relation_no =  #{relationNo,jdbcType=VARCHAR}
            </if>
            <if test="warehouseNo != null and warehouseNo  != ''" >
                warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null and warehouseName  != ''" >
                warehouse_name = #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="goodsNo != null and goodsNo  != ''" >
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="stockNum != null" >
                stock_num = #{stockNum,jdbcType=DECIMAL},
            </if>
            <if test="merchantNo != null and merchantNo  != ''" >
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null and merchantName  != ''" >
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null and storeNo  != ''" >
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null and storeName  != ''" >
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitNo != null and calculationUnitNo  != ''" >
                #{calculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitName != null and calculationUnitName  != ''" >
                #{calculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitSymbol != null and calculationUnitSymbol  != ''" >
                #{calculationUnitSymbol,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null" >
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null and createNo  != ''" >
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null and createName  != ''" >
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null and modifyNo  != ''" >
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName  != ''" >
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null" >
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null and companyNo != ''" >
                company_no = #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null and companyName != ''" >
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null and branchNo != ''" >
                branch_no = #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null and branchName != ''" >
                branch_name = #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="parentGoodsNo != null and parentGoodsNo != ''" >
                parent_goods_no = #{parentGoodsNo,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and relation_no =  #{relationNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <if test="companyNo != null and companyNo != ''">
                    and company_no = #{companyNo}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and branch_no = #{branchNo}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and goods_no = #{goodsNo,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain" >
        update
            im_warehouse_goods_relation
        set
            <if test="modifyNo != null" >
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and relation_no =  #{relationNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <if test="companyNo != null and companyNo != ''">
                    and company_no = #{companyNo}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and branch_no = #{branchNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and goods_no = #{goodsNo,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>

    <update id="batchLogicDelete" parameterType="java.util.List" >
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update im_warehouse_goods_relation
            set
            <if test="item.modifyNo != null" >
                modify_no = #{item.modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyName != null" >
                modify_name = #{item.modifyName,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyTime != null" >
                modify_time = #{item.modifyTime,jdbcType=TIMESTAMP},
            </if>

            delete_flag = 2
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.relationNo  != null and item.relationNo  != ''">
                        and relation_no =  #{item.relationNo}
                    </if>
                    <if test="item.warehouseNo != null and item.warehouseNo != ''">
                        and warehouse_no = #{item.warehouseNo}
                    </if>
                    <if test="item.companyNo != null and item.companyNo != ''">
                        and company_no = #{item.companyNo}
                    </if>
                    <if test="item.branchNo != null and item.branchNo != ''">
                        and branch_no = #{item.branchNo}
                    </if>
                    <if test="item.storeNo  != null and item.storeNo  != ''">
                        and store_no = #{item.storeNo,jdbcType=VARCHAR}
                    </if>
                    <if test="item.goodsNo  != null and item.goodsNo  != ''">
                        and goods_no = #{item.goodsNo,jdbcType=VARCHAR}
                    </if>
                    <include refid="Base_Column_Where" />
                </trim>
            </where>
        </foreach>
    </update>

    <!-- 1.删除关联的仓库及其下的商品 -->
    <!-- 2.删除仓库关联的商品 -->
    <delete id="logicDeleteByParams" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseGoodsRelationDomain">
        delete from im_warehouse_goods_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationNo  != null and relationNo  != ''">
                    and relation_no =  #{relationNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <if test="companyNo != null and companyNo != ''">
                    and company_no = #{companyNo}
                </if>
                <if test="branchNo != null and branchNo != ''">
                    and branch_no = #{branchNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and goods_no = #{goodsNo,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </delete>

    <select id="selectInventoryWarningGoodsList" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseGoodsRelationVo">
        select
        g.goods_no ,
        g.parent_goods_no ,
        g.goods_name ,
        g.first_attribute_value_name ,
        g.second_attribute_value_name ,
        g.third_attribute_value_name ,
        g.multi_unit_type,
        g.multi_unit_goods_no,
        if(iw.virtual_warehouse_flag = 2, '', iw.warehouse_name) warehouse_name ,
        ifnull(iwgr.stock_num, 0) stock_num
        from
        goods g
        inner join (
        select
        g1.parent_goods_no,
        ifnull(sum(grs.real_stock_num), 0) real_stock_num
        from
        goods g1
        left join goods_real_stock grs on
        grs.goods_no = g1.goods_no
        <where>
            g1.store_no = #{storeNo}
            and g1.inventory_warning_flag = 2
            and g1.delete_flag = 1
        </where>
        group by
        g1.parent_goods_no
        ) t on
        t.parent_goods_no = g.parent_goods_no
        left join
        im_warehouse_goods_relation iwgr on
        iwgr.goods_no = g.goods_no and iwgr.delete_flag = 1
        left join im_warehouse iw on
        iw.warehouse_no = iwgr.warehouse_no
        <where>
            <![CDATA[t.real_stock_num <= #{inventoryWarningNum}]]>
            and g.store_no = #{storeNo}
            and g.series_type != '1001'
            AND g.multi_unit_type != '1002'
        </where>
        order by
        g.inventory_warning_time desc,
        g.parent_goods_no
    </select>

    <update id="updateStockNumZero">
        update
        im_warehouse_goods_relation
        set
        stock_num = 0.00
        where
        goods_no in
        <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>

    </update>
</mapper>
