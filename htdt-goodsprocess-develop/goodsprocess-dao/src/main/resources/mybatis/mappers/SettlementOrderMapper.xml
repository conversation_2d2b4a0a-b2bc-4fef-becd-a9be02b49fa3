<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.SettlementOrderDao">
    <resultMap id="ResultMap" type="cn.htdt.goodsprocess.domain.SettlementOrderDomain">
        <result column="id" property="id"/>
        <result column="settlement_no" property="settlementNo"/>
        <result column="associated_document_no" property="associatedDocumentNo"/>
        <result column="associated_document_type" property="associatedDocumentType"/>
        <result column="status" property="status"/>
        <result column="settlement_amount" property="settlementAmount"/>
        <result column="settlement_time" property="settlementTime"/>
        <result column="settlement_user_no" property="settlementUserNo"/>
        <result column="settlement_user_name" property="settlementUserName"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
        <result column="platform_type" property="platformType"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="create_no" property="createNo"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,settlement_no,associated_document_no,associated_document_type,status,settlement_amount,settlement_time,settlement_user_no,settlement_user_name,company_no,company_name,branch_no,branch_name,platform_type,merchant_no,merchant_name,store_no,store_name,create_no,create_name,create_time,modify_no,modify_name,modify_time,delete_flag
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <sql id="Set_Column">
        <set>
            <if test="settlementNo != null">settlement_no=#{settlementNo},</if>
            <if test="associatedDocumentNo != null">associated_document_no=#{associatedDocumentNo},</if>
            <if test="associatedDocumentType != null">associated_document_type=#{associatedDocumentType},</if>
            <if test="status != null">status=#{status},</if>
            <if test="settlementAmount != null">settlement_amount=#{settlementAmount},</if>
            <if test="settlementTime != null">settlement_time=#{settlementTime},</if>
            <if test="settlementUserNo != null">settlement_user_no=#{settlementUserNo},</if>
            <if test="settlementUserName != null">settlement_user_name=#{settlementUserName},</if>
            <if test="companyNo != null">company_no=#{companyNo},</if>
            <if test="companyName != null">company_name=#{companyName},</if>
            <if test="branchNo != null">branch_no=#{branchNo},</if>
            <if test="branchName != null">branch_name=#{branchName},</if>
            <if test="platformType != null">platform_type=#{platformType},</if>
            <if test="merchantNo != null">merchant_no=#{merchantNo},</if>
            <if test="merchantName != null">merchant_name=#{merchantName},</if>
            <if test="storeNo != null">store_no=#{storeNo},</if>
            <if test="storeName != null">store_name=#{storeName},</if>
            <if test="createNo != null">create_no=#{createNo},</if>
            <if test="createName != null">create_name=#{createName},</if>
            <if test="createTime != null">create_time=#{createTime},</if>
            <if test="modifyNo != null">modify_no=#{modifyNo},</if>
            <if test="modifyName != null">modify_name=#{modifyName},</if>
            <if test="modifyTime != null">modify_time=#{modifyTime},</if>
            <if test="deleteFlag != null">delete_flag=#{deleteFlag},</if>
        </set>
    </sql>

    <select id="getSettlementOrderListForPage" parameterType="cn.htdt.goodsprocess.vo.AtomSettlementOrderVo" resultMap="ResultMap">
        select
            <include refid="Base_Column_List" />
        from settlement_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="settlementNo != null and settlementNo != ''">
                    and settlement_no = #{settlementNo}
                </if>
                <if test="associatedDocumentNo != null and associatedDocumentNo != ''">
                    and associated_document_no = #{associatedDocumentNo}
                </if>
                <if test="associatedDocumentType != null and associatedDocumentType != ''">
                    and associated_document_type = #{associatedDocumentType}
                </if>
                <if test="status != null">
                    and status = #{status}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>

                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="startCreateTime != null">
                    <![CDATA[ and date_format(create_time, '%Y-%m-%d') >= #{startCreateTime} ]]>
                </if>
                <if test="endCreateTime != null">
                    <![CDATA[ and date_format(create_time, '%Y-%m-%d') <= #{endCreateTime} ]]>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
            order by create_time desc
        </where>
    </select>

    <select id="getSettlementOrderDetail" parameterType="cn.htdt.goodsprocess.vo.AtomSettlementOrderVo" resultMap="ResultMap">
        select
            <include refid="Base_Column_List" />
        from settlement_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="settlementNo != null and settlementNo != ''">
                    and settlement_no = #{settlementNo}
                </if>
                <if test="associatedDocumentNo != null and associatedDocumentNo != ''">
                    and associated_document_no = #{associatedDocumentNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateSettlementOrder" parameterType="cn.htdt.goodsprocess.domain.StoreCategoryDomain">
        update
        settlement_order
        <set>
            <if test="associatedDocumentNo != null and associatedDocumentNo != ''">
                associated_document_no = #{associatedDocumentNo},
            </if>
            <if test="associatedDocumentType != null and associatedDocumentType != ''">
                associated_document_type = #{associatedDocumentType},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="merchantNo != null and merchantNo != ''">
                merchant_no = #{merchantNo},
            </if>
            <if test="storeNo != null and storeNo != ''">
                store_no = #{storeNo},
            </if>
            <if test="settlementAmount != null ">
                settlement_amount = #{settlementAmount},
            </if>
            <if test="settlementTime != null ">
                settlement_time = #{settlementTime},
            </if>
            <if test="settlementUserNo != null and settlementUserNo != ''">
                settlement_user_no = #{settlementUserNo},
            </if>
            <if test="settlementUserName != null and settlementUserName != ''">
                settlement_user_name = #{settlementUserName},
            </if>
            <if test="companyNo != null and companyNo != ''">
                company_no = #{companyNo},
            </if>
            <if test="companyName != null and companyName != ''">
                company_name = #{companyName},
            </if>
            <if test="branchNo != null and branchNo != ''">
                branch_no = #{branchNo},
            </if>
            <if test="platformType != null and platformType != ''">
                platform_type = #{platformType},
            </if>
            <if test="branchName != null and branchName != ''">
                branch_name = #{branchName}
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName},
            </if>
            modify_time = NOW()
        </set>
        <where>
            settlement_no = #{settlementNo}
            <include refid="Base_Column_Where"/>
        </where>
    </update>
    
    <update id="cancelSettlementOrder" parameterType="cn.htdt.goodsprocess.domain.StoreCategoryDomain">
        update
        settlement_order
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <if test="settlementNos != null and settlementNos.size() > 0">
                and settlement_no in
                <foreach collection="settlementNos" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="settlementNo != null and settlementNo != ''">
                and settlement_no = #{settlementNo}
            </if>
            <!--结算状态为结算-->
            and status = 2
            <include refid="Base_Column_Where"/>
        </where>
    </update>

    <update id="markSettlementOrder" parameterType="cn.htdt.goodsprocess.domain.StoreCategoryDomain">
        update
        settlement_order
        <set>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="settlementAmount != null ">
                settlement_amount = #{settlementAmount},
            </if>
            <if test="settlementUserNo != null and settlementUserNo != ''">
                settlement_user_no = #{settlementUserNo},
            </if>
            <if test="settlementUserName != null and settlementUserName != ''">
                settlement_user_name = #{settlementUserName},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName},
            </if>
            modify_time = NOW(),
            settlement_time = NOW()
        </set>
        <where>
            <if test="settlementNos != null and settlementNos.size() > 0">
                and settlement_no in
                <foreach collection="settlementNos" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="settlementNo != null and settlementNo != ''">
                and settlement_no = #{settlementNo}
            </if>
            <!--结算状态为未结算-->
            and status = 1
            <include refid="Base_Column_Where"/>
        </where>
    </update>


</mapper>