<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htdt.goodsprocess.dao.PurchaseReturnOrderDao">
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.PurchaseReturnOrderDomain">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="return_code" property="returnCode" jdbcType="VARCHAR"/>
        <result column="purchase_code" property="purchaseCode" jdbcType="VARCHAR"/>
        <result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
        <result column="supplier_code" property="supplierCode" jdbcType="VARCHAR"/>
        <result column="supplier_name" property="supplierName" jdbcType="VARCHAR"/>
        <result column="return_date" property="returnDate" jdbcType="TIMESTAMP"/>
        <result column="purchase_date" property="purchaseDate" jdbcType="TIMESTAMP"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="attach_name" property="attachName" jdbcType="VARCHAR"/>
        <result column="attach_path" property="attachPath" jdbcType="VARCHAR"/>
        <result column="receive_contact_name" property="receiveContactName" jdbcType="VARCHAR"/>
        <result column="receive_contact_mobile" property="receiveContactMobile" jdbcType="VARCHAR"/>
        <result column="receive_province_code" property="receiveProvinceCode" jdbcType="VARCHAR"/>
        <result column="receive_province_name" property="receiveProvinceName" jdbcType="VARCHAR"/>
        <result column="receive_city_code" property="receiveCityCode" jdbcType="VARCHAR"/>
        <result column="receive_city_name" property="receiveCityName" jdbcType="VARCHAR"/>
        <result column="receive_district_code" property="receiveDistrictCode" jdbcType="VARCHAR"/>
        <result column="receive_district_name" property="receiveDistrictName" jdbcType="VARCHAR"/>
        <result column="receive_town_code" property="receiveTownCode" jdbcType="VARCHAR"/>
        <result column="receive_town_name" property="receiveTownName" jdbcType="VARCHAR"/>
        <result column="receive_detail_address" property="receiveDetailAddress" jdbcType="VARCHAR"/>
        <result column="send_contact_name" property="sendContactName" jdbcType="VARCHAR"/>
        <result column="send_contact_mobile" property="sendContactMobile" jdbcType="VARCHAR"/>
        <result column="send_province_code" property="sendProvinceCode" jdbcType="VARCHAR"/>
        <result column="send_province_name" property="sendProvinceName" jdbcType="VARCHAR"/>
        <result column="send_city_code" property="sendCityCode" jdbcType="VARCHAR"/>
        <result column="send_city_name" property="sendCityName" jdbcType="VARCHAR"/>
        <result column="send_district_code" property="sendDistrictCode" jdbcType="VARCHAR"/>
        <result column="send_district_name" property="sendDistrictName" jdbcType="VARCHAR"/>
        <result column="send_town_code" property="sendTownCode" jdbcType="VARCHAR"/>
        <result column="send_town_name" property="sendTownName" jdbcType="VARCHAR"/>
        <result column="send_detail_address" property="sendDetailAddress" jdbcType="VARCHAR"/>
        <result column="return_status" property="returnStatus" jdbcType="INTEGER"/>
        <result column="total_return_num" property="totalReturnNum" jdbcType="DECIMAL"/>
        <result column="total_purchase_price" property="totalPurchasePrice" jdbcType="DECIMAL"/>
        <result column="total_return_price" property="totalReturnPrice" jdbcType="DECIMAL"/>
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="merchant_name" property="merchantName" jdbcType="VARCHAR"/>
        <result column="store_no" property="storeNo" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="disable_flag" property="disableFlag" jdbcType="TINYINT"/>
        <result column="create_no" property="createNo" jdbcType="VARCHAR"/>
        <result column="create_name" property="createName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_no" property="modifyNo" jdbcType="VARCHAR"/>
        <result column="modify_name" property="modifyName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="company_no" property="companyNo" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="branch_no" property="branchNo" jdbcType="VARCHAR"/>
        <result column="branch_name" property="branchName" jdbcType="VARCHAR"/>
        <result column="platform_type" property="platformType" jdbcType="VARCHAR"/>
        <result column="ds_receive_contact_mobile" property="dsReceiveContactMobile" jdbcType="VARCHAR"/>
        <result column="ds_receive_detail_address" property="dsReceiveDetailAddress" jdbcType="VARCHAR"/>
        <result column="ds_send_contact_mobile" property="dsSendContactMobile" jdbcType="VARCHAR"/>
        <result column="ds_send_detail_address" property="dsSendDetailAddress" jdbcType="VARCHAR"/>
    </resultMap>

    <resultMap id="PurchaseReturnOrderResultMap" extends="BaseResultMap"
               type="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderVo">
        <result column="goods_name_compose" property="goodName" jdbcType="VARCHAR"/>
        <collection property="returnOrderGoodList" select="selectPurchaseReturnOrderGood" column="return_code"
                    ofType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodDetailVo">
        </collection>
    </resultMap>


    <sql id="Base_Column_List">
    id, return_code, purchase_code, source_type, supplier_code, supplier_name, return_date, 
    purchase_date, remark, attach_name, attach_path, receive_contact_name, receive_contact_mobile, 
    receive_province_code, receive_province_name, receive_city_code, receive_city_name, 
    receive_district_code, receive_district_name, receive_town_code, receive_town_name, 
    receive_detail_address, send_contact_name, send_contact_mobile, send_province_code, 
    send_province_name, send_city_code, send_city_name, send_district_code, send_district_name, 
    send_town_code, send_town_name, send_detail_address, return_status,
    total_return_num, total_purchase_price, total_return_price, merchant_no, merchant_name, 
    store_no, store_name, disable_flag, create_no, create_name, create_time, modify_no, 
    modify_name, modify_time, delete_flag, company_no, company_name, branch_no, branch_name,platform_type,
    ds_receive_contact_mobile, ds_receive_detail_address, ds_send_contact_mobile, ds_send_detail_address,purchase_type
  </sql>

    <sql id="Base_Purchase_Order_Column_List">
    p.return_code,
    p.return_date,
	p.purchase_code,
	p.supplier_code,
	p.supplier_name,
	SUM( g.returned_count) total_return_num,
	SUM( g.purchase_unit_price * g.returned_count ) total_purchase_price,
	p.total_return_price,
	p.receive_contact_name,
	p.receive_contact_mobile,
    p.send_contact_name,
	p.send_contact_mobile,
    p.create_name,
	p.create_time,
    p.return_status,
	p.ds_receive_contact_mobile,
	p.ds_send_contact_mobile,
	GROUP_CONCAT(g.goods_name SEPARATOR ',' ) AS goods_name_compose,
    p.purchase_type
  </sql>

    <!--状态标识-->
    <sql id="Base_Column_Where">
        and delete_flag = 1
  </sql>

    <sql id="Base_Column_Where_Purchase_Return">
        <if test="storeNo  != null and storeNo  != ''">
            and p.store_no = #{storeNo,jdbcType=VARCHAR}
        </if>
        <if test="storeName != null and storeName != ''">
            and p.store_name = #{storeName}
        </if>
        <if test="merchantNo  != null and merchantNo  != ''">
            and p.merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="merchantName != null and merchantName != ''">
            and p.merchant_name = #{merchantName}
        </if>
        <if test="platformType != null and platformType != ''">
            AND P.platform_type= #{platformType,jdbcType=VARCHAR}
        </if>
        <if test="supplierCode != null and supplierCode != ''">
            and (p.supplier_code like CONCAT(CONCAT('%', #{supplierCode}),'%') or p.supplier_name like
            CONCAT(CONCAT('%', #{supplierCode}),'%'))
        </if>
        <if test="purchaseCode != null and purchaseCode != ''">
            and p.purchase_code like CONCAT(CONCAT('%', #{purchaseCode}),'%')
        </if>
        <if test="returnCode != null and returnCode != ''">
            and p.return_code like CONCAT(CONCAT('%', #{returnCode}),'%')
        </if>
        <if test="goodName != null and goodName != ''">
            and (g.goods_no like CONCAT(CONCAT('%', #{goodName}),'%') or g.goods_name like CONCAT(CONCAT('%',
            #{goodName}),'%'))
        </if>

        <if test="startTime != null and startTime != '' and  endTime != null and endTime != ''">
            <![CDATA[and p.create_time  between #{startTime} and #{endTime}]]>
        </if>
        <!-- 退货日期条件过滤 -->
        <if test="returnDateStartStr != null and returnDateStartStr != ''">
            <![CDATA[
                and  date_format(p.return_date, '%Y-%m-%d')  >= #{returnDateStartStr}
             ]]>
        </if>
        <if test="returnDateEndStr != null and returnDateEndStr != ''">
            <![CDATA[
                and date_format(p.return_date, '%Y-%m-%d') <= #{returnDateEndStr}
            ]]>
        </if>
        <if test="returnStatus != null">
            and p.return_status=#{returnStatus}
        </if>
        and p.delete_flag = 1
    </sql>
    <!--根据退货单编码 查询退货单详情-->
    <select id="selectReturnOrderBaseInfoByReturnCode" resultMap="BaseResultMap" parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from purchase_return_order
        where return_code = #{returnOrderCode,jdbcType=VARCHAR}
    </select>


    <!--更新采购退货单状态-->
    <update id="updatePurchaseReturnOrderStatus" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderDomain">
        update purchase_return_order
        <set>
            <if test="returnStatus != null and returnStatus!=''">
                return_status = #{returnStatus,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and return_code = #{returnCode,jdbcType=VARCHAR}
                </if>
            </trim>
        </where>
    </update>

    <!--更新采购退货单状态-->
    <update id="updatePurchaseReturnOrderDeleteFlag"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderDomain">
        update purchase_return_order
        <set>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="returnCode != null and returnCode != ''">
                    and return_code = #{returnCode,jdbcType=VARCHAR}
                </if>
            </trim>
        </where>
    </update>

    <select id="selectReturnCountByPurchaseCode" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
        count(1)
        from purchase_return_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    and purchase_code= #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>
    <!--勿删-->
    <select id="selectPurchaseReturnOrderGood" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderGoodDetailVo"
            parameterType="java.lang.String">
        select
            goods_name as goodsName,
            return_request_count as returnedCount,
            purchase_unit_price as purchaseUnitPrice ,
            purchase_unit_price * returned_count as purchasePrice,
            returned_count as  warehouseOutCount
        from
            purchase_return_order_goods
        where
            return_code=#{return_code}
        order by
            create_time desc
    </select>

    <select id="getAllPurchaseReturnOrderPageByParams" resultMap="PurchaseReturnOrderResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderVo">
        select
        <include refid="Base_Purchase_Order_Column_List"/>
        from
        purchase_return_order p
        LEFT JOIN purchase_return_order_goods g ON p.return_code = g.return_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where_Purchase_Return"/>
                <if test="goodsNameKeyWord != null and goodsNameKeyWord != ''">
                    and (
                    g.goods_name like CONCAT(CONCAT('%', #{goodsNameKeyWord}),'%')
                    or g.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsNameKeyWord}),'%')
                    or g.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsNameKeyWord}),'%')
                    )
                </if>
            </trim>
        </where>
        group by p.return_code,
        p.return_date,
        p.purchase_code,
        p.supplier_code,
        p.supplier_name,
        p.total_return_num,
        p.total_purchase_price,
        p.total_return_price,
        p.receive_contact_name,
        p.receive_contact_mobile,
        p.send_contact_name,
        p.send_contact_mobile,
        p.create_name,
        p.create_time,
        p.return_status,
        p.ds_receive_contact_mobile,
        p.ds_send_contact_mobile
        order by p.create_time desc
    </select>

    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderDomain">
        insert into purchase_return_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="returnCode != null">
                return_code,
            </if>
            <if test="purchaseCode != null">
                purchase_code,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="supplierCode != null">
                supplier_code,
            </if>
            <if test="supplierName != null">
                supplier_name,
            </if>
            <if test="returnDate != null">
                return_date,
            </if>
            <if test="purchaseDate != null">
                purchase_date,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="attachName != null">
                attach_name,
            </if>
            <if test="attachPath != null">
                attach_path,
            </if>
            <if test="receiveContactName != null">
                receive_contact_name,
            </if>
            <if test="receiveContactMobile != null">
                receive_contact_mobile,
            </if>
            <if test="dsReceiveContactMobile != null">
                ds_receive_contact_mobile,
            </if>
            <if test="receiveProvinceCode != null">
                receive_province_code,
            </if>
            <if test="receiveProvinceName != null">
                receive_province_name,
            </if>
            <if test="receiveCityCode != null">
                receive_city_code,
            </if>
            <if test="receiveCityName != null">
                receive_city_name,
            </if>
            <if test="receiveDistrictCode != null">
                receive_district_code,
            </if>
            <if test="receiveDistrictName != null">
                receive_district_name,
            </if>
            <if test="receiveTownCode != null">
                receive_town_code,
            </if>
            <if test="receiveTownName != null">
                receive_town_name,
            </if>
            <if test="receiveDetailAddress != null">
                receive_detail_address,
            </if>
            <if test="dsReceiveDetailAddress != null">
                ds_receive_detail_address,
            </if>
            <if test="sendContactName != null">
                send_contact_name,
            </if>
            <if test="sendContactMobile != null">
                send_contact_mobile,
            </if>
            <if test="dsSendContactMobile != null">
                ds_send_contact_mobile,
            </if>
            <if test="sendProvinceCode != null">
                send_province_code,
            </if>
            <if test="sendProvinceName != null">
                send_province_name,
            </if>
            <if test="sendCityCode != null">
                send_city_code,
            </if>
            <if test="sendCityName != null">
                send_city_name,
            </if>
            <if test="sendDistrictCode != null">
                send_district_code,
            </if>
            <if test="sendDistrictName != null">
                send_district_name,
            </if>
            <if test="sendTownCode != null">
                send_town_code,
            </if>
            <if test="sendTownName != null">
                send_town_name,
            </if>
            <if test="sendDetailAddress != null">
                send_detail_address,
            </if>
            <if test="dsSendDetailAddress != null">
                ds_send_detail_address,
            </if>
            <if test="returnStatus != null">
                return_status,
            </if>

            <if test="totalReturnNum != null">
                total_return_num,
            </if>
            <if test="totalPurchasePrice != null">
                total_purchase_price,
            </if>
            <if test="totalReturnPrice != null">
                total_return_price,
            </if>
            <if test="merchantNo != null">
                merchant_no,
            </if>
            <if test="merchantName != null">
                merchant_name,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
            <if test="disableFlag != null">
                disable_flag,
            </if>
            <if test="createNo != null">
                create_no,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyNo != null">
                modify_no,
            </if>
            <if test="modifyName != null">
                modify_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="companyNo != null">
                company_no,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="branchNo != null">
                branch_no,
            </if>
            <if test="branchName != null">
                branch_name,
            </if>
            <if test="platformType != null">
                platform_type,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="returnCode != null">
                #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="purchaseCode != null">
                #{purchaseCode,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="returnDate != null">
                #{returnDate,jdbcType=TIMESTAMP},
            </if>
            <if test="purchaseDate != null">
                #{purchaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="attachName != null">
                #{attachName,jdbcType=VARCHAR},
            </if>
            <if test="attachPath != null">
                #{attachPath,jdbcType=VARCHAR},
            </if>
            <if test="receiveContactName != null">
                #{receiveContactName,jdbcType=VARCHAR},
            </if>
            <if test="receiveContactMobile != null">
                #{receiveContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="dsReceiveContactMobile != null">
                #{dsReceiveContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="receiveProvinceCode != null">
                #{receiveProvinceCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveProvinceName != null">
                #{receiveProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="receiveCityCode != null">
                #{receiveCityCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveCityName != null">
                #{receiveCityName,jdbcType=VARCHAR},
            </if>
            <if test="receiveDistrictCode != null">
                #{receiveDistrictCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveDistrictName != null">
                #{receiveDistrictName,jdbcType=VARCHAR},
            </if>
            <if test="receiveTownCode != null">
                #{receiveTownCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveTownName != null">
                #{receiveTownName,jdbcType=VARCHAR},
            </if>
            <if test="receiveDetailAddress != null">
                #{receiveDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="dsReceiveDetailAddress != null">
                #{dsReceiveDetailAddress, jdbcType=VARCHAR},
            </if>
            <if test="sendContactName != null">
                #{sendContactName,jdbcType=VARCHAR},
            </if>
            <if test="sendContactMobile != null">
                #{sendContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="dsSendContactMobile != null">
                #{dsSendContactMobile, jdbcType=VARCHAR},
            </if>
            <if test="sendProvinceCode != null">
                #{sendProvinceCode,jdbcType=VARCHAR},
            </if>
            <if test="sendProvinceName != null">
                #{sendProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="sendCityCode != null">
                #{sendCityCode,jdbcType=VARCHAR},
            </if>
            <if test="sendCityName != null">
                #{sendCityName,jdbcType=VARCHAR},
            </if>
            <if test="sendDistrictCode != null">
                #{sendDistrictCode,jdbcType=VARCHAR},
            </if>
            <if test="sendDistrictName != null">
                #{sendDistrictName,jdbcType=VARCHAR},
            </if>
            <if test="sendTownCode != null">
                #{sendTownCode,jdbcType=VARCHAR},
            </if>
            <if test="sendTownName != null">
                #{sendTownName,jdbcType=VARCHAR},
            </if>
            <if test="sendDetailAddress != null">
                #{sendDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="dsSendDetailAddress != null">
                #{dsSendDetailAddress, jdbcType=VARCHAR},
            </if>
            <if test="returnStatus != null">
                #{returnStatus,jdbcType=INTEGER},
            </if>

            <if test="totalReturnNum != null">
                #{totalReturnNum,jdbcType=DECIMAL},
            </if>
            <if test="totalPurchasePrice != null">
                #{totalPurchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="totalReturnPrice != null">
                #{totalReturnPrice,jdbcType=DECIMAL},
            </if>
            <if test="merchantNo != null">
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null">
                #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null">
                #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null">
                #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="platformType != null">
                #{platformType,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderDomain">
        update purchase_return_order
        <set>
            <if test="returnCode != null">
                return_code = #{returnCode,jdbcType=VARCHAR},
            </if>
            <if test="purchaseCode != null">
                purchase_code = #{purchaseCode,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                supplier_code = #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                supplier_name = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="returnDate != null">
                return_date = #{returnDate,jdbcType=TIMESTAMP},
            </if>
            <if test="purchaseDate != null">
                purchase_date = #{purchaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="attachName != null">
                attach_name = #{attachName,jdbcType=VARCHAR},
            </if>
            <if test="attachPath != null">
                attach_path = #{attachPath,jdbcType=VARCHAR},
            </if>
            <if test="receiveContactName != null">
                receive_contact_name = #{receiveContactName,jdbcType=VARCHAR},
            </if>
            <if test="receiveContactMobile != null">
                receive_contact_mobile = #{receiveContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="dsReceiveContactMobile != null">
                ds_receive_contact_mobile = #{dsReceiveContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="receiveProvinceCode != null">
                receive_province_code = #{receiveProvinceCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveProvinceName != null">
                receive_province_name = #{receiveProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="receiveCityCode != null">
                receive_city_code = #{receiveCityCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveCityName != null">
                receive_city_name = #{receiveCityName,jdbcType=VARCHAR},
            </if>
            <if test="receiveDistrictCode != null">
                receive_district_code = #{receiveDistrictCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveDistrictName != null">
                receive_district_name = #{receiveDistrictName,jdbcType=VARCHAR},
            </if>
            <if test="receiveTownCode != null">
                receive_town_code = #{receiveTownCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveTownName != null">
                receive_town_name = #{receiveTownName,jdbcType=VARCHAR},
            </if>
            <if test="receiveDetailAddress != null">
                receive_detail_address = #{receiveDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="dsReceiveDetailAddress != null">
                ds_receive_detail_address = #{dsReceiveDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="sendContactName != null">
                send_contact_name = #{sendContactName,jdbcType=VARCHAR},
            </if>
            <if test="sendContactMobile != null">
                send_contact_mobile = #{sendContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="dsSendContactMobile != null">
                ds_send_contact_mobile = #{dsSendContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="sendProvinceCode != null">
                send_province_code = #{sendProvinceCode,jdbcType=VARCHAR},
            </if>
            <if test="sendProvinceName != null">
                send_province_name = #{sendProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="sendCityCode != null">
                send_city_code = #{sendCityCode,jdbcType=VARCHAR},
            </if>
            <if test="sendCityName != null">
                send_city_name = #{sendCityName,jdbcType=VARCHAR},
            </if>
            <if test="sendDistrictCode != null">
                send_district_code = #{sendDistrictCode,jdbcType=VARCHAR},
            </if>
            <if test="sendDistrictName != null">
                send_district_name = #{sendDistrictName,jdbcType=VARCHAR},
            </if>
            <if test="sendTownCode != null">
                send_town_code = #{sendTownCode,jdbcType=VARCHAR},
            </if>
            <if test="sendTownName != null">
                send_town_name = #{sendTownName,jdbcType=VARCHAR},
            </if>
            <if test="sendDetailAddress != null">
                send_detail_address = #{sendDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="dsSendDetailAddress != null">
                ds_send_detail_address = #{dsSendDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="returnStatus != null">
                return_status = #{returnStatus,jdbcType=INTEGER},
            </if>

            <if test="totalReturnNum != null">
                total_return_num = #{totalReturnNum,jdbcType=DECIMAL},
            </if>
            <if test="totalPurchasePrice != null">
                total_purchase_price = #{totalPurchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="totalReturnPrice != null">
                total_return_price = #{totalReturnPrice,jdbcType=DECIMAL},
            </if>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null">
                company_no = #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null">
                branch_no = #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null">
                branch_name = #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="platformType != null">
                platform_type= #{platformType,jdbcType=VARCHAR},
            </if>
            <if test="purchaseType != null">
                purchase_type= #{purchaseType,jdbcType=TINYINT}
            </if>
        </set>
        where return_code = #{returnCode,jdbcType=VARCHAR}
    </update>

    <select id="selectByParams" parameterType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderDomain"
            resultType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderDomain">
        select
        <include refid="Base_Column_List"/>
        from
        purchase_return_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode !=''">
                    and purchase_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
            </trim>
        </where>
    </select>

    <select id="selectOrderCountByStatus" resultType="cn.htdt.goodsprocess.vo.AtomResOrderStatusCountVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderVo">
        select
            return_status orderStatus,
            count(1) as orderCount
        from purchase_return_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo  != null and storeNo  != ''">
                    and store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="platformType != null and platformType != ''">
                    platform_type= #{platformType,jdbcType=VARCHAR}
                </if>
                <if test="returnStatusList != null and returnStatusList.size > 0">
                    and return_status in
                    <foreach collection="returnStatusList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and delete_flag = 1
            </trim>
        </where>
        group by return_status
    </select>

    <select id="selectPurchaseReturnOrder" parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseReturnOrderVo"
            resultType="cn.htdt.goodsprocess.domain.PurchaseReturnOrderDomain">
        select
            pro.return_code as returnCode,
            pro.purchase_code as purchaseCode,
            pro.supplier_code as supplierCode,
            pro.supplier_name as supplierName,
            pro.return_date as returnDate,
            pro.return_status as returnStatus,
            pro.platform_type as platformType,
            pro.create_time as createTime,
            pro.purchase_type as purchaseType
        from purchase_return_order pro
        <if test="queryStr != null and queryStr != ''">
            left join purchase_return_order_goods prog on prog.return_code = pro.return_code and prog.delete_flag = 1
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo  != null and storeNo  != ''">
                    and pro.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="storeName != null and storeName != ''">
                    and pro.store_name = #{storeName}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and pro.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantName != null and merchantName != ''">
                    and pro.merchant_name = #{merchantName}
                </if>
                <if test="returnCode != null and returnCode !=''">
                    and pro.return_code = #{returnCode,jdbcType=VARCHAR}
                </if>
                <if test="purchaseCode != null and purchaseCode !=''">
                    and pro.purchase_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <if test="returnStatus != null">
                    and pro.return_status = #{returnStatus,jdbcType=VARCHAR}
                </if>
                <if test="queryStr != null and queryStr != ''">
                    and (prog.goods_name LIKE CONCAT('%', #{queryStr,jdbcType=VARCHAR}, '%')
                    or pro.supplier_name LIKE CONCAT('%',#{queryStr,jdbcType=VARCHAR},'%')
                    or pro.purchase_code = #{queryStr,jdbcType=VARCHAR})
                </if>
                and pro.delete_flag = 1
            </trim>
        </where>
        GROUP BY pro.return_code
        ORDER BY pro.return_date DESC, pro.create_time DESC
    </select>

</mapper>