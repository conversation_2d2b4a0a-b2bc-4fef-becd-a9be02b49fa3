<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsGroupsDao">
    <resultMap type="cn.htdt.goodsprocess.domain.GoodsGroupsDomain" id="GoodsGroupsResult">
        <result property="id"    column="id"    />
        <result property="goodsGroupsNo"    column="goods_groups_no"    />
        <result property="merchantGoodsGroupsNo"    column="merchant_goods_groups_no"    />
        <result property="goodsGroupsName"    column="goods_groups_name"    />
        <result property="warehouseFlag"    column="warehouse_flag"    />
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="goods_source_type" property="goodsSourceType"/>
        <result property="realStockNum"    column="real_stock_num"    />
        <result property="calculationUnitNo"    column="calculation_unit_no"    />
        <result property="relatedGoodsNames"    column="related_goods_names"    />
        <result property="effectiveRegion"    column="effective_region"    />
        <result property="effectiveRegionNames"    column="effective_region_names"    />
        <result property="deleteFlag"    column="delete_flag"    />
        <result property="createNo"    column="create_no"    />
        <result property="createName"    column="create_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="modifyNo"    column="modify_no"    />
        <result property="modifyName"    column="modify_name"    />
        <result property="modifyTime"    column="modify_time"    />
    </resultMap>
    <resultMap id="goodsGroupsDetailResultMap" extends="GoodsGroupsResult"
               type="cn.htdt.goodscenter.dto.response.AtomResGoodsGroupsDTO">
        <result property="goodsNo"    column="goods_no"    />
        <collection property="groupsRelatedGoodsList" select="selectGoodsGroupsRelatedGoods" column="goods_groups_no"
                    ofType="cn.htdt.goodscenter.dto.response.AtomResGoodsGroupsRelatedGoodsDTO">
        </collection>
    </resultMap>
    <!--勿删-->
    <select id="selectGoodsGroupsRelatedGoods" resultType="cn.htdt.goodscenter.dto.response.AtomResGoodsGroupsRelatedGoodsDTO"
            parameterType="java.lang.String">
       select id, goods_no, goods_groups_no, goods_form, goods_type,
       goods_name,attribute_names, calculation_unit_no,
       calculation_unit_symbol,calculation_unit_name,
       goods_mapping_stock_num, goods_groups_mapping_stock_num,
       create_no, create_name, create_time, modify_no, delete_flag,
       modify_name, modify_time, category_no, category_name, goods_model from goods_groups_related_goods
        where
            goods_groups_no=#{goodsGroupsNo}
        order by
            create_time desc
    </select>
    <sql id="selectGoodsGroupsVo">
        select id, goods_groups_no, goods_groups_name, warehouse_flag, real_stock_num,merchant_no,
        merchant_name,store_no,store_name,goods_source_type, calculation_unit_no,merchant_goods_groups_no,
        related_goods_names, effective_region, effective_region_names,
        delete_flag, create_no, create_name, create_time, modify_no,
        modify_name, modify_time from goods_groups
    </sql>
    <sql id="Base_GoodsGroups_Column_List">
    gg.id,
    gg.goods_groups_no,
    gg.merchant_goods_groups_no,
    gg.goods_groups_name,
    gg.warehouse_flag,
    gg.real_stock_num,
    gg.merchant_no,
    gg.merchant_name,
    gg.store_no,
    gg.store_name,
    gg.goods_source_type,
    gg.calculation_unit_no,
    gg.related_goods_names,
    gg.effective_region,
    gg.effective_region_names,
    gg.delete_flag,
    gg.create_no,
    gg.create_name,
    gg.create_time,
    gg.modify_no,
    gg.modify_name,
    gg.modify_time
  </sql>
    <sql id="Base_Column_Where_GoodsGroups">
        <if test="goodsGroupsNo != null  and goodsGroupsNo != ''"> and gg.goods_groups_no = #{goodsGroupsNo}</if>
        <if test="merchantGoodsGroupsNo != null  and merchantGoodsGroupsNo != ''"> and gg.merchant_goods_groups_no = #{merchantGoodsGroupsNo}</if>
        <if test="goodsGroupsName != null  and goodsGroupsName != ''"> and gg.goods_groups_name like concat('%', #{goodsGroupsName}, '%')</if>
        <if test="relatedGoodsNames != null  and relatedGoodsNames != ''"> and gg.related_goods_names like concat('%', #{relatedGoodsNames}, '%')</if>
        <if test="warehouseFlag != null "> and gg.warehouse_flag = #{warehouseFlag}</if>
        <if test="calculationUnitNo != null  and calculationUnitNo != ''"> and gg.calculation_unit_no = #{calculationUnitNo}</if>
        and gg.delete_flag = 1
    </sql>

    <select id="selectGoodsGroupsByGoodsNo" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo" resultMap="GoodsGroupsResult">
        select <include refid="Base_GoodsGroups_Column_List"/>
        from goods_groups gg
        inner join goods_groups_related_goods ggrg on gg.goods_groups_no = ggrg.goods_groups_no
        where gg.delete_flag = 1
        and ggrg.goods_no = #{goodsNo}
        <if test="merchantNo != null  and merchantNo != ''"> and gg.merchant_no = #{merchantNo}</if>
        <if test="storeNo != null  and storeNo != ''"> and gg.store_no = #{storeNo}</if>
    </select>

    <select id="selectGoodsGroupsByGoodsNos" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo" resultMap="goodsGroupsDetailResultMap">
        select <include refid="Base_GoodsGroups_Column_List"/>
        , ggrg.goods_no
        from goods_groups gg
        inner join goods_groups_related_goods ggrg on gg.goods_groups_no = ggrg.goods_groups_no
        where gg.delete_flag = 1
        <if test="goodsNos != null and goodsNos.size() > 0">
            and ggrg.goods_no IN
            <foreach collection="goodsNos" item="item" index="index"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="goodsNo != null  and goodsNo != ''"> and ggrg.goods_no = #{goodsNo}</if>
        <if test="merchantNo != null  and merchantNo != ''"> and gg.merchant_no = #{merchantNo}</if>
        <if test="storeNo != null  and storeNo != ''"> and gg.store_no = #{storeNo}</if>
    </select>

    <select id="selectGoodsGroupsBySubGoodsGroupsNo" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo" resultMap="GoodsGroupsResult">
        select <include refid="Base_GoodsGroups_Column_List"/>
            from goods_groups gg
            where gg.goods_groups_no in(select merchant_goods_groups_no from goods_groups where goods_groups_no = #{goodsGroupsNo})
    </select>

    <select id="selectWarehouseAllocationOrderParams" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo" resultMap="GoodsGroupsResult">
        SELECT
            <include refid="Base_GoodsGroups_Column_List"/>,
            (IFNULL( r.deliver_stock_num, 0 ) + IFNULL( r.freeze_stock_num, 0 )) AS stockNum,
            r.real_stock_num as realStockNum
        FROM goods_groups gg
        LEFT JOIN goods_real_stock r ON r.goods_no = gg.merchant_goods_groups_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsGroupsNoList != null and goodsGroupsNoList.size() > 0">
                    and gg.goods_groups_no IN
                    <foreach collection="goodsGroupsNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and gg.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="getGoodsGroupsDetail" resultMap="goodsGroupsDetailResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo">
        select
        ggs.id,
        ggs.goods_groups_no,
        ggs.merchant_goods_groups_no,
        ggs.goods_groups_name,
        ggs.warehouse_flag,
        ggs.merchant_no,
        ggs.merchant_name,
        ggs.store_no,
        ggs.store_name,
        ggs.goods_source_type,
        ggs.calculation_unit_no,
        ggs.related_goods_names,
        ggs.effective_region,
        ggs.effective_region_names,
        ggs.delete_flag,
        ggs.create_no,
        ggs.create_name,
        ggs.create_time,
        ggs.modify_no,
        ggs.modify_name,
        ggs.modify_time,grs.real_stock_num from goods_groups ggs
        left join goods_real_stock grs on grs.goods_no = ggs.goods_groups_no
        <where>
            ggs.delete_flag = 1
            <if test="goodsGroupsNo != null  and goodsGroupsNo != ''"> and ggs.goods_groups_no = #{goodsGroupsNo}</if>
        </where>
    </select>

    <select id="selectGoodsGroupsList" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo" resultMap="GoodsGroupsResult">
        <include refid="selectGoodsGroupsVo"/>
        <where>
            delete_flag = 1
            <if test="goodsGroupsNo != null  and goodsGroupsNo != ''"> and goods_groups_no = #{goodsGroupsNo}</if>
            <if test="merchantGoodsGroupsNo != null  and merchantGoodsGroupsNo != ''"> and merchant_goods_groups_no = #{merchantGoodsGroupsNo}</if>
            <if test="goodsGroupsName != null  and goodsGroupsName != ''"> and goods_groups_name like concat('%', #{goodsGroupsName}, '%')</if>
            <if test="bossAppGoodsSearchKey != null  and bossAppGoodsSearchKey != ''"> and goods_groups_name like concat('%', #{bossAppGoodsSearchKey}, '%')</if>
            <if test="relatedGoodsNames != null  and relatedGoodsNames != ''"> and related_goods_names like concat('%', #{relatedGoodsNames}, '%')</if>
            <if test="warehouseFlag != null "> and warehouse_flag = #{warehouseFlag}</if>
            <if test="calculationUnitNo != null  and calculationUnitNo != ''"> and calculation_unit_no = #{calculationUnitNo}</if>
            <if test="createNo != null  and createNo != ''"> and create_no = #{createNo}</if>
            <if test="modifyNo != null  and modifyNo != ''"> and modify_no = #{modifyNo}</if>
            <if test="merchantNo != null  and merchantNo != ''"> and merchant_no = #{merchantNo}</if>
            <if test="storeNo != null  and storeNo != ''"> and store_no = #{storeNo}</if>
            <if test="ucRegionListStr != null and ucRegionListStr != ''">
                and effective_region  REGEXP ( #{ucRegionListStr} )
            </if>
            <!--平台身份查询全部列表（包括平台、商家、店铺自建） -->
            <if test="queryType==1001">
                and goods_source_type in ('1001','1002','1003','1005')
            </if>
            <!--平台身份查询全部列表（包括平台自建、商家自建、店铺自建、中台同步）-->
            <if test="queryType==1010">
                and g.goods_source_type in ('1001','1002','1003','1005')
            </if>
            <!--平台身份查询平台列表（包括平台自建） -->
            <if test="queryType==1002">
                and goods_source_type in ('1001')
            </if>
            <!--平台身份查询商家/店铺列表（包括商家、店铺自建） -->
            <if test="queryType==1003">
                and goods_source_type in ('1002','1003','1005')
            </if>
            <!--商家身份查询商家列表（包括商家自建） -->
            <if test="queryType==1004">
                and goods_source_type in ('1002','1005')
            </if>
            <!--商家身份查询店铺列表（包括店铺自建） -->
            <if test="queryType==1005">
                and goods_source_type in ('1003')
            </if>
            <!--查询店铺自建商品列表 -->
            <if test="queryType==1013">
                and goods_source_type in ('1003')
            </if>
            <!--店铺身份查询列表（包括商家分发、店铺自建） -->
            <if test="queryType==1006">
                and goods_source_type in ('1003')
            </if>
            <if test="notExistGoodsGroups != null  and notExistGoodsGroups.size() > 0">
                and goods_groups_no not in
                <foreach item="goodsGroupsNo" collection="notExistGoodsGroups" open="(" separator="," close=")">
                    #{goodsGroupsNo}
                </foreach>
            </if>
            order by modify_time desc
        </where>
    </select>

    <select id="getNoWarehouseGoodsPage" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo" resultMap="GoodsGroupsResult">
        select ggs.id, ggs.goods_groups_no, ggs.goods_groups_name, ggs.warehouse_flag, ggs.merchant_no,ggs.merchant_goods_groups_no,
        ggs.merchant_name,ggs.store_no,ggs.store_name,ggs.goods_source_type, ggs.calculation_unit_no,
        ggs.related_goods_names, ggs.effective_region, ggs.effective_region_names,
        ggs.delete_flag, ggs.create_no, ggs.create_name, ggs.create_time, ggs.modify_no,
        ggs.modify_name, ggs.modify_time, grs.real_stock_num from goods_groups ggs
        left join goods_real_stock grs on grs.goods_no = ggs.goods_groups_no
        <where>
            ggs.delete_flag = 1
            <if test="goodsGroupsNo != null  and goodsGroupsNo != ''"> and ggs.goods_groups_no = #{goodsGroupsNo}</if>
            <if test="goodsGroupsName != null  and goodsGroupsName != ''"> and ggs.goods_groups_name like concat('%', #{goodsGroupsName}, '%')</if>
            <if test="bossAppGoodsSearchKey != null  and bossAppGoodsSearchKey != ''"> and (ggs.goods_groups_name like concat('%', #{bossAppGoodsSearchKey}, '%') or ggs.goods_groups_no = #{bossAppGoodsSearchKey})</if>
            <if test="relatedGoodsNames != null  and relatedGoodsNames != ''"> and ggs.related_goods_names like concat('%', #{relatedGoodsNames}, '%')</if>
            <if test="warehouseFlag != null "> and ggs.warehouse_flag = #{warehouseFlag}</if>
            <if test="calculationUnitNo != null  and calculationUnitNo != ''"> and ggs.calculation_unit_no = #{calculationUnitNo}</if>
            <if test="createNo != null  and createNo != ''"> and ggs.create_no = #{createNo}</if>
            <if test="modifyNo != null  and modifyNo != ''"> and ggs.modify_no = #{modifyNo}</if>
            <if test="merchantNo != null  and merchantNo != ''"> and ggs.merchant_no = #{merchantNo}</if>
            <if test="storeNo != null  and storeNo != ''"> and ggs.store_no = #{storeNo}</if>
            <if test="ucRegionListStr != null and  ucRegionListStr != ''">
                and effective_region  REGEXP ( #{ucRegionListStr} )
            </if>
            <!--平台身份查询全部列表（包括平台、商家、店铺自建） -->
            <if test="queryType==1001">
                and ggs.goods_source_type in ('1001','1002','1003','1005')
            </if>
            <!--平台身份查询平台列表（包括平台自建） -->
            <if test="queryType==1002">
                and ggs.goods_source_type in ('1001')
            </if>
            <!--平台身份查询商家/店铺列表（包括商家、店铺自建） -->
            <if test="queryType==1003">
                and ggs.goods_source_type in ('1002','1003','1005')
            </if>
            <!--商家身份查询商家列表（包括商家自建） -->
            <if test="queryType==1004">
                and ggs.goods_source_type in ('1002','1005')
            </if>
            <!--商家身份查询店铺列表（包括店铺自建） -->
            <if test="queryType==1005">
                and ggs.goods_source_type in ('1003','1005','2001')
            </if>
            <!--店铺身份查询列表（包括商家分发、店铺自建） -->
            <if test="queryType==1006">
                and ggs.goods_source_type in ('1003','2001')
            </if>
            <!--店铺身份查询列表（包括商家分发、店铺自建） -->
            <if test="queryType==1013">
                and ggs.goods_source_type in ('1003')
            </if>
            <if test="queryType==1012">
                and ggs.goods_source_type in ('2001')
            </if>
            <if test="notExistGoodsGroups != null  and notExistGoodsGroups.size() > 0">
                and ggs.goods_groups_no not in
                <foreach item="goodsGroupsNo" collection="notExistGoodsGroups" open="(" separator="," close=")">
                    #{goodsGroupsNo}
                </foreach>
            </if>
            order by ggs.modify_time desc
        </where>
    </select>

    <select id="selectGoodsGroupsById" parameterType="Long" resultMap="GoodsGroupsResult">
        <include refid="selectGoodsGroupsVo"/>
        where id = #{id}
    </select>

    <select id="selectGoodsGroupsByNos" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo" resultMap="GoodsGroupsResult">
        select
        <include refid="Base_Column_List"/>
        , u.calculation_unit_name as assist_calculation_unit_name
        from goods_groups ggs
        left join calculation_unit u on ggs.calculation_unit_no = u.calculation_unit_no
        where
        1 = 1
        <if test="goodsGroupsNo != null  and goodsGroupsNo != ''"> and goods_groups_no = #{goodsGroupsNo}</if>
        <if test="goodsGroupsNoList != null and goodsGroupsNoList.size() > 0">
            and goods_groups_no in
            <foreach item="goodsGroupsNo" collection="goodsGroupsNoList" open="(" separator="," close=")">
                #{goodsGroupsNo}
            </foreach>
        </if>
        <include refid="Base_Column_Where"/>
    </select>
    <select id="selectGoodsGroupsByMerchantGoodsGroupsNo" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsGroupsVo" resultMap="GoodsGroupsResult">
        select
        <include refid="Base_Column_List"/>
        , u.calculation_unit_name as assist_calculation_unit_name
        from goods_groups ggs
        left join calculation_unit u on ggs.calculation_unit_no = u.calculation_unit_no
        where
        ggs.delete_flag = 1 and ggs.merchant_goods_groups_no = #{merchantGoodsGroupsNo}
        <if test="goodsSourceType != null and goodsSourceType != ''">
            and   ggs.goods_source_type = #{goodsSourceType}
        </if>
        <include refid="Base_Column_Where"/>
    </select>
    <!---商家自建排重-->
    <select id="getGoodsGroupsDetailByGoodsGroupsName" parameterType="cn.htdt.goodsprocess.dto.request.goodsgroups.ReqGoodsGroupsDTO" resultMap="GoodsGroupsResult">
        select
        <include refid="Base_Column_List"/>
        from goods_groups ggs
        where
        ggs.delete_flag = 1 and
        ggs.goods_groups_name =#{goodsGroupsName}
        <if test="merchantNo != null  and merchantNo != ''"> and merchant_no = #{merchantNo}</if>
        <if test="storeNo != null  and storeNo != ''">
             and store_no = #{storeNo}
            <if test="goodsSourceType != null and goodsSourceType != ''">
                and   goods_source_type = #{goodsSourceType}
            </if>
        </if>
        <if test="storeNo == null  or storeNo == ''"> and store_no = ''</if>
        <if test="storeNoAllList != null  and storeNoAllList.size() > 0">
            and ggs.store_no not in
            <foreach item="storeNo" collection="storeNoAllList" open="(" separator="," close=")">
                #{storeNo}
            </foreach>
        </if>
    </select>
    <!---店铺自建排重-->
    <select id="getDetailForStoreBuildDistinct" parameterType="cn.htdt.goodsprocess.dto.request.goodsgroups.ReqGoodsGroupsDTO" resultMap="GoodsGroupsResult">
        SELECT * FROM goods_groups ggs
        WHERE ggs.delete_flag = 1
          AND ggs.goods_groups_name =#{goodsGroupsName}
          AND ggs.store_no = #{storeNo}
          AND ggs.goods_source_type ='1003'
        UNION
        SELECT * FROM goods_groups ggs
        WHERE
            ggs.delete_flag = 1
          AND ggs.goods_groups_name =#{goodsGroupsName}
          AND ggs.merchant_no != #{merchantNo}
          AND ggs.goods_source_type !='2001'
          AND ggs.store_no = ''
    </select>

    <update id="updateGoodsGroups" parameterType="cn.htdt.goodsprocess.domain.GoodsGroupsDomain">
        update goods_groups
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodsGroupsName != null  and goodsGroupsName != ''">  goods_groups_name = #{goodsGroupsName},</if>
            <if test="warehouseFlag != null">warehouse_flag = #{warehouseFlag},</if>
            <if test="realStockNum != null">real_stock_num = #{realStockNum},</if>
            <if test="calculationUnitNo != null and calculationUnitNo != ''">calculation_unit_no = #{calculationUnitNo},</if>
            <if test="goodsSourceType != null and goodsSourceType != ''">
                 goods_source_type = #{goodsSourceType},
            </if>
            <if test="relatedGoodsNames != null">related_goods_names = #{relatedGoodsNames},</if>
            <if test="effectiveRegion != null">effective_region = #{effectiveRegion},</if>
            <if test="effectiveRegionNames != null">effective_region_names = #{effectiveRegionNames},</if>
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="modifyNo != null and modifyNo != ''">modify_no = #{modifyNo},</if>
            <if test="modifyName != null and modifyName != ''">modify_name = #{modifyName},</if>
            <if test="modifyTime != null">modify_time = now()</if>
        </trim>
        where goods_groups_no = #{goodsGroupsNo}
    </update>

    <update id="modifyLogicDeleteGoodsGroups" parameterType="cn.htdt.goodsprocess.domain.GoodsGroupsDomain">
        update goods_groups
        <trim prefix="SET" suffixOverrides=",">
            <if test="deleteFlag != null">delete_flag = #{deleteFlag},</if>
            <if test="modifyNo != null and modifyNo != ''">modify_no = #{modifyNo},</if>
            <if test="modifyName != null and modifyName != ''">modify_name = #{modifyName},</if>
            <if test="modifyTime != null">modify_time = now()</if>
        </trim>
        where goods_groups_no = #{goodsGroupsNo}
    </update>
    <delete id="modifyDeleteGoodsGroups" parameterType="cn.htdt.goodsprocess.domain.GoodsGroupsDomain">
        delete from goods_groups  where goods_groups_no = #{goodsGroupsNo}
                                           and merchant_goods_groups_no = #{merchantGoodsGroupsNo}
                                           and  goods_source_type = #{goodsSourceType}
    </delete>

    <delete id="deleteGoodsGroupsById" parameterType="Long">
        delete from goods_groups where id = #{id}
    </delete>

    <delete id="deleteGoodsGroupsByIds" parameterType="String">
        delete from goods_groups where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_Where">
        <if test="storeNo != null and storeNo != ''">
            and ggs.store_no = #{storeNo}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            and ggs.merchant_no = #{merchantNo}
        </if>
    </sql>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        ggs.id,
        ggs.goods_groups_no,
        ggs.merchant_goods_groups_no,
        ggs.goods_groups_name,
        ggs.warehouse_flag,
        ggs.real_stock_num,
        ggs.calculation_unit_no,
        ggs.related_goods_names,
        ggs.effective_region,
        ggs.effective_region_names,
        ggs.delete_flag,
        ggs.merchant_no,
        ggs.merchant_name,
        ggs.store_no,
        ggs.store_name,
        ggs.goods_source_type,
        ggs.create_no,
        ggs.create_name,
        ggs.create_time,
        ggs.modify_no,
        ggs.modify_name,
        ggs.modify_time
    </sql>
</mapper>
