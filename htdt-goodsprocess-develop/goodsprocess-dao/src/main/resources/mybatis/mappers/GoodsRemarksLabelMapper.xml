<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsRemarksLabelDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsRemarksLabelDomain">
        <result column="label_no" property="labelNo"/>
        <result column="label_content" property="labelContent"/>
        <result column="sort_value" property="sortValue"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        label_no,
        label_content,
        sort_value,
        store_no,
        store_name,
        merchant_no,
        merchant_name
    </sql>

    <sql id="Base_Column_Where">
        <choose>
            <when test="deleteFlag != null and deleteFlag == 2">
            </when>
            <otherwise>
                and delete_flag = 1
            </otherwise>
        </choose>
    </sql>

    <select id="selectByParams" parameterType="cn.htdt.goodsprocess.domain.GoodsRemarksLabelDomain"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        goods_remarks_label
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="labelContent != null and labelContent !=''">
                    and label_content = #{labelContent}
                </if>
                <if test="storeNo != null and storeNo !=''">
                    and store_no = #{storeNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        order by
        sort_value
    </select>

    <update id="batchUpdateByParam" parameterType="cn.htdt.goodsprocess.domain.GoodsRemarksLabelDomain">
        <foreach collection="list" item="item" open="" close="" separator=";">
            update
            goods_remarks_label
            set
            <if test="item.sortValue != null">
                sort_value = #{item.sortValue},
            </if>
            <if test="item.modifyNo != null and item.modifyNo != ''">
                modify_no = #{item.modifyNo},
            </if>
            <if test="item.modifyName != null and item.modifyName != ''">
                modify_name = #{item.modifyName},
            </if>
            modify_time = NOW()
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.labelNo != null and item.labelNo != ''">
                        and label_no = #{item.labelNo}
                    </if>
                    <if test="item.storeNo != null and item.storeNo !=''">
                        and store_no = #{item.storeNo}
                    </if>
                    and delete_flag = 1
                </trim>
            </where>
        </foreach>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.GoodsRemarksLabelDomain">
        update
        goods_remarks_label
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="labelNo != null and labelNo != ''">
                    and label_no = #{labelNo}
                </if>
                <if test="storeNo != null and storeNo !=''">
                    and store_no = #{storeNo}
                </if>
                and delete_flag = 1
            </trim>
        </where>
    </update>
</mapper>
