<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsAuditWhiteDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsAuditWhiteDomain">
        <result column="id" property="id" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="audit_type" property="auditType" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        audit_type, merchant_no, merchant_name, store_no, store_name, disable_flag, create_no, modify_no
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.domain.GoodsAuditWhiteDomain">
        select
            <include refid="Base_Column_List" />
        from
            goods_audit_white
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </select>

    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.GoodsAuditWhiteDomain" >
        insert into
            goods_audit_white
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="auditType != null" >
                audit_type,
            </if>
            <if test="merchantNo != null" >
                merchant_no,
            </if>
            <if test="merchantName != null" >
                merchant_name,
            </if>
            <if test="storeNo != null" >
                store_no,
            </if>
            <if test="storeName != null" >
                store_name,
            </if>
            <if test="disableFlag != null" >
                disable_flag,
            </if>
            <if test="createNo != null" >
                create_no,
            </if>
            <if test="createName != null" >
                create_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyNo != null" >
                modify_no,
            </if>
            <if test="modifyName != null" >
                modify_name,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="deleteFlag != null" >
                delete_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="auditType != null" >
                #{auditType,jdbcType=INTEGER},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null" >
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null" >
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null" >
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null" >
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null" >
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null" >
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null" >
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null" >
                #{deleteFlag,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsAuditWhiteDomain" >
        update
            goods_audit_white
        <set >
            <if test="auditType != null" >
                audit_type = #{auditType,jdbcType=INTEGER},
            </if>
            <if test="merchantNo != null" >
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null" >
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null" >
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null" >
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null" >
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null" >
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null" >
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null" >
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null" >
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.GoodsAuditWhiteDomain" >
        update
            goods_audit_white
        set
            <if test="modifyNo != null" >
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
     </update>
</mapper>
