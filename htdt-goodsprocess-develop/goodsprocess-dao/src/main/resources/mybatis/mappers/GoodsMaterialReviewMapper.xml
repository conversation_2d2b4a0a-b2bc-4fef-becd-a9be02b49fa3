<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsMaterialReviewDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsMaterialReviewDomain">
        <result column="id" property="id" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="material_review_no" property="materialReviewNo" />
        <result column="media_no" property="mediaNo" />
        <result column="request_json" property="requestJson" />
        <result column="response_result" property="responseResult" />
        <result column="video_audit_status" property="videoAuditStatus" />
        <result column="media_type" property="mediaType" />
        <result column="company_no" property="companyNo" />
        <result column="company_name" property="companyName" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="branch_no" property="branchNo" />
        <result column="branch_name" property="branchName" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
        <result column="disable_flag" property="disableFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        create_name,
        create_time,
        modify_name,
        modify_time,
        material_review_no, media_no, request_json, response_result, video_audit_status, media_type, 
        company_no,company_name, merchant_no,merchant_name
        store_no, store_name, branch_no, branch_name, create_no, modify_no, disable_flag
    </sql>
    
    <sql id="Base_Column_Where">
		and delete_flag = 1
	</sql>
	
    <!-- 修改 -->
	<update id="updateByPrimaryKeySelective"
		parameterType="cn.htdt.goodsprocess.domain.GoodsMaterialReviewDomain">
		update
		goods_material_review
		<set>
			<if test="mediaNo != null">
				media_no =  #{mediaNo,jdbcType=VARCHAR},
			</if>
			<if test="responseResult != null">
				response_result = #{responseResult,jdbcType=VARCHAR},
			</if>
			<if test="responseResult != null">
				response_result = #{responseResult,jdbcType=VARCHAR},
			</if>
			<if test="videoAuditStatus != null">
				video_audit_status = #{videoAuditStatus,jdbcType=VARCHAR},
			</if>
			<if test="mediaType != null">
				media_type = #{mediaType,jdbcType=VARCHAR},
			</if>
			<if test="merchantNo != null">
				merchant_no = #{merchantNo,jdbcType=VARCHAR},
			</if>
			<if test="merchantName != null">
				merchant_name = #{merchantName,jdbcType=VARCHAR},
			</if>
			<if test="storeNo != null">
				store_no = #{storeNo,jdbcType=VARCHAR},
			</if>
			<if test="storeName != null">
				store_name = #{storeName,jdbcType=VARCHAR},
			</if>
			<if test="companyNo != null">
				company_no = #{companyNo,jdbcType=VARCHAR},
			</if>
			<if test="companyName != null">
				company_name = #{companyName,jdbcType=VARCHAR},
			</if>
			<if test="branchNo != null">
				branch_no = #{branchNo,jdbcType=VARCHAR},
			</if>
			<if test="branchName != null">
				branch_name = #{branchName,jdbcType=VARCHAR},
			</if>
			<if test="disableFlag != null">
				disable_flag = #{disableFlag,jdbcType=TINYINT},
			</if>
			<if test="createNo != null">
				create_no = #{createNo,jdbcType=VARCHAR},
			</if>
			<if test="createName != null">
				create_name = #{createName,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="modifyNo != null">
				modify_no = #{modifyNo,jdbcType=VARCHAR},
			</if>
			<if test="modifyName != null">
				modify_name = #{modifyName,jdbcType=VARCHAR},
			</if>
			<if test="modifyTime != null">
				modify_time = #{modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null">
				delete_flag = #{deleteFlag,jdbcType=TINYINT},
			</if>
		</set>
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="materialReviewNo != null and materialReviewNo != ''">
					and material_review_no = #{materialReviewNo}
				</if>
				<if test="mediaType != null and mediaType != ''">
					and media_type = #{mediaType}
				</if>
				<if test="mediaNo != null and mediaNo != ''">
					and media_no = #{mediaNo}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</update>
	
	<!-- 逻辑删除 -->
	<update id="logicDelete"
		parameterType="cn.htdt.goodsprocess.domain.GoodsMaterialReviewDomain">
		update
		goods_material_review
		set
		<if test="modifyNo != null">
			modify_no = #{modifyNo,jdbcType=VARCHAR},
		</if>
		<if test="modifyName != null">
			modify_name = #{modifyName,jdbcType=VARCHAR},
		</if>
		<if test="modifyTime != null">
			modify_time = #{modifyTime,jdbcType=TIMESTAMP},
		</if>
		delete_flag = 2
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="materialReviewNo != null and materialReviewNo != ''">
					and material_review_no = #{materialReviewNo}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</update>
	
	<!-- 根据条件查询 -->
	<select id="selectByParams"
		resultType="cn.htdt.goodsprocess.domain.GoodsMaterialReviewDomain">
		select
		<include refid="Base_Column_List" />
		from
		goods_material_review
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="materialReviewNo != null and materialReviewNo != ''">
					and material_review_no = #{materialReviewNo}
				</if>
				<if test="mediaType != null and mediaType != ''">
					and media_type = #{mediaType}
				</if>
				<if test="videoAuditStatus != null and videoAuditStatus != ''">
					and video_audit_status = #{videoAuditStatus}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</select>
	

</mapper>
