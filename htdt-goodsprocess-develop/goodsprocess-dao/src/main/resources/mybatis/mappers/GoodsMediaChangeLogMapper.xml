<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsMediaChangeLogDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsMediaChangeLogDomain">
        <result column="id" property="id" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="media_no" property="mediaNo" />
        <result column="goods_no" property="goodsNo" />
        <result column="sort_value" property="sortValue" />
        <result column="media_type" property="mediaType" />
        <result column="picture_url" property="pictureUrl" />
        <result column="main_picture_flag" property="mainPictureFlag" />
        <result column="video_url" property="videoUrl" />
        <result column="file_name" property="fileName" />
        <result column="file_size" property="fileSize" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
        <result column="company_no" property="companyNo" />
        <result column="company_name" property="companyName" />
        <result column="branch_no" property="branchNo" />
        <result column="branch_name" property="branchName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        company_no,
        company_name,
        branch_no,
        branch_name,
        goods_no, sort_value, media_type, picture_url, main_picture_flag, video_url, file_name, file_size, merchant_no, merchant_name, store_no, store_name, disable_flag, create_no, modify_no
    </sql>


    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.domain.GoodsMediaChangeLogDomain">
        select
            <include refid="Base_Column_List" />
        from
            goods_describe_change_log
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </select>

</mapper>
