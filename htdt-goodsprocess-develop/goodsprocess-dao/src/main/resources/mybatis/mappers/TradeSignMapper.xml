<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.TradeSignDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.TradeSignDomain">
        <result column="id" property="id" />
        <result column="data_source" property="dataSource" />
        <result column="data_source_name" property="dataSourceName" />
        <result column="profiles_active" property="profilesActive" />
        <result column="salt" property="salt" />
        <result column="versions" property="versions" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        data_source, data_source_name, profiles_active, salt, versions
    </sql>

    <select id="selectSignByParams" parameterType="cn.htdt.goodsprocess.domain.TradeSignDomain" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from trade_sign
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="dataSource  != null and dataSource  != ''">
                    and data_source = #{dataSource}
                </if>
                <if test="profilesActive  != null and profilesActive  != ''">
                    and profiles_active = #{profilesActive}
                </if>
                <if test="versions  != null and versions  != ''">
                    and versions = #{versions}
                </if>
            </trim>
        </where>
    </select>

</mapper>
