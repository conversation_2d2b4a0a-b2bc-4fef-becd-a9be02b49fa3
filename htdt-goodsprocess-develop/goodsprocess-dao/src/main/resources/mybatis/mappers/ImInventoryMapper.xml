<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.ImInventoryDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.ImInventoryDomain">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="inventory_code" property="inventoryCode"/>
        <result column="warehouse_no" property="warehouseNo"/>
        <result column="warehouse_code" property="warehouseCode"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="oper_type" property="operType"/>
        <result column="source_oper_type" property="sourceOperType"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
    </resultMap>
    <resultMap id="VoBaseResultMap" type="cn.htdt.goodsprocess.vo.AtomInventoryVo" extends="BaseResultMap">
        <result column="warehouse_delete_flag" property="warehouseDeleteFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        i.id,
        i.create_name,
        i.create_time,
        i.modify_name,
        i.modify_time,
        i.delete_flag,
        i.inventory_type,
        i.inventory_code, i.warehouse_no, i.warehouse_code,i.warehouse_name, i.oper_type, i.source_oper_type, i.merchant_no, i.merchant_name, i.store_no, i.store_name, i.disable_flag, i.create_no, i.modify_no, i.company_no, i.company_name, i.branch_no, i.branch_name
    </sql>
    <sql id="Base_Column_List_Other">
        w
        .
        warehouse_name
    </sql>
    <sql id="Base_Column_List_Warehouse">
        w
        .
        delete_flag
        as warehouse_delete_flag
    </sql>

    <sql id="Base_Column_Where">
        and i.delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomInventoryVo">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Base_Column_List_Other"/>,
        <include refid="Base_Column_List_Warehouse"/>
        from
        im_inventory i left join im_warehouse w on i.warehouse_no=w.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and i.warehouse_no = #{warehouseNo}
                </if>
                <if test="warehouseName != null and warehouseName != ''">
                    and w.warehouse_name like CONCAT(CONCAT('%', #{warehouseName}),'%')
                </if>
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.inventory_code like CONCAT(CONCAT('%', #{inventoryCode}),'%')
                </if>
                <if test="operType != null and operType != ''">
                    and i.oper_type = #{operType}
                </if>
                <if test="createName != null and createName != ''">
                    and i.create_name like CONCAT(CONCAT('%', #{createName}),'%')
                </if>
                <if test="createTimeStart != null">
                    <![CDATA[
                        and  date_format(i.create_time, '%Y-%m-%d %H:%i:%s')  >= DATE_FORMAT(#{createTimeStart} ,'%Y-%m-%d %H:%i:%s')
                     ]]>
                </if>
                <if test="createTimeEnd != null">
                    <![CDATA[
                        and date_format(i.create_time, '%Y-%m-%d %H:%i:%s') <= DATE_FORMAT(#{createTimeEnd},'%Y-%m-%d %H:%i:%s')
                     ]]>
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and i.company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and i.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and i.store_no = #{storeNo}
                </if>
                <if test="inventoryType  != null">
                    and i.inventory_type = #{inventoryType}
                </if>
                <if test="loginIdentity  != null">
                    and w.login_identity = #{loginIdentity}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        ORDER BY i.modify_time DESC
    </select>

    <select id="selectByCode" resultType="cn.htdt.goodsprocess.domain.ImInventoryDomain"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from
        im_inventory i
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.inventory_code = #{inventoryCode}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        order by i.create_time desc
    </select>
    <!-- 查盘点下的类目 -->
    <select id="selectInventoryCategoryCode" resultType="cn.htdt.goodsprocess.vo.AtomInventoryVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomInventoryVo">
        select distinct i.inventory_code, sc.category_name from im_inventory_detail i
        left join goods g on i.goods_no =g.goods_no
        left join sale_category sc on g.category_no =sc.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.inventory_code = #{inventoryCode}
                </if>
                <if test="inventoryList != null and inventoryList.size() > 0">
                    and i.inventory_code IN
                    <foreach item="inventoryCode" collection="inventoryList" open="(" close=")" separator=",">
                        #{inventoryCode}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        group by i.inventory_code, sc.category_name
    </select>

    <update id="batchLogicDelete" parameterType="java.util.List">
        update
        im_inventory i
        set
        i.delete_flag = 2
        <where>
            i.delete_flag = 1
            and inventory_code in
            <foreach collection="list" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </where>
    </update>

    <update id="updateByCode" parameterType="cn.htdt.goodsprocess.domain.ImInventoryDomain">
        update
        im_inventory i
        <set>
            <if test="inventoryCode != null">
                i.inventory_code = #{inventoryCode},
            </if>
            <if test="warehouseNo != null">
                i.warehouse_no = #{warehouseNo},
            </if>
            <if test="warehouseCode != null">
                i.warehouse_code = #{warehouseCode},
            </if>
            <if test="warehouseName != null">
                i.warehouse_name = #{warehouseName},
            </if>
            <if test="operType != null">
                i.oper_type = #{operType},
            </if>
            <if test="sourceOperType != null">
                i.source_oper_type = #{sourceOperType},
            </if>
            <if test="merchantNo != null">
                i.merchant_no = #{merchantNo},
            </if>
            <if test="merchantName != null">
                i.merchant_name = #{merchantName},
            </if>
            <if test="storeNo != null">
                i.store_no = #{storeNo},
            </if>
            <if test="storeName != null">
                i.store_name = #{storeName},
            </if>
            <if test="disableFlag != null">
                i.disable_flag = #{disableFlag},
            </if>
            <if test="createNo != null">
                i.create_no = #{createNo},
            </if>
            <if test="createName != null">
                i.create_name = #{createName},
            </if>
            <if test="createTime != null">
                i.create_time = #{createTime},
            </if>
            <if test="modifyNo != null">
                i.modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null">
                i.modify_name = #{modifyName},
            </if>
            <if test="modifyTime != null">
                i.modify_time = #{modifyTime},
            </if>
            <if test="deleteFlag != null">
                i.delete_flag = #{deleteFlag},
            </if>
            <if test="companyNo != null">
                i.company_no = #{companyNo},
            </if>
            <if test="companyName != null">
                i.company_name = #{companyName},
            </if>
            <if test="branchNo != null">
                i.branch_no = #{branchNo},
            </if>
            <if test="branchName != null">
                i.branch_name = #{branchName},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and inventory_code = #{inventoryCode}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>
</mapper>
