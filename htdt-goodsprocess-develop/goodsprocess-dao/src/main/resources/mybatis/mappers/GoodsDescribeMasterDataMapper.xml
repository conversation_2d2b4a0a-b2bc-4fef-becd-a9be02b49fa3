<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsDescribeMasterDataDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsDescribeMasterDataDomain">
        <result column="id" property="id" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="goods_no" property="goodsNo" />
        <result column="content" property="content" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        goods_no, content, disable_flag, create_no, modify_no
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByGoodsNo" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.domain.GoodsDescribeDomain">
        select
            <include refid="Base_Column_List" />
        from
            goods_describe_master_data
        <where>
            goods_no = #{goodsNo}
        </where>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsDescribeDomain" >
        update
        goods_describe_master_data
        <set >
            <if test="content != null" >
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
            <if test="deleteFlag != null" >
                delete_flag = #{deleteFlag},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>


</mapper>
