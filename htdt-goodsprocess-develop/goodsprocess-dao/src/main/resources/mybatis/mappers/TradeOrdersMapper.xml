<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.TradeOrdersDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.TradeOrdersDomain">
        <result column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="buyer_code" property="buyerCode" />
        <result column="buyer_name" property="buyerName" />
        <result column="seller_type" property="sellerType" />
        <result column="seller_name" property="sellerName" />
        <result column="order_from" property="orderFrom" />
        <result column="total_goods_count" property="totalGoodsCount" />
        <result column="total_goods_amount" property="totalGoodsAmount" />
        <result column="total_freight" property="totalFreight" />
        <result column="total_discount_amount" property="totalDiscountAmount" />
        <result column="shop_discount_amount" property="shopDiscountAmount" />
        <result column="platform_discount_amount" property="platformDiscountAmount" />
        <result column="used_rebate_amount" property="usedRebateAmount" />
        <result column="bargaining_order_amount" property="bargainingOrderAmount" />
        <result column="bargaining_order_freight" property="bargainingOrderFreight" />
        <result column="order_total_amount" property="orderTotalAmount" />
        <result column="order_pay_amount" property="orderPayAmount" />
        <result column="create_order_time" property="createOrderTime" />
        <result column="order_status" property="orderStatus" />
        <result column="is_cancel_order" property="isCancelOrder" />
        <result column="pay_type" property="payType" />
        <result column="pay_order_time" property="payOrderTime" />
        <result column="is_need_invoice" property="isNeedInvoice" />
        <result column="invoice_type" property="invoiceType" />
        <result column="invoice_notify" property="invoiceNotify" />
        <result column="invoice_company_name" property="invoiceCompanyName" />
        <result column="tax_man_id" property="taxManId" />
        <result column="bank_name" property="bankName" />
        <result column="delivery_type" property="deliveryType" />
        <result column="consignee_name" property="consigneeName" />
        <result column="consignee_address_province" property="consigneeAddressProvince" />
        <result column="consignee_address_city" property="consigneeAddressCity" />
        <result column="consignee_address_district" property="consigneeAddressDistrict" />
        <result column="consignee_address_town" property="consigneeAddressTown" />
        <result column="logistics_company" property="logisticsCompany" />
        <result column="logistics_no" property="logisticsNo" />
        <result column="consignee_phone_num" property="consigneePhoneNum" />
        <result column="ds_consignee_phone_num" property="dsConsigneePhoneNum" />
        <result column="contact_phone" property="contactPhone" />
        <result column="bank_account" property="bankAccount" />
        <result column="consignee_address" property="consigneeAddress" />
        <result column="ds_consignee_address" property="dsConsigneeAddress" />
        <result column="consignee_address_detail" property="consigneeAddressDetail" />
        <result column="ds_consignee_address_detail" property="dsConsigneeAddressDetail" />
        <result column="data_source" property="dataSource" />
    </resultMap>

    <!-- 通用查询映射结果 -->
    <resultMap id="PurchaseTradeOrderResultMap" type="cn.htdt.goodsprocess.dto.response.tradeorder.ResPurchaseTradeOrderDTO">
        <result column="order_no" property="orderNo" />
        <result column="seller_name" property="sellerName" />
        <result column="buyer_code" property="buyerCode" />
        <result column="order_status" property="orderStatus" />
        <result column="order_pay_amount" property="orderPayAmount" />
        <result column="order_total_amount" property="orderTotalAmount" />
        <result column="create_order_time" property="createOrderTime" />
        <result column="total_goods_count" property="totalGoodsCount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        order_no, buyer_code, buyer_name, seller_type, seller_name, order_from, total_goods_count, total_goods_amount, total_freight, total_discount_amount, shop_discount_amount, platform_discount_amount, used_rebate_amount, bargaining_order_amount, bargaining_order_freight, order_total_amount, order_pay_amount, create_order_time, order_status, is_cancel_order, pay_type, pay_order_time, is_need_invoice, invoice_type, invoice_notify, invoice_company_name, tax_man_id, bank_name, delivery_type, consignee_name, consignee_address_province, consignee_address_city, consignee_address_district, consignee_address_town, logistics_company, logistics_no, consignee_phone_num, ds_consignee_phone_num, contact_phone, bank_account, consignee_address, ds_consignee_address, consignee_address_detail, ds_consignee_address_detail, data_source
    </sql>

    <select id="selectPurchaseTradeOrderByPage" parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultMap="PurchaseTradeOrderResultMap">
        SELECT
        tos.order_no,
        tos.seller_name,
        tos.buyer_code,
        tos.order_status,
        tos.order_pay_amount,
        tos.order_total_amount,
        tos.create_order_time,
        tos.total_goods_count,
        tos.total_goods_amount,
        tos.create_order_time
        FROM trade_orders tos
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and tos.buyer_code = #{buyerCode}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    and (
                    tos.order_no like CONCAT(CONCAT('%', #{orderNo}),'%')
                    or
                    tos.seller_name like CONCAT(CONCAT('%', #{orderNo}),'%')
                    )
                </if>
                <if test="orderStatus != null and orderStatus != ''">
                    and tos.order_status = #{orderStatus}
                </if>
                <if test="sellerName != null and sellerName != ''">
                    and tos.seller_name like CONCAT(CONCAT('%', #{sellerName}),'%')
                </if>

                <if test="null != day and 1 == day">
                    AND to_days(tos.create_order_time) = to_days(now())
                </if>
                <if test="null != day and 2 == day">
                    AND TO_DAYS( NOW( ) ) - TO_DAYS( tos.create_order_time) = 1
                </if>
                <if test="null != day and 3 == day">
                    AND date(tos.create_order_time) >= DATE_SUB(CURDATE(), INTERVAL 6 DAY)
                </if>
                <if test="null != day and 4 == day">
                    AND DATE_FORMAT(tos.create_order_time, '%Y%m') = DATE_FORMAT(CURDATE(), '%Y%m')
                </if>
                <if test="startCreateTime != null">
                    <![CDATA[ and tos.create_order_time >= #{startCreateTime} ]]>
                </if>
                <if test="endCreateTime != null">
                    <![CDATA[ and tos.create_order_time <= #{endCreateTime} ]]>
                </if>
            </trim>
        </where>
        order by tos.create_order_time desc
    </select>

    <select id="selectPurchaseTradeOrderInfo" parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultMap="PurchaseTradeOrderResultMap">
        SELECT
        tos.order_no,
        tos.seller_name,
        tos.buyer_code,
        tos.order_status,
        tos.order_pay_amount,
        tos.order_total_amount,
        tos.create_order_time,
        tos.total_goods_count
        FROM trade_orders tos
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and tos.buyer_code = #{buyerCode}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    and tos.order_no = #{orderNo}
                </if>
            </trim>
        </where>
        order by tos.create_order_time desc
    </select>

    <select id="selectByOrderNoList" parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderNoDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        trade_orders
        <where>
            order_no IN
            <foreach item="orderNo" collection="orderNoList" open="(" close=")" separator=",">
                #{orderNo}
            </foreach>
            <if test="dataSource != null and dataSource != ''">
                and data_source = #{dataSource}
            </if>
        </where>
    </select>

    <update id="batchUpdateOrder" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update
                trade_orders
            set
            <if test="item.orderStatus != null and item.orderStatus != ''">
                order_status = #{item.orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.isCancelOrder != null and item.isCancelOrder != ''">
                is_cancel_order = #{item.isCancelOrder,jdbcType=VARCHAR},
            </if>
            <if test="item.payOrderTime != null and item.payOrderTime != ''">
                pay_order_time = #{item.payOrderTime,jdbcType=VARCHAR},
            </if>
            <if test="item.logisticsCompany != null and item.logisticsCompany != ''">
                logistics_company = #{item.logisticsCompany,jdbcType=VARCHAR},
            </if>
            <if test="item.logisticsNo != null and item.orderStatus != ''">
                logistics_no = #{item.logisticsNo,jdbcType=VARCHAR},
            </if>
                modify_time = NOW()
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    and order_no = #{item.orderNo}
                </trim>
            </where>
        </foreach>
    </update>

    <select id="selectTradeOrdersCount"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultType="cn.htdt.goodsprocess.dto.response.tradeorder.ResPurchaseTradeOrderDTO">
        SELECT sum(tos.order_pay_amount) as totalOrderPayAmount, count(tos.order_no) as totalOrderCount
        FROM trade_orders tos
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and tos.buyer_code = #{buyerCode}
                </if>
                <if test="startCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') >= #{startCreateOrderTime} ]]>
                </if>
                <if test="endCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') <= #{endCreateOrderTime} ]]>
                </if>
            </trim>
        </where>
    </select>

    <select id="selectLastTradeOrders"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultType="cn.htdt.goodsprocess.dto.response.tradeorder.ResPurchaseTradeOrderDTO">
        SELECT tos.create_order_time, tos.order_pay_amount
        FROM trade_orders tos
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and tos.buyer_code = #{buyerCode}
                </if>
                order by tos.create_order_time desc limit 0, 1
            </trim>
        </where>
    </select>

    <select id="selectB2BTradeOrderByPage" parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultMap="PurchaseTradeOrderResultMap">
        SELECT
        tos.buyer_code,
        count(1) total_goods_count,
        sum(tos.total_goods_amount) total_goods_amount
        FROM trade_orders tos
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and tos.buyer_code = #{buyerCode}
                </if>
                <if test="startCreateTime != null">
                    <![CDATA[ and tos.create_order_time >= #{startCreateTime} ]]>
                </if>
                <if test="endCreateTime != null">
                    <![CDATA[ and tos.create_order_time <= #{endCreateTime} ]]>
                </if>
            </trim>
        </where>
        group by tos.buyer_code
    </select>

</mapper>
