<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_help_code" property="goodsHelpCode"/>
        <result column="third_goods_no" property="thirdGoodsNo"/>
        <result column="goods_form" property="goodsForm"/>
        <result column="goods_type" property="goodsType"/>
        <result column="virtual_goods_type" property="virtualGoodsType"/>
        <result column="foods_type" property="foodsType"/>
        <result column="category_no" property="categoryNo"/>
        <result column="category_name" property="categoryName"/>
        <result column="brand_no" property="brandNo"/>
        <result column="brand_name" property="brandName"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_initial" property="goodsInitial"/>
        <result column="goods_name_full_pinyin" property="goodsNameFullPinyin"/>
        <result column="goods_name_full_initials" property="goodsNameFullInitials"/>
        <result column="warehouse_flag" property="warehouseFlag"/>
        <result column="parent_goods_no" property="parentGoodsNo"/>
        <result column="freight_template_no" property="freightTemplateNo"/>
        <result column="sales_area_no" property="salesAreaNo"/>
        <result column="goods_source_type" property="goodsSourceType"/>
        <result column="goods_model" property="goodsModel"/>
        <result column="goods_sale_description" property="goodsSaleDescription"/>
        <result column="calculation_unit_no" property="calculationUnitNo"/>
        <result column="standard_flag" property="standardFlag"/>
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo"/>
        <result column="main_unit_num" property="mainUnitNum"/>
        <result column="assist_unit_num" property="assistUnitNum"/>
        <result column="conversion_rate" property="conversionRate"/>
        <result column="barcode" property="barcode"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="delivery_way" property="deliveryWay"/>
        <result column="first_shelf_time" property="firstShelfTime"/>
        <result column="audit_message" property="auditMessage"/>
        <result column="main_data_flag" property="mainDataFlag"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="goods_status" property="goodsStatus"/>
        <result column="first_attribute_name" property="firstAttributeName"/>
        <result column="first_attribute_value_name" property="firstAttributeValueName"/>
        <result column="second_attribute_name" property="secondAttributeName"/>
        <result column="second_attribute_value_name" property="secondAttributeValueName"/>
        <result column="third_attribute_name" property="thirdAttributeName"/>
        <result column="third_attribute_value_name" property="thirdAttributeValueName"/>
        <result column="original_goods_no" property="originalGoodsNo"/>
        <result column="series_type" property="seriesType"/>
        <result column="stockNum" property="stockNums"/>
        <result column="calculation_unit_name" property="calculationUnitName"/>
        <result column="assist_calculation_unit_name" property="assistCalculationUnitName"/>
        <result column="sales_volume" property="salesVolume"/>
        <result column="browse_amount" property="browseAmount"/>
        <result column="goods_video" property="goodsVideo"/>
        <result column="distribution_status" property="distributionStatus"/>
        <result column="distribute_goods_flag" property="distributeGoodsFlag"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_data_modify_flag" property="auditDataModifyFlag"/>
        <result column="limit_price" property="limitPrice"/>
        <result column="market_price" property="marketPrice"/>
        <result column="purchase_price" property="purchasePrice"/>
        <result column="retail_price" property="retailPrice"/>
        <result column="audit_type" property="auditType"/>
        <result column="imei_flag" property="imeiFlag"/>
        <result column="down_message" property="downMessage"/>
        <result column="saleCategoryDisableFlag" property="saleCategoryDisableFlag"/>
        <result column="cloud_pool_supply_price" property="cloudPoolSupplyPrice"/>
        <result column="latest_modify_time" property="latestModifyTime"/>
        <result column="apply_status" property="applyStatus"/>
        <result column="failure_type" property="failureType"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_record_no" property="auditRecordNo"/>
        <result column="supply_store_no" property="supplyStoreNo"/>
        <result column="cloud_pool_goods_no" property="cloudPoolGoodsNo"/>
        <result column="import_store_type" property="importStoreType"/>
        <result column="first_category_no" property="firstCategoryNo"/>
        <result column="second_category_no" property="secondCategoryNo"/>
        <result column="third_category_no" property="thirdCategoryNo"/>
        <result column="app_channel_source" property="appChannelSource"/>
        <result column="full_id_path" property="fullIdPath"/>
        <result column="full_name_path" property="fullNamePath"/>
        <result column="inventory_warning_flag" property="inventoryWarningFlag"/>
        <result column="real_stock_num" property="realStockNum"/>
        <result column="sort_num" property="sortNum"/>
        <result column="shelf_type" property="shelfType"/>
        <result column="merchant_goods_no" property="merchantGoodsNo"/>
        <result column="validity_period_manage_flag" property="validityPeriodManageFlag"/>
        <result column="quality_guarantee_period" property="qualityGuaranteePeriod"/>
        <result column="shelf_life_unit" property="shelfLifeUnit"/>
        <result column="custom_goods_no" property="customGoodsNo"/>
        <result column="multi_unit_type" property="multiUnitType"/>
        <result column="multi_unit_goods_no" property="multiUnitGoodsNo"/>
        <result column="allow_custom_price" property="allowCustomPrice"/>
        <result column="channel_sales" property="channelSales"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        g.channel_sales,
        g.id,
		g.goods_no,
		g.goods_help_code,
		g.third_goods_no,
		g.goods_form,
		g.goods_type,
		g.virtual_goods_type,
		g.foods_type,
		g.category_no,
		g.brand_no,
		g.goods_name,
		g.goods_initial,
		g.goods_name_full_pinyin,
		g.goods_name_full_initials,
		g.warehouse_flag,
		g.parent_goods_no,
		g.freight_template_no,
		g.sales_area_no,
		g.goods_source_type,
		g.goods_model,
		g.goods_sale_description,
		g.calculation_unit_no,
		g.standard_flag,
		g.assist_calculation_unit_no,
		g.main_unit_num,
		g.assist_unit_num,
		g.conversion_rate,
		g.barcode,
		g.payment_method,
		g.delivery_way,
		g.first_shelf_time,
		g.audit_status,
		g.audit_data_modify_flag,
		g.imei_flag,
		g.audit_message,
		g.main_data_flag,
		g.merchant_no,
		g.merchant_name,
		g.store_no,
		g.store_name,
		g.disable_flag,
		g.create_no,
		g.create_name,
		g.create_time,
		g.modify_no,
		g.modify_name,
		g.modify_time,
		g.delete_flag,
		g.company_no,
		g.company_name,
		g.branch_no,
		g.branch_name,
		g.supplier_code,
		g.supplier_name,
		g.first_attribute_name,
		g.first_attribute_value_name,
		g.second_attribute_name,
		g.second_attribute_value_name,
		g.third_attribute_name,
		g.third_attribute_value_name,
		g.limit_price,
		g.market_price,
		g.purchase_price,
		g.retail_price,
		g.original_goods_no,
		g.series_type,
		g.goods_status,
		g.sales_volume,
		g.browse_amount,
		g.goods_video,
		g.distribution_status,
		g.distribute_goods_flag,
		g.audit_type,
		g.audit_record_no,
		g.down_message,
		g.cloud_pool_supply_price,
		g.latest_modify_time,
		g.supply_store_no,
		g.cloud_pool_goods_no,
		g.app_channel_source,
		g.shelf_type,
		g.merchant_goods_no,
		g.validity_period_manage_flag,
		g.quality_guarantee_period,
		g.shelf_life_unit,
		g.custom_goods_no,
        g.multi_unit_type,
        g.multi_unit_goods_no,
        g.allow_custom_price
    </sql>
    <sql id="Select_Base_Column_List">
        g.channel_sales,
        g.goods_no,
		g.third_goods_no,
		g.goods_help_code,
		g.goods_name,
		g.barcode,
		g.freight_template_no,
		g.first_attribute_value_name,
        g.second_attribute_value_name,
        g.third_attribute_value_name,
		g.warehouse_flag,
		g.imei_flag,
		g.store_no,
		g.goods_source_type,
		g.supply_store_no
    </sql>
    <sql id="Select_Goods_Column_List">
        g.channel_sales,
        g.goods_no,
        g.goods_type,
		g.original_goods_no,
		g.third_goods_no,
		g.goods_help_code,
		g.goods_name,
		g.category_no,
		sc.category_name,
		g.brand_no,
		b.brand_name,
		g.calculation_unit_no,
		g.standard_flag,
		g.assist_calculation_unit_no,
		g.barcode,
		g.main_unit_num,
		g.assist_unit_num,
		g.conversion_rate,
		g.freight_template_no,
        g.first_attribute_name,
		g.first_attribute_value_name,
        g.second_attribute_name,
        g.second_attribute_value_name,
        g.third_attribute_name,
        g.third_attribute_value_name,
		g.warehouse_flag,
		g.imei_flag,
		g.goods_form,
		g.retail_price,
		g.purchase_price,
		g.merchant_goods_no,
		g.validity_period_manage_flag,
		g.quality_guarantee_period,
		g.shelf_life_unit,
		g.custom_goods_no,
        g.multi_unit_type,
        g.multi_unit_goods_no
    </sql>
    <sql id="Select_Base_Column_stockNum">
        g.channel_sales,
        g.calculation_unit_no,
		g.imei_flag,
		g.goods_name,
		g.goods_no,
		g.category_no,
		g.delete_flag,
		g.goods_source_type,
		g.warehouse_flag,
        g.first_attribute_value_name,
        g.second_attribute_value_name,
        g.third_attribute_value_name,
		(IFNULL( r.deliver_stock_num, 0 ) + IFNULL( r.freeze_stock_num, 0 )) AS
		stockNum,
		r.real_stock_num realStockNum,
		g.multi_unit_type,
		g.multi_unit_goods_no,
		g.validity_period_manage_flag,
		g.quality_guarantee_period,
		g.shelf_life_unit
    </sql>

    <sql id="Select_Base_Column_Category_Brand">
        sc.disable_flag saleCategoryDisableFlag,
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no,
        (case when sc.category_no = '10001' then '' else sc.full_name_path end) category_name,
		brand_name,
		b.data_source_type brandDataSourceType
    </sql>

    <sql id="Select_Base_Column_store_Category">
        sc.store_category_no categoryNo,
        sc.store_category_name categoryName,
		b.brand_name,
		b.data_source_type brandDataSourceType
    </sql>
    <sql id="Select_Base_Column_store_Category_goods_relation">
        scg.sort_num
    </sql>
    <!-- 商品选择-活动用 -->
    <sql id="Select_Goods_Column_List_Promotion">
        g.goods_no,
        g.goods_form,
        g.goods_name,
        g.first_attribute_value_name,
        g.second_attribute_value_name,
        g.third_attribute_value_name,
        g.category_no,
        sc.category_name,
        g.brand_no,
        b.brand_name,
        g.retail_price,
		g.series_type,
        g.original_goods_no,
        g.store_no,
        g.multi_unit_type,
        g.multi_unit_goods_no,
        g.main_unit_num,
        g.assist_unit_num,
        g.calculation_unit_no
    </sql>
    <!-- 云池商品选择-活动用 -->
    <sql id="Select_Cloud_Goods_Column_List_Promotion">
        g.goods_no,
        g.goods_form,
        g.goods_name,
        g.first_attribute_value_name,
        g.second_attribute_value_name,
        g.third_attribute_value_name,
        g.category_no,
        sc.category_name,
        g.brand_no,
        b.brand_name,
        g.supply_store_no,
        g.original_goods_no,
		g.series_type,
        g.cloud_pool_supply_price,
        g.store_no,
        g.retail_price
    </sql>

    <!-- 打印商品标签, 查询子品 -->
    <sql id="Select_Sub_Goods_Print_Column_List">
        g.goods_no,
        g.original_goods_no,
        g.goods_form,
        g.goods_name,
        g.first_attribute_name,
        g.first_attribute_value_name,
        g.second_attribute_name,
        g.second_attribute_value_name,
        g.third_attribute_name,
        g.third_attribute_value_name,
        g.brand_no,
        g.calculation_unit_no,
	    g.assist_calculation_unit_no,
	    g.main_unit_num,
	    g.assist_unit_num,
	    g.barcode,
		g.series_type,
        g.store_no,
        g.retail_price,
        g.market_price
    </sql>

    <sql id="Base_Column_Where">
        <if test="goodsNo != null and goodsNo != ''">
            and g.goods_no = #{goodsNo}
        </if>
        <if test="thirdGoodsNo != null and thirdGoodsNo != ''">
            and g.third_goods_no = #{thirdGoodsNo}
        </if>
        <if test="categoryNo != null and categoryNo != ''">
            and g.category_no = #{categoryNo}
        </if>
        <if test="brandNo != null and brandNo != ''">
            and g.brand_no = #{brandNo}
        </if>
        <if test="goodsName != null and goodsName != ''">
            and g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
        </if>
        <if test="goodsForm != null and goodsForm != ''">
            <if test="goodsForm != '1004'">
                and g.goods_form = #{goodsForm} and multi_unit_goods_no = ''
            </if>
            <if test="goodsForm == '1004'">
                and g.multi_unit_goods_no != ''
            </if>
        </if>
        <if test="barcode != null and barcode != ''">
            and g.barcode = #{barcode}
        </if>
        <if test="goodsHelpCode != null and goodsHelpCode != ''">
            and g.goods_help_code LIKE CONCAT (CONCAT('%',#{goodsHelpCode}),'%')
        </if>
        <choose>
            <when test="goodsType != null and goodsType != ''">
                and g.goods_type = #{goodsType}
            </when>
            <otherwise>
                <if test="(goodsNo == null or goodsNo == '') and (goodsNos == null or goodsNos.size() == 0)">
                    and g.goods_type != '1002'
                </if>
            </otherwise>
        </choose>
        <if test="notWeighGoodsFlag != null and notWeighGoodsFlag == 2">
            and g.goods_type != '1004'
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            and g.merchant_no = #{merchantNo}
        </if>
        <if test="storeNo != null and storeNo != ''">
            and g.store_no = #{storeNo}
        </if>

        <if test="storeNoList != null and storeNoList.size() > 0">
            and g.store_no in
            <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        <if test="goodsSourceType != null and goodsSourceType != ''">
            and g.goods_source_type = #{goodsSourceType}
        </if>
        <if test="goodsStatus != null and goodsStatus != ''">
            and g.goods_status = #{goodsStatus}
        </if>
        <if test="parentGoodsNo != null and parentGoodsNo !=''">
            and g.parent_goods_no = #{parentGoodsNo}
        </if>
        <if test="originalGoodsNo!=null and originalGoodsNo!=''">
            and g.original_goods_no=#{originalGoodsNo}
        </if>
        <if test="cloudPoolGoodsNo!=null and cloudPoolGoodsNo!=''">
            and g.cloud_pool_goods_no=#{cloudPoolGoodsNo}
        </if>
        <if test="freightTemplateNo != null and freightTemplateNo != ''">
            and g.freight_template_no = #{freightTemplateNo}
        </if>
        <if test="distributionStatus != null and distributionStatus != ''">
            and g.distribution_status = #{distributionStatus}
        </if>
        <if test="distributeGoodsFlag != null">
            and g.distribute_goods_flag = #{distributeGoodsFlag}
        </if>
        <if test="auditType != null and auditType != ''">
            and g.audit_type = #{auditType}
        </if>
        <if test="mainDataFlag != null">
            and g.main_data_flag = #{mainDataFlag}
        </if>
        <if test="disableFlag != null">
            and g.disable_flag = #{disableFlag}
        </if>
        <!--平台身份查询全部列表（包括平台、商家、店铺自建） -->
        <if test="queryType==1001">
            and g.goods_source_type in ('1001','1002','1003')
        </if>
        <!--平台身份查询平台列表（包括平台自建） -->
        <if test="queryType==1002">
            and g.goods_source_type in ('1001')
        </if>
        <!--平台身份查询商家/店铺列表（包括商家、店铺自建） -->
        <if test="queryType==1003">
            and g.goods_source_type in ('1002','1003')
        </if>
        <!--商家身份查询商家列表（包括商家自建） -->
        <if test="queryType==1004">
            and g.goods_source_type in ('1002')
        </if>
        <!--商家身份查询店铺列表（包括店铺自建） -->
        <if test="queryType==1005">
            and g.goods_source_type in ('1003')
        </if>
        <!--店铺身份查询列表（包括商家分发、店铺自建） -->
        <if test="queryType== 1006">
            and g.goods_source_type in ('1003','1005')
        </if>
        <!--店铺身份查询列表（包括商家分发、店铺自建、云池） -->
        <if test="queryType == '1009'">
            and g.goods_source_type in ('1003','1005','1006')
        </if>
        <!--查询云池分销商品列表 -->
        <if test="queryType==1007">
            and g.goods_source_type in ('1006')
        </if>
        <!--平台身份查询全部列表（包括平台自建、商家自建、店铺自建、中台同步）-->
        <if test="queryType==1010">
            and g.goods_source_type in ('1001','1002','1003','1007')
        </if>
        <!--查询中台同步商品列表 -->
        <if test="queryType==1011">
            and g.goods_source_type in ('1007')
        </if>
        <!--查询商家同步商品列表 -->
        <if test="queryType==1012">
            and g.goods_source_type in ('1003')
            and g.merchant_goods_no is not null
        </if>
        <!--查询店铺自建商品列表 -->
        <if test="queryType==1013">
            and g.goods_source_type in ('1003')
            and g.merchant_goods_no is null
        </if>
        <!--排除主品，只展示子品 1002 -->
        <if test="childFlag==1002">
            and g.series_type !='1001'
        </if>
        <if test="seriesType != null and seriesType != ''">
            and g.series_type =#{seriesType}
        </if>
        <if test="auditType != null and auditType != ''">
            and g.audit_type = #{auditType}
        </if>
        <if test="warehouseFlag != null">
            and g.warehouse_flag = #{warehouseFlag}
        </if>
        <if test="imeiFlag != null">
            and g.imei_flag = #{imeiFlag}
        </if>
        <if test="auditStatus != null and auditStatus != ''">
            and g.audit_status = #{auditStatus}
        </if>
        <!--是否有可售库存 主品有货-->
        <if test="stockDisableFlag !=null and childFlag != 1002">
            and exists(select 1 from goods_real_stock st
            where st.available_stock_num > 0
            and (g.original_goods_no = st.parent_goods_no or st.parent_goods_no = g.multi_unit_goods_no))
        </if>
        <!--是否有可售库存 秒杀活动添加列表页的只要有货的子品商品-->
        <if test="stockDisableFlag !=null and childFlag == 1002">
            and st.available_stock_num > 0
        </if>
        <if test="stockFlag == 1">
            and ( select sum(st.available_stock_num) stockNum from goods_real_stock st left join goods g2 on g2.goods_no = st.goods_no where g2.parent_goods_no = g.goods_no) > 0
        </if>
        <if test="stockFlag == 2">
            and ( select sum(st.available_stock_num) stockNum from goods_real_stock st left join goods g2 on g2.goods_no = st.goods_no where g2.parent_goods_no = g.goods_no) <![CDATA[ <= ]]> 0
        </if>
        <if test="distributionDisableFlag !=null and distributionDisableFlag != ''">
            and not exists(select 1 from store_goods_distribution dis
            where
            g.parent_goods_no = dis.goods_no
            and dis.disable_flag = 2
            and dis.delete_flag = 1)
        </if>
        <if test="merchantGoodsNo != null and merchantGoodsNo !=''">
            and g.merchant_goods_no = #{merchantGoodsNo}
        </if>
        <if test="isCustomGoodsNo != null and isCustomGoodsNo !=''">
            and g.custom_goods_no is not null and g.custom_goods_no != ''
        </if>
        <!-- 是否展示辅单位 -->
        <if test="null == isAuxiliaryUnit or '1'.toString() == isAuxiliaryUnit">
            <if test="(goodsNo == null or goodsNo == '') and (goodsNos == null or goodsNos.size() == 0)">
                and g.multi_unit_type != '1002'
            </if>
        </if>
        <if test="null != isAuxiliaryUnit and '3'.toString() == isAuxiliaryUnit">
            and g.multi_unit_type = ''
        </if>
        <if test="multiUnitType != null and multiUnitType != ''">
            and g.multi_unit_type = #{multiUnitType}
        </if>
        <if test="multiUnitGoodsNo != null and multiUnitGoodsNo != ''">
            and g.multi_unit_goods_no = #{multiUnitGoodsNo}
        </if>
        and g.delete_flag = 1
    </sql>

    <sql id="Base_Hxg_Column_Where">
        <if test="goodsNo != null and goodsNo != ''">
            and g.goods_no = #{goodsNo}
        </if>
        <if test="brandNo != null and brandNo != ''">
            and g.brand_no = #{brandNo}
        </if>
        <if test="storeNo != null and storeNo != ''">
            and g.store_no = #{storeNo}
        </if>
        <if test="goodsStatus != null and goodsStatus != ''">
            and g.goods_status = #{goodsStatus}
        </if>
        <if test="auditStatus != null and auditStatus != ''">
            and g.audit_status = #{auditStatus}
        </if>
        <if test="auditType != null and auditType != ''">
            and g.audit_type = #{auditType}
        </if>
        <if test="null == isAuxiliaryUnit or '1'.toString() == isAuxiliaryUnit">
            <if test="(goodsNo == null or goodsNo == '') and (goodsNos == null or goodsNos.size() == 0)">
                and g.multi_unit_type != '1002'
            </if>
        </if>
        <if test="null != isAuxiliaryUnit and '3'.toString() == isAuxiliaryUnit">
            and g.multi_unit_type = ''
        </if>
        <!--上架类型区域, 正常上架的范围应该包括仅上架至活动区域的范围-->
        <if test="shelfType != null and shelfType != ''">
            <choose>
                <when test="shelfType =='1002'.toString()">
                    and g.shelf_type in ('1001', '1002')
                </when>
                <otherwise>
                    and g.shelf_type = #{shelfType}
                </otherwise>
            </choose>
        </if>
        <choose>
            <when test="childFlag != null and childFlag==1002">
                and g.series_type !='1001'
            </when>
            <otherwise>
                and g.series_type !='1002'
            </otherwise>
        </choose>
        and g.distribution_status != '3004'
        and g.delete_flag = 1
        and g.disable_flag = 2
        and g.goods_type != '1002'
    </sql>

    <sql id="GoodsShowStatus_Column_Where">
        <choose>
            <!--审核中或者审核失败 -->
            <when test="goodsShowStatus== 2002 or goodsShowStatus == 2004">
                <![CDATA[ and g.audit_status = #{goodsShowStatus}]]>
            </when>
            <!--已失效 -->
            <when test="goodsShowStatus == 3004">
                <![CDATA[ and g.distribution_status = #{goodsShowStatus}]]>
            </when>
            <!--未上架,必须是店铺自建或者商家分发，分发状态不等于已失效 ,审核状态不等于审核中和审核失败-->
            <when test="goodsShowStatus == 1001">
                <![CDATA[
                    and g.goods_status = '1001'
                    and g.audit_status not in ('2002','2004')
                    and g.goods_source_type in ('1003','1005')
                    and g.distribution_status not in ('3004')
                ]]>
            </when>
            <!--已上架,必须是店铺自建或者商家分发，分发状态分发成功且审核状态也是成功 -->
            <when test="goodsShowStatus == 1002">
                <![CDATA[
                    and g.goods_status = '1002'
                    and g.audit_status = '2003'
                    and g.goods_source_type in ('1003','1005')
                    and g.distribution_status not in ('3001','3002','3004')
                ]]>
            </when>
            <!--未分发,必须是商家自建，审核状态不等于审核中和审核失败-->
            <when test="goodsShowStatus == 3001">
                <![CDATA[
                    and g.audit_status not in ('2002','2004')
                    and g.goods_source_type in ('1002')
                    and g.distribution_status ='3001'
                ]]>
            </when>
            <!--已分发,必须是商家自建，审核状态等于审核成功 -->
            <when test="goodsShowStatus == 3003">
                <![CDATA[
                    and g.audit_status in ('2003')
                    and g.goods_source_type in ('1002')
                    and g.distribution_status ='3003'
                ]]>
            </when>
            <!--未分发,必须是商家自建，审核状态不等于审核中和审核失败-->
            <when test="goodsShowStatus == 3005">
                <![CDATA[
                    and g.audit_status not in ('2002','2004')
                    and g.goods_source_type in ('1002')
                    and g.distribution_status ='3005'
                ]]>
            </when>
            <!--已分发,必须是商家自建，审核状态等于审核成功 -->
            <when test="goodsShowStatus == 3007">
                <![CDATA[
                    and g.audit_status in ('2003')
                    and g.goods_source_type in ('1002')
                    and g.distribution_status ='3007'
                ]]>
            </when>
        </choose>
    </sql>

    <sql id="Select_Series_Column_List">
        g.goods_no,
		g.first_attribute_name,
		g.first_attribute_value_name,
		g.second_attribute_name,
		g.second_attribute_value_name,
		g.third_attribute_name,
		g.third_attribute_value_name
    </sql>


    <!-- 商品选择用 -->
    <!-- 商品编码和名称条件 -->
    <sql id="Select_Base_Column_Where">
        <if test="goodsStr  != null and goodsStr  != ''">
            and (g.goods_no like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%") OR g.goods_name LIKE
            CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
        </if>
        <if test="goodsNo != null and goodsNo != ''">
            and g.goods_no = #{goodsNo}
        </if>
        <if test="goodsName != null and goodsName != ''">
            and g.goods_name LIKE CONCAT('%',#{goodsName,jdbcType=VARCHAR},'%')
        </if>
        <choose>
            <when test="goodsType != null and goodsType != ''">
                and g.goods_type = #{goodsType}
            </when>
            <otherwise>
                <if test="(goodsNo == null or goodsNo == '') and (goodsNos == null or goodsNos.size() == 0)">
                    and g.goods_type != '1002'
                </if>
            </otherwise>
        </choose>
    </sql>
    <!-- 商品编码和名称条件-分开 -->
    <sql id="Select_Base_Column_Where_Split">
        <if test="goodsNo != null and goodsNo != ''">
            and g.goods_no = #{goodsNo}
        </if>
        <if test="goodsName != null and goodsName != ''">
            and g.goods_name LIKE CONCAT('%',#{goodsName,jdbcType=VARCHAR},'%')
        </if>
    </sql>
    <!-- 品牌条件 -->
    <sql id="Select_Base_Column_Where_Brand">
        <if test="brandNo != null and brandNo != ''">
            and g.brand_no = #{brandNo}
        </if>
    </sql>
    <!-- 销售类目条件 -->
    <sql id="Select_Base_Column_Where_Category">
        <if test="categoryNo != null and categoryNo != ''">
            and g.category_no = #{categoryNo}
        </if>
        <if test="fullIdPath != null and fullIdPath!=''">
            and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
        </if>
    </sql>
    <!-- 平台、商家、店铺编码条件 -->
    <sql id="Select_Base_Column_Where_PMSCode">
        <if test="merchantNo != null and merchantNo != ''">
            and g.merchant_no = #{merchantNo}
        </if>
        <if test="storeNo != null and storeNo != ''">
            and g.store_no = #{storeNo}
        </if>
    </sql>
    <!-- 平台、商家、店铺自建条件 -->
    <sql id="Select_Base_Column_Where_PMS">
        <!--平台身份查询平台列表（包括平台自建） -->
        <if test="queryType==1002">
            and g.goods_source_type in ('1001')
        </if>
        <!--商家身份查询商家列表（包括商家自建） -->
        <if test="queryType==1004">
            and g.goods_source_type in ('1002')
        </if>
        <!--商家身份查询店铺列表（包括店铺自建） -->
        <if test="queryType==1005">
            and g.goods_source_type in ('1003', '1005')
        </if>
    </sql>
    <!-- 上架条件 -->
    <sql id="Select_Base_Column_Where_Status">
        <if test="goodsStatus != null and goodsStatus != ''">
            and g.goods_status = #{goodsStatus}
        </if>
    </sql>
    <!-- 审核通过条件 -->
    <sql id="Select_Base_Column_Where_Audit">
        <if test="auditStatus != null and auditStatus != ''">
            and g.audit_status = #{auditStatus}
        </if>
    </sql>
    <!-- 排除主品，只展示子品条件1002 只展示主品条件1001 -->
    <sql id="Select_Base_Column_Where_Child">
        <if test="childFlag==1001">
            and g.series_type !='1002'
        </if>
        <if test="childFlag==1002">
            and g.series_type !='1001'
        </if>
    </sql>
    <!-- 选择展示主子品 -->
    <sql id="Select_Base_Column_Where_Child_Choose">
        <if test="childFlag==1002">
            and g.series_type !='1001'
        </if>
        <!-- 20230928蛋品-吴鑫鑫-商品管理-多规格商品查询 -->
        <if test="childFlag != null and childFlag != '' and childFlag != '1002'">
            and g.series_type !='1002'
        </if>
    </sql>
    <!-- 是否有可售库存 有货 -->
    <sql id="Select_Base_Column_Where_Has">
        <if test="stockDisableFlag !=null ">
            and exists(select 1 from goods_real_stock st
            where st.available_stock_num > 0
            and g.original_goods_no = st.parent_goods_no)
        </if>
    </sql>
    <!-- 是否有可售库存 有货 区分主品 子品-->
    <sql id="Select_Base_Column_Where_Has_Child">
        <if test="stockDisableFlag !=null and childFlag != 1002">
            and exists(select 1 from goods_real_stock st
            where st.available_stock_num > 0
            and (g.original_goods_no = st.parent_goods_no or (g.multi_unit_type = '1002' and g.multi_unit_goods_no = st.goods_no)))
        </if>
        <if test="stockDisableFlag !=null and childFlag == 1002">
            and exists(select 1 from goods_real_stock st
            where st.available_stock_num > 0
            and (g.original_goods_no = st.goods_no or (g.multi_unit_type = '1002' and g.multi_unit_goods_no = st.goods_no)))
        </if>
    </sql>
    <!-- 商品是否删除条件 -->
    <sql id="Select_Base_Column_Where_Delete">
        and g.delete_flag = 1
    </sql>
    <!-- 供货店铺ID -->
    <sql id="Select_Base_Column_Where_SupplyStore">
        <if test="storeNo != null and storeNo != ''">
            and g.store_no = #{storeNo}
        </if>
    </sql>
    <!-- 商品是否云池 -->
    <sql id="Select_Base_Column_Where_Cloud">
        <if test="goodsSourceType != null and goodsSourceType != ''">
            and g.goods_source_type = #{goodsSourceType}
        </if>
    </sql>


    <!-- 同步商品信息到es库使用-->
    <select id="selectAllGoodsPage" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSyncGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods g
        <where>
            g.delete_flag = 1
            <if test="goodsNos != null and goodsNos.size() > 0">
                and g.goods_no IN
                <foreach collection="goodsNos" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="startTime != null">
                and g.create_time &gt;= #{startTime}
            </if>
            <if test="endTime != null">
                and g.modify_time &lt;= #{endTime}
            </if>
        </where>
    </select>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select distinct
        <include refid="Base_Column_List"/>,
        g.sort_num,
        <include refid="Select_Base_Column_Category_Brand"/>
        from
        goods g
        LEFT JOIN sale_category sc ON g.category_no = sc.category_no
        LEFT JOIN brand b ON g.brand_no = b.brand_no
        <if test="bossAppGoodsSearchKey != null and bossAppGoodsSearchKey != ''">
            LEFT JOIN goods g1 on g1.parent_goods_no = g.parent_goods_no
        </if>
        <if test="pcBarcodeSearch != null and pcBarcodeSearch != ''">
            LEFT JOIN goods g1 on g1.parent_goods_no = g.parent_goods_no
        </if>
        <if test="goodsNameOrBarCodeOrImei != null and goodsNameOrBarCodeOrImei != ''">
            LEFT JOIN goods g1 on g1.parent_goods_no = g.parent_goods_no
        </if>
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <include refid="GoodsShowStatus_Column_Where"/>
                <!--列表查询不展示子品，且非门店开单 -->
                <if test="queryType!=null and queryType!='' and storeBillingFlag!=2 and childFlag!=1002">
                    and g.series_type !='1002'
                </if>
                <!--店铺身份查询列表（包括商家分发、店铺自建） -->
                <if test="queryType==1006">
                    <!-- 门店开单查询非主询非主品的商品-->
                    <if test="storeBillingFlag==2">
                        and g.series_type !='1001'
                    </if>
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="parentGoodsNos != null and parentGoodsNos.size() > 0">
                    and g.parent_goods_no IN
                    <foreach collection="parentGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="originalGoodsNos != null and originalGoodsNos.size() > 0">
                    and g.original_goods_no IN
                    <foreach collection="originalGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="cloudPoolGoodsNos != null and cloudPoolGoodsNos.size() > 0">
                    and g.cloud_pool_goods_no IN
                    <foreach collection="cloudPoolGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="barcodeList != null and barcodeList.size() > 0">
                    and g.barcode IN
                    <foreach collection="barcodeList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                <if test="fullIdPaths != null and fullIdPaths.size() > 0">
                    and sc.full_id_path in
                    <foreach collection="fullIdPaths" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="categoryNos != null and categoryNos.size() > 0">
                    and
                    <foreach collection="categoryNos" item="item" index="index"
                             open="(" separator="or" close=")">
                        sc.full_id_path like CONCAT(CONCAT(#{item}),'%')
                    </foreach>
                </if>
                <if test="brandNos != null and brandNos.size() > 0">
                    and b.brand_no IN
                    <foreach collection="brandNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="brandName != null and brandName != ''">
                    and b.brand_name LIKE CONCAT (CONCAT('%',#{brandName}),'%')
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and g.store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="merchantGoodsNos != null and merchantGoodsNos.size() > 0">
                    and g.merchant_goods_no in
                    <foreach collection="merchantGoodsNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 店铺秒杀活动添加商品页的商品查询条件 -->
                <if test="goodsNoOrName != null and goodsNoOrName != ''">
                    and (
                    g.goods_no = #{goodsNoOrName} or g.goods_name like CONCAT(CONCAT('%', #{goodsNoOrName}),'%')
                    )
                </if>

                <if test="goodsNameOrBarCodeOrImei != null and goodsNameOrBarCodeOrImei != ''">
                    and (
                    g1.barcode = #{goodsNameOrBarCodeOrImei}
                    or g1.goods_name like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    or g1.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    or g1.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    or g1.goods_no in (select t.goods_no from goods_imei t where t.imei = #{goodsNameOrBarCodeOrImei})
                    or g1.goods_help_code like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    )
                </if>
                <if test="goodsInitial != null and goodsInitial != ''">
                    and g.goods_initial = #{goodsInitial}
                </if>
                <!-- 排除指定的商品 -->
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <!-- 字段占用新加字段goodsCategoryNos 类目下商品 -->
                <if test="goodsCategoryNos != null and goodsCategoryNos.size() > 0">
                    and g.category_no IN
                    <foreach collection="goodsCategoryNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="foodsType!=null and foodsType != ''">
                    and g.foods_type = #{foodsType}
                </if>
                <if test="bossAppGoodsSearchKey != null and bossAppGoodsSearchKey != ''">
                    and (
                    g1.barcode = #{bossAppGoodsSearchKey}
                    or g1.goods_name like CONCAT(CONCAT('%', #{bossAppGoodsSearchKey}),'%')
                    )
                    and g1.delete_flag = 1
                </if>
                <if test="pcBarcodeSearch != null and pcBarcodeSearch != ''">
                    and g1.barcode = #{pcBarcodeSearch}
                    and g1.delete_flag = 1
                </if>

                <!--一体机AI识别商品用-->
                <if test="scGoodsName != null and scGoodsName != ''">
                    and g.goods_name = #{scGoodsName}
                </if>
                <if test="scGoodsNameList != null and scGoodsNameList.size() > 0">
                    and g.goods_name IN
                    <foreach collection="scGoodsNameList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!--小程序, 不同区域, 查询不同上架类型的云池商品, 正常上架的范围应该包括仅上架至活动区域的范围-->
                <if test="shelfType != null and shelfType != ''">
                    <choose>
                        <when test="shelfType =='1002'.toString()">
                            and g.shelf_type in ('1001', '1002')
                        </when>
                        <otherwise>
                            and g.shelf_type = #{shelfType}
                        </otherwise>
                    </choose>
                </if>
                <!--查询不入库存的商品，且过滤绑定了商品组的商品-->
                <if test="noWarehouseGoods == 1">
                    and NOT EXISTS(select 1 from goods_groups_related_goods ggrg where ggrg.goods_no =g.goods_no and ggrg.delete_flag = 1)
                    and g.warehouse_flag != 2
                </if>
            </trim>
        </where>
        <choose>
            <when test="scSortField != null and scSortField == 'sortNum'">
                order by isnull(g.sort_num), g.sort_num ,g.modify_time desc
            </when>
            <otherwise>
                order by g.modify_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getGoodsByParam" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsAsyncVo"
            resultType="cn.htdt.goodsprocess.vo.AtomGoodsAsyncVo">
        select distinct
        <include refid="Base_Column_List"/>
        <if test="queryFlag != null and queryFlag == 1002">
            ,g2.goods_no storeGoodsNo
        </if>
        from
        goods g
        <if test="queryFlag != null and queryFlag == 1002">
            inner join goods g2 on g.goods_no = g2.merchant_goods_no and g2.delete_flag = 1
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="queryFlag == 1002">
                    and g2.goods_no in
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryFlag == 1001">
                    and g.store_no = #{storeNo}
                    and g.merchant_goods_no in
                    <foreach collection="merchantGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
    </select>

<!--    202503 商品列表查询-->
    <select id="getGoodsStoreCategory" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.DecoAtomGoodsVo">
        select
        <include refid="Base_Column_List"/>,
        <include refid="Select_Base_Column_store_Category"/>,
        <include refid="Select_Base_Column_store_Category_goods_relation"/>
        from
        goods g
        LEFT JOIN store_category_goods_relation scg ON (g.parent_goods_no = scg.goods_no or g.multi_unit_goods_no = scg.goods_no)
        left join store_category sc on scg.store_category_no = sc.store_category_no
        LEFT JOIN brand b ON g.brand_no = b.brand_no
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <include refid="GoodsShowStatus_Column_Where"/>
                <!--列表查询不展示子品，且非门店开单 -->
                <if test="queryType!=null and queryType!='' and storeBillingFlag!=2">
                    and g.series_type !='1002'
                </if>
                <!--店铺身份查询列表（包括商家分发、店铺自建） -->
                <if test="queryType==1006">
                    <!-- 门店开单查询非主询非主品的商品-->
                    <if test="storeBillingFlag==2">
                        and g.series_type !='1001'
                    </if>
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="parentGoodsNos != null and parentGoodsNos.size() > 0">
                    and g.parent_goods_no IN
                    <foreach collection="parentGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="originalGoodsNos != null and originalGoodsNos.size() > 0">
                    and g.original_goods_no IN
                    <foreach collection="originalGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="cloudPoolGoodsNos != null and cloudPoolGoodsNos.size() > 0">
                    and g.cloud_pool_goods_no IN
                    <foreach collection="cloudPoolGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="barcodeList != null and barcodeList.size() > 0">
                    and g.barcode IN
                    <foreach collection="barcodeList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <choose>
                    <when test="fullIdPath != null and fullIdPath == '10001'">
                        and sc.full_id_path is null
                    </when>
                    <when test="fullIdPath != null and fullIdPath!=''">
                        and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                    </when>
                </choose>
                <choose>
                    <when test="storeCategoryFullIdPath != null and storeCategoryFullIdPath == '10001'">
                        and sc.full_id_path is null
                    </when>
                    <when test="storeCategoryFullIdPath != null and storeCategoryFullIdPath!=''">
                        and sc.full_id_path like CONCAT(CONCAT(#{storeCategoryFullIdPath}),'%')
                    </when>
                </choose>
                <if test="categoryNos != null and categoryNos.size() > 0">
                    and
                    <foreach collection="categoryNos" item="item" index="index"
                             open="(" separator="or" close=")">
                        sc.full_id_path like CONCAT(CONCAT(#{item}),'%')
                    </foreach>
                </if>
                <if test="storeCategoryNo != null and storeCategoryNo != ''">
                    and sc.store_category_no = #{storeCategoryNo}
                </if>
                <if test="brandNos != null and brandNos.size() > 0">
                    and b.brand_no IN
                    <foreach collection="brandNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="brandName != null and brandName != ''">
                    and b.brand_name LIKE CONCAT (CONCAT('%',#{brandName}),'%')
                </if>
                <!-- 店铺秒杀活动添加商品页的商品查询条件 -->
                <if test="goodsNoOrName != null and goodsNoOrName != ''">
                    and (
                    g.goods_no = #{goodsNoOrName} or g.goods_name like CONCAT(CONCAT('%', #{goodsNoOrName}),'%')
                    )
                </if>
                <if test="goodsInitial != null and goodsInitial != ''">
                    and g.goods_initial = #{goodsInitial}
                </if>
                <!-- 排除指定的商品 -->
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <!--一体机AI识别商品用-->
                <if test="goodsNameOrBarCodeOrImei != null and goodsNameOrBarCodeOrImei != ''">
                    and (
                    g.barcode = #{goodsNameOrBarCodeOrImei}
                    or g.goods_name like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    or g.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    or g.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    or g.goods_no in (select t.goods_no from goods_imei t where t.imei = #{goodsNameOrBarCodeOrImei})
                    or g.goods_help_code like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    )
                </if>
                <if test="scGoodsName != null and scGoodsName != ''">
                    and g.goods_name = #{scGoodsName}
                </if>
                <if test="scGoodsNameList != null and scGoodsNameList.size() > 0">
                    and g.goods_name IN
                    <foreach collection="scGoodsNameList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="foodsType!=null and foodsType != ''">
                    and g.foods_type = #{foodsType}
                </if>
            </trim>
        </where>
        group by g.goods_no
        <choose>
            <when test="scSortField != null and scSortField == 'sortNum'">
                order by isnull(scg.sort_num),scg.sort_num
            </when>
            <otherwise>
                order by g.modify_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getDecoGoodsPage" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods g
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <include refid="GoodsShowStatus_Column_Where"/>
                <!--列表查询不展示子品，且非门店开单 -->
                <if test="queryType!=null and queryType!='' and storeBillingFlag!=2">
                    and g.series_type !='1002'
                </if>
                <!--店铺身份查询列表（包括商家分发、店铺自建） -->
                <if test="queryType==1006">
                    <!-- 门店开单查询非主询非主品的商品-->
                    <if test="storeBillingFlag==2">
                        and g.series_type !='1001'
                    </if>
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="parentGoodsNos != null and parentGoodsNos.size() > 0">
                    and g.parent_goods_no IN
                    <foreach collection="parentGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="originalGoodsNos != null and originalGoodsNos.size() > 0">
                    and g.original_goods_no IN
                    <foreach collection="originalGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="cloudPoolGoodsNos != null and cloudPoolGoodsNos.size() > 0">
                    and g.cloud_pool_goods_no IN
                    <foreach collection="cloudPoolGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="barcodeList != null and barcodeList.size() > 0">
                    and g.barcode IN
                    <foreach collection="barcodeList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 店铺秒杀活动添加商品页的商品查询条件 -->
                <if test="goodsNoOrName != null and goodsNoOrName != ''">
                    and (
                    g.goods_no = #{goodsNoOrName} or g.goods_name like CONCAT(CONCAT('%', #{goodsNoOrName}),'%')
                    )
                </if>
                <if test="goodsInitial != null and goodsInitial != ''">
                    and g.goods_initial = #{goodsInitial}
                </if>
                <!-- 排除指定的商品 -->
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="foodsType!=null and foodsType != ''">
                    and g.foods_type = #{foodsType}
                </if>
            </trim>
        </where>
        group by g.goods_no
        <choose>
            <when test="orderBy=='sortNum'">
                order by scg.sort_num
            </when>
            <otherwise>
                order by g.modify_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getDecoGoodsPageByCategory" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.DecoAtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods g
        inner join store_category_goods_relation scg on
        g.parent_goods_no = scg.goods_no
        inner join store_category sc1 on
        scg.store_category_no = sc1.store_category_no
        inner join sale_category sc2 on
        g.category_no = sc2.category_no
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <include refid="GoodsShowStatus_Column_Where"/>
                <!--列表查询不展示子品，且非门店开单 -->
                <if test="queryType!=null and queryType!='' and storeBillingFlag!=2">
                    and g.series_type !='1002'
                </if>
                <!--店铺身份查询列表（包括商家分发、店铺自建） -->
                <if test="queryType==1006">
                    <!-- 门店开单查询非主询非主品的商品-->
                    <if test="storeBillingFlag==2">
                        and g.series_type !='1001'
                    </if>
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="parentGoodsNos != null and parentGoodsNos.size() > 0">
                    and g.parent_goods_no IN
                    <foreach collection="parentGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="originalGoodsNos != null and originalGoodsNos.size() > 0">
                    and g.original_goods_no IN
                    <foreach collection="originalGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="cloudPoolGoodsNos != null and cloudPoolGoodsNos.size() > 0">
                    and g.cloud_pool_goods_no IN
                    <foreach collection="cloudPoolGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="barcodeList != null and barcodeList.size() > 0">
                    and g.barcode IN
                    <foreach collection="barcodeList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <choose>
                    <when test="fullIdPath != null and fullIdPath == '10001'">
                        and sc2.full_id_path is null
                    </when>
                    <when test="fullIdPath != null and fullIdPath!=''">
                        and sc2.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                    </when>
                </choose>
                <if test="fullIdPaths != null and fullIdPaths.size() > 0">
                    and sc2.full_id_path in
                    <foreach collection="fullIdPaths" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <choose>
                    <when test="storeCategoryFullIdPath != null and storeCategoryFullIdPath == '10001'">
                        and sc1.full_id_path is null
                    </when>
                    <when test="storeCategoryFullIdPath != null and storeCategoryFullIdPath!=''">
                        and sc1.full_id_path like CONCAT(CONCAT(#{storeCategoryFullIdPath}),'%')
                    </when>
                </choose>
                <if test="storeCategoryFullIdPaths != null and storeCategoryFullIdPaths.size() > 0">
                    and sc1.full_id_path in
                    <foreach collection="storeCategoryFullIdPaths" item="item" index="index" open="(" separator=","
                             close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="categoryNos != null and categoryNos.size() > 0">
                    and
                    <foreach collection="categoryNos" item="item" index="index"
                             open="(" separator="or" close=")">
                        sc.full_id_path like CONCAT(CONCAT(#{item}),'%')
                    </foreach>
                </if>
                <if test="storeCategoryNo != null and storeCategoryNo != ''">
                    and sc.store_category_no = #{storeCategoryNo}
                </if>
                <!-- 店铺秒杀活动添加商品页的商品查询条件 -->
                <if test="goodsNoOrName != null and goodsNoOrName != ''">
                    and (
                    g.goods_no = #{goodsNoOrName} or g.goods_name like CONCAT(CONCAT('%', #{goodsNoOrName}),'%')
                    )
                </if>
                <if test="goodsInitial != null and goodsInitial != ''">
                    and g.goods_initial = #{goodsInitial}
                </if>
                <!-- 排除指定的商品 -->
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="foodsType!=null and foodsType != ''">
                    and g.foods_type = #{foodsType}
                </if>
            </trim>
        </where>
        group by g.goods_no
        <choose>
            <when test="orderBy=='sortNum'">
                order by scg.sort_num
            </when>
            <otherwise>
                order by g.modify_time desc
            </otherwise>
        </choose>
    </select>

    <select id="getHxgGoodsPageList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods g
        <if test="(goodsName != null and goodsName != '')
            or (categoryNos != null and categoryNos.size() > 0)
            or (firstCategoryNo != null and firstCategoryNo !='')
            or (secondCategoryNo != null and secondCategoryNo !='')
            or (thirdCategoryNo != null and thirdCategoryNo !='')
            or (categoryNo != null and categoryNo !='')">
        LEFT JOIN sale_category sc ON g.category_no = sc.category_no
        </if>
        <if test="(goodsName != null and goodsName != '') or (brandNos != null and brandNos.size() > 0)">
        LEFT JOIN brand b ON g.brand_no = b.brand_no
        </if>
        <if test="storeCategoryNos != null and storeCategoryNos.size() > 0">
            left join store_category_goods_relation scgr on scgr.goods_no = g.goods_no and scgr.delete_flag = 1
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Hxg_Column_Where"/>
                <if test="goodsName != null and goodsName != ''">
                    and (g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                    or sc.category_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                    or b.brand_name like CONCAT(CONCAT('%', #{goodsName}),'%'))
                </if>
                <if test="goodsType != null and goodsType != ''">
                    and g.goods_type = #{goodsType}
                </if>
                <if test="categoryNos != null and categoryNos.size() > 0">
                    and sc.category_no IN
                    <foreach collection="categoryNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="storeCategoryNos != null and storeCategoryNos.size() > 0">
                    and scgr.store_category_no IN
                    <foreach collection="storeCategoryNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="firstCategoryNo != null and firstCategoryNo !='' ">
                    and sc.first_category_no = #{firstCategoryNo}
                </if>
                <if test="secondCategoryNo != null and secondCategoryNo !='' ">
                    and sc.second_category_no = #{secondCategoryNo}
                </if>
                <if test="thirdCategoryNo != null and thirdCategoryNo !='' ">
                    and sc.third_category_no = #{thirdCategoryNo}
                </if>
                <if test="categoryNo != null and categoryNo !='' ">
                    and sc.category_no = #{categoryNo}
                </if>
                <if test="brandNos != null and brandNos.size() > 0">
                    and b.brand_no IN
                    <foreach collection="brandNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="cloudPoolGoodsNos != null and cloudPoolGoodsNos.size() > 0">
                    and g.cloud_pool_goods_no IN
                    <foreach collection="cloudPoolGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="merchantGoodsNos != null and merchantGoodsNos.size() > 0">
                    and g.merchant_goods_no IN
                    <foreach collection="merchantGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <choose>
                    <when test="goodsSourceType == '1006'.toString()">
                        and g.goods_source_type = #{goodsSourceType}
                    </when>
                    <when test="queryType == '1012'.toString()">
                        and g.goods_source_type in ('1003')
                        and g.merchant_goods_no is not null
                    </when>
                    <when test="queryType == '1009'.toString()">
                        and g.goods_source_type in ('1003','1005','1006')
                    </when>
                    <otherwise>
                        and g.goods_source_type in ('1003','1005')
                    </otherwise>
                </choose>
                <if test="hxgSortValue != null and hxgSortValue!=''">
                    <![CDATA[
                	order by ${hxgSortValue} ${hxgSortType}
                ]]>
                </if>
                <if test="hxgSortValue != null and hxgSortValue!='' and shopDecoSortValue != null and shopDecoSortValue!=''">
                    <![CDATA[
                	order by ${hxgSortValue} ${hxgSortType},${shopDecoSortValue} ${shopDecoSortType}
                ]]>
                </if>
            </trim>
        </where>
    </select>

    <select id="getNonSaleCategoryHxgGoodsCount" resultType="java.lang.Integer"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        count(*)
        from
        goods g
        INNER JOIN sale_category sc
        ON g.category_no = sc.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Hxg_Column_Where"/>
                <choose>
                    <when test="goodsSourceType == '1006'.toString()">
                        and g.goods_source_type = #{goodsSourceType}
                    </when>
                    <when test="queryType == '1009'.toString()">
                        and g.goods_source_type in ('1003','1005','1006')
                    </when>
                    <otherwise>
                        and g.goods_source_type in ('1003','1005')
                    </otherwise>
                </choose>
                <if test="categoryNo != null and categoryNo !='' ">
                    and sc.category_no = #{categoryNo}
                </if>
                AND sc.delete_flag = '1'
            </trim>
        </where>
    </select>

    <select id="getHxgGoodsStoreCategoryPageList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <!--根据店铺类目查询商品时, 是否需要去重 -->
        <if test="storeCategoryDistinctGoodFlag !=null and storeCategoryDistinctGoodFlag != '' and storeBillingFlag = 2">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from
        goods g
        LEFT JOIN store_category_goods_relation scg
        ON g.goods_no = scg.goods_no
        left join store_category stc on scg.store_category_no = stc.store_category_no
        LEFT JOIN brand b
        ON g.brand_no = b.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Hxg_Column_Where"/>
                <if test="goodsName != null and goodsName != ''">
                    and (g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                    or stc.store_category_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                    or b.brand_name like CONCAT(CONCAT('%', #{goodsName}),'%'))
                </if>
                <if test="storeCategoryNos != null and storeCategoryNos.size() > 0">
                    and scg.store_category_no IN
                    <foreach collection="storeCategoryNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and g.goods_source_type in ('1003','1005')
                <if test="hxgSortValue != null and hxgSortValue!='' and hxgSortType != null and hxgSortType!=''">
                    <![CDATA[
                	order by ${hxgSortValue} ${hxgSortType}
                ]]>
                </if>
            </trim>
        </where>
    </select>

    <!--汇享购查询无类目商品-->
    <select id="getHxgNonStoreCategoryGoodsPageList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods g
        LEFT JOIN store_category_goods_relation scg
        ON g.goods_no = scg.goods_no and scg.delete_flag = 1
        left join store_category stc on scg.store_category_no = stc.store_category_no and stc.delete_flag = 1
        LEFT JOIN brand b
        ON g.brand_no = b.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Hxg_Column_Where"/>
                <if test="goodsName != null and goodsName != ''">
                    and (g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                    or stc.store_category_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                    or b.brand_name like CONCAT(CONCAT('%', #{goodsName}),'%'))
                </if>
                and g.goods_source_type in ('1003','1005')
                and stc.id is null
                <if test="hxgSortValue != null and hxgSortValue!='' and hxgSortType != null and hxgSortType!=''">
                    <![CDATA[
                	order by ${hxgSortValue} ${hxgSortType}
                ]]>
                </if>
            </trim>
        </where>
    </select>


    <select id="getHxgGoodsCategoryList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        DISTINCT sc.category_no, sc.category_name
        from
        goods g
        INNER JOIN sale_category sc
        ON g.category_no = sc.category_no
        LEFT JOIN brand b
        ON g.brand_no = b.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Hxg_Column_Where"/>
                <if test="goodsSourceType != null and goodsSourceType != ''">
                    and g.goods_source_type = #{goodsSourceType}
                </if>
            </trim>
        </where>
    </select>

    <select id="getDistributionGoodsPageList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods g
        LEFT JOIN sale_category sc
        ON g.category_no = sc.category_no
        LEFT JOIN brand b
        ON g.brand_no = b.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Hxg_Column_Where"/>
                <if test="goodsName != null and goodsName != ''">
                    and (g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                    or sc.category_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                    or b.brand_name like CONCAT(CONCAT('%', #{goodsName}),'%'))
                </if>
                <if test="categoryNos != null and categoryNos.size() > 0">
                    and sc.category_no IN
                    <foreach collection="categoryNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="firstCategoryNo != null and firstCategoryNo !=''">
                    and sc.first_category_no = #{firstCategoryNo}
                </if>
                and g.disable_flag = 2
                <if test="cloudGoods == 1">
                    and g.goods_source_type = '1006'
                </if>
                <if test="distributionGoods == 1">
                    and g.distribute_goods_flag = 2
                </if>
                <if test="cloudGoodsAndDistributionGoods == 1 ">
                    and (g.goods_source_type = '1006' or g.distribute_goods_flag = 2)
                </if>
                <![CDATA[
                	order by ${hxgSortValue} ${hxgSortType} ,g.goods_source_type  ${hxgSortType}
                ]]>
            </trim>
        </where>
    </select>

    <select id="getEveryDayGoodsPageList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods g
        LEFT JOIN sale_category sc
        ON g.category_no = sc.category_no
        LEFT JOIN brand b
        ON g.brand_no = b.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Hxg_Column_Where"/>
                <if test="goodsSourceType != null and goodsSourceType != ''">
                    and g.goods_source_type = #{goodsSourceType}
                </if>
                and g.disable_flag = 2
                <![CDATA[
                	order by g.modify_time desc
                	limit 20
                ]]>
            </trim>
        </where>
    </select>

    <select id="getStoreGoodsList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods g
        LEFT JOIN sale_category sc
        ON g.category_no = sc.category_no
        LEFT JOIN brand b
        ON g.brand_no = b.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Hxg_Column_Where"/>
                and g.goods_source_type in ('1003','1005','1006')
                <![CDATA[
                	order by g.modify_time desc
                	limit 3
                ]]>
            </trim>
        </where>
    </select>

    <select id="selectGoodsByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from goods g
        LEFT JOIN sale_category sc
        ON g.category_no = sc.category_no
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <include refid="Select_Base_Column_Where"/>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="brandNos != null and brandNos.size() > 0">
                    and g.brand_no IN
                    <foreach collection="brandNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="originalGoodsNos != null and originalGoodsNos.size() > 0">
                    and original_goods_no IN
                    <foreach collection="originalGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="cloudPoolGoodsNos != null and cloudPoolGoodsNos.size() > 0">
                    and cloud_pool_goods_no IN
                    <foreach collection="cloudPoolGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
        order by g.create_time desc
    </select>

    <!--获取用户下的绑定商品 关联品牌、属性、计量单位获取名称 系列商品主品不能够采购-->
    <select id="selectGoodsInfoByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Select_Goods_Column_List"/>
        from goods g
        LEFT JOIN sale_category sc
        ON g.category_no = sc.category_no
        LEFT JOIN brand b
        ON g.brand_no = b.brand_no
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <include refid="Select_Base_Column_Where"/>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="notExistsGroupsFlag != null and notExistsGroupsFlag =='2'.toString()">
                    and NOT EXISTS (
                        SELECT 1 FROM goods_groups_related_goods rg
                        WHERE rg.goods_no = g.goods_no
                        <if test="relatedGoodsList != null and relatedGoodsList.size() > 0">
                            and rg.goods_no not in
                            <foreach item="goodsNo" collection="relatedGoodsList" open="(" separator="," close=")">
                                #{goodsNo}
                            </foreach>
                        </if>
                    )
                    and NOT EXISTS (
                    SELECT 1 FROM goods_groups_related_goods rg
                    WHERE rg.goods_no = g.merchant_goods_no
                    )
                </if>
                and g.series_type !='1001'
                and g.delete_flag = 1
            </trim>
        </where>
        order by g.modify_time desc
    </select>

    <select id="selectGoodsByNo" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from goods g
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.goods_no = #{goodsNo}
                and g.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="selectGoodsByThirdGoodsNo" resultMap="BaseResultMap"
            parameterType="java.lang.String">
        select
        <include refid="Base_Column_List"/>
        from goods g
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.third_goods_no = #{thirdGoodsNo}
                and g.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="selectGoodsListByThirdGoodsNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from goods g
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.third_goods_no in
                <foreach collection="thirdGoodsNos" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and g.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="selectGoodsListByCustomGoodsNos" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from goods g
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo == null">
                    and g.goods_source_type = '1002'
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and g.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and g.store_no = #{storeNo}
                </if>
                and g.custom_goods_no in
                <foreach collection="customGoodsNos" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and g.delete_flag = 1
            </trim>
        </where>
    </select>

    <!-- 根据goodsNos查询一批商品信息 (批量查询编号需要后续查询商品图片,就算商品删除了,待评价的图片还需要商品编号查询,所以去除delete_flag条件)-->
    <select id="selectGoodsByNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from goods g
        WHERE g.goods_no in
        <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!-- 需要查询storeNo为''的场景 -->
    <select id="selectGoodsByParam" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from goods g
        WHERE g.delete_flag = 1
        <if test="goodsNos != null and goodsNos.size() > 0">
            and g.goods_no in
            <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="multiUnitGoodsNos != null and multiUnitGoodsNos.size() > 0">
            and g.multi_unit_goods_no in
            <foreach collection="multiUnitGoodsNos" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="goodsForm != null and goodsForm != ''">
            <if test="goodsForm != '1004'">
                and g.goods_form = #{goodsForm} and multi_unit_goods_no = ''
            </if>
            <if test="goodsForm == '1004'">
                and g.multi_unit_goods_no != ''
            </if>
        </if>
        <if test="multiUnitType != null and multiUnitType != ''">
            and g.multi_unit_type = #{multiUnitType}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            and g.merchant_no = #{merchantNo}
        </if>
        <if test="storeNo != null">
            and g.store_no = #{storeNo}
        </if>
        order by id
    </select>

    <!-- 通过商品编号获取商品记录 -->
    <select id="selectDetailByGoodsNo" resultType="cn.htdt.goodsprocess.domain.GoodsDomain">
        select
        <include refid="Base_Column_List"/>
        from goods g
        where g.delete_flag = 1 and g.goods_no = #{goodsNo} AND store_no = #{storeNo}
    </select>

    <!-- 通过系列商品主品编号获取系列商品记录 -->
    <select id="selectByParentGoodsNo" resultType="cn.htdt.goodsprocess.domain.GoodsDomain">
        select
        <include refid="Base_Column_List"/>
        from goods g
        where g.delete_flag = 1 and g.parent_goods_no = #{parentGoodsNo} AND store_no = #{storeNo}
    </select>

    <!-- 通过系列商品主品编号获取系列商品编号 -->
    <select id="selectGoodsNoByParentGoodsNo" resultType="cn.htdt.goodsprocess.vo.AtomHxgParentAndSubGoodsNoVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        g.goods_no as goodsNo, g.parent_goods_no as parentGoodsNo
        from goods g
        where g.store_no = #{storeNo} AND g.delete_flag = 1
        <if test="seriesType != null and seriesType !=''">
            and g.series_type = #{seriesType}
        </if>
        <if test="parentGoodsNos != null and parentGoodsNos.size() > 0">
            and g.parent_goods_no in
            <foreach collection="parentGoodsNos" item="item" index="index"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 通过系列商品主品编号获取系列商品最高价和最低价 -->
    <select id="selectRetailPriceByParentGoodsNo" resultType="cn.htdt.goodsprocess.vo.AtomMaxMinPriceVo">
        select max(retail_price) maxPrice, min(retail_price) minPrice
        from goods g
        where g.delete_flag = 1
          and g.parent_goods_no = #{parentGoodsNo}
          AND store_no = #{storeNo}
    </select>

    <select id="selectGoodsByNos" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        , u.calculation_unit_name calculation_unit_name,
        u2.calculation_unit_name assist_calculation_unit_name,
        sc.category_name,
        b.brand_name
        from goods g
        LEFT JOIN calculation_unit u on g.calculation_unit_no =
        u.calculation_unit_no
        LEFT JOIN calculation_unit u2 on g.assist_calculation_unit_no =
        u2.calculation_unit_no
        LEFT JOIN sale_category sc ON g.category_no = sc.category_no
        LEFT JOIN brand b ON g.brand_no = b.brand_no
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsDomain">
        update
        goods
        <set>
            <if test="channelSales != null and channelSales != ''">
                channel_sales = #{channelSales},
            </if>

            <if test="thirdGoodsNo != null">
                third_goods_no = #{thirdGoodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsForm != null">
                goods_form = #{goodsForm,jdbcType=VARCHAR},
            </if>
            <if test="goodsType != null">
                goods_type = #{goodsType,jdbcType=VARCHAR},
            </if>
            <if test="categoryNo != null">
                category_no = #{categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="brandNo != null">
                brand_no = #{brandNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>

            <if test="goodsNameFullPinyin != null and goodsNameFullPinyin != ''">
                goods_name_full_pinyin = #{goodsNameFullPinyin,jdbcType=VARCHAR},
            </if>
            <if test="goodsNameFullInitials != null and goodsNameFullInitials != ''">
                goods_name_full_initials = #{goodsNameFullInitials,jdbcType=VARCHAR},
            </if>
            <if test="warehouseFlag != null">
                warehouse_flag = #{warehouseFlag,jdbcType=TINYINT},
            </if>
            <if test="freightTemplateNo != null">
                freight_template_no = #{freightTemplateNo,jdbcType=VARCHAR},
            </if>
            <if test="salesAreaNo != null">
                sales_area_no = #{salesAreaNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsModel != null">
                goods_model = #{goodsModel,jdbcType=VARCHAR},
            </if>
            <if test="goodsSaleDescription != null">
                goods_sale_description = #{goodsSaleDescription,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitNo != null">
                calculation_unit_no = #{calculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="standardFlag != null">
                standard_flag = #{standardFlag,jdbcType=TINYINT},
            </if>
            <choose>
                <when test="standardFlag != null and standardFlag == 1">
                    assist_calculation_unit_no = '',
                    main_unit_num = null,
                    assist_unit_num = null,
                    conversion_rate = 1,
                </when>
                <otherwise>
                    <if test="assistCalculationUnitNo != null">
                        assist_calculation_unit_no = #{assistCalculationUnitNo,jdbcType=VARCHAR},
                    </if>
                    <if test="mainUnitNum != null">
                        main_unit_num = #{mainUnitNum,jdbcType=DECIMAL},
                    </if>
                    <if test="assistUnitNum != null">
                        assist_unit_num = #{assistUnitNum,jdbcType=DECIMAL},
                    </if>
                    <if test="conversionRate != null">
                        conversion_rate = #{conversionRate,jdbcType=DECIMAL},
                    </if>
                </otherwise>
            </choose>
            <if test="barcode != null">
                barcode = #{barcode,jdbcType=VARCHAR},
            </if>
            <if test="paymentMethod != null">
                payment_method = #{paymentMethod,jdbcType=VARCHAR},
            </if>
            <if test="deliveryWay != null">
                delivery_way = #{deliveryWay,jdbcType=VARCHAR},
            </if>
            <if test="firstShelfTime != null">
                first_shelf_time = #{firstShelfTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditMessage != null">
                audit_message = #{auditMessage,jdbcType=VARCHAR},
            </if>
            <if test="mainDataFlag != null">
                main_data_flag = #{mainDataFlag,jdbcType=TINYINT},
            </if>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            limit_price = #{limitPrice,jdbcType=DECIMAL},
            market_price = #{marketPrice,jdbcType=DECIMAL},
            purchase_price = #{purchasePrice,jdbcType=DECIMAL},
            <if test="retailPrice != null">
                retail_price = #{retailPrice,jdbcType=DECIMAL},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="imeiFlag != null">
                imei_flag = #{imeiFlag,jdbcType=TINYINT},
            </if>
            <if test="goodsStatus != null">
                goods_status = #{goodsStatus,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=VARCHAR},
            </if>
            <if test="goodsHelpCode != null">
                goods_help_code = #{goodsHelpCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                supplier_code = #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                supplier_name = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="auditDataModifyFlag != null">
                audit_data_modify_flag = #{auditDataModifyFlag,jdbcType=TINYINT},
            </if>
            <if test="distributionStatus != null">
                distribution_status = #{distributionStatus,jdbcType=VARCHAR},
            </if>
            <if test="distributeGoodsFlag != null">
                distribute_goods_flag = #{distributeGoodsFlag,jdbcType=INTEGER},
            </if>
            <if test="foodsType != null">
                foods_type = #{foodsType,jdbcType=VARCHAR},
            </if>
            <if test="validityPeriodManageFlag != null">
                validity_period_manage_flag = #{validityPeriodManageFlag,jdbcType=INTEGER},
            </if>
            <if test="qualityGuaranteePeriod != null">
                quality_guarantee_period = #{qualityGuaranteePeriod,jdbcType=INTEGER},
            </if>
            <if test="shelfLifeUnit != null">
                shelf_life_unit = #{shelfLifeUnit,jdbcType=VARCHAR},
            </if>
            <if test="qualityGuaranteePeriod == null and validityPeriodManageFlag != null and validityPeriodManageFlag == 1">
                quality_guarantee_period = null,
                shelf_life_unit = null,
            </if>
            <if test="customGoodsNo != null">
                custom_goods_no = #{customGoodsNo,jdbcType=VARCHAR},
            </if>
            <if test="allowCustomPrice != null">
                allow_custom_price = #{allowCustomPrice,jdbcType=INTEGER},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
            </trim>
        </where>
    </update>

    <update id="updateGoodsStatus" parameterType="cn.htdt.goodsprocess.domain.GoodsDomain">
        update
        goods
        <set>
            <if test="goodsStatus != null">
                goods_status = #{goodsStatus,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=VARCHAR},
            </if>
            <if test="distributionStatus != null">
                distribution_status = #{distributionStatus,jdbcType=VARCHAR},
            </if>
            <if test="imeiFlag != null">
                imei_flag = #{imeiFlag},
            </if>
            <if test="distributeGoodsFlag != null">
                distribute_goods_flag = #{distributeGoodsFlag,jdbcType=INTEGER},
            </if>
            <if test="downMessage != null">
                down_message = #{downMessage,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="storeNo !=null and storeNo !=''">
                    and store_no = #{storeNo}
                </if>
                <if test="originalGoodsNo!=null and originalGoodsNo!=''">
                    and original_goods_no=#{originalGoodsNo}
                </if>

            </trim>
        </where>
    </update>

    <update id="updateGoodsDefaultCategory" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        update
        goods
        <set>
            <if test="categoryNo != null">
                category_no = #{categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="storeNo !=null and storeNo !=''">
                    and store_no = #{storeNo}
                </if>
                <if test="originalGoodsNo!=null and originalGoodsNo!=''">
                    and original_goods_no=#{originalGoodsNo}
                </if>

            </trim>
        </where>
    </update>

    <update id="updateGoodsDefaultBrand" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        update
        goods
        <set>
            <if test="brandNo != null">
                brand_no = #{brandNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="storeNo !=null and storeNo !=''">
                    and store_no = #{storeNo}
                </if>
                <if test="originalGoodsNo!=null and originalGoodsNo!=''">
                    and original_goods_no=#{originalGoodsNo}
                </if>

            </trim>
        </where>
    </update>


    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.GoodsDomain">
        update
        goods
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        </if>
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="storeNo !=null and storeNo !=''">
                    and store_no = #{storeNo}
                </if>
                <if test="originalGoodsNo!=null and originalGoodsNo!=''">
                    and original_goods_no=#{originalGoodsNo}
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and goods_source_type != '1006'
            </trim>
        </where>
    </update>

    <!-- 批量删除商品 -->
    <update id="batchLogicDelete" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        update
        goods
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        </if>
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and goods_no in
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="originalGoodsNos != null and originalGoodsNos.size() > 0">
                    and original_goods_no in
                    <foreach collection="originalGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and goods_source_type != '1006'
            </trim>
        </where>
    </update>

    <!-- 批量更新商品信息 -->
    <update id="batchUpdateGoods" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        update
        goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="goodsForm != null">
                goods_form = #{goodsForm,jdbcType=VARCHAR},
            </if>
            <if test="goodsType != null">
                goods_type = #{goodsType,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsSaleDescription != null">
                goods_sale_description = #{goodsSaleDescription,jdbcType=VARCHAR},
            </if>
            <if test="categoryNo != null">
                category_no = #{categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="brandNo != null">
                brand_no = #{brandNo,jdbcType=VARCHAR},
            </if>
            <if test="barcode != null">
                barcode = #{barcode,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitNo != null">
                calculation_unit_no = #{calculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="imeiFlag != null">
                imei_flag = #{imeiFlag,jdbcType=TINYINT},
            </if>
            <choose>
                <when test="standardFlag != null and standardFlag == 1">
                    assist_calculation_unit_no = '',
                    main_unit_num = null,
                    assist_unit_num = null,
                    conversion_rate = 1,
                </when>
                <otherwise>
                    <if test="assistCalculationUnitNo != null">
                        assist_calculation_unit_no = #{assistCalculationUnitNo,jdbcType=VARCHAR},
                    </if>
                    <if test="mainUnitNum != null">
                        main_unit_num = #{mainUnitNum,jdbcType=DECIMAL},
                    </if>
                    <if test="assistUnitNum != null">
                        assist_unit_num = #{assistUnitNum,jdbcType=DECIMAL},
                    </if>
                    <if test="conversionRate != null">
                        conversion_rate = #{conversionRate,jdbcType=DECIMAL},
                    </if>
                </otherwise>
            </choose>
            <if test="limitPrice != null">
                limit_price = #{limitPrice,jdbcType=DECIMAL},
            </if>
            <if test="marketPrice != null">
                market_price = #{marketPrice,jdbcType=DECIMAL},
            </if>
            <if test="purchasePrice != null">
                purchase_price = #{purchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="retailPrice != null">
                retail_price = #{retailPrice,jdbcType=DECIMAL},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="goodsStatus != null and goodsStatus != ''">
                goods_status = #{goodsStatus,jdbcType=VARCHAR},
            </if>
            <if test="shelfType != null and shelfType != ''">
                shelf_type = #{shelfType,jdbcType=VARCHAR},
            </if>
            <if test="warehouseFlag != null">
                warehouse_flag = #{warehouseFlag,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null">
                audit_status = #{auditStatus,jdbcType=VARCHAR},
            </if>
            <if test="downMessage != null">
                down_message = #{downMessage,jdbcType=VARCHAR},
            </if>
            <if test="distributionStatus != null">
                distribution_status = #{distributionStatus,jdbcType=VARCHAR},
            </if>
            <if test="distributeGoodsFlag != null">
                distribute_goods_flag = #{distributeGoodsFlag,jdbcType=INTEGER},
            </if>
            <if test="latestModifyTime != null">
                latest_modify_time = #{latestModifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="mainDataFlag != null">
                main_data_flag = #{mainDataFlag,jdbcType=TINYINT},
            </if>
            <if test="customGoodsNo != null">
                custom_goods_no = #{customGoodsNo},
            </if>
            <if test="allowCustomPrice != null">
                allow_custom_price = #{allowCustomPrice},
            </if>
            <if test="validityPeriodManageFlag != null">
                validity_period_manage_flag = #{validityPeriodManageFlag},
            </if>
            <if test="qualityGuaranteePeriod != null">
                quality_guarantee_period = #{qualityGuaranteePeriod},
            </if>
            <if test="shelfLifeUnit != null and shelfLifeUnit != ''">
                shelf_life_unit = #{shelfLifeUnit},
            </if>
            modify_time = NOW()
        </trim>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsSourceType != null and goodsSourceType != ''">
                    and goods_source_type = #{goodsSourceType}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNos !=null and goodsNos.size() > 0">
                    and goods_no in
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="originalGoodsNos !=null and originalGoodsNos.size() > 0">
                    and original_goods_no in
                    <foreach collection="originalGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="merchantGoodsNos !=null and merchantGoodsNos.size() > 0">
                    and merchant_goods_no in
                    <foreach collection="merchantGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="merchantGoodsNo != null and merchantGoodsNo != ''">
                    and merchant_goods_no = #{merchantGoodsNo}
                </if>
                <if test="disableFlag != null and disableFlag != ''">
                    and disable_flag = #{disableFlag}
                </if>
            </trim>
        </where>
    </update>

    <select id="selectWarehouseAllocationOrderParams" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Base_Column_stockNum"/>,
        sc.category_name, sc.full_id_path, sc.full_name_path
        FROM
        goods g
        <!-- //20230825蛋品-盛守武-调拨管理 20230825-添加OR r.goods_no = g.multi_unit_goods_no条件，兼容多单位商品查询主单位商品库存信息 -->
        LEFT JOIN goods_real_stock r ON (r.goods_no = g.goods_no OR r.goods_no = g.multi_unit_goods_no)
        LEFT JOIN sale_category sc ON g.category_no = sc.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and g.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="getGoodsAuditPageList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Select_Base_Column_List"/>
        from goods g
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateGoodsAuditStatus" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        update
        goods
        <set>
            <if test="auditStatus != null and auditStatus != ''">
                audit_status = #{auditStatus,jdbcType=VARCHAR},
            </if>
            <if test="auditRecordNo != null and auditRecordNo != ''">
                audit_record_no = #{auditRecordNo},
            </if>
            <if test="auditDataModifyFlag != null and auditDataModifyFlag != ''">
                audit_data_modify_flag = #{auditDataModifyFlag,jdbcType=TINYINT},
            </if>
            <if test="auditType != null and auditType != ''">
                audit_type = #{auditType,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="goodsNos != null">
                    and goods_no IN
                    <foreach collection="goodsNos" item="goodsNo" open="(" close=")" separator=",">
                        #{goodsNo}
                    </foreach>
                </if>
                and delete_flag = 1
            </trim>
        </where>
    </update>

    <!-- 汇享购获取系列商品列表 -->
    <select id="selectSeriesGoodsByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Select_Series_Column_List"/>
        from goods g
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <!-- 更新商品销量-新增 -->
    <update id="updateGoodsSales" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        update
        goods
        <trim prefix="SET" suffixOverrides=",">
            modify_time = NOW(),
            <if test="salesVolume != null">
                sales_volume = sales_volume + #{salesVolume,jdbcType=DECIMAL},
            </if>
        </trim>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo !=null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
            </trim>
        </where>
    </update>

    <!-- 更新商品销量-扣减 -->
    <update id="updateGoodsSalesDel" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        update
        goods
        <trim prefix="SET" suffixOverrides=",">
            modify_time = NOW(),
            <if test="salesVolume != null">
                sales_volume = sales_volume - #{salesVolume,jdbcType=DECIMAL},
            </if>
        </trim>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo !=null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
            </trim>
        </where>
    </update>

    <update id="batchUpdateGoodsInfo" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update
            goods
            set
            <if test="item.thirdGoodsNo != null">
                third_goods_no = #{item.thirdGoodsNo,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsForm != null and item.goodsForm != ''">
                goods_form = #{item.goodsForm,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsType != null and item.goodsType != ''">
                goods_type = #{item.goodsType,jdbcType=VARCHAR},
            </if>
            <if test="item.categoryNo != null and item.categoryNo != ''">
                category_no = #{item.categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="item.brandNo != null and item.brandNo != ''">
                brand_no = #{item.brandNo,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsName != null and item.goodsName != ''">
                goods_name = #{item.goodsName,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsNameFullPinyin != null and item.goodsNameFullPinyin != ''">
                goods_name_full_pinyin = #{item.goodsNameFullPinyin,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsNameFullInitials != null and item.goodsNameFullInitials != ''">
                goods_name_full_initials = #{item.goodsNameFullInitials,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsInitial != null and item.goodsInitial != ''">
                goods_initial = #{item.goodsInitial,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsStatus != null and item.goodsStatus != ''">
                goods_status = #{item.goodsStatus},
            </if>
            <if test="item.freightTemplateNo != null and item.freightTemplateNo != ''">
                freight_template_no = #{item.freightTemplateNo,jdbcType=VARCHAR},
            </if>
            <if test="item.salesAreaNo != null">
                sales_area_no = #{item.salesAreaNo,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsModel != null">
                goods_model = #{item.goodsModel,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsSaleDescription != null">
                goods_sale_description = #{item.goodsSaleDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.calculationUnitNo != null and item.calculationUnitNo != ''">
                calculation_unit_no = #{item.calculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="item.standardFlag != null">
                standard_flag = #{item.standardFlag,jdbcType=TINYINT},
            </if>
            <if test="item.assistCalculationUnitNo != null">
                assist_calculation_unit_no = #{item.assistCalculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="item.conversionRate != null">
                conversion_rate = #{item.conversionRate,jdbcType=DECIMAL},
            </if>
            <if test="item.barcode != null">
                barcode = #{item.barcode,jdbcType=VARCHAR},
            </if>
            <if test="item.auditMessage != null">
                audit_message = #{item.auditMessage,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierName != null">
                supplier_name = #{item.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="item.mainDataFlag != null">
                main_data_flag = #{item.mainDataFlag,jdbcType=TINYINT},
            </if>
            <if test="item.goodsHelpCode != null">
                goods_help_code = #{item.goodsHelpCode,jdbcType=VARCHAR},
            </if>
            <if test="item.warehouseFlag != null">
                warehouse_flag = #{item.warehouseFlag,jdbcType=TINYINT},
            </if>
            <if test="item.imeiFlag != null">
                imei_flag = #{item.imeiFlag,jdbcType=TINYINT},
            </if>
            <if test="item.supplyStoreNo != null">
                supply_store_no = #{item.supplyStoreNo,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsVideo != null">
                goods_video = #{item.goodsVideo,jdbcType=TINYINT},
            </if>
            <if test="item.auditStatus != null and item.auditStatus != ''">
                audit_status = #{item.auditStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.auditRecordNo != null">
                audit_record_no = #{item.auditRecordNo,jdbcType=VARCHAR},
            </if>
            <if test="item.distributionStatus != null">
                distribution_status = #{item.distributionStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.auditDataModifyFlag != null">
                audit_data_modify_flag = #{item.auditDataModifyFlag,jdbcType=TINYINT},
            </if>
            <if test="item.distributeGoodsFlag != null">
                distribute_goods_flag = #{item.distributeGoodsFlag,jdbcType=BIT},
            </if>
            <if test="item.firstAttributeName != null">
                first_attribute_name = #{item.firstAttributeName,jdbcType=VARCHAR},
            </if>
            <if test="item.firstAttributeValueName != null">
                first_attribute_value_name = #{item.firstAttributeValueName,jdbcType=VARCHAR},
            </if>
            <if test="item.secondAttributeName != null">
                second_attribute_name = #{item.secondAttributeName,jdbcType=VARCHAR},
            </if>
            <if test="item.secondAttributeValueName != null">
                second_attribute_value_name = #{item.secondAttributeValueName,jdbcType=VARCHAR},
            </if>
            <if test="item.thirdAttributeName != null">
                third_attribute_name = #{item.thirdAttributeName,jdbcType=VARCHAR},
            </if>
            <if test="item.thirdAttributeValueName != null">
                third_attribute_value_name = #{item.thirdAttributeValueName,jdbcType=VARCHAR},
            </if>
            <if test="item.limitPrice != null">
                limit_price = #{item.limitPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.marketPrice != null">
                market_price = #{item.marketPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.purchasePrice != null">
                purchase_price = #{item.purchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="item.retailPrice != null">
                retail_price = #{item.retailPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.auditType != null">
                audit_type = #{item.auditType,jdbcType=VARCHAR},
            </if>
            <if test="item.cloudPoolSupplyPrice != null">
                cloud_pool_supply_price = #{item.cloudPoolSupplyPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.deliveryWay != null">
                delivery_way = #{item.deliveryWay},
            </if>
            <if test="item.paymentMethod != null">
                payment_method = #{item.paymentMethod},
            </if>
            <if test="item.storeName != null and item.storeName != ''">
                store_name = #{item.storeName},
            </if>
            <if test="item.sortNum != null and item.sortNum != ''">
                sort_num = #{item.sortNum},
            </if>
            <if test="item.modifyNo != null and item.modifyNo != ''">
                modify_no = #{item.modifyNo},
            </if>
            <if test="item.modifyName != null and item.modifyName != ''">
                modify_name = #{item.modifyName},
            </if>
            <if test="item.customGoodsNo != null">
                custom_goods_no = #{item.customGoodsNo,jdbcType=VARCHAR},
            </if>
            <if test="item.allowCustomPrice != null">
                allow_custom_price = #{item.allowCustomPrice},
            </if>
            <if test="item.validityPeriodManageFlag != null">
                validity_period_manage_flag = #{item.validityPeriodManageFlag},
            </if>
            <if test="item.qualityGuaranteePeriod != null">
                quality_guarantee_period = #{item.qualityGuaranteePeriod},
            </if>
            <if test="item.shelfLifeUnit != null and item.shelfLifeUnit != ''">
                shelf_life_unit = #{item.shelfLifeUnit},
            </if>
            <if test="item.channelSales != null and item.channelSales != ''">
                channel_sales = #{item.channelSales},
            </if>
            modify_time = NOW()
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.goodsNo != null and item.goodsNo != ''">
                        and goods_no = #{item.goodsNo}
                    </if>
                    <if test="item.originalGoodsNo != null and item.originalGoodsNo != ''">
                        and original_goods_no = #{item.originalGoodsNo}
                    </if>
                    <if test="item.goodsSourceType != null and item.goodsSourceType != ''">
                        and goods_source_type = #{item.goodsSourceType}
                    </if>
                    <if test="item.storeNo != null and item.storeNo != ''">
                        and store_no = #{item.storeNo}
                    </if>
                </trim>
            </where>
        </foreach>
    </update>

    <update id="batchUpdateCoolPoolGoodsInfo" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update
            goods
            set
            <if test="item.thirdGoodsNo != null">
                third_goods_no = #{item.thirdGoodsNo,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsForm != null and item.goodsForm != ''">
                goods_form = #{item.goodsForm,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsType != null and item.goodsType != ''">
                goods_type = #{item.goodsType,jdbcType=VARCHAR},
            </if>
            <if test="item.categoryNo != null and item.categoryNo != ''">
                category_no = #{item.categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="item.brandNo != null and item.brandNo != ''">
                brand_no = #{item.brandNo,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsName != null and item.goodsName != ''">
                goods_name = #{item.goodsName,jdbcType=VARCHAR},
            </if>
            <if test="item.freightTemplateNo != null and item.freightTemplateNo != ''">
                freight_template_no = #{item.freightTemplateNo,jdbcType=VARCHAR},
            </if>
            <if test="item.salesAreaNo != null">
                sales_area_no = #{item.salesAreaNo,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsModel != null">
                goods_model = #{item.goodsModel,jdbcType=VARCHAR},
            </if>
            <if test="item.goodsSaleDescription != null">
                goods_sale_description = #{item.goodsSaleDescription,jdbcType=VARCHAR},
            </if>
            <if test="item.calculationUnitNo != null and item.calculationUnitNo != ''">
                calculation_unit_no = #{item.calculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="item.standardFlag != null">
                standard_flag = #{item.standardFlag,jdbcType=TINYINT},
            </if>
            <if test="item.assistCalculationUnitNo != null">
                assist_calculation_unit_no = #{item.assistCalculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="item.conversionRate != null">
                conversion_rate = #{item.conversionRate,jdbcType=DECIMAL},
            </if>
            <if test="item.barcode != null">
                barcode = #{item.barcode,jdbcType=VARCHAR},
            </if>
            <if test="item.firstShelfTime != null">
                first_shelf_time = #{item.firstShelfTime,jdbcType=TIMESTAMP},
            </if>
            <if test="item.auditMessage != null">
                audit_message = #{item.auditMessage,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierCode != null">
                supplier_code = #{item.supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="item.supplierName != null">
                supplier_name = #{item.supplierName,jdbcType=VARCHAR},
            </if>
            <if test="item.mainDataFlag != null">
                main_data_flag = #{item.mainDataFlag,jdbcType=TINYINT},
            </if>
            <if test="item.goodsHelpCode != null">
                goods_help_code = #{item.goodsHelpCode,jdbcType=VARCHAR},
            </if>
            <if test="item.warehouseFlag != null">
                warehouse_flag = #{item.warehouseFlag,jdbcType=TINYINT},
            </if>
            <if test="item.imeiFlag != null">
                imei_flag = #{item.imeiFlag,jdbcType=TINYINT},
            </if>
            <if test="item.goodsVideo != null">
                goods_video = #{item.goodsVideo,jdbcType=TINYINT},
            </if>
            <if test="item.auditStatus != null and item.auditStatus != ''">
                audit_status = #{item.auditStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.auditRecordNo != null">
                audit_record_no = #{item.auditRecordNo,jdbcType=VARCHAR},
            </if>
            <if test="item.distributionStatus != null">
                distribution_status = #{item.distributionStatus,jdbcType=VARCHAR},
            </if>
            <if test="item.auditDataModifyFlag != null">
                audit_data_modify_flag = #{item.auditDataModifyFlag,jdbcType=TINYINT},
            </if>
            <if test="item.distributeGoodsFlag != null">
                distribute_goods_flag = #{item.distributeGoodsFlag,jdbcType=BIT},
            </if>
            <if test="item.firstAttributeName != null">
                first_attribute_name = #{item.firstAttributeName,jdbcType=VARCHAR},
            </if>
            <if test="item.firstAttributeValueName != null">
                first_attribute_value_name = #{item.firstAttributeValueName,jdbcType=VARCHAR},
            </if>
            <if test="item.secondAttributeName != null">
                second_attribute_name = #{item.secondAttributeName,jdbcType=VARCHAR},
            </if>
            <if test="item.secondAttributeValueName != null">
                second_attribute_value_name = #{item.secondAttributeValueName,jdbcType=VARCHAR},
            </if>
            <if test="item.thirdAttributeName != null">
                third_attribute_name = #{item.thirdAttributeName,jdbcType=VARCHAR},
            </if>
            <if test="item.thirdAttributeValueName != null">
                third_attribute_value_name = #{item.thirdAttributeValueName,jdbcType=VARCHAR},
            </if>
            <if test="item.limitPrice != null">
                limit_price = #{item.limitPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.marketPrice != null">
                market_price = #{item.marketPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.purchasePrice != null">
                purchase_price = #{item.purchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="item.retailPrice != null">
                retail_price = #{item.retailPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.auditType != null">
                audit_type = #{item.auditType,jdbcType=VARCHAR},
            </if>
            <if test="item.cloudPoolSupplyPrice != null">
                cloud_pool_supply_price = #{item.cloudPoolSupplyPrice,jdbcType=DECIMAL},
            </if>
            <if test="item.deliveryWay != null and item.deliveryWay != ''">
                delivery_way = #{item.deliveryWay},
            </if>
            <if test="item.paymentMethod != null and item.paymentMethod != ''">
                payment_method = #{item.paymentMethod},
            </if>
            <!--上下架类型-->
            <if test="item.shelfType != null and item.shelfType != ''">
                shelf_type = #{item.shelfType},
            </if>
            <if test="item.modifyNo != null and item.modifyNo != ''">
                modify_no = #{item.modifyNo},
            </if>
            <if test="item.modifyName != null and item.modifyName != ''">
                modify_name = #{item.modifyName},
            </if>
            <if test="item.channelSales != null and item.channelSales != ''">
                channel_sales = #{channelSales},
            </if>

            modify_time = NOW()
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    and cloud_pool_goods_no = #{item.cloudPoolGoodsNo}
                    and store_no IN (
                    SELECT
                    store_no
                    FROM
                    cloud_pool_goods_distribution_relation
                    WHERE
                    delete_flag = '1'
                    <if test="item.seriesCloudPoolGoodsNo != null and item.seriesCloudPoolGoodsNo != ''">
                        and goods_no = #{item.seriesCloudPoolGoodsNo}
                    </if>
                    GROUP BY
                    store_no
                    )
                </trim>
            </where>
        </foreach>
    </update>

    <update id="updateCloudPoolGoodsDisableFlag" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        update
        goods
        set
        <if test="disableFlag != null and disableFlag != ''">
            disable_flag = #{disableFlag},
        </if>
        <if test="shelfType != null and shelfType != ''">
            shelf_type = #{shelfType},
        </if>
        <if test="modifyNo != null and modifyNo != ''">
            modify_no = #{modifyNo},
        </if>
        <if test="modifyName != null and modifyName != ''">
            modify_name = #{modifyName},
        </if>
        modify_time = NOW()
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="cloudPoolGoodsNo != null and cloudPoolGoodsNo != ''">
                    and cloud_pool_goods_no = #{cloudPoolGoodsNo}
                </if>
                <if test="cloudPoolGoodsNos != null and cloudPoolGoodsNos.size() > 0">
                    and cloud_pool_goods_no IN
                    <foreach collection="cloudPoolGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="originalGoodsNos != null and originalGoodsNos.size() > 0">
                    and original_goods_no IN
                    <foreach collection="originalGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="disableFlag != null and disableFlag == '2'.toString()">
                    and store_no IN (
                    SELECT
                    store_no
                    FROM
                    cloud_pool_goods_distribution_relation
                    WHERE
                    delete_flag = '1'
                    <if test="cloudPoolGoodsNo != null and cloudPoolGoodsNo != ''">
                        and goods_no = #{cloudPoolGoodsNo}
                    </if>
                    <if test="cloudPoolGoodsNos != null and cloudPoolGoodsNos.size() > 0">
                        and goods_no IN
                        <foreach collection="cloudPoolGoodsNos" item="item" index="index"
                                 open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    GROUP BY
                    store_no
                    )
                </if>
                and goods_source_type = '1006'
            </trim>
        </where>
    </update>

    <sql id="Select_Cloud_Pool_Apply_Goods_Column_List">
        g.goods_no,
		g.goods_form,
		g.goods_type,
		g.category_no,
		g.brand_no,
		g.goods_name,
		g.goods_source_type,
		g.calculation_unit_no,
		g.merchant_no,
		g.merchant_name,
		g.store_no,
		g.store_name,
		g.goods_status,
		g.original_goods_no,
		g.market_price,
		g.purchase_price,
		g.retail_price,
		g.cloud_pool_supply_price,
		g.series_type,
		g.sales_volume,
		g.first_attribute_name,
		g.first_attribute_value_name,
		g.second_attribute_name,
		g.second_attribute_value_name,
		g.third_attribute_name,
		g.third_attribute_value_name,
		g.parent_goods_no,
		g.cloud_pool_goods_no,
		cpa.apply_status,
		cpa.audit_message,
		cpa.failure_type,
		cpa.shelf_status,
		cpa.shelf_type,
		g.modify_time,
		cpa.audit_time,
		g.delete_flag,
		cpa.import_store_type,
		cpa.disable_flag
    </sql>

    <select id="selectCloudPoolApplyGoodsByParam" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Cloud_Pool_Apply_Goods_Column_List"/>
        FROM
        goods g
        LEFT JOIN cloud_pool_apply cpa ON cpa.goods_no = g.goods_no and cpa.disable_flag = '2'
        LEFT JOIN sale_category sc ON g.category_no = sc.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and g.goods_no = #{goodsNo}
                </if>
                <if test="categoryNo != null and categoryNo != ''">
                    and g.category_no = #{categoryNo}
                </if>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                <if test="brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                </if>
                <if test="goodsForm != null and goodsForm != ''">
                    and g.goods_form = #{goodsForm}
                </if>
                <if test="goodsType != null and goodsType != ''">
                    and g.goods_type = #{goodsType}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and g.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and g.store_no = #{storeNo}
                </if>
                <if test="applyStatusList == null">
                    <if test="applyStatus == null or applyStatus == ''">
                        and (cpa.apply_status IS NULL OR cpa.apply_status = '1003')
                    </if>
                </if>
                <if test="applyStatus != null and applyStatus != ''">
                    and cpa.apply_status = #{applyStatus}
                </if>
                <if test="applyStatusList != null and applyStatusList.size() > 0">
                    and cpa.apply_status IN
                    <foreach collection="applyStatusList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="auditDataModifyFlag != null and auditDataModifyFlag != ''">
                    and g.audit_data_modify_flag = #{auditDataModifyFlag}
                </if>
                and g.goods_source_type IN ('1003')
                and g.series_type != '1002'
                and g.merchant_goods_no is null
                and g.disable_flag = '2'
                and g.delete_flag = '1'
            </trim>
        </where>
        order by
        <if test="applyStatus == null or applyStatus == '' or applyStatus == '1001'">
            g.modify_time DESC
        </if>
        <if test="applyStatus == '1002' or applyStatus == '1003'">
            cpa.audit_time DESC
        </if>
    </select>

    <select id="selectCloudPoolGoodsByParam" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Select_Cloud_Pool_Apply_Goods_Column_List"/>
        FROM
        goods g
        INNER JOIN cloud_pool_apply cpa ON cpa.goods_no = g.parent_goods_no and history_success_flag = 2 and
        cpa.delete_flag = 1
        <!-- PC端创建秒杀活动，查询可设置参与的云池商品，需要根据全类目id路径查询 -->
        <if test="(secKillQueryFlag != null and secKillQueryFlag == 2) or (fullIdPath != null and fullIdPath != '')">
            LEFT JOIN sale_category sc ON g.category_no = sc.category_no
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and g.goods_no = #{goodsNo}
                </if>
                <if test="categoryNo != null and categoryNo != ''">
                    and g.category_no = #{categoryNo}
                </if>
                <if test="brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                </if>
                <if test="goodsForm != null and goodsForm != ''">
                    and g.goods_form = #{goodsForm}
                </if>
                <if test="goodsType != null and goodsType != ''">
                    and g.goods_type = #{goodsType}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and g.store_no = #{storeNo}
                </if>
                <if test="deleteFlag != null and deleteFlag != ''">
                    and g.delete_flag = #{deleteFlag}
                </if>
                <if test="applyStatus != null and applyStatus != ''">
                    and cpa.apply_status = #{applyStatus}
                </if>
                <if test="shelfStatus != null and shelfStatus != ''">
                    and cpa.shelf_status = #{shelfStatus}
                </if>
                <if test="disableFlag != null and disableFlag != ''">
                    and cpa.disable_flag = #{disableFlag}
                </if>
                <if test="cloudPoolGoodsStatus != null and cloudPoolGoodsStatus == '1004'">
                    and (cpa.apply_status in ('1001','1003') or cpa.disable_flag = '1')
                </if>
                <if test="goodsSaleStatus != null and goodsSaleStatus == '1002'">
                    and (cpa.apply_status in ('1001','1003') or cpa.shelf_status = '1001' or cpa.disable_flag = '1' or
                    g.delete_flag = '2')
                </if>
                <!-- 排除主品，只展示子品条件 -->
                <include refid="Select_Base_Column_Where_Child"/>
                <!-- 根据指定的商品编码集合过滤查询 -->
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 排除指定的商品 -->
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 平台秒杀活动添加商品页的商品查询条件 -->
                <if test="goodsNoOrName != null and goodsNoOrName != ''">
                    and (
                    g.goods_no = #{goodsNoOrName} or g.goods_name like CONCAT(CONCAT('%', #{goodsNoOrName}),'%')
                    )
                </if>
                <!-- 平台秒杀活动添加商品页的店铺查询条件 -->
                <if test="storeNoOrName != null and storeNoOrName != ''">
                    and (
                    g.store_no = #{storeNoOrName} or g.store_name like CONCAT(CONCAT('%', #{storeNoOrName}),'%')
                    )
                </if>
                <!--是否有可售库存 秒杀活动添加列表页的只要有货的子品商品-->
                <if test="stockDisableFlag !=null and childFlag == 1002">
                    and exists(select 1 from goods_real_stock st
                    where st.available_stock_num > 0
                    and g.original_goods_no = st.goods_no)
                </if>
                <!-- PC端创建秒杀活动，查询可设置参与的云池商品，需要根据全类目id路径查询 -->
                <if test="(secKillQueryFlag != null and secKillQueryFlag == 2) and (fullIdPath != null and fullIdPath != '')">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
            </trim>
        </where>
        order by
        cpa.commission_config_time DESC
    </select>

    <select id="getCloudPoolGoodsList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from
        goods g
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <if test="parentGoodsNos != null and parentGoodsNos.size() > 0">
                    and parent_goods_no IN
                    <foreach collection="parentGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="originalGoodsNos != null and originalGoodsNos.size() > 0">
                    and original_goods_no IN
                    <foreach collection="originalGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 排除主品，只展示子品条件 -->
                <include refid="Select_Base_Column_Where_Child"/>
                and goods_source_type = '1006'
            </trim>
        </where>
    </select>

    <!-- 商品选择查询-活动用 -->
    <select id="selectGoodsSelectList" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Select_Goods_Column_List_Promotion"/>
        from
        goods g LEFT JOIN sale_category sc ON g.category_no = sc.category_no
        LEFT JOIN brand b ON g.brand_no = b.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <!-- 商品编码和名称条件 -->
                <include refid="Select_Base_Column_Where"/>
                <include refid="Select_Base_Column_Where_Split"/>
                <!-- 品牌条件 -->
                <include refid="Select_Base_Column_Where_Brand"/>
                <!-- 销售类目条件 -->
                <include refid="Select_Base_Column_Where_Category"/>
                <!-- 平台、商家、店铺编码条件 -->
                <include refid="Select_Base_Column_Where_PMSCode"/>
                <!-- 平台、商家、店铺自建条件 -->
                <include refid="Select_Base_Column_Where_PMS"/>
                <!-- 上架条件 -->
                <include refid="Select_Base_Column_Where_Status"/>
                <!-- 审核通过条件 -->
                <include refid="Select_Base_Column_Where_Audit"/>
                <!-- 排除主品，只展示子品条件 -->
                <include refid="Select_Base_Column_Where_Child"/>
                <!-- 是否有可售库存 有货 -->
                <include refid="Select_Base_Column_Where_Has_Child"/>
                <!-- 商品是否删除条件 -->
                <include refid="Select_Base_Column_Where_Delete"/>
                <!-- 店铺ID -->
                <include refid="Select_Base_Column_Where_SupplyStore"/>
                <!-- 是否展示辅单位 -->
                <if test="null == isAuxiliaryUnit or '1'.toString() == isAuxiliaryUnit">
                    <if test="(goodsNo == null or goodsNo == '') and (goodsNos == null or goodsNos.size() == 0)">
                        and g.multi_unit_type != '1002'
                    </if>
                </if>
                <if test="null != isAuxiliaryUnit and '3'.toString() == isAuxiliaryUnit">
                    and g.multi_unit_type = ''
                </if>
                <!-- 排除指定的商品 -->
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
    </select>

    <!-- 云池商品选择查询-活动用 -->
    <select id="selectCloudGoodsSelectList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Select_Cloud_Goods_Column_List_Promotion"/>
        from
        goods g
        INNER JOIN cloud_pool_apply cpa ON cpa.goods_no = g.goods_no and history_success_flag = 2
        LEFT JOIN sale_category sc ON g.category_no = sc.category_no
        LEFT JOIN brand b ON g.brand_no = b.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <!-- 商品编码和名称条件 -->
                <include refid="Select_Base_Column_Where"/>
                <include refid="Select_Base_Column_Where_Split"/>
                <!-- 品牌条件 -->
                <include refid="Select_Base_Column_Where_Brand"/>
                <!-- 销售类目条件 -->
                <include refid="Select_Base_Column_Where_Category"/>
                <!-- 平台、商家、店铺编码条件 -->
                <include refid="Select_Base_Column_Where_PMSCode"/>
                <!-- 上架条件 -->
                <if test="goodsStatus != null and goodsStatus != ''">
                    and cpa.shelf_status = #{goodsStatus} and cpa.apply_status = '1002'
                </if>
                and cpa.disable_flag = '2'
                <!-- 审核通过条件 -->
                <include refid="Select_Base_Column_Where_Audit"/>
                <!-- 排除主品，只展示子品条件 -->
                <include refid="Select_Base_Column_Where_Child"/>
                <!-- 是否有可售库存 有货 -->
                <include refid="Select_Base_Column_Where_Has"/>
                <!-- 商品是否删除条件 -->
                <include refid="Select_Base_Column_Where_Delete"/>
                <!-- 供货店铺ID -->
                <include refid="Select_Base_Column_Where_SupplyStore"/>
                <!-- 商品是否云池 -->
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
    </select>
    <select id="countByParams" resultType="java.lang.Integer"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        count(1)
        from goods g
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="GoodsShowStatus_Column_Where"/>
                <if test="storeNo != null and storeNo != ''">
                    AND g.store_no = #{storeNo}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    AND g.merchant_no = #{merchantNo}
                </if>
                <!--平台身份查询全部列表（包括平台、商家、店铺自建） -->
                <if test="queryType==1001">
                    and g.goods_source_type in ('1001','1002','1003')
                </if>
                <!--平台身份查询平台列表（包括平台自建） -->
                <if test="queryType==1002">
                    and g.goods_source_type in ('1001')
                </if>
                <!--平台身份查询商家/店铺列表（包括商家、店铺自建） -->
                <if test="queryType==1003">
                    and g.goods_source_type in ('1002','1003')
                </if>
                <!--商家身份查询商家列表（包括商家自建） -->
                <if test="queryType==1004">
                    and g.goods_source_type in ('1002')
                </if>
                <!--商家身份查询店铺列表（包括店铺自建） -->
                <if test="queryType==1005">
                    and g.goods_source_type in ('1003')
                </if>
                <!--店铺身份查询列表（包括商家分发、店铺自建） -->
                <if test="queryType==1006">
                    and g.goods_source_type in ('1003','1005')
                </if>
                <!-- 是否展示辅单位 -->
                <if test="null == isAuxiliaryUnit or '1'.toString() == isAuxiliaryUnit">
                    <if test="(goodsNo == null or goodsNo == '') and (goodsNos == null or goodsNos.size() == 0)">
                        and g.multi_unit_type != '1002'
                    </if>
                </if>
                <choose>
                    <when test="goodsType != null and goodsType != ''">
                        and g.goods_type = #{goodsType}
                    </when>
                    <otherwise>
                        <if test="(goodsNo == null or goodsNo == '') and (goodsNos == null or goodsNos.size() == 0)">
                            and g.goods_type != '1002'
                        </if>
                    </otherwise>
                </choose>
                and g.series_type != '1002'
                and g.delete_flag = 1
            </trim>
        </where>
    </select>

    <!-- 查询店铺是否存在分销商品 -->
    <select id="countDistributionGoodsByStoreNo" parameterType="cn.htdt.goodsprocess.domain.GoodsDomain"
            resultType="java.lang.Integer">
        SELECT count(1)
        from goods
        WHERE delete_flag = 1
          AND store_no = #{storeNo}
          AND merchant_no = #{merchantNo}
          AND distribute_goods_flag = 2
    </select>

    <!-- 查询新上架的三款商品 -->
    <select id="selectGoodsNameNoByCreatTime" resultType="cn.htdt.goodsprocess.vo.AtomGoodsNameNoVo">
        SELECT goods_name goodsName, goods_no goodsNo, retail_price retailPrice
        FROM goods
        WHERE delete_flag = 1
          AND distribute_goods_flag = 2
          AND goods_status = '1002'
          AND store_no = #{storeNo}
        ORDER BY create_time DESC
        limit 3
    </select>

    <!-- 分销商品列表 (app消息推送-手动选择) -->
    <select id="manualMessagePush" resultType="cn.htdt.goodsprocess.vo.AtomGoodsManualMessagePushVo">
        select g.goods_name goodsName,g.retail_price retailPrice,g.goods_no goodsNo,g.parent_goods_no parentGoodsNo
        from goods g left join goods_real_stock s on g.goods_no = s.goods_no
        where g.delete_flag = 1 AND g.distribute_goods_flag = 2 and (g.goods_no = g.parent_goods_no or g.parent_goods_no
        = '')
        <if test="storeNo != null and storeNo != ''">
            AND g.store_no = #{storeNo} AND s.store_no = #{storeNo}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            AND g.merchant_no = #{merchantNo} AND s.merchant_no = #{merchantNo}
        </if>
        <if test="goodsName != null and goodsName != ''">
            and g.goods_name LIKE CONCAT(CONCAT('%',#{goodsName}),'%')
        </if>
        ORDER BY g.modify_time DESC
    </select>
    <!-- BossApp 商品选择查询-活动用 -->
    <select id="getBossAppGoodsPage" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        <include refid="Base_Column_List"/>,
        <choose>
            <when test="childFlag == 1002">
                rs.available_stock_num as realStockNum
            </when>
            <otherwise>
                (
                SELECT
                sum(rs.available_stock_num)
                FROM
                goods_real_stock rs
                WHERE (rs.parent_goods_no = g.goods_no OR rs.parent_goods_no = g.multi_unit_goods_no)
                ) AS realStockNum
            </otherwise>
        </choose>
        from
        goods g LEFT JOIN goods_real_stock rs ON g.original_goods_no = rs.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <!-- 商品编码和名称条件 -->
                <include refid="Select_Base_Column_Where"/>
                <!-- 平台、商家、店铺编码条件 -->
                <include refid="Select_Base_Column_Where_PMSCode"/>
                <!-- 平台、商家、店铺自建条件 -->
                <include refid="Select_Base_Column_Where_PMS"/>
                <!-- 上架条件 -->
                <include refid="Select_Base_Column_Where_Status"/>
                <!-- 审核通过条件 -->
                <include refid="Select_Base_Column_Where_Audit"/>
                <!-- 排除主品，只展示子品条件 -->
                <include refid="Select_Base_Column_Where_Child_Choose"/>
                <!-- 是否有可售库存 有货 -->
                <include refid="Select_Base_Column_Where_Has_Child"/>
                <!-- 商品是否删除条件 -->
                <include refid="Select_Base_Column_Where_Delete"/>
                <!--店铺身份查询列表（包括商家分发、店铺自建） -->
                <if test="queryType==1006">
                    and g.goods_source_type in ('1003','1005')
                </if>
                <!--查询云池分销商品列表 -->
                <if test="queryType==1007">
                    and g.goods_source_type in ('1006')
                </if>
                <!--店铺非分销商品 -->
                <if test="queryType==1008">
                    and exists(
                    select 1 from goods_real_stock s where
                    (s.parent_goods_no = g.goods_no or s.parent_goods_no = g.multi_unit_goods_no)
                    and s.available_stock_num > 0
                    )
                    and g.goods_source_type in ('1003','1005')
                    and not exists(select 1 from store_goods_distribution dis
                    where
                    g.parent_goods_no = dis.goods_no
                    and dis.disable_flag = 2
                    and dis.delete_flag = 1)
                </if>
                <if test="queryType==1010">
                    and g.goods_source_type in ('1003')
                </if>
                <!--查询商家同步商品列表 -->
                <if test="queryType==1012">
                    and g.goods_source_type in ('1003')
                    and g.merchant_goods_no is not null
                </if>
                <if test="imeiFlag != null">
                    and g.imei_flag = #{imeiFlag}
                </if>
                <!--是否有可售库存 秒杀活动添加列表页的只要有货的子品商品-->
                <if test="stockDisableFlag !=null and childFlag == 1002">
                    and exists(select 1 from goods_real_stock st
                    where st.available_stock_num > 0
                    and st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
                </if>
                <!-- 查询指定的商品 -->
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 排除指定的商品 -->
                <if test="notExistGoods != null and notExistGoods.size() > 0">
                    and g.goods_no not IN
                    <foreach collection="notExistGoods" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="notExistsGroupsFlag != null and notExistsGroupsFlag =='2'.toString()">
                    and NOT EXISTS (
                    SELECT 1 FROM goods_groups_related_goods rg
                    WHERE rg.goods_no = g.goods_no
                    )
                    and NOT EXISTS (
                    SELECT 1 FROM goods_groups_related_goods rg
                    WHERE rg.goods_no = g.merchant_goods_no
                    )
                </if>
                <!-- 是否展示辅单位 -->
                <if test="null == isAuxiliaryUnit or '1'.toString() == isAuxiliaryUnit">
                    <if test="(goodsNo == null or goodsNo == '') and (goodsNos == null or goodsNos.size() == 0)">
                        and g.multi_unit_type != '1002'
                    </if>
                </if>
                <if test="null != isAuxiliaryUnit and '3'.toString() == isAuxiliaryUnit">
                    and g.multi_unit_type = ''
                </if>
                <![CDATA[
                	order by ${hxgSortValue} ${hxgSortType}
                ]]>
            </trim>
        </where>
    </select>

    <!--查询是否有无品牌商品-->
    <select id="countNoBrandGoods" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo"
            resultType="java.lang.Integer">
        select count(1)
        from goods g
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo != null and merchantNo != ''">
                    AND g.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    AND g.store_no = #{storeNo}
                </if>
                <if test="categoryNo != null and categoryNo != ''">
                    AND g.category_no = #{categoryNo}
                </if>
                <if test="brandNo != null and brandNo != ''">
                    AND g.brand_no = #{brandNo}
                </if>
                AND g.goods_source_type in ('1003','1005')
                AND g.audit_status = '2003'
                AND g.delete_flag = '1'
                and g.goods_type != '1002'
            </trim>
        </where>
    </select>

    <!--获取店铺下商品名称首字母-->
    <select id="selectGoodsInitialByStoreGoods" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo"
            resultType="java.lang.String">
        select g.goods_initial
        from goods g
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo != null and merchantNo != ''">
                    AND g.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    AND g.store_no = #{storeNo}
                </if>
                <choose>
                    <when test="goodsType != null and goodsType != ''">
                        and g.goods_type = #{goodsType}
                    </when>
                    <otherwise>
                        and g.goods_type != '1002'
                    </otherwise>
                </choose>
                AND g.goods_source_type in ('1003','1005')
                AND g.audit_status = '2003'
                AND g.delete_flag = '1'
            </trim>
        </where>
        group by g.goods_initial
        order by g.goods_initial
    </select>

    <select id="selectDuplicateCount" resultType="int" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        count(1)
        from
        goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="calculationUnitNo!=null and calculationUnitNo!=''">
                    and calculation_unit_no=#{calculationUnitNo}
                </if>
                and replace(goods_name,' ','')=#{goodsName}
                and (series_type = '1001' or series_type ='')
                and delete_flag = 1
                <!-- 多单位商品子品不参与校验 -->
                and multi_unit_type != '1002'
                <if test="goodsNo!=null and goodsNo!=''">
                    and goods_no!=#{goodsNo}
                </if>
                <!-- 平台新建商品校验 -->
                <if test='loginIdentity == 1'>
                    and merchant_no = ''
                </if>
                <!-- 商家新建商品校验 -->
                <if test='loginIdentity == 2'>
                    and merchant_no = #{merchantNo}
                    and store_no =''
                </if>
                <!-- 店铺新建/修改商品校验 -->
                <if test='loginIdentity == 4 or loginIdentity == 8 or loginIdentity == null or loginIdentity == ""'>
                    and store_no = #{storeNo}
                    and goods_source_type in('1005','1003')
                </if>
            </trim>
        </where>
    </select>

    <select id="selectCountByMerchantGoodsNo" resultType="java.lang.String">
        select goods_no from goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and delete_flag = 1
                <if test="merchantGoodsNo != null and merchantGoodsNo != ''">
                    and merchant_goods_no = #{merchantGoodsNo}
                </if>
                <!-- 店铺新建/修改商品校验 -->
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
            </trim>
        </where>
    </select>

    <select id="selectGoodsRealStock" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        g.parent_goods_no ,
        g.goods_name ,
        g.inventory_warning_flag ,
        g.multi_unit_type,
        g.multi_unit_goods_no,
        ifnull(sum(grs.real_stock_num), 0) real_stock_num
        from
        goods g
        left join goods_real_stock grs on
        grs.goods_no = g.goods_no
        <if test="categoryType != null and categoryType == 1002">
            left join sale_category sc on g.category_no = sc.category_no
        </if>
        <if test="categoryType != null and categoryType == 1001">
            left join store_category_goods_relation scg on g.goods_no = scg.goods_no
            left join store_category sc on scg.store_category_no = sc.store_category_no
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    AND g.store_no = #{storeNo}
                </if>
                <if test="parentGoodsNos != null and parentGoodsNos.size() > 0">
                    and g.parent_goods_no IN
                    <foreach collection="parentGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="queryType==1005">
                    and g.goods_source_type in ('1003')
                </if>
                <if test="inventoryWarningFlag != null">
                    and g.inventory_warning_flag = #{inventoryWarningFlag}
                </if>
                <if test="brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo}
                </if>
                <if test="categoryType != null and categoryType == 1002 and fullIdPath != null and fullIdPath != ''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                <if test="categoryType != null and categoryType == 1001 and fullIdPath != null and fullIdPath != ''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
                <if test="goodsNameOrBarCodeOrImei != null and goodsNameOrBarCodeOrImei != ''">
                    and (
                    g.barcode = #{goodsNameOrBarCodeOrImei}
                    or g.goods_name like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    or g.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    or g.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsNameOrBarCodeOrImei}),'%')
                    )
                </if>
                and g.delete_flag = '1' AND g.multi_unit_type != '1002'
            </trim>
        </where>
        group by
        g.parent_goods_no ,
        g.goods_name
        order by
        g.create_time desc
    </select>

    <update id="updateGoodsInventoryWarningFlag" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        update
        goods
        <trim prefix="SET" suffixOverrides=",">
            <if test="inventoryWarningFlag != null">
                inventory_warning_flag = #{inventoryWarningFlag},
            </if>
            <if test="inventoryWarningTime != null">
                inventory_warning_time = #{inventoryWarningTime},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName},
            </if>
            modify_time = NOW()
        </trim>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="parentGoodsNo != null and parentGoodsNo != ''">
                    and parent_goods_no = #{parentGoodsNo}
                </if>
                <if test="parentGoodsNos != null and parentGoodsNos.size() > 0">
                    and parent_goods_no IN
                    <foreach collection="parentGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
            </trim>
        </where>
    </update>
    <select id="selectGoodsCountOfParam" resultType="int">
        select
        count(1)
        from
        goods g
        where
        delete_flag = 1
        and disable_flag = 2
        <if test="goodsNo!=null and goodsNo!=''">
            and goods_no = #{goodsNo}
        </if>
        <if test="goodsStatus!=null and goodsStatus!=''">
            and goods_status = #{goodsStatus}
        </if>
        <if test="goodsType != null and goodsType != ''">
            and g.goods_type = #{goodsType}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            AND g.merchant_no = #{merchantNo}
        </if>
        <if test="storeNo != null and storeNo != ''">
            AND g.store_no = #{storeNo}
        </if>
        <!--商家身份查询商家列表（包括商家自建） -->
        <if test="queryType==1004">
            and g.goods_source_type in ('1002')
        </if>
        <!--店铺身份查询列表（包括商家分发、店铺自建） -->
        <if test="queryType== 1006">
            and g.goods_source_type in ('1003','1005')
        </if>
    </select>

    <select id="selectGoodsStoreCategory" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo"
            resultType="java.lang.String">
        select
        scg.store_category_no
        from
        goods g
        left join store_category_goods_relation scg ON g.parent_goods_no = scg.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                AND g.delete_flag = '1'
            </trim>
        </where>
        group by
        scg.store_category_no
    </select>


    <select id="selectNoStoreCategoryGoodsCount" resultType="int">
        select
        count(*)
        from
        goods g
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <if test="storeNo != null and storeNo != ''">
                    and g.store_no = #{storeNo}
                    and g.series_type !='1002'
                    <!--不存在店铺类目关联表-->
                    and not exists (
                    select
                    1
                    from store_category_goods_relation scgr
                    where g.goods_no = scgr.goods_no
                    )
                </if>
            </trim>
        </where>
    </select>

    <select id="selectHxgNoStoreCategoryGoodsCount" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo"
            resultType="int">
        select
        count(*)
        from
        goods g
        left join store_category_goods_relation scgr on g.goods_no = scgr.goods_no and scgr.delete_flag = 1
        left join store_category sc on sc.store_category_no = scgr.store_category_no and sc.delete_flag = 1
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <if test="storeNo != null and storeNo != ''">
                    and g.store_no = #{storeNo}
                    and g.delete_flag = 1
                    and g.series_type !='1002'
                    and g.distribution_status != '3004'
                    and g.disable_flag = 2
                    and g.goods_type != '1002'
                    and g.goods_source_type in ('1003', '1005')
                    <if test="goodsStatus != null and goodsStatus != ''">
                        and g.goods_status = #{goodsStatus}
                    </if>
                    <if test="auditStatus != null and auditStatus != ''">
                        and g.audit_status = #{auditStatus}
                    </if>
                    and sc.id is null
                </if>
            </trim>
        </where>
    </select>

    <select id="getBossAppSubGoodsListPage" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.PrintAtomGoodsVo">
        select
        <include refid="Select_Sub_Goods_Print_Column_List"/>,
        <include refid="Select_Base_Column_store_Category"/>
        from
        goods g
        LEFT JOIN store_category_goods_relation scgr
        ON g.parent_goods_no = scgr.goods_no
        LEFT JOIN store_category sc on scgr.store_category_no = sc.store_category_no
        LEFT JOIN brand b
        ON g.brand_no = b.brand_no
        <if test="stockDisableFlag !=null and childFlag == 1002">
            left join goods_real_stock st on (st.goods_no = g.original_goods_no or st.goods_no = g.multi_unit_goods_no)
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
                <include refid="GoodsShowStatus_Column_Where"/>
                <!--不同身份查询不同来源的商品-->
                <if test="storeCategoryNos != null and storeCategoryNos.size() > 0">
                    and (sc.store_category_no in
                    <foreach collection="storeCategoryNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    <choose>
                        <!--当前类目集合, 是否包含无分类-->
                        <when test="noCategoryFlag != null">
                            or scgr.store_category_no is null)
                        </when>
                        <otherwise>
                            )
                        </otherwise>
                    </choose>
                </if>
                <choose>
                    <!--当前类目集合, 是否包含无分类-->
                    <when test="noCategoryFlag != null">
                        and (sc.delete_flag = 1 or scgr.store_category_no is null)
                    </when>
                    <otherwise>
                        and (sc.delete_flag = 1)
                    </otherwise>
                </choose>
                and g.series_type !='1001'
            </trim>
        </where>
        order by g.modify_time desc
    </select>

    <resultMap id="goodsStockInfoMap" type="cn.htdt.goodsprocess.vo.AtomGoodsStockInfoVo">
        <result column="original_goods_no" property="originalGoodsNo"/>
        <result column="stock_num" property="stockNum"/>
        <result column="purchase_price" property="purchasePrice"/>
        <result column="create_time" property="createTime"/>
        <result column="bill_code" property="billCode"/>
    </resultMap>

    <!--2023-09-08蛋品-采购管理-采购单商品行信息-主单位商品编号-->
    <!--注：ifnull(p.raw_goods_no,p.goods_no) 多单位商品修改，如果raw_goods_no为空就是非多单位商品，采购单里放的都是子品，如果不为空则为多单位商品的主商品编号-->
    <select id="selectGoodsStockInfoList" resultMap="goodsStockInfoMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsSaleInfoVo">
        select t.realru as stock_num,
        p.purchase_unit_price * t.realru as purchase_price,
        ifnull(p.raw_goods_no,p.goods_no) as original_goods_no,
        t.create_time,
        t.bill_code
        from (select bill_code,
        DATE_FORMAT(create_time, '%Y-%m-%d') as create_time,
        sum(IF(bill_type = '1005', stock_num, 0)) - sum(IF(bill_type = '1006', stock_num, 0)) as realru,
        original_goods_no
        from im_warehouse_stock_flow_record
        where bill_type in ('1005', '1006')
        <if test="originalGoodsNoList != null and originalGoodsNoList.size > 0">
            and original_goods_no in
            <foreach collection="originalGoodsNoList" index="index" item="originalGoodsNo" open="("
                     separator="," close=")">
                #{originalGoodsNo}
            </foreach>
        </if>
        <if test="startTime != null">
            <![CDATA[ AND create_time >= #{startTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ AND create_time <= #{endTime} ]]>
        </if>
        group by original_goods_no, bill_code) t
        left join purchase_order_goods p on t.bill_code = p.purchase_code and t.original_goods_no = ifnull(p.raw_goods_no,p.goods_no)
        where t.realru > 0
    </select>

    <select id="selectGoodsPriceList" resultMap="goodsStockInfoMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsSaleInfoVo">
        select
        ifnull(g.purchase_price , g.retail_price) as purchase_price,
        g.original_goods_no
        from goods g
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="originalGoodsNoList != null and originalGoodsNoList.size > 0">
                    and g.goods_no in
                    <foreach collection="originalGoodsNoList" index="index" item="originalGoodsNo" open="("
                             separator="," close=")">
                        #{originalGoodsNo}
                    </foreach>
                </if>
            </trim>
        </where>
    </select>


    <select id="selectGoodsStockNumList" resultMap="goodsStockInfoMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsSaleInfoVo">
        select sum(i.current_stock_num) as stock_num, t.original_goods_no
        from (select max(create_time) as create_time, original_goods_no, warehouse_no
        from im_warehouse_stock_flow_record
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="originalGoodsNoList != null and originalGoodsNoList.size > 0">
                    and original_goods_no in
                    <foreach collection="originalGoodsNoList" index="index" item="originalGoodsNo" open="("
                             separator="," close=")">
                        #{originalGoodsNo}
                    </foreach>
                </if>
                <if test="startTime != null">
                    <![CDATA[ AND create_time >= #{startTime} ]]>
                </if>
                <if test="endTime != null">
                    <![CDATA[ AND create_time <= #{endTime} ]]>
                </if>
            </trim>
        </where>
        group by original_goods_no,warehouse_no) t
        left join im_warehouse_stock_flow_record i on t.original_goods_no = i.original_goods_no
        and t.create_time = i.create_time group by t.original_goods_no
    </select>

    <select id="selectGoodsCountByGoodsType" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        g.goods_type ,
        count(1) goodsNum
        from
        goods g
        <where>
            g.delete_flag = 1
            and g.disable_flag = 2
            and g.series_type != '1001'
            <if test="merchantNo != null and merchantNo != ''">
                AND g.merchant_no = #{merchantNo}
            </if>
            <if test="storeNo != null and storeNo != ''">
                AND g.store_no = #{storeNo}
            </if>
            <!--商家身份查询商家列表（包括商家自建） -->
            <if test="queryType==1004">
                and g.goods_source_type in ('1002')
            </if>
            <!--店铺身份查询列表（包括商家分发、店铺自建） -->
            <if test="queryType== 1006">
                and g.goods_source_type in ('1003','1005')
            </if>
        </where>
        group by
        g.goods_type
    </select>

    <select id="selectGoodsSkuCount" resultType="java.lang.Integer"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        count(*)
        from
        goods g
        <where>
            g.delete_flag = 1
            and g.disable_flag = 2
            <!--只查询子品或者普通商品-->
            and g.series_type != '1001'
            <!--排除虚拟商品-->
            and g.goods_type != '1002'
            <if test="merchantNo != null and merchantNo != ''">
                AND g.merchant_no = #{merchantNo}
            </if>
            <if test="storeNo != null and storeNo != ''">
                AND g.store_no = #{storeNo}
            </if>
            <!--商家身份查询商家列表（包括商家自建） -->
            <if test="queryType==1004">
                and g.goods_source_type = '1002'
            </if>
            <!--商家身份查询（包括商家自建+店铺自建(去除同步)） -->
            <if test="queryType==1014">
                and (g.goods_source_type = '1002' or (g.goods_source_type = '1003' and g.merchant_goods_no is null))
            </if>
            <!--店铺身份查询列表（店铺自建） -->
            <if test="queryType== 1006">
                and g.goods_source_type = '1003'
            </if>
        </where>
    </select>

    <select id="selectGoodsSortNum" resultType="int">
        select
        g.sort_num
        from
        goods g
        left join sale_category sc on sc.category_no = g.category_no
        <where>
            g.store_no = #{storeNo}
            and sc.second_category_no = #{categoryNo}
            and g.goods_type = #{goodsType}
            and g.goods_source_type in ('1003','1005')
            and g.goods_no = #{goodsNo}
        </where>
    </select>

    <select id="selectGoodsWarehouseFlag" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo" resultMap="BaseResultMap">
        select
            g.goods_no,
            g.multi_unit_type,
            g.multi_unit_goods_no,
            g.warehouse_flag,
            g.validity_period_manage_flag,
            g.shelf_life_unit,
            g.quality_guarantee_period
        from goods g
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    AND g.goods_no = #{goodsNo}
                </if>
            </trim>
        </where>
    </select>

    <update id="moveBetweenSortNum">
        update
        goods g,
        sale_category sc
        set
        g.sort_num = sort_num + #{moveValue}
        <where>
            g.store_no = #{storeNo}
            and g.goods_type = #{goodsType}
            and g.foods_type = '1001'
            and g.goods_source_type in ('1003','1005')
            and sc.category_no = g.category_no
            and sc.second_category_no = #{categoryNo}
            and g.sort_num between #{startMoveSortNum} and #{endMoveSortNum}
        </where>
    </update>

    <update id="updateSortNum">
        update
        goods g,
        sale_category sc
        set
        g.sort_num = #{endSortNum}
        <where>
            g.store_no = #{storeNo}
            and g.foods_type = '1001'
            and g.goods_source_type in ('1003','1005')
            and g.goods_no = #{goodsNo}
            and sc.category_no = g.category_no
            and sc.second_category_no = #{categoryNo}
            and g.sort_num = #{startSortNum}
        </where>
    </update>

    <update id="updateGoodsDistributionStatus" parameterType="cn.htdt.goodsprocess.domain.GoodsDomain">
        update
        goods
        set
        distribution_status = '3005'
        <where>
            goods_no = #{goodsNo}
            and (select count(1)
            from goods_distribution_shop_relation gd
            where gd.goods_no = #{goodsNo}
            and delete_flag = 1) = 0
        </where>
    </update>

    <update id="batchUpdateGoodsDistributionStatus" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        <foreach collection="goodsNos" separator=";" item="item">
            update
            goods
            set
            distribution_status = '3005'
            <where>
                goods_no = #{item}
                and (select count(1)
                from goods_distribution_shop_relation gd
                where gd.goods_no = #{item}
                and delete_flag = 1) = 0
            </where>
        </foreach>
    </update>

    <select id="selectListByMerchantGoodsNo"  parameterType="cn.htdt.goodsprocess.domain.GoodsDomain"  resultMap="BaseResultMap">
        select * from goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and delete_flag = 1
                <if test="merchantGoodsNo != null and merchantGoodsNo != ''">
                    and merchant_goods_no = #{merchantGoodsNo}
                </if>
                <!-- 店铺新建/修改商品校验 -->
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
            </trim>
        </where>
    </select>

</mapper>
