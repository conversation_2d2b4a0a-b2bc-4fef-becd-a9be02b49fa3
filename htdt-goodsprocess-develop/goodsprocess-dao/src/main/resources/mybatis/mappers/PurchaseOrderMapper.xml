<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htdt.goodsprocess.dao.PurchaseOrderDao">
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.PurchaseOrderDomain">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="purchase_code" property="purchaseCode" jdbcType="VARCHAR"/>
        <result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
        <result column="supplier_code" property="supplierCode" jdbcType="VARCHAR"/>
        <result column="supplier_name" property="supplierName" jdbcType="VARCHAR"/>
        <result column="purchase_date" property="purchaseDate" jdbcType="TIMESTAMP"/>
        <result column="purchaser_name" property="purchaserName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="attach_name" property="attachName" jdbcType="VARCHAR"/>
        <result column="attach_path" property="attachPath" jdbcType="VARCHAR"/>
        <result column="receive_contact_name" property="receiveContactName" jdbcType="VARCHAR"/>
        <result column="receive_contact_mobile" property="receiveContactMobile" jdbcType="VARCHAR"/>
        <result column="receive_province_code" property="receiveProvinceCode" jdbcType="VARCHAR"/>
        <result column="receive_province_name" property="receiveProvinceName" jdbcType="VARCHAR"/>
        <result column="receive_city_code" property="receiveCityCode" jdbcType="VARCHAR"/>
        <result column="receive_city_name" property="receiveCityName" jdbcType="VARCHAR"/>
        <result column="receive_district_code" property="receiveDistrictCode" jdbcType="VARCHAR"/>
        <result column="receive_district_name" property="receiveDistrictName" jdbcType="VARCHAR"/>
        <result column="receive_town_code" property="receiveTownCode" jdbcType="VARCHAR"/>
        <result column="receive_town_name" property="receiveTownName" jdbcType="VARCHAR"/>
        <result column="receive_detail_address" property="receiveDetailAddress" jdbcType="VARCHAR"/>
        <result column="send_contact_name" property="sendContactName" jdbcType="VARCHAR"/>
        <result column="send_contact_mobile" property="sendContactMobile" jdbcType="VARCHAR"/>
        <result column="send_province_code" property="sendProvinceCode" jdbcType="VARCHAR"/>
        <result column="send_province_name" property="sendProvinceName" jdbcType="VARCHAR"/>
        <result column="send_city_code" property="sendCityCode" jdbcType="VARCHAR"/>
        <result column="send_city_name" property="sendCityName" jdbcType="VARCHAR"/>
        <result column="send_district_code" property="sendDistrictCode" jdbcType="VARCHAR"/>
        <result column="send_district_name" property="sendDistrictName" jdbcType="VARCHAR"/>
        <result column="send_town_code" property="sendTownCode" jdbcType="VARCHAR"/>
        <result column="send_town_name" property="sendTownName" jdbcType="VARCHAR"/>
        <result column="send_detail_address" property="sendDetailAddress" jdbcType="VARCHAR"/>
        <result column="order_status" property="orderStatus" jdbcType="INTEGER"/>
        <result column="total_purchase_num" property="totalPurchaseNum" jdbcType="DECIMAL"/>
        <result column="total_purchase_price" property="totalPurchasePrice" jdbcType="DECIMAL"/>
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="merchant_name" property="merchantName" jdbcType="VARCHAR"/>
        <result column="store_no" property="storeNo" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="disable_flag" property="disableFlag" jdbcType="TINYINT"/>
        <result column="create_no" property="createNo" jdbcType="VARCHAR"/>
        <result column="create_name" property="createName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_no" property="modifyNo" jdbcType="VARCHAR"/>
        <result column="modify_name" property="modifyName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="company_no" property="companyNo" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="branch_no" property="branchNo" jdbcType="VARCHAR"/>
        <result column="branch_name" property="branchName" jdbcType="VARCHAR"/>
        <result column="platform_type" property="platformType" jdbcType="VARCHAR"/>
        <result column="ds_receive_contact_mobile" property="dsReceiveContactMobile" jdbcType="VARCHAR"/>
        <result column="ds_receive_detail_address" property="dsReceiveDetailAddress" jdbcType="VARCHAR"/>
        <result column="ds_send_contact_mobile" property="dsSendContactMobile" jdbcType="VARCHAR"/>
        <result column="ds_send_detail_address" property="dsSendDetailAddress" jdbcType="VARCHAR"/>
        <result column="purchase_type" property="purchaseType" jdbcType="INTEGER"/>
    </resultMap>

    <resultMap id="PurchaseReturnOrderResultMap" extends="BaseResultMap" type="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        <result column="goods_num" property="goodsNum" jdbcType="INTEGER"/>
        <result column="goods_name_concat" property="goodsNameArr" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="PurchaseOrder_import_Column_List">
        po
        .
        purchase_code
        AS purchaseCode, po.supplier_code AS supplierCode, po.supplier_name AS supplierName, po.total_purchase_price AS totalPurchasePrice,
    po.send_contact_name AS sendContactName, po.send_contact_mobile AS sendContactMobile, po.create_time AS createTime,
    pog.purchase_goods_code AS purchaseGoodsCode,pog.goods_no as goodsNo,pog.goods_name AS goodsName,pog.purchase_unit_price AS purchaseUnitPrice,
    pog.purchase_unit AS purchaseUnit,pog.expect_receive_date AS expectReceiveDate,
    pog.purchase_num AS purchaseNum,pog.purchase_price AS purchasePrice,g.first_attribute_value_name AS firstAttributeValueName,
    g.second_attribute_value_name AS secondAttributeValueName,g.third_attribute_value_name AS thirdAttributeValueName
    , po.ds_send_contact_mobile as dsSendContactMobile,pog.remark AS remark
    </sql>
    <sql id="Base_Column_List">
        id
        , purchase_code, source_type, supplier_code, supplier_name, purchase_date, purchaser_name,
    remark, attach_name, attach_path, receive_contact_name, receive_contact_mobile, receive_province_code, 
    receive_province_name, receive_city_code, receive_city_name, receive_district_code, 
    receive_district_name, receive_town_code, receive_town_name, receive_detail_address, 
    send_contact_name, send_contact_mobile, send_province_code, send_province_name, send_city_code, 
    send_city_name, send_district_code, send_district_name, send_town_code, send_town_name, 
    send_detail_address, order_status, total_purchase_num, total_purchase_price, merchant_no, 
    merchant_name, store_no, store_name, disable_flag, create_no, create_name, create_time, 
    modify_no, modify_name, modify_time, delete_flag, company_no, company_name, branch_no, 
    branch_name,platform_type, ds_receive_contact_mobile, ds_receive_detail_address, ds_send_contact_mobile, ds_send_detail_address, purchase_type
    </sql>
    <sql id="Page_PurchaseOrder_Column_List">
        po.id, po.purchase_code, po.source_type, po.supplier_code, po.supplier_name, po.purchase_date, po.purchaser_name,
    po.remark,po.attach_name, po.attach_path, po.receive_contact_name, po.receive_contact_mobile, po.receive_province_code,
    po.receive_province_name, po.receive_city_code, po.receive_city_name, po.receive_district_code,
    po.receive_district_name, po.receive_town_code, po.receive_town_name, po.receive_detail_address,
    po.send_contact_name, po.send_contact_mobile, po.send_province_code, po.send_province_name, po.send_city_code,
    po.send_city_name, po.send_district_code, po.send_district_name, po.send_town_code, po.send_town_name,
    po.send_detail_address, po.order_status, po.total_purchase_num, po.total_purchase_price, po.merchant_no,
    po.merchant_name, po.store_no, po.store_name, po.disable_flag, po.create_no, po.create_name, po.create_time,
    po.modify_no, po.modify_name, po.modify_time, po.delete_flag, po.company_no, po.company_name, po.branch_no,
    po.branch_name,po.platform_type, po.ds_receive_contact_mobile, po.ds_receive_detail_address, po.ds_send_contact_mobile, po.ds_send_detail_address,po.purchase_type
    </sql>


    <!--状态标识-->
    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>
    <sql id="Base_Column_Goods_Detail_Where">
        and pog.delete_flag = 1
    </sql>

    <!--采购单列表查询-->
    <select id="selectPagePurchaseOrderByParam" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select
        <include refid="Page_PurchaseOrder_Column_List"/>
        from purchase_order po
        LEFT JOIN purchase_order_goods pog ON po.purchase_code = pog.purchase_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo  != null">
                    and po.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null">
                    and po.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>

                <if test="storeNoList != null and storeNoList.size() > 0 ">
                    and po.store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="platformType != null and platformType != ''">
                    and po.platform_type= #{platformType,jdbcType=VARCHAR}
                </if>
                <if test="sourceType != null and sourceType != ''">
                    and po.source_type = #{sourceType,jdbcType=VARCHAR}
                </if>
                <if test="orderStatus != null and orderStatus != ''">
                    and po.order_status = #{orderStatus,jdbcType=INTEGER}
                </if>
                <if test="supplierName  != null and supplierName  != ''">
                    <choose>
                        <when test="querySenceId != null and querySenceId == 2">
                            and po.supplier_name LIKE CONCAT('%',#{supplierName,jdbcType=VARCHAR},'%')
                        </when>
                        <otherwise>
                            and (po.supplier_code LIKE CONCAT('%',#{supplierName,jdbcType=VARCHAR},'%')
                            or po.supplier_name LIKE CONCAT('%',#{supplierName,jdbcType=VARCHAR},'%'))
                        </otherwise>
                    </choose>
                </if>
                <if test="purchaseCode  != null and purchaseCode  != ''">
                    and po.purchase_code LIKE CONCAT('%',#{purchaseCode,jdbcType=VARCHAR},'%')
                </if>
                <if test="goodsNo  != null and goodsNo  != ''">
                    and (pog.goods_no LIKE CONCAT('%',#{goodsNo,jdbcType=VARCHAR},'%')
                    or pog.goods_name LIKE CONCAT('%',#{goodsName,jdbcType=VARCHAR},'%'))
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and pog.goods_name LIKE CONCAT('%', #{goodsName,jdbcType=VARCHAR}, '%')
                </if>
                <if test="queryStr != null and queryStr != ''">
                    and (pog.goods_name LIKE CONCAT('%', #{queryStr,jdbcType=VARCHAR}, '%')
                        or po.supplier_name LIKE CONCAT('%',#{queryStr,jdbcType=VARCHAR},'%'))
                </if>
                <if test="createTimeStart != null">
                    <![CDATA[
                        and  po.create_time  >= #{createTimeStart}
                    ]]>
                </if>
                <if test="createTimeEnd != null">
                    <![CDATA[
                        and po.create_time <= #{createTimeEnd}
                    ]]>
                </if>
                <!-- 采购时间过滤 -->
                <if test="purchaseDateStart != null">
                    <![CDATA[
                        and  po.purchase_date >= #{purchaseDateStart}
                    ]]>
                </if>
                <if test="purchaseType != null">
                    and po.purchase_type = #{purchaseType,jdbcType=INTEGER}
                </if>
                and po.delete_flag = 1
            </trim>
        </where>
        GROUP BY po.purchase_code
        ORDER BY po.purchase_date DESC, po.create_time DESC
    </select>

    <!-- 查询采购单的商品的数量-批量 -->
    <select id="selectBatchPurchaseGoodsCount" resultType="cn.htdt.goodsprocess.vo.PurchaseOrderVo"
            parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select pog.purchase_code, count(1) totalCount from purchase_order_goods pog
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCodeList != null and purchaseCodeList.size() > 0">
                    and pog.purchase_code IN
                    <foreach item="purchaseCode" collection="purchaseCodeList" open="(" close=")" separator=",">
                        #{purchaseCode}
                    </foreach>
                </if>
                <include refid="Base_Column_Goods_Detail_Where"/>
            </trim>
        </where>
        group by pog.purchase_code
    </select>

    <!-- 采购订单月度汇总 -->
    <select id="selectPurchaseOrderMonthCount" parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo" resultType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select
        DATE_FORMAT( po.create_time, '%Y-%m' ) AS groupTime,
        COUNT( po.purchase_code ) AS totalOrder,
        SUM( po.total_purchase_price) AS totalAmount
        from purchase_order po
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo  != null">
                    and po.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null">
                    and po.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="platformType != null and platformType != ''">
                    and po.platform_type= #{platformType,jdbcType=VARCHAR}
                </if>
                <if test="sourceType != null and sourceType != ''">
                    and po.source_type = #{sourceType,jdbcType=VARCHAR}
                </if>
                <if test="orderStatus != null and orderStatus != ''">
                    and po.order_status = #{orderStatus,jdbcType=INTEGER}
                </if>
                <if test="supplierName  != null and supplierName  != ''">
                    and (po.supplier_code LIKE CONCAT('%',#{supplierName,jdbcType=VARCHAR},'%')
                    or po.supplier_name LIKE CONCAT('%',#{supplierName,jdbcType=VARCHAR},'%'))
                </if>
                <if test="purchaseCode  != null and purchaseCode  != ''">
                    and po.purchase_code LIKE CONCAT('%',#{purchaseCode,jdbcType=VARCHAR},'%')
                </if>
                <!-- 创建时间 -->
                <if test="createTimeStart != null">
                    <![CDATA[
                        and  po.create_time >= #{createTimeStart}
                    ]]>
                </if>
                <if test="createTimeEnd != null">
                    <![CDATA[
                        and po.create_time <= #{createTimeEnd}
                    ]]>
                </if>
                <!-- 采购时间过滤 -->
                <if test="purchaseDateStart != null">
                    <![CDATA[
                        and  po.purchase_date >= #{purchaseDateStart}
                    ]]>
                </if>
                <if test="purchaseDateEnd != null">
                    <![CDATA[
                        and po.purchase_date <= #{purchaseDateEnd}
                    ]]>
                </if>
                and po.delete_flag = 1
            </trim>
        </where>
        GROUP BY groupTime ORDER BY groupTime DESC
    </select>

    <!-- 采购退款订单月度汇总 -->
    <select id="selectPurchaseReturnOrderMonthCount" parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo" resultType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select
        DATE_FORMAT( po.create_time, '%Y-%m' ) AS groupTime,
        COUNT( prg.return_code ) AS totalOrder,
        IFNULL(SUM( prg.total_return_price), 0) AS totalAmount
        from purchase_return_order prg
        LEFT JOIN purchase_order po ON po.purchase_code = prg.purchase_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo  != null">
                    and po.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null">
                    and po.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="platformType != null and platformType != ''">
                    and po.platform_type= #{platformType,jdbcType=VARCHAR}
                </if>
                <if test="sourceType != null and sourceType != ''">
                    and po.source_type = #{sourceType,jdbcType=VARCHAR}
                </if>
                <if test="orderStatus != null and orderStatus != ''">
                    and po.order_status = #{orderStatus,jdbcType=INTEGER}
                </if>
                <if test="supplierName  != null and supplierName  != ''">
                    and (po.supplier_code LIKE CONCAT('%',#{supplierName,jdbcType=VARCHAR},'%')
                    or po.supplier_name LIKE CONCAT('%',#{supplierName,jdbcType=VARCHAR},'%'))
                </if>
                <if test="purchaseCode  != null and purchaseCode  != ''">
                    and po.purchase_code LIKE CONCAT('%',#{purchaseCode,jdbcType=VARCHAR},'%')
                </if>
                <!-- 创建时间 -->
                <if test="createTimeStart != null">
                    <![CDATA[
                        and po.create_time >= #{createTimeStart}
                    ]]>
                </if>
                <if test="createTimeEnd != null">
                    <![CDATA[
                        and po.create_time <= #{createTimeEnd}
                    ]]>
                </if>
                <!-- 采购时间过滤 -->
                <if test="purchaseDateStart != null">
                    <![CDATA[
                        and  po.purchase_date >= #{purchaseDateStart}
                    ]]>
                </if>
                <if test="purchaseDateEnd != null">
                    <![CDATA[
                        and po.purchase_date <= #{purchaseDateEnd}
                    ]]>
                </if>
                and po.delete_flag = 1
            </trim>
        </where>
        GROUP BY groupTime
    </select>

    <!-- 首页采购概况 -->
    <select id="selectPurchaseCount" parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo" resultType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select
        count(po.purchase_code) as totalOrder
        from
        purchase_order po
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    and po.store_no = #{storeNo}
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and po.store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="merchantNo != null and merchantNo  != ''">
                    and po.merchant_no = #{merchantNo}
                </if>
                <if test="platformType != null and platformType != ''">
                    and po.platform_type= #{platformType}
                </if>
                <!-- 修改时间 -->
                <if test="modifyTimeStart != null">
                    <![CDATA[
                        and po.modify_time >= #{modifyTimeStart}
                    ]]>
                </if>
                <if test="modifyTimeEnd != null">
                    <![CDATA[
                        and po.modify_time <= #{modifyTimeEnd}
                    ]]>
                </if>
                and po.order_status = 26
                and po.delete_flag = 1
            </trim>
        </where>
    </select>

    <!-- 首页采购商品概况 -->
    <select id="selectPurchaseGoodsCount" parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo" resultType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select
        sum(pog.purchase_unit_price * pog.storage_count) as totalAmount,
        count(distinct pog.goods_no) as totalCount
        from
        purchase_order po
        left join purchase_order_goods pog on
        po.purchase_code = pog.purchase_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    and po.store_no = #{storeNo}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and po.merchant_no = #{merchantNo}
                </if>
                <if test="platformType != null and platformType != ''">
                    and po.platform_type= #{platformType}
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and po.store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!-- 修改时间 -->
                <if test="modifyTimeStart != null">
                    <![CDATA[
                        and po.modify_time >= #{modifyTimeStart}
                    ]]>
                </if>
                <if test="modifyTimeEnd != null">
                    <![CDATA[
                        and po.modify_time <= #{modifyTimeEnd}
                    ]]>
                </if>
                and pog.storage_count > 0
                and po.order_status = 26
                and po.delete_flag = 1
            </trim>
        </where>
    </select>

    <!--采购单详情查询-->
    <select id="selectPurchaseOrderDetailByCode" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.PurchaseOrderDomain">
        select
        <include refid="Base_Column_List"/>
        from purchase_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode  != null and purchaseCode  != ''">
                    and purchase_code = #{purchaseCode}
                </if>
            </trim>
        </where>
    </select>

    <!--根据商品编码 查询采购单信息表 待入库或部分入库状态-->
    <select id="selectPurchaseOrderByGoodsNo" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select
        <include refid="Page_PurchaseOrder_Column_List"/>
        from purchase_order po
        LEFT JOIN purchase_order_goods pog ON pog.purchase_code = po.purchase_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode  != null and purchaseCode  != ''">
                    and po.purchase_code = #{purchaseCode}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and pog.goods_no = #{goodsNo}
                </if>
                AND po.source_type = '1001'
                AND po.order_status IN ('23', '24')
                AND po.delete_flag = 1
                AND pog.delete_flag = 1
                AND pog.order_status != '1003'
            </trim>
        </where>

    </select>

    <!--采购单列表导出-->
    <select id="selectPurchaseOrderExcel" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderExcelVo"
            parameterType="java.util.ArrayList">
        select
        <include refid="PurchaseOrder_import_Column_List"/>
        from purchase_order po
        LEFT JOIN purchase_order_goods pog ON po.purchase_code = pog.purchase_code
        left join goods g on pog.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCodeList  != null  and purchaseCodeList.size()>0">
                    and pog.purchase_code in
                    <foreach collection="purchaseCodeList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and pog.delete_flag = 1
            </trim>
        </where>
        ORDER BY pog.create_time DESC
    </select>

    <!--根据供应商编码获取是否存在采购单-->
    <select id="selectOrderCountBySupplierCode" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
        count(1)
        from purchase_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="supplierCode != null and supplierCode != ''">
                    and supplier_code = #{supplierCode,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <!--条件新增-->
    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.PurchaseOrderDomain">
        insert into purchase_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="purchaseCode != null">
                purchase_code,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="supplierCode != null">
                supplier_code,
            </if>
            <if test="supplierName != null">
                supplier_name,
            </if>
            <if test="purchaseDate != null">
                purchase_date,
            </if>
            <if test="purchaserName != null">
                purchaser_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="attachName != null">
                attach_name,
            </if>
            <if test="attachPath != null">
                attach_path,
            </if>
            <if test="receiveContactName != null">
                receive_contact_name,
            </if>
            <if test="receiveContactMobile != null">
                receive_contact_mobile,
            </if>
            <if test="dsReceiveContactMobile != null">
                ds_receive_contact_mobile,
            </if>
            <if test="receiveProvinceCode != null">
                receive_province_code,
            </if>
            <if test="receiveProvinceName != null">
                receive_province_name,
            </if>
            <if test="receiveCityCode != null">
                receive_city_code,
            </if>
            <if test="receiveCityName != null">
                receive_city_name,
            </if>
            <if test="receiveDistrictCode != null">
                receive_district_code,
            </if>
            <if test="receiveDistrictName != null">
                receive_district_name,
            </if>
            <if test="receiveTownCode != null">
                receive_town_code,
            </if>
            <if test="receiveTownName != null">
                receive_town_name,
            </if>
            <if test="receiveDetailAddress != null">
                receive_detail_address,
            </if>
            <if test="dsReceiveDetailAddress != null">
                ds_receive_detail_address,
            </if>
            <if test="sendContactName != null">
                send_contact_name,
            </if>
            <if test="sendContactMobile != null">
                send_contact_mobile,
            </if>
            <if test="dsSendContactMobile != null">
                ds_send_contact_mobile,
            </if>
            <if test="sendProvinceCode != null">
                send_province_code,
            </if>
            <if test="sendProvinceName != null">
                send_province_name,
            </if>
            <if test="sendCityCode != null">
                send_city_code,
            </if>
            <if test="sendCityName != null">
                send_city_name,
            </if>
            <if test="sendDistrictCode != null">
                send_district_code,
            </if>
            <if test="sendDistrictName != null">
                send_district_name,
            </if>
            <if test="sendTownCode != null">
                send_town_code,
            </if>
            <if test="sendTownName != null">
                send_town_name,
            </if>
            <if test="sendDetailAddress != null">
                send_detail_address,
            </if>
            <if test="dsSendDetailAddress != null">
                ds_send_detail_address,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="totalPurchaseNum != null">
                total_purchase_num,
            </if>
            <if test="totalPurchasePrice != null">
                total_purchase_price,
            </if>
            <if test="merchantNo != null">
                merchant_no,
            </if>
            <if test="merchantName != null">
                merchant_name,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
            <if test="disableFlag != null">
                disable_flag,
            </if>
            <if test="createNo != null">
                create_no,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyNo != null">
                modify_no,
            </if>
            <if test="modifyName != null">
                modify_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="companyNo != null">
                company_no,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="branchNo != null">
                branch_no,
            </if>
            <if test="branchName != null">
                branch_name,
            </if>
            <if test="platformType != null ">
                platform_type
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="purchaseCode != null">
                #{purchaseCode,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="purchaseDate != null">
                #{purchaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="purchaserName != null">
                #{purchaserName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="attachName != null">
                #{attachName,jdbcType=VARCHAR},
            </if>
            <if test="attachPath != null">
                #{attachPath,jdbcType=VARCHAR},
            </if>
            <if test="receiveContactName != null">
                #{receiveContactName,jdbcType=VARCHAR},
            </if>
            <if test="receiveContactMobile != null">
                #{receiveContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="dsReceiveContactMobile != null">
                #{dsReceiveContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="receiveProvinceCode != null">
                #{receiveProvinceCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveProvinceName != null">
                #{receiveProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="receiveCityCode != null">
                #{receiveCityCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveCityName != null">
                #{receiveCityName,jdbcType=VARCHAR},
            </if>
            <if test="receiveDistrictCode != null">
                #{receiveDistrictCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveDistrictName != null">
                #{receiveDistrictName,jdbcType=VARCHAR},
            </if>
            <if test="receiveTownCode != null">
                #{receiveTownCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveTownName != null">
                #{receiveTownName,jdbcType=VARCHAR},
            </if>
            <if test="receiveDetailAddress != null">
                #{receiveDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="dsReceiveDetailAddress != null">
                #{dsReceiveDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="sendContactName != null">
                #{sendContactName,jdbcType=VARCHAR},
            </if>
            <if test="sendContactMobile != null">
                #{sendContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="dsSendContactMobile != null">
                #{dsSendContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="sendProvinceCode != null">
                #{sendProvinceCode,jdbcType=VARCHAR},
            </if>
            <if test="sendProvinceName != null">
                #{sendProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="sendCityCode != null">
                #{sendCityCode,jdbcType=VARCHAR},
            </if>
            <if test="sendCityName != null">
                #{sendCityName,jdbcType=VARCHAR},
            </if>
            <if test="sendDistrictCode != null">
                #{sendDistrictCode,jdbcType=VARCHAR},
            </if>
            <if test="sendDistrictName != null">
                #{sendDistrictName,jdbcType=VARCHAR},
            </if>
            <if test="sendTownCode != null">
                #{sendTownCode,jdbcType=VARCHAR},
            </if>
            <if test="sendTownName != null">
                #{sendTownName,jdbcType=VARCHAR},
            </if>
            <if test="sendDetailAddress != null">
                #{sendDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="dsSendDetailAddress != null">
                #{dsSendDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="totalPurchaseNum != null">
                #{totalPurchaseNum,jdbcType=DECIMAL},
            </if>
            <if test="totalPurchasePrice != null">
                #{totalPurchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="merchantNo != null">
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null">
                #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null">
                #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null">
                #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="platformType != null ">
                #{platformType,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.PurchaseOrderDomain">
        update purchase_order
        <set>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="supplierCode != null">
                supplier_code = #{supplierCode,jdbcType=VARCHAR},
            </if>
            <if test="supplierName != null">
                supplier_name = #{supplierName,jdbcType=VARCHAR},
            </if>
            <if test="purchaseDate != null">
                purchase_date = #{purchaseDate,jdbcType=TIMESTAMP},
            </if>
            <if test="purchaserName != null">
                purchaser_name = #{purchaserName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="attachName != null">
                attach_name = #{attachName,jdbcType=VARCHAR},
            </if>
            <if test="attachPath != null">
                attach_path = #{attachPath,jdbcType=VARCHAR},
            </if>
            <if test="receiveContactName != null">
                receive_contact_name = #{receiveContactName,jdbcType=VARCHAR},
            </if>
            <if test="receiveContactMobile != null">
                receive_contact_mobile = #{receiveContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="dsReceiveContactMobile != null">
                ds_receive_contact_mobile = #{dsReceiveContactMobile, jdbcType=VARCHAR},
            </if>
            <if test="receiveProvinceCode != null">
                receive_province_code = #{receiveProvinceCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveProvinceName != null">
                receive_province_name = #{receiveProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="receiveCityCode != null">
                receive_city_code = #{receiveCityCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveCityName != null">
                receive_city_name = #{receiveCityName,jdbcType=VARCHAR},
            </if>
            <if test="receiveDistrictCode != null">
                receive_district_code = #{receiveDistrictCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveDistrictName != null">
                receive_district_name = #{receiveDistrictName,jdbcType=VARCHAR},
            </if>
            <if test="receiveTownCode != null">
                receive_town_code = #{receiveTownCode,jdbcType=VARCHAR},
            </if>
            <if test="receiveTownName != null">
                receive_town_name = #{receiveTownName,jdbcType=VARCHAR},
            </if>
            <if test="receiveDetailAddress != null">
                receive_detail_address = #{receiveDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="dsReceiveDetailAddress != null">
                ds_receive_detail_address = #{dsReceiveDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="sendContactName != null">
                send_contact_name = #{sendContactName,jdbcType=VARCHAR},
            </if>
            <if test="sendContactMobile != null">
                send_contact_mobile = #{sendContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="dsSendContactMobile != null">
                ds_send_contact_mobile = #{dsSendContactMobile,jdbcType=VARCHAR},
            </if>
            <if test="sendProvinceCode != null">
                send_province_code = #{sendProvinceCode,jdbcType=VARCHAR},
            </if>
            <if test="sendProvinceName != null">
                send_province_name = #{sendProvinceName,jdbcType=VARCHAR},
            </if>
            <if test="sendCityCode != null">
                send_city_code = #{sendCityCode,jdbcType=VARCHAR},
            </if>
            <if test="sendCityName != null">
                send_city_name = #{sendCityName,jdbcType=VARCHAR},
            </if>
            <if test="sendDistrictCode != null">
                send_district_code = #{sendDistrictCode,jdbcType=VARCHAR},
            </if>
            <if test="sendDistrictName != null">
                send_district_name = #{sendDistrictName,jdbcType=VARCHAR},
            </if>
            <if test="sendTownCode != null">
                send_town_code = #{sendTownCode,jdbcType=VARCHAR},
            </if>
            <if test="sendTownName != null">
                send_town_name = #{sendTownName,jdbcType=VARCHAR},
            </if>
            <if test="sendDetailAddress != null">
                send_detail_address = #{sendDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="dsSendDetailAddress != null">
                ds_send_detail_address = #{dsSendDetailAddress,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="totalPurchaseNum != null">
                total_purchase_num = #{totalPurchaseNum,jdbcType=DECIMAL},
            </if>
            <if test="totalPurchasePrice != null">
                total_purchase_price = #{totalPurchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null">
                company_no = #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null">
                branch_no = #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null">
                branch_name = #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="platformType != null ">
                platform_type = #{platformType,jdbcType=VARCHAR}
            </if>
        </set>
        where purchase_code = #{purchaseCode,jdbcType=VARCHAR}
    </update>

    <!--根据采购单编码修改状态-->
    <update id="updatePurchaseOrderStatus" parameterType="cn.htdt.goodsprocess.vo.AtomUpdatePurchaseOrderStatusVo">
        update purchase_order
        <set>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=TINYINT},
            </if>
            <if test="canReturnNum != null">
                <choose>
                    <when test="operateType != null and operateType = 'add'">
                        can_return_num = can_return_num + #{canReturnNum,jdbcType=DECIMAL},
                    </when>
                    <otherwise>
                        can_return_num = can_return_num - #{canReturnNum,jdbcType=DECIMAL},
                    </otherwise>
                </choose>
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    and purchase_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
            </trim>
        </where>
    </update>

    <!--根据采购单编码修改状态-->
    <update id="updateCanReturnNum" parameterType="cn.htdt.goodsprocess.vo.AtomUpdatePurchaseOrderStatusVo">
        update purchase_order
        <set>
            <if test="canReturnNum != null">
                <choose>
                    <when test="operateType != null and operateType = 'add'">
                        can_return_num = can_return_num + #{canReturnNum,jdbcType=DECIMAL},
                    </when>
                    <otherwise>
                        can_return_num = can_return_num - #{canReturnNum,jdbcType=DECIMAL},
                    </otherwise>
                </choose>
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    and purchase_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
            </trim>
        </where>
    </update>

    <!--根据采购单编码删除-->
    <update id="logicDeleteByPurchaseCode" parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        update purchase_order
        <set>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    and purchase_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <if test="purchaseCodeList != null and purchaseCodeList.size > 0">
                    and purchase_code in
                    <foreach collection="purchaseCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <!--根据采购单编码修改为强制完成-->
    <update id="updateOrderStatusFinishByPurchaseCode" parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        update purchase_order
        <set>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=TINYINT},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null and purchaseCode != ''">
                    and purchase_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <if test="purchaseCodeList != null and purchaseCodeList.size > 0">
                    and purchase_code in
                    <foreach collection="purchaseCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
    </update>

    <!-- 一体机查询采购单分页列表 -->
    <select id="selectPagePurchaseOrderForCashier" resultMap="PurchaseReturnOrderResultMap"
            parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select
        <include refid="Page_PurchaseOrder_Column_List"/>,
        count(pog.goods_no) as goods_num,
        group_concat(pog.goods_name) goods_name_concat
        from purchase_order po
        LEFT JOIN purchase_order_goods pog ON po.purchase_code = pog.purchase_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null">
                    and po.store_no = #{storeNo}
                </if>
                <if test="orderStatus != null and orderStatus != ''">
                    and po.order_status = #{orderStatus}
                </if>
                <if test="purchaseCode  != null and purchaseCode  != ''">
                    and po.purchase_code = #{purchaseCode}
                </if>
                <if test="goodsName  != null and goodsName  != ''">
                    and pog.goods_name LIKE CONCAT('%',#{goodsName},'%')
                </if>
                <if test="goodsNameKeyWord != null and goodsNameKeyWord != ''">
                    and (
                    pog.goods_name like CONCAT(CONCAT('%', #{goodsNameKeyWord}),'%')
                    or pog.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsNameKeyWord}),'%')
                    or pog.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsNameKeyWord}),'%')
                    )
                </if>
                <if test="supplierCode != null and supplierCode != ''">
                    and po.supplier_code = #{supplierCode}
                </if>
                <if test="supplierName != null and supplierName != ''">
                    and po.supplier_name like CONCAT('%',#{supplierName},'%')
                </if>
                <if test="searchContent != null and searchContent != ''">
                    and (po.purchase_code = #{searchContent} or po.supplier_name like CONCAT('%',#{searchContent},'%'))
                </if>
                <!-- 采购时间过滤 -->
                <if test="purchaseDateStart != null">
                    <![CDATA[
                        and  po.purchase_date  >= #{purchaseDateStart}
                    ]]>
                </if>
                <if test="purchaseDateEnd != null ">
                    <![CDATA[
                        and po.purchase_date <= #{purchaseDateEnd}
                    ]]>
                </if>
                and po.delete_flag = 1
            </trim>
        </where>
        GROUP BY
        <include refid="Page_PurchaseOrder_Column_List"/>
        ORDER BY po.create_time DESC
    </select>

    <!-- 一体机查询采购单分页列表 -->
    <select id="selectOrderCountByStatus" resultType="cn.htdt.goodsprocess.vo.AtomResOrderStatusCountVo"
            parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select
            order_status orderStatus,
            count(1) as orderCount
        from purchase_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null">
                    and store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null">
                    and merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="platformType != null and platformType != ''">
                    and platform_type= #{platformType,jdbcType=VARCHAR}
                </if>
                <if test="orderStatusList != null and orderStatusList.size > 0">
                    and order_status in
                    <foreach collection="orderStatusList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and delete_flag = 1
            </trim>
        </where>
        GROUP BY order_status
    </select>

    <!--采购单列表查询-->
    <select id="selectPageCanreturnPurchaseOrderList" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.vo.PurchaseOrderVo">
        select
            po.id, po.purchase_code, po.source_type, po.supplier_code, po.supplier_name, po.purchase_date,
            po.remark,po.order_status, po.total_purchase_num, po.total_purchase_price, po.merchant_no,
            po.merchant_name, po.store_no, po.store_name, po.disable_flag, po.create_no, po.create_name, po.create_time,
            po.modify_no, po.modify_name, po.modify_time, po.delete_flag, po.platform_type, po.purchase_type
        from purchase_order po
        LEFT JOIN purchase_order_goods pog ON po.purchase_code = pog.purchase_code
        LEFT JOIN im_warehouse_stock_flow_record iws on iws.sub_bill_code = pog.purchase_goods_code
        LEFT JOIN im_warehouse_goods_relation iwg on iwg.warehouse_no = iws.warehouse_no and iwg.goods_no = iws.goods_no and iwg.delete_flag = 1
        LEFT JOIN goods_real_stock grs on grs.goods_no = iws.goods_no and grs.delete_flag = 1
        LEFT JOIN goods gs ON gs.goods_no = pog.goods_no and po.purchase_type = 0
        left join goods_groups ggs on ggs.goods_groups_no = pog.goods_no and po.purchase_type = 1
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo  != null">
                    and po.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null">
                    and po.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="platformType != null and platformType != ''">
                    and po.platform_type= #{platformType,jdbcType=VARCHAR}
                </if>
                <if test="sourceType != null and sourceType != ''">
                    and po.source_type = #{sourceType,jdbcType=VARCHAR}
                </if>
                <if test="queryStr != null and queryStr != ''">
                    and (pog.goods_name LIKE CONCAT('%', #{queryStr,jdbcType=VARCHAR}, '%')
                    or po.supplier_name LIKE CONCAT('%',#{queryStr,jdbcType=VARCHAR},'%'))
                </if>
                <if test="createTimeStart != null">
                    <![CDATA[
                        and  po.create_time  >= #{createTimeStart}
                    ]]>
                </if>
                <if test="createTimeEnd != null">
                    <![CDATA[
                        and po.create_time <= #{createTimeEnd}
                    ]]>
                </if>
                <!-- 采购时间过滤 -->
                <if test="purchaseDateStart != null">
                    <![CDATA[
                        and  po.purchase_date >= #{purchaseDateStart}
                    ]]>
                </if>
                <if test="purchaseDateEnd != null">
                    <![CDATA[
                        and po.purchase_date <= #{purchaseDateEnd}
                    ]]>
                </if>
                and po.order_status in (24,26)
                and po.delete_flag = 1
                and po.can_return_num > 0
                and ((pog.warehouse_type = 2 AND iwg.stock_num > 0) or (pog.warehouse_type = 1 AND grs.real_stock_num > 0))
                AND (pog.warehouse_type = gs.warehouse_flag or pog.warehouse_type = ggs.warehouse_flag)
            </trim>
        </where>
        GROUP BY po.purchase_code
        ORDER BY po.create_time DESC
    </select>

</mapper>