<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.MdmSaleCategoryDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.MdmSaleCategoryDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="category_no" property="categoryNo"/>
        <result column="category_name" property="categoryName"/>
        <result column="full_id_path" property="fullIdPath"/>
        <result column="full_name_path" property="fullNamePath"/>
        <result column="category_level" property="categoryLevel"/>
        <result column="parent_no" property="parentNo"/>
        <result column="first_category_no" property="firstCategoryNo"/>
        <result column="second_category_no" property="secondCategoryNo"/>
        <result column="third_category_no" property="thirdCategoryNo"/>
        <result column="picture_url" property="pictureUrl"/>
        <result column="sort_value" property="sortValue"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="mdm_disable_flag" property="mdmDisableFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        category_no,
        category_name,
        full_id_path,
        full_name_path,
        category_level,
        parent_no,
        first_category_no,
        second_category_no,
        third_category_no,
        picture_url,
        sort_value,
        disable_flag,
        mdm_disable_flag
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.MdmSaleCategoryDomain">
        select
        <include refid="Base_Column_List"/>
        from
        mdm_sale_category
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="categoryNo != null and categoryNo != ''">
                    and category_no = #{categoryNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        order by
        sort_value
    </select>

    <update id="updateByCategoryNo" parameterType="cn.htdt.goodsprocess.domain.SaleCategoryDomain">
        update
        mdm_sale_category
        <set>
            <if test="categoryName != null and categoryName != ''">
                category_name = #{categoryName,jdbcType=VARCHAR},
            </if>
            <if test="fullIdPath != null and fullIdPath != ''">
                full_id_path = #{fullIdPath,jdbcType=VARCHAR},
            </if>
            <if test="fullNamePath != null and fullNamePath != ''">
                full_name_path = #{fullNamePath,jdbcType=VARCHAR},
            </if>
            <if test="categoryLevel != null and categoryLevel != ''">
                category_level = #{categoryLevel,jdbcType=VARCHAR},
            </if>
            <if test="parentNo != null and parentNo != ''">
                parent_no = #{parentNo,jdbcType=VARCHAR},
            </if>
            <if test="firstCategoryNo != null and firstCategoryNo != ''">
                first_category_no = #{firstCategoryNo,jdbcType=VARCHAR},
            </if>
            <if test="secondCategoryNo != null and secondCategoryNo != ''">
                second_category_no = #{secondCategoryNo,jdbcType=VARCHAR},
            </if>
            <if test="thirdCategoryNo != null and thirdCategoryNo != ''">
                third_category_no = #{thirdCategoryNo,jdbcType=VARCHAR},
            </if>
            <if test="pictureUrl != null and pictureUrl != ''">
                picture_url = #{pictureUrl,jdbcType=VARCHAR},
            </if>
            <if test="sortValue != null and sortValue != ''">
                sort_value = #{sortValue,jdbcType=INTEGER},
            </if>
            <if test="disableFlag != null and disableFlag != ''">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="mdmDisableFlag != null and mdmDisableFlag != ''">
                mdm_disable_flag = #{mdmDisableFlag,jdbcType=TINYINT},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="categoryNo != null and categoryNo != ''">
                    and category_no = #{categoryNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

</mapper>
