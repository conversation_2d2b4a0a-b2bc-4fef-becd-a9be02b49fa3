<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.DifferenceOrderDao">
    <resultMap id="ResultMap" type="cn.htdt.goodsprocess.domain.DifferenceOrderDomain">
        <result column="id" property="id"/>
        <result column="difference_no" property="differenceNo"/>
        <result column="requisition_no" property="requisitionNo"/>
        <result column="difference_count" property="differenceCount"/>
        <result column="difference_amount" property="differenceAmount"/>
        <result column="status" property="status"/>
        <result column="in_storage_user" property="inStorageUser"/>
        <result column="in_storage_time" property="inStorageTime"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
        <result column="platform_type" property="platformType"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="create_no" property="createNo"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,difference_no,requisition_no,difference_count,difference_amount,status,in_storage_user, in_storage_time,company_no,company_name,branch_no,branch_name,platform_type,merchant_no,merchant_name,store_no,store_name,create_no,create_name,create_time,modify_no,modify_name,modify_time,delete_flag
    </sql>

    <sql id="Set_Column">
        <set>
            <if test="differenceNo != null">difference_no=#{differenceNo},</if>
            <if test="requisitionNo != null">requisition_no=#{requisitionNo},</if>
            <if test="differenceCount != null">difference_count=#{differenceCount},</if>
            <if test="differenceAmount != null">difference_amount=#{differenceAmount},</if>
            <if test="status != null">status=#{status},</if>
            <if test="companyNo != null">company_no=#{companyNo},</if>
            <if test="companyName != null">company_name=#{companyName},</if>
            <if test="branchNo != null">branch_no=#{branchNo},</if>
            <if test="branchName != null">branch_name=#{branchName},</if>
            <if test="platformType != null">platform_type=#{platformType},</if>
            <if test="merchantNo != null">merchant_no=#{merchantNo},</if>
            <if test="merchantName != null">merchant_name=#{merchantName},</if>
            <if test="storeNo != null">store_no=#{storeNo},</if>
            <if test="storeName != null">store_name=#{storeName},</if>
            <if test="createNo != null">create_no=#{createNo},</if>
            <if test="createName != null">create_name=#{createName},</if>
            <if test="createTime != null">create_time=#{createTime},</if>
            <if test="modifyNo != null">modify_no=#{modifyNo},</if>
            <if test="modifyName != null">modify_name=#{modifyName},</if>
            <if test="modifyTime != null">modify_time=#{modifyTime},</if>
            <if test="deleteFlag != null">delete_flag=#{deleteFlag},</if>
        </set>
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="getDifferenceOrderList" parameterType="cn.htdt.goodsprocess.vo.AtomDifferenceOrderVo" resultMap="ResultMap">
        select
        <include refid="Base_Column_List" />
        from difference_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="differenceNo != null and differenceNo != ''">
                    and difference_no = #{differenceNo}
                </if>
                <if test="differenceNos != null and differenceNos.size() > 0">
                    and difference_no in
                    <foreach collection="differenceNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="requisitionNo != null and requisitionNo != ''">
                    and requisition_no = #{requisitionNo}
                </if>
                <if test="requisitionNos != null and requisitionNos.size() > 0">
                    and requisition_no in
                    <foreach collection="requisitionNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="status != null and status != ''">
                    and status = #{status}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>

                <if test="storeNoList != null and storeNoList.size() > 0">
                    and store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <if test="startCreateTime != null">
                    <![CDATA[ and date_format(create_time, '%Y-%m-%d') >= #{startCreateTime} ]]>
                </if>
                <if test="endCreateTime != null">
                    <![CDATA[ and date_format(create_time, '%Y-%m-%d') <= #{endCreateTime} ]]>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
            order by create_time desc
        </where>
    </select>

    <select id="getDifferenceOrderDetail" parameterType="cn.htdt.goodsprocess.vo.AtomDifferenceOrderVo" resultMap="ResultMap">
        select
        <include refid="Base_Column_List" />
        from difference_order
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="differenceNo != null and differenceNo != ''">
                    and difference_no = #{differenceNo}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="requisitionNo != null and requisitionNo != ''">
                    and requisition_no = #{requisitionNo}
                </if>
                <if test="status != null and status != ''">
                    and status = #{status}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateDifferenceOrder" parameterType="cn.htdt.goodsprocess.domain.DifferenceOrderDomain">
        update
        difference_order
        <set>
            <if test="differenceCount != null">
                difference_count = #{differenceCount},
            </if>
            <if test="differenceAmount != null">
                difference_amount = #{differenceAmount},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="merchantNo != null and merchantNo != ''">
                merchant_no = #{merchantNo},
            </if>
            <if test="storeNo != null and storeNo != ''">
                store_no = #{storeNo},
            </if>
            <if test="companyNo != null and companyNo != ''">
                company_no = #{companyNo},
            </if>
            <if test="companyName != null and companyName != ''">
                company_name = #{companyName},
            </if>
            <if test="branchNo != null and branchNo != ''">
                branch_no = #{branchNo},
            </if>
            <if test="platformType != null and platformType != ''">
                platform_type = #{platformType},
            </if>
            <if test="branchName != null and branchName != ''">
                branch_name = #{branchName}
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName},
            </if>
            <if test="inStorageUser != null and inStorageUser != ''">
                in_storage_user = #{inStorageUser},
            </if>
            <if test="inStorageTime != null ">
                in_storage_time = #{inStorageTime},
            </if>
            <choose>
                <when test="inStorageTime != null">
                    modify_time =  #{inStorageTime}
                </when>
                <otherwise>
                    modify_time = NOW()
                </otherwise>
            </choose>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="differenceNo != null and differenceNo != ''">
                    and difference_no = #{differenceNo}
                </if>
                <if test="requisitionNo != null and requisitionNo != ''">
                    and requisition_no = #{requisitionNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>


</mapper>