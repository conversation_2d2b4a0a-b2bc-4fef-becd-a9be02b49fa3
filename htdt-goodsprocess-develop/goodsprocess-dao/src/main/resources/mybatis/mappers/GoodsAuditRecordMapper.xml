<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsAuditRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="GoodsAuditRecordInfo" type="cn.htdt.goodsprocess.vo.AtomResGoodsAuditRecordVo">
        <result column="audit_record_no" property="auditRecordNo"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_message" property="auditMessage"/>
        <result column="audit_type" property="auditType"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_form" property="goodsForm"/>
        <result column="goods_type" property="goodsType"/>
        <result column="goods_name" property="goodsName"/>
        <result column="category_no" property="categoryNo"/>
        <result column="category_name" property="categoryName"/>
        <result column="brand_no" property="brandNo"/>
        <result column="brand_name" property="brandName"/>
        <result column="calculation_unit_no" property="calculationUnitNo"/>
        <result column="calculation_unit_name" property="calculationUnitName"/>
        <result column="retail_price" property="retailPrice"/>
        <result column="purchase_price" property="purchasePrice"/>
        <result column="create_time" property="createTime"/>
        <result column="audit_user_no" property="auditUserNo"/>
        <result column="audit_user_name" property="auditUserName"/>
        <result column="audit_time" property="auditTime"/>
        <result column="goods_source_type" property="goodsSourceType"/>
    </resultMap>

    <select id="getGoodsAuditPageList" resultMap="GoodsAuditRecordInfo"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        g.audit_record_no,
        g.audit_status,
        gar.audit_message,
        g.audit_type,
        g.merchant_no,
        g.merchant_name,
        g.store_no,
        g.store_name,
        g.goods_no,
        g.goods_form,
        g.goods_type,
        g.goods_name,
        g.category_no,
        sc.full_name_path as category_name,
        g.brand_no,
        b.brand_name,
        g.calculation_unit_no,
        g.retail_price,
        g.purchase_price,
        gar.create_time,
        gar.audit_time,
        gar.audit_user_no,
        gar.audit_user_name,
        g.goods_source_type
        from goods g
        INNER JOIN goods_audit_record gar
        ON gar.audit_record_no = g.audit_record_no
        LEFT JOIN sale_category sc
        ON g.category_no = sc.category_no
        LEFT JOIN brand b
        ON g.brand_no = b.brand_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsName != null and goodsName != ''">
                    and g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                </if>
                <if test="categoryNo != null and categoryNo != ''">
                    and g.category_no = #{categoryNo}
                </if>
                <if test="brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo}
                </if>
                <if test="goodsForm != null and goodsForm != ''">
                    and g.goods_form = #{goodsForm}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and g.goods_no = #{goodsNo}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and g.merchant_no = #{merchantNo}
                    and g.store_no = ""
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and g.store_no = #{storeNo}
                </if>
                <if test="storeName != null and storeName != ''">
                    and g.store_name  like CONCAT(CONCAT('%', #{storeName}),'%')
                </if>
                <choose>
                    <when test="goodsType != null and goodsType != ''">
                        and g.goods_type = #{goodsType}
                    </when>
                    <otherwise>
                        and g.goods_type != '1002'
                    </otherwise>
                </choose>
                <if test="auditType != null and auditType != ''">
                    and g.audit_type = #{auditType}
                </if>
                <if test="auditStatus != null and auditStatus != ''">
                    and g.audit_status = #{auditStatus}
                </if>
                <if test="auditStatus == null or auditStatus == ''">
                    and g.audit_status > '2002'
                </if>
                and g.goods_source_type in ('1001','1002','1003')
                and g.series_type !='1002'
                and g.delete_flag = 1
                and gar.disable_flag = 2
            </trim>
        </where>
        ORDER BY
        gar.create_time DESC
    </select>

    <select id="getGoodsAuditInfoList" resultMap="GoodsAuditRecordInfo"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsVo">
        select
        g.audit_record_no,
        g.audit_status,
        gar.audit_message,
        g.audit_type,
        g.merchant_no,
        g.merchant_name,
        g.store_no,
        g.store_name,
        g.goods_no,
        g.goods_form,
        g.goods_type,
        g.goods_name,
        g.category_no,
        g.brand_no,
        g.calculation_unit_no,
        g.retail_price,
        g.purchase_price,
        gar.create_time,
        gar.audit_time,
        gar.audit_user_no,
        gar.audit_user_name
        from goods g
        INNER JOIN goods_audit_record gar
        ON gar.audit_record_no = g.audit_record_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and g.goods_no = #{goodsNo}
                </if>
                <if test="goodsNos != null">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="goodsNo" open="(" close=")" separator=",">
                        #{goodsNo}
                    </foreach>
                </if>
                and g.goods_source_type in ('1001','1002','1003')
                and g.series_type !='1002'
                and g.delete_flag = 1
                and gar.disable_flag = 2
            </trim>
        </where>
    </select>

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsAuditRecordDomain">
        <result column="create_time" property="createTime"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="audit_record_no" property="auditRecordNo"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="audit_type" property="auditType"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_message" property="auditMessage"/>
        <result column="audit_user_no" property="auditUserNo"/>
        <result column="audit_user_name" property="auditUserName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        create_time,
        modify_name,
        modify_time,
        modify_no,
        audit_record_no, goods_no, audit_type, audit_status, audit_message, audit_user_no, audit_user_name, create_no
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.GoodsAuditRecordDomain">
        select
        <include refid="Base_Column_List"/>
        from
        goods_audit_record
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateByParam" parameterType="cn.htdt.goodsprocess.vo.AtomReqGoodsAuditRecordVo">
        update
        goods_audit_record
        <set>
            <if test="auditStatus != null and auditStatus != ''">
                audit_status = #{auditStatus,jdbcType=VARCHAR},
            </if>
            <if test="auditMessage != null and auditMessage != ''">
                audit_message = #{auditMessage,jdbcType=VARCHAR},
            </if>
            <if test="auditUserNo != null and auditUserNo != ''">
                audit_user_no = #{auditUserNo,jdbcType=VARCHAR},
            </if>
            <if test="auditUserName != null and auditUserName != ''">
                audit_user_name = #{auditUserName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null and disableFlag != ''">
                disable_flag = #{disableFlag},
            </if>
            <if test="auditTime != null">
                audit_time = #{auditTime},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="auditRecordNo != null and auditRecordNo != ''">
                    and audit_record_no = #{auditRecordNo}
                </if>
                <if test="auditRecordNoList != null">
                    and audit_record_no IN
                    <foreach collection="auditRecordNoList" item="auditRecordNo" open="(" close=")" separator=",">
                        #{auditRecordNo}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>
</mapper>
