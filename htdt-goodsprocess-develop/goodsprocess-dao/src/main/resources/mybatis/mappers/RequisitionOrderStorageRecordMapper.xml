<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.RequisitionOrderStorageRecordDao">
    <resultMap id="ResultMap" type="cn.htdt.goodsprocess.domain.RequisitionOrderStorageRecordDomain">
        <result column="id" property="id"/>
        <result column="record_no" property="recordNo"/>
        <result column="association_type" property="associationType"/>
        <result column="association_no" property="associationNo"/>
        <result column="storage_type" property="storageType"/>
        <result column="merchant_goods_no" property="merchantGoodsNo"/>
        <result column="merchant_goods_name" property="merchantGoodsName"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="barcode" property="barcode"/>
        <result column="item_price" property="itemPrice"/>
        <result column="requisition_num" property="requisitionNum"/>
        <result column="batch_no" property="batchNo"/>
        <result column="out_storage_count" property="outStorageCount"/>
        <result column="out_storage_amount" property="outStorageAmount"/>
        <result column="in_storage_count" property="inStorageCount"/>
        <result column="in_storage_amount" property="inStorageAmount"/>
        <result column="warehouse_flag" property="warehouseFlag"/>
        <result column="warehouse_no" property="warehouseNo"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="create_no" property="createNo"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,record_no,association_type,association_no,storage_type,merchant_goods_no,merchant_goods_name,goods_no,goods_name,barcode,item_price,requisition_num,batch_no,out_storage_count,out_storage_amount,in_storage_count,in_storage_amount,warehouse_flag,warehouse_no,warehouse_name,create_no,create_name,create_time,modify_no,modify_name,modify_time,delete_flag
    </sql>

    <sql id="Set_Column">
        <set>

            <if test="recordNo != null">record_no=#{recordNo},</if>
            <if test="associationType != null">association_type=#{associationType},</if>
            <if test="associationNo != null">association_no=#{associationNo},</if>
            <if test="storageType != null">storage_type=#{storageType},</if>
            <if test="merchantGoodsNo != null">merchant_goods_no=#{merchantGoodsNo},</if>
            <if test="goodsNo != null">goods_no=#{goodsNo},</if>
            <if test="goodsName != null">goods_name=#{goodsName},</if>
            <if test="barcode != null">barcode=#{barcode},</if>
            <if test="itemPrice != null">item_price=#{itemPrice},</if>
            <if test="requisitionNum != null">requisition_num=#{requisitionNum},</if>
            <if test="batchNo != null">batch_no=#{batchNo},</if>
            <if test="outStorageCount != null">out_storage_count=#{outStorageCount},</if>
            <if test="outStorageAmount != null">out_storage_amount=#{outStorageAmount},</if>
            <if test="inStorageCount != null">in_storage_count=#{inStorageCount},</if>
            <if test="inStorageAmount != null">in_storage_amount=#{inStorageAmount},</if>
            <if test="warehouseFlag != null">warehouse_flag=#{warehouseFlag},</if>
            <if test="warehouseNo != null">warehouse_no=#{warehouseNo},</if>
            <if test="warehouseName != null">warehouse_name=#{warehouseName},</if>
            <if test="createNo != null">create_no=#{createNo},</if>
            <if test="createName != null">create_name=#{createName},</if>
            <if test="createTime != null">create_time=#{createTime},</if>
            <if test="modifyNo != null">modify_no=#{modifyNo},</if>
            <if test="modifyName != null">modify_name=#{modifyName},</if>
            <if test="modifyTime != null">modify_time=#{modifyTime},</if>
            <if test="deleteFlag != null">delete_flag=#{deleteFlag},</if>
        </set>
    </sql>


    <select id="getMaxBatchNo" resultType="int">
        select IFNULL(max(batch_no), 0) + 1 batchNo
        from requisition_order_storage_record
        where association_type = #{associationType}
          and association_no = #{associationNo}
    </select>


</mapper>