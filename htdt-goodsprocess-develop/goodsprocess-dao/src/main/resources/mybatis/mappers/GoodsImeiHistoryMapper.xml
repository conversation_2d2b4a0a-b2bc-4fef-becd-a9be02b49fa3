<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsImeiHistoryDao">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.vo.GoodsImeiHistoryVo">
        <result column="id" property="id"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="imei" property="imei"/>
        <result column="create_no" property="createNo"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="custom_goods_no" property="customGoodsNo"/>
        <result column="custom_store_no" property="customStoreNo"/>
        <result column="transport_flag" property="transportFlag"/>
        <result column="sale_flag" property="saleFlag"/>
        <result column="inventory_state" property="inventoryState"/>
        <result column="inventory_type" property="inventoryType"/>
        <result column="supplier_code" property="supplierCode"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="inventory_date" property="inventoryDate"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, goods_no,goods_name, imei, create_no, create_name, create_time, modify_no, modify_name, modify_time, merchant_no, merchant_name, store_no, store_name, custom_goods_no, custom_store_no, transport_flag,sale_flag, inventory_state, inventory_type, supplier_code, delete_flag, inventory_date
    </sql>
    <!-- 组合查询结果列 -->
    <sql id="Base_Column_List_Compose">
        ih.id, ih.goods_no, ih.imei, ih.create_no, ih.create_name, ih.create_time, ih.modify_no, ih.modify_name, ih.modify_time, ih.merchant_no, ih.merchant_name, ih.store_no, ih.store_name, ih.custom_goods_no, ih.custom_store_no, ih.transport_flag, ih.sale_flag, ih.inventory_state, ih.inventory_type, ih.supplier_code, ih.delete_flag, ih.inventory_date,
        g.goods_name
    </sql>


    <insert id="insertGoodsImeiHistoryByYesterday">
        INSERT INTO goods_imei_history(goods_no,goods_name, imei, create_no, create_name, create_time, modify_no,
        modify_name,
        modify_time, merchant_no, merchant_name, store_no, store_name,
        custom_goods_no, custom_store_no, transport_flag, sale_flag, inventory_state,
        inventory_type, supplier_code, delete_flag, inventory_date)

        SELECT gi.goods_no,

        CONCAT_WS('-',
        g.goods_name,
        IF
        ( g.first_attribute_value_name != '', g.first_attribute_value_name , null ),
        IF
        ( g.second_attribute_value_name != '', g.second_attribute_value_name , null ),
        IF
        ( g.third_attribute_value_name != '', g.third_attribute_value_name , null )) AS goods_name,
        gi.imei,
        gi.create_no,
        gi.create_name,
        gi.create_time,
        gi.modify_no,
        gi.modify_name,
        gi.modify_time,
        gi.merchant_no,
        gi.merchant_name,
        gi.store_no,
        gi.store_name,
        gi.custom_goods_no,
        gi.custom_store_no,
        gi.transport_flag,
        gi.sale_flag,
        gi.inventory_state,
        gi.inventory_type,
        gi.supplier_code,
        gi.delete_flag,
        DATE_SUB(CURDATE(), INTERVAL 1 DAY) as inventory_date
        FROM goods_imei gi left join goods g on gi.goods_no = g.goods_no
        where gi.delete_flag = 1
            <if test="merchantNo != null and merchantNo !=''">
                AND gi.merchant_no = #{merchantNo}
            </if>

    </insert>

    <delete id="deleteGoodsImeiHistoryBefore31Days">
        delete from goods_imei_history
        where
        inventory_date &lt; (DATE_SUB(CURDATE(), INTERVAL 31 DAY ))
        <if test="merchantNo != null and merchantNo !=''">
            and merchant_no = #{merchantNo}
        </if>
    </delete>


    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.GoodsImeiHistoryVo">
        select
        <include refid="Base_Column_List_Compose"/>
        from
        goods_imei_history ih,goods g
        <where>
            ih.goods_no = g.goods_no
            <if test="goodNoOrName != null and goodNoOrName != ''">
                and (ih.goods_no = #{goodNoOrName,jdbcType=VARCHAR}
                OR
                ih.goods_name like concat('%',concat(#{goodNoOrName},'%'))
                )
            </if>
            <if test="imei != null and imei != ''">
                and ih.imei = #{imei}
            </if>
            <if test="storeName != null and storeName != ''">
                and ih.store_name LIKE CONCAT (CONCAT('%',#{storeName}),'%')
            </if>
            <if test="storeNo != null">
                and ih.store_no = #{storeNo}
            </if>
            <if test="merchantNo != null and merchantNo != ''">
                and ih.merchant_no = #{merchantNo}
            </if>
            <if test="customGoodsNo != null and customGoodsNo != ''">
                and ih.custom_goods_no = #{customGoodsNo}
            </if>
            <if test="inventoryDateStart != null and inventoryDateEnd != null and inventoryDateStart &lt;= inventoryDateEnd">
                and ih.inventory_date between #{inventoryDateStart} and #{inventoryDateEnd}
            </if>
        </where>
        order by ih.id desc
    </select>
</mapper>
