<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.MiddlegroundGoodsSyncRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.MiddlegroundGoodsSyncRecordDomain">
        <result column="id" property="id"/>
        <result column="sync_status" property="syncStatus"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_info_json" property="goodsInfoJson"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        sync_status,
        goods_no,
        goods_name,
        goods_info_json
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.MiddlegroundGoodsSyncRecordDomain">
        select
        <include refid="Base_Column_List"/>
        from
        middleground_goods_sync_record
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="syncStatus != null and syncStatus != ''">
                    and sync_status = #{syncStatus}
                </if>
            </trim>
        </where>
        limit 50
    </select>

    <update id="updateByParams" parameterType="cn.htdt.goodsprocess.domain.MiddlegroundGoodsSyncRecordDomain">
        update
        middleground_goods_sync_record
        <set>
            <if test="syncStatus != null and syncStatus != ''">
                sync_status = #{syncStatus},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="id != null">
                    and id = #{id}
                </if>
            </trim>
        </where>
    </update>
</mapper>
