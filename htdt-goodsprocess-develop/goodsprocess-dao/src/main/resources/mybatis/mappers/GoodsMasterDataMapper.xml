<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsMasterDataDao">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.vo.AtomGoodsMasterDataVo">
		<result column="id" property="id" />
		<result column="create_name" property="createName" />
		<result column="create_time" property="createTime" />
		<result column="modify_name" property="modifyName" />
		<result column="modify_time" property="modifyTime" />
		<result column="delete_flag" property="deleteFlag" />
		<result column="goods_no" property="goodsNo" />
		<result column="parent_goods_no" property="parentGoodsNo" />
		<result column="goods_form" property="goodsForm" />
		<result column="goods_type" property="goodsType" />
		<result column="category_no" property="categoryNo" />
		<result column="category_name" property="categoryName" />
		<result column="brand_no" property="brandNo" />
		<result column="brand_name" property="brandName" />
		<result column="goods_name" property="goodsName" />
		<result column="goods_name_full_pinyin" property="goodsNameFullPinyin"/>
		<result column="goods_name_full_initials" property="goodsNameFullInitials"/>
		<result column="goods_source_type" property="goodsSourceType" />
		<result column="data_source_goods_no" property="dataSourceGoodsNo" />
		<result column="goods_sale_description" property="goodsSaleDescription" />
		<result column="calculation_unit_no" property="calculationUnitNo" />
		<result column="barcode" property="barcode" />
		<result column="disable_flag" property="disableFlag" />
		<result column="create_no" property="createNo" />
		<result column="modify_no" property="modifyNo" />
		<result column="first_attribute_name" property="firstAttributeName" />
		<result column="first_attribute_value_name" property="firstAttributeValueName" />
		<result column="second_attribute_name" property="secondAttributeName" />
		<result column="second_attribute_value_name" property="secondAttributeValueName" />
		<result column="third_attribute_name" property="thirdAttributeName" />
		<result column="third_attribute_value_name" property="thirdAttributeValueName" />
		<result column="series_type" property="seriesType" />
		<result column="goods_video" property="goodsVideo" />
		<result column="first_category_no" property="firstCategoryNo" />
		<result column="second_category_no" property="secondCategoryNo" />
		<result column="third_category_no" property="thirdCategoryNo" />
		<result column="full_name_path" property="fullNamePath" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		g.id,
		g.goods_no,
		g.parent_goods_no,
		g.goods_form,
		g.goods_type,
		g.category_no,
		g.brand_no,
		g.goods_name,
		g.goods_name_full_pinyin,
		g.goods_name_full_initials,
		g.goods_source_type,
		g.data_source_goods_no,
		g.goods_sale_description,
		g.calculation_unit_no,
		g.barcode,
		g.disable_flag,
		g.create_no,
		g.create_name,
		g.create_time,
		g.modify_no,
		g.modify_name,
		g.modify_time,
		g.delete_flag,
		g.first_attribute_name,
		g.first_attribute_value_name,
		g.second_attribute_name,
		g.second_attribute_value_name,
		g.third_attribute_name,
		g.third_attribute_value_name,
		g.series_type,
		g.goods_video
	</sql>

	<sql id="Select_Base_Column_Category_Brand">
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no,
        (case when sc.category_no = '10001' then '' else sc.full_name_path end) category_name,
		b.brand_name,
		b.data_source_type brandDataSourceType
	</sql>

	<sql id="Base_Column_Where">
		<if test="goodsNo != null and goodsNo != ''">
			and g.goods_no = #{goodsNo}
		</if>
		<choose>
			<when test="categoryNo != null and categoryNo != ''">
				and g.category_no = #{categoryNo}
			</when>
			<otherwise>
				<if test="fullIdPaths != null and fullIdPaths.size() > 0">
					and
					<foreach collection="fullIdPaths" item="fullId" index="index" open="(" close=")" separator="or">
						sc.full_id_path like CONCAT(#{fullId},'%')
					</foreach>
				</if>
			</otherwise>
		</choose>
		<if test="brandNo != null and brandNo != ''">
			and g.brand_no = #{brandNo}
		</if>
		<if test="goodsName != null and goodsName != ''">
			and g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
		</if>
		<if test="goodsForm != null and goodsForm != ''">
			and g.goods_form = #{goodsForm}
		</if>
		<if test="barcode != null and barcode != ''">
			and g.barcode = #{barcode}
		</if>
		<if test="goodsType != null and goodsType != ''">
			and g.goods_type = #{goodsType}
		</if>
		<if test="parentGoodsNo != null and parentGoodsNo !=''">
			and g.parent_goods_no = #{parentGoodsNo}
		</if>
		<if test="goodsSourceType != null and goodsSourceType != ''">
			and g.goods_source_type = #{goodsSourceType}
		</if>
		<if test="dataSourceGoodsNo != null and dataSourceGoodsNo != ''">
			and g.data_source_goods_no = #{dataSourceGoodsNo}
		</if>
		<if test="seriesType != null and seriesType != ''">
			and g.series_type = #{seriesType}
		</if>
		<!-- 列表查询排除子品-->
		<if test="listFlag== 2">
			and g.series_type !='1002'
		</if>
		and g.delete_flag = 1
	</sql>

	<select id="selectByParams" resultMap="BaseResultMap" >
		select
		<include refid="Base_Column_List" />,<include refid="Select_Base_Column_Category_Brand"/>
		from
		goods_master_data g
		INNER JOIN sale_category sc
		ON g.category_no = sc.category_no
		<if test="disableFlag!=null">
			AND sc.disable_flag = #{disableFlag}
		</if>
		left JOIN brand b
		ON g.brand_no = b.brand_no
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="goodsNameKeyWord != null and goodsNameKeyWord != ''">
					and (
					g.goods_name like CONCAT(CONCAT('%', #{goodsNameKeyWord}),'%')
					or g.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsNameKeyWord}),'%')
					or g.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsNameKeyWord}),'%')
					)
				</if>
				<include refid="Base_Column_Where"/>
			</trim>
		</where>
		order by g.modify_time desc
	</select>

	<select id="selectCount" resultType="Integer">
		select
		count(1)
		from
		goods_master_data g
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</select>

	<update id="batchUpdate">
		<foreach collection="goodsList" item="item" index="index" open=""
				 close="" separator=";">
			update
			goods_master_data
			<set>
				<if test="item.barcode != null">
					barcode = #{item.barcode,jdbcType=VARCHAR},
				</if>
			</set>
			<where>
				<trim suffixOverrides="AND | OR" prefix="1=1">
					<if test="item.goodsNo != null and item.goodsNo != ''">
						and goods_no = #{item.goodsNo,jdbcType=VARCHAR}
					</if>
				</trim>
			</where>
		</foreach>
	</update>

	<update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsDomain">
		update
		goods_master_data
		<set>
			<if test="goodsForm != null">
				goods_form = #{goodsForm,jdbcType=VARCHAR},
			</if>
			<if test="goodsType != null">
				goods_type = #{goodsType,jdbcType=VARCHAR},
			</if>
			<if test="categoryNo != null">
				category_no = #{categoryNo,jdbcType=VARCHAR},
			</if>
			<if test="brandNo != null">
				brand_no = #{brandNo,jdbcType=VARCHAR},
			</if>
			<if test="goodsName != null">
				goods_name = #{goodsName,jdbcType=VARCHAR},
			</if>
			<if test="goodsNameFullPinyin != null and goodsNameFullPinyin != ''">
				goods_name_full_pinyin = #{goodsNameFullPinyin,jdbcType=VARCHAR},
			</if>
			<if test="goodsNameFullInitials != null and goodsNameFullInitials != ''">
				goods_name_full_initials = #{goodsNameFullInitials,jdbcType=VARCHAR},
			</if>
			<if test="goodsSaleDescription != null">
				goods_sale_description = #{goodsSaleDescription,jdbcType=VARCHAR},
			</if>
			<if test="calculationUnitNo != null">
				calculation_unit_no = #{calculationUnitNo,jdbcType=VARCHAR},
			</if>
			<if test="barcode != null">
				barcode = #{barcode,jdbcType=VARCHAR},
			</if>
			<if test="disableFlag != null">
				disable_flag = #{disableFlag,jdbcType=TINYINT},
			</if>
			<if test="modifyNo != null">
				modify_no = #{modifyNo,jdbcType=VARCHAR},
			</if>
			<if test="modifyName != null">
				modify_name = #{modifyName,jdbcType=VARCHAR},
			</if>
			<if test="modifyTime != null">
				modify_time = #{modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null">
				delete_flag = #{deleteFlag,jdbcType=TINYINT},
			</if>
		</set>
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				and delete_flag = 1
				<if test="goodsNo != null and goodsNo != ''">
					and goods_no = #{goodsNo}
				</if>
			</trim>
		</where>
	</update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsMasterDataVo">
		update
		goods_master_data
		set
		<if test="modifyNo != null">
			modify_no = #{modifyNo,jdbcType=VARCHAR},
		</if>
		<if test="modifyName != null">
			modify_name = #{modifyName,jdbcType=VARCHAR},
		</if>
		<if test="modifyTime != null">
			modify_time = #{modifyTime,jdbcType=TIMESTAMP},
		</if>
		delete_flag = 2
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="goodsNo != null and goodsNo != ''">
					and goods_no = #{goodsNo}
				</if>
				<if test="goodsNoList != null and goodsNoList.size() > 0">
					and goods_no IN
					<foreach collection="goodsNoList" item="item" index="index"
							 open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
			</trim>
		</where>
	</update>

</mapper>
