<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.BarCodeScaleFlagDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.BarCodeScaleFlagDomain">
        <result column="id" property="id"/>
        <result column="bar_code_flag" property="barCodeFlag"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        bar_code_flag,
        store_no,
        store_name,
        merchant_no,
        merchant_name
    </sql>

    <select id="selectByParam" parameterType="cn.htdt.goodsprocess.domain.BarCodeScaleFlagDomain" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM bar_code_scale_flag t
        <where>
            <trim prefix="1=1 " suffixOverrides="AND | OR">
                <if test="barCodeFlag != null and barCodeFlag != ''">
                    and t.bar_code_flag = #{barCodeFlag}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and t.store_no = #{storeNo}
                </if>
                and t.delete_flag = 1
            </trim>
        </where>
        order by t.bar_code_flag
    </select>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.BarCodeScaleFlagDomain">
        update
        bar_code_scale_flag
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            store_no = #{storeNo}
            and bar_code_flag = #{barCodeFlag}
            and delete_flag = 1
        </where>
    </update>
</mapper>
