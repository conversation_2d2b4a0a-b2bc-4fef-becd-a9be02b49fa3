<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsAttributeModelDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsAttributeModelDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="attribute_model_json" property="attributeModelJson"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        goods_no, attribute_model_json
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsAttributeModelVo"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        goods_attribute_model
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo !=''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="goodsNoList != null and goodsNoList.size() > 0">
                    and goods_no in
                    <foreach collection="goodsNoList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateGoodsAttributeModel" parameterType="cn.htdt.goodsprocess.domain.GoodsAttributeModelDomain">
        update
        goods_attribute_model
        <set>
            <if test="attributeModelJson != null and attributeModelJson != ''">
                attribute_model_json = #{attributeModelJson,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            (
            goods_no = #{goodsNo}
            or goods_no IN ( select g.goods_no from goods g where g.merchant_goods_no = #{goodsNo} and g.delete_flag = 1 )
            )
            <include refid="Base_Column_Where"/>
        </where>
    </update>
</mapper>
