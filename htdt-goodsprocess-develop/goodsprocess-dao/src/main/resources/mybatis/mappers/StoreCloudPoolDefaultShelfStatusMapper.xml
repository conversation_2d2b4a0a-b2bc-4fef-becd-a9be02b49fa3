<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.StoreCloudPoolDefaultShelfStatusDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.StoreCloudPoolDefaultShelfStatusDomain">
        <result column="store_no" property="storeNo"/>
        <result column="default_on_shelves" property="defaultOnShelves"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        store_no,
        default_on_shelves
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomStoreCloudPoolDefaultShelfStatusVo">
        select
        <include refid="Base_Column_List"/>
        from
        store_cloud_pool_default_shelf_status
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="storeNos != null and storeNos.size() > 0">
                    and store_no IN
                    <foreach collection="storeNos" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateByParam" parameterType="cn.htdt.goodsprocess.domain.StoreCloudPoolDefaultShelfStatusDomain">
        update
        store_cloud_pool_default_shelf_status
        <set>
            <if test="defaultOnShelves != null and defaultOnShelves !=''">
                default_on_shelves = #{defaultOnShelves,jdbcType=INTEGER},
            </if>
            <if test="modifyNo != null and modifyNo !=''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName !=''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            store_no = #{storeNo}
            <include refid="Base_Column_Where"/>
        </where>
    </update>
</mapper>
