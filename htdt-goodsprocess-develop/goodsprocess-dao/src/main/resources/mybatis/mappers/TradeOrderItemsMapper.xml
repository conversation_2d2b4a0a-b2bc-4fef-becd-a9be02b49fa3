<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.TradeOrderItemsDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.TradeOrderItemsDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="order_no" property="orderNo"/>
        <result column="order_item_no" property="orderItemNo"/>
        <result column="channel_code" property="channelCode"/>
        <result column="goods_name" property="goodsName"/>
        <result column="sku_code" property="skuCode"/>
        <result column="sku_picture_url" property="skuPictureUrl"/>
        <result column="first_category_id" property="firstCategoryId"/>
        <result column="first_category_name" property="firstCategoryName"/>
        <result column="second_category_id" property="secondCategoryId"/>
        <result column="second_category_name" property="secondCategoryName"/>
        <result column="third_category_id" property="thirdCategoryId"/>
        <result column="third_category_name" property="thirdCategoryName"/>
        <result column="last_category_id" property="lastCategoryId"/>
        <result column="last_category_name" property="lastCategoryName"/>
        <result column="brand_id" property="brandId"/>
        <result column="brand_name" property="brandName"/>
        <result column="goods_count" property="goodsCount"/>
        <result column="sale_price" property="salePrice"/>
        <result column="goods_price" property="goodsPrice"/>
        <result column="trade_price" property="tradePrice"/>
        <result column="goods_amount" property="goodsAmount"/>
        <result column="goods_freight" property="goodsFreight"/>
        <result column="total_discount_amount" property="totalDiscountAmount"/>
        <result column="shop_discount_amount" property="shopDiscountAmount"/>
        <result column="platform_discount_amount" property="platformDiscountAmount"/>
        <result column="used_rebate_amount" property="usedRebateAmount"/>
        <result column="bargaining_goods_amount" property="bargainingGoodsAmount"/>
        <result column="bargaining_goods_freight" property="bargainingGoodsFreight"/>
        <result column="order_item_total_amount" property="orderItemTotalAmount"/>
        <result column="order_item_pay_amount" property="orderItemPayAmount"/>
        <result column="goods_real_price" property="goodsRealPrice"/>
        <result column="refund_status" property="refundStatus"/>
    </resultMap>

    <resultMap id="ExtendResultMap" type="cn.htdt.goodsprocess.dto.response.tradeorder.ResPurchaseTradeOrderDTO">
        <result column="order_no" property="orderNo"/>
        <result column="order_item_no" property="orderItemNo"/>
        <result column="channel_code" property="channelCode"/>
        <result column="goods_name" property="goodsName"/>
        <result column="sku_code" property="skuCode"/>
        <result column="sku_picture_url" property="skuPictureUrl"/>
        <result column="goods_count" property="goodsCount"/>
        <result column="order_status" property="orderStatus"/>
        <result column="data_source" property="dataSource" />
        <result column="goods_no" property="goodsNo"/>
        <result column="stock_flag" property="stockFlag"/>
        <result column="seller_type" property="sellerType" />
        <result column="seller_name" property="sellerName" />
        <result column="create_order_time" property="createOrderTime" />
        <!-- 快照信息 -->
        <result column="warehouse_flag" property="warehouseFlag"/>
        <result column="warehouse_no" property="warehouseNo"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="standard_flag" property="standardFlag"/>
        <result column="calculation_unit_name" property="calculationUnitName"/>
        <result column="assist_calculation_unit_name" property="assistCalculationUnitName"/>
        <result column="main_unit_num" property="mainUnitNum"/>
        <result column="assist_unit_num" property="assistUnitNum"/>
        <result column="conversion_rate" property="conversionRate"/>
        <result column="htdt_goods_name" property="htdtGoodsName"/>
        <result column="unit_relation_num" property="unitRelationNum" />
        <result column="unit_name" property="unitName" />
        <result column="stock_flag" property="stockFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        order_no, order_item_no, channel_code, item_code, goods_name, sku_code, sku_picture_url, first_category_id, first_category_name, second_category_id, second_category_name, third_category_id, third_category_name, last_category_id, last_category_name, brand_id, brand_name, goods_count, sale_price, goods_price, trade_price, goods_amount, goods_freight, total_discount_amount, shop_discount_amount, platform_discount_amount, used_rebate_amount, bargaining_goods_amount, bargaining_goods_freight, order_item_total_amount, order_item_pay_amount, goods_real_price, refund_status,unit_name,stock_flag
    </sql>

    <sql id="Extend_Column_List">
        toi.order_no, toi.order_item_no, toi.channel_code, toi.item_code, toi.goods_name, toi.sku_code, toi.sku_picture_url, toi.goods_count,
        tos.seller_type, tos.seller_name, tos.create_order_time, tos.order_status, tos.data_source,
        (case when r2.stock_flag =2 then r2.goods_no else r.goods_no end) goods_no,
        (case when r2.stock_flag =2 then r2.stock_flag else r.stock_flag end) stock_flag,
        (case when r2.stock_flag =2 then r2.htdt_goods_name else r.htdt_goods_name end) htdt_goods_name,
        (case when r2.stock_flag =2 then r2.warehouse_flag else r.warehouse_flag end) warehouse_flag,
        (case when r2.stock_flag =2 then r2.warehouse_no else r.warehouse_no end) warehouse_no,
        (case when r2.stock_flag =2 then r2.warehouse_name else r.warehouse_name end) warehouse_name,
        (case when r2.stock_flag =2 then r2.standard_flag else r.standard_flag end) standard_flag,
        (case when r2.stock_flag =2 then r2.calculation_unit_name else r.calculation_unit_name end) calculation_unit_name,
        (case when r2.stock_flag =2 then r2.assist_calculation_unit_name else r.assist_calculation_unit_name end) assist_calculation_unit_name,
        (case when r2.stock_flag =2 then r2.main_unit_num else r.main_unit_num end) main_unit_num,
        (case when r2.stock_flag =2 then r2.assist_unit_num else r.assist_unit_num end) assist_unit_num,
        (case when r2.stock_flag =2 then r2.conversion_rate else r.conversion_rate end) conversion_rate,
        (case when r2.stock_flag =2 then r2.unit_relation_num else r.unit_relation_num end) unit_relation_num,
        (case when r2.stock_flag =2 then r2.unit_relation else r.unit_relation end) unit_relation
   </sql>
    <sql id="Extend_Column_List_Notnull">
        toi.order_no, toi.order_item_no, toi.channel_code, toi.item_code, toi.goods_name, toi.sku_code, toi.sku_picture_url, toi.goods_count,
        tos.seller_type, tos.seller_name, tos.create_order_time, tos.order_status,tos.data_source, r.goods_no, r.stock_flag, r.htdt_goods_name, r.warehouse_flag, r.warehouse_no, r.warehouse_name,
        r.standard_flag, r.calculation_unit_name, r.assist_calculation_unit_name, r.main_unit_num, r.assist_unit_num, r.conversion_rate, r.unit_relation_num unitRelationNum
   </sql>
    <!--g.warehouse_no qcWarehouseNo, g.warehouse_name qcWarehouseName,-->
    <sql id="Goods_Extend_Column_List">
        g.goods_name qcGoodsName, g.warehouse_flag qcWarehouseFlag, g.standard_flag qcStandardFlag, g.calculation_unit_no qcCalculationUnitNo, g.assist_calculation_unit_no qcAssistCalculationUnitNo,
        g.main_unit_num qcMainUnitNum, g.assist_unit_num qcAssistUnitNum, g.conversion_rate qcConversionRate
   </sql>

    <select id="selectTradeOrderItems"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultMap="ExtendResultMap">
        SELECT
        <include refid="Extend_Column_List"/>,
        <include refid="Goods_Extend_Column_List"/>
        FROM trade_order_items toi
        LEFT JOIN trade_orders tos on tos.order_no = toi.order_no
        left join trade_order_items_relation r on toi.sku_code=r.sku_code and tos.buyer_code=r.relation_buyer_code and r.stock_flag=1
        left join trade_order_items_relation r2 on toi.order_item_no =r2.order_item_no and tos.buyer_code=r2.relation_buyer_code and r2.stock_flag=2
        left join goods g on r.goods_no=g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and buyer_code = #{buyerCode}
                </if>
                <if test="buyerCodes != null and buyerCodes.size() > 0">
                    and buyer_code IN
                    <foreach collection="buyerCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dataSource != null and dataSource != ''">
                    and tos.data_source = #{dataSource}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and (instr(r2.htdt_goods_name, #{goodsName}) > 0 or instr(g.goods_name, #{goodsName}) > 0)
                </if>
                <if test="purchaseGoodsName != null and purchaseGoodsName != ''">
                    and instr(toi.goods_name, #{purchaseGoodsName}) > 0
                </if>
                <if test="relationGoodsName != null and relationGoodsName != ''">
                    and (instr(toi.goods_name, #{relationGoodsName}) > 0 or instr(r2.htdt_goods_name, #{relationGoodsName}) > 0)
                </if>
                <if test="startCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') >= #{startCreateOrderTime} ]]>
                </if>
                <if test="endCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') <= #{endCreateOrderTime} ]]>
                </if>
            </trim>
        </where>
        order by toi.modify_time desc, toi.id asc
    </select>

    <select id="selectTradeOrderItemsStock"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultMap="ExtendResultMap">
        select * from (
        SELECT
        <include refid="Extend_Column_List"/>,
        <include refid="Goods_Extend_Column_List"/>
        FROM trade_order_items toi
        LEFT JOIN trade_orders tos on tos.order_no = toi.order_no
        left join trade_order_items_relation r on toi.sku_code=r.sku_code and tos.buyer_code=r.relation_buyer_code and
        r.stock_flag=1
        left join trade_order_items_relation r2 on toi.order_item_no =r2.order_item_no and
        tos.buyer_code=r2.relation_buyer_code and r2.stock_flag=2
        left join goods g on r.goods_no=g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and buyer_code = #{buyerCode}
                </if>
                <if test="buyerCodes != null and buyerCodes.size() > 0">
                    and buyer_code IN
                    <foreach collection="buyerCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dataSource != null and dataSource != ''">
                    and tos.data_source = #{dataSource}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and (instr(r2.htdt_goods_name, #{goodsName}) > 0 or instr(g.goods_name, #{goodsName}) > 0)
                </if>
                <if test="purchaseGoodsName != null and purchaseGoodsName != ''">
                    and instr(toi.goods_name, #{purchaseGoodsName}) > 0
                </if>
                <if test="relationGoodsName != null and relationGoodsName != ''">
                    and (instr(toi.goods_name, #{relationGoodsName}) > 0 or instr(r2.htdt_goods_name,
                    #{relationGoodsName}) > 0)
                </if>
                <if test="startCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') >= #{startCreateOrderTime} ]]>
                </if>
                <if test="endCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') <= #{endCreateOrderTime} ]]>
                </if>
            </trim>
        </where>
        order by toi.modify_time desc, toi.id asc
        ) a
        <where>
            <if test="stockFlag != null">
                and stock_flag = #{stockFlag}
            </if>
        </where>
    </select>

    <select id="selectTradeOrderItemsStockFlagNotNull"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultMap="ExtendResultMap">
        SELECT
        <include refid="Extend_Column_List_Notnull"/>,
        <include refid="Goods_Extend_Column_List"/>
        FROM trade_order_items toi
        LEFT JOIN trade_orders tos on tos.order_no = toi.order_no
        <if test="stockFlag != null">
            <if test="stockFlag == 1">
                left join trade_order_items_relation r on toi.sku_code=r.sku_code and tos.buyer_code=r.relation_buyer_code and r.stock_flag=1
            </if>
            <if test="stockFlag == 2">
                left join trade_order_items_relation r on toi.order_item_no =r.order_item_no and tos.buyer_code=r.relation_buyer_code and r.stock_flag=2
            </if>
        </if>
        left join goods g on r.goods_no=g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and buyer_code = #{buyerCode}
                </if>
                <if test="buyerCodes != null and buyerCodes.size() > 0">
                    and buyer_code IN
                    <foreach collection="buyerCodes" item="item" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="dataSource != null and dataSource != ''">
                    and tos.data_source = #{dataSource}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and (instr(r.htdt_goods_name, #{goodsName}) > 0 or instr(g.goods_name, #{goodsName}) > 0)
                </if>
                <if test="stockFlag != null">
                    and r.stock_flag = #{stockFlag}
                </if>
                <if test="purchaseGoodsName != null and purchaseGoodsName != ''">
                    and instr(toi.goods_name, #{purchaseGoodsName}) > 0
                </if>
                <if test="relationGoodsName != null and relationGoodsName != ''">
                    and (instr(toi.goods_name, #{relationGoodsName}) > 0 or instr(r.htdt_goods_name, #{relationGoodsName}) > 0)
                </if>
                <if test="startCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') >= #{startCreateOrderTime} ]]>
                </if>
                <if test="endCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') <= #{endCreateOrderTime} ]]>
                </if>
            </trim>
        </where>
        order by toi.modify_time desc, toi.id asc
    </select>

    <!-- 判断是否所有订单下此商品都入库 -->
    <select id="selectExistTradeOrderItemsRelationCount"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultType="java.lang.Integer">
        SELECT count(1)  FROM trade_order_items toi
        LEFT JOIN trade_orders tos on tos.order_no = toi.order_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and buyer_code = #{buyerCode}
                </if>
                <if test="skuCode != null and skuCode != ''">
                    and toi.sku_code = #{skuCode}
                </if>
                and not exists(select 1 from trade_order_items_relation r where r.sku_code=toi.sku_code and r.relation_buyer_code=tos.buyer_code and r.order_item_no =toi.order_item_no and r.stock_flag=2)
            </trim>
        </where>
    </select>

    <delete id="delNotStockRelation" parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO">
        delete from trade_order_items_relation where sku_code = #{skuCode} and relation_buyer_code = #{buyerCode} and stock_flag=1 and (order_item_no is null or order_item_no ='')
    </delete>

    <select id="selectTradeOrderItemsList"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultMap="ExtendResultMap">
        SELECT
        toi.order_no,
        toi.order_item_no,
        toi.channel_code,
        toi.goods_name,
        toi.goods_count,
        toi.unit_name,
        toi.stock_flag,
        tos.seller_type,
        tos.seller_name,
        tos.create_order_time,
        tos.order_status
        FROM trade_order_items toi
        LEFT JOIN trade_orders tos on tos.order_no = toi.order_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and tos.buyer_code = #{buyerCode}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    and toi.order_no = #{orderNo}
                </if>
                <if test="startCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') >= #{startCreateOrderTime} ]]>
                </if>
                <if test="endCreateOrderTime != null">
                    <![CDATA[ and date_format(tos.create_order_time, '%Y-%m-%d') <= #{endCreateOrderTime} ]]>
                </if>
            </trim>
        </where>
        order by tos.create_order_time desc
    </select>

    <select id="selectTradeOrderItemsCount"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqPurchaseTradeOrderDTO" resultType="java.lang.Integer">
        SELECT count(1)
        FROM trade_order_items toi
        LEFT JOIN trade_orders tos on tos.order_no = toi.order_no
        LEFT JOIN goods g on g.goods_no = toi.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="buyerCode != null and buyerCode != ''">
                    and buyer_code = #{buyerCode}
                </if>
            </trim>
        </where>
    </select>

    <select id="selectOneTradeOrderItems"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqTradeOrderItemsDTO" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM trade_order_items
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="orderNo != null and orderNo != ''">
                    and order_no = #{orderNo}
                </if>
                <if test="skuCode != null and skuCode != ''">
                    and sku_code = #{skuCode}
                </if>
                <if test="orderItemNo != null and orderItemNo != ''">
                    and order_item_no = #{orderItemNo}
                </if>
            </trim>
        </where>
    </select>

    <select id="selectTradeOrderItemList"
            parameterType="cn.htdt.goodsprocess.dto.request.tradeorder.ReqTradeOrderItemsDTO" resultType="cn.htdt.goodsprocess.dto.response.tradeorder.ResPurchaseTradeOrderItemDTO">
        SELECT
        <include refid="Base_Column_List"/>
        FROM trade_order_items
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="orderNo != null and orderNo != ''">
                    and order_no = #{orderNo}
                </if>
                <if test="orderNoList != null and orderNoList.size > 0">
                    and order_no in
                    <foreach collection="orderNoList" item="orderNo" open="(" close=")" separator=",">
                        #{orderNo}
                    </foreach>
                </if>
                <if test="skuCode != null and skuCode != ''">
                    and sku_code = #{skuCode}
                </if>
                <if test="orderItemNo != null and orderItemNo != ''">
                    and order_item_no = #{orderItemNo}
                </if>
            </trim>
        </where>
    </select>
    <!-- 更新关联商品或入库标识 -->
    <update id="updateOneGoodsByTrade" parameterType="cn.htdt.goodsprocess.domain.TradeOrderItemsDomain">
        update
        trade_order_items
        <set>
            <if test="goodsNo != null and goodsNo != ''">
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="standardFlag != null and standardFlag != ''">
                standard_flag = #{standardFlag,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitName != null and calculationUnitName != ''">
                calculation_unit_name = #{calculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="assistCalculationUnitName != null and assistCalculationUnitName != ''">
                assist_calculation_unit_name = #{assistCalculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="mainUnitNum != null">
                main_unit_num = #{mainUnitNum,jdbcType=DECIMAL},
            </if>
            <if test="assistUnitNum != null">
                assist_unit_num = #{assistUnitNum,jdbcType=DECIMAL},
            </if>
            <if test="conversionRate != null">
                conversion_rate = #{conversionRate,jdbcType=DECIMAL},
            </if>
            <if test="stockFlag != null">
                stock_flag = #{stockFlag,jdbcType=TINYINT},
            </if>
            <if test="warehouseFlag != null">
                warehouse_flag = #{warehouseFlag,jdbcType=TINYINT},
            </if>
            <if test="warehouseNo != null and warehouseNo != ''">
                warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null and warehouseName != ''">
                warehouse_name = #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="htdtGoodsName != null and htdtGoodsName != ''">
                htdt_goods_name = #{htdtGoodsName,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="orderNo != null and orderNo != ''">
                    and order_no = #{orderNo}
                </if>
                <if test="skuCode != null and skuCode != ''">
                    and sku_code = #{skuCode}
                </if>
                <if test="orderItemNo != null and orderItemNo != ''">
                    and order_item_no = #{orderItemNo}
                </if>
            </trim>
        </where>
    </update>

    <!-- 根据千橙商品编号更新关联千橙商品信息-恢复默认 -->
    <update id="updateTradeOrderItems" parameterType="cn.htdt.goodsprocess.domain.TradeOrderItemsDomain">
        update
        trade_order_items
        <set>
            goods_no = '',
            standard_flag = 1,
            calculation_unit_name ='',
            assist_calculation_unit_name = '',
            main_unit_num = 1,
            assist_unit_num = 1,
            conversion_rate = 1.00,
            stock_flag = 1,
            warehouse_flag = 1,
            warehouse_no = '',
            warehouse_name = ,
            htdt_goods_name = ''
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="stockFlag != null">
                    and stock_flag = #{stockFlag}
                </if>
            </trim>
        </where>
    </update>

    <!-- 根据千橙商品编号更新关联千橙商品信息-恢复默认 -->
    <update id="batchUpdateTradeOrderItems" parameterType="cn.htdt.goodsprocess.vo.AtomTradeOrderItemsVo">
        update
        trade_order_items
        <set>
            goods_no = '',
            standard_flag = 1,
            calculation_unit_name ='',
            assist_calculation_unit_name = '',
            main_unit_num = 1,
            assist_unit_num = 1,
            conversion_rate = 1.00,
            stock_flag = 1,
            warehouse_flag = 1,
            warehouse_no = '',
            warehouse_name = '',
            htdt_goods_name = ''
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size > 0">
                    and goods_no IN
                    <foreach collection="goodsNos" item="goodsNo" open="(" close=")" separator=",">
                        #{goodsNo}
                    </foreach>
                </if>
                <if test="stockFlag != null">
                    and stock_flag = #{stockFlag}
                </if>
            </trim>
        </where>
    </update>

</mapper>
