<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.StoreSaleCategorySortDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.StoreSaleCategorySortDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="store_no" property="storeNo"/>
        <result column="category_no" property="categoryNo"/>
        <result column="sc_sort_num" property="scSortNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        store_no, category_no, sc_sort_num
    </sql>

    <update id="batchUpdateByParam"
            parameterType="cn.htdt.goodsprocess.domain.StoreSaleCategorySortDomain">
        <foreach collection="list" item="item" separator=";">
            update
            store_sale_category_sort
            set
            sc_sort_num = #{item.scSortNum},
            <if test="item.modifyNo != null and item.modifyNo != ''">
                modify_no = #{item.modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyName != null and item.modifyName != ''">
                modify_name = #{item.modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
            <where>
                store_no = #{item.storeNo}
                and category_no = #{item.categoryNo}
            </where>
        </foreach>
    </update>

    <select id="selectSaleCategorySortNum" parameterType="cn.htdt.goodsprocess.vo.AtomSortNumVo" resultType="int">
        select
        sc_sort_num
        from
        store_sale_category_sort
        <where>
            store_no = #{storeNo}
            and category_no = #{categoryNo}
        </where>
    </select>

    <update id="moveBetweenSortNum" parameterType="cn.htdt.goodsprocess.vo.AtomSortNumVo">
        update
        store_sale_category_sort
        set
        sc_sort_num = sc_sort_num + #{moveValue}
        <where>
            store_no = #{storeNo}
            and sc_sort_num between #{startMoveSortNum} and #{endMoveSortNum}
        </where>
    </update>

    <update id="updateSortNum" parameterType="cn.htdt.goodsprocess.vo.AtomSortNumVo">
        update
        store_sale_category_sort
        set
        sc_sort_num = #{endSortNum}
        <where>
            store_no = #{storeNo}
            and category_no = #{categoryNo}
            and sc_sort_num = #{startSortNum}
        </where>
    </update>
</mapper>
