<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.CloudPoolApplyDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.CloudPoolApplyDomain">
        <result column="apply_no" property="applyNo"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="apply_status" property="applyStatus"/>
        <result column="audit_time" property="auditTime"/>
        <result column="audit_message" property="auditMessage"/>
        <result column="failure_type" property="failureType"/>
        <result column="audit_user_no" property="auditUserNo"/>
        <result column="audit_user_name" property="auditUserName"/>
        <result column="shelf_status" property="shelfStatus"/>
        <result column="shelf_type" property="shelfType"/>
        <result column="import_store_type" property="importStoreType"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="history_success_flag" property="historySuccessFlag"/>
        <result column="commission_config_time" property="commissionConfigTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        apply_no,
        goods_no,
        apply_status,
        audit_time,
        audit_message,
        failure_type,
        audit_user_no,
        audit_user_name,
        shelf_status,
        shelf_type,
        import_store_type,
        merchant_no,
        store_no,
        store_name,
        disable_flag,
        history_success_flag,
        commission_config_time
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByGoodsNo" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomCloudPoolApplyVo">
        select
        <include refid="Base_Column_List"/>
        from
        cloud_pool_apply
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="goodsNoList != null">
                    and goods_no IN
                    <foreach collection="goodsNoList" item="goodsNo" separator="," open="(" close=")">
                        #{goodsNo}
                    </foreach>
                </if>
                <if test="disableFlag != null and disableFlag != ''">
                    and disable_flag = #{disableFlag}
                </if>
                <choose>
                    <when test="deleteFlag != null and deleteFlag == 3">
                    </when>
                    <otherwise>
                        <include refid="Base_Column_Where"/>
                    </otherwise>
                </choose>
            </trim>
        </where>
    </select>

    <select id="selectLastUpdateGoodsByGoodsNo" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomCloudPoolApplyVo">
        select
        <include refid="Base_Column_List"/>
        from
        cloud_pool_apply
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                AND apply_status = '1002'
                AND import_store_type = '1001'
                AND disable_flag = '2'
                <if test="goodsUpdateTime != null">
                    AND commission_config_time > #{goodsUpdateTime}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateByParam" parameterType="cn.htdt.goodsprocess.vo.AtomCloudPoolApplyVo">
        update cloud_pool_apply
        <set>
            <if test="applyStatus != null and applyStatus != ''">
                apply_status = #{applyStatus},
            </if>
            <if test="auditTime != null ">
                audit_time = #{auditTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditMessage != null and auditMessage != ''">
                audit_message = #{auditMessage},
            </if>
            <if test="failureType != null and failureType != ''">
                failure_type = #{failureType},
            </if>
            <if test="auditUserNo != null and auditUserNo != ''">
                audit_user_no = #{auditUserNo},
            </if>
            <if test="auditUserName != null and auditUserName != ''">
                audit_user_name = #{auditUserName},
            </if>
            <if test="shelfStatus != null and shelfStatus != ''">
                shelf_status = #{shelfStatus},
            </if>
            <!--上架类型-->
            <if test="shelfType != null and shelfType != ''">
                shelf_type = #{shelfType},
            </if>
            <if test="importStoreType != null and importStoreType != ''">
                import_store_type = #{importStoreType},
            </if>
            <if test="disableFlag != null and disableFlag != ''">
                disable_flag = #{disableFlag},
            </if>
            <if test="historySuccessFlag != null and historySuccessFlag != ''">
                history_success_flag = #{historySuccessFlag},
            </if>
            <if test="commissionConfigTime != null">
                commission_config_time = #{commissionConfigTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName},
            </if>
            <if test="deleteFlag != null and deleteFlag != ''">
                delete_flag = #{deleteFlag},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="goodsNoList != null">
                    and goods_no IN
                    <foreach collection="goodsNoList" item="goodsNo" separator="," open="(" close=")">
                        #{goodsNo}
                    </foreach>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <delete id="deleteByGoodsNo" parameterType="cn.htdt.goodsprocess.domain.CloudPoolApplyDomain">
        delete from cloud_pool_apply where goods_no = #{goodsNo}
    </delete>
    <select id="selectMerchantCloudPoolGoodsCount" resultType="int">
        select
            count(1)
        from
            cloud_pool_apply
        where
            merchant_no = #{merchantNo}
            and disable_flag = 1
            and delete_flag = 1
            and apply_status in('1001', '1002')
            and shelf_status = '1002'
    </select>

</mapper>
