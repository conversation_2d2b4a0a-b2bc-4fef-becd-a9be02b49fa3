<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htdt.goodsprocess.dao.CloudPoolApplyGoodsLogsDao" >
  <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.CloudPoolApplyGoodsLogsDomain" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="cloud_pool_goods_no" property="cloudPoolGoodsNo" jdbcType="VARCHAR" />
    <result column="change_type" property="changeType" jdbcType="VARCHAR" />
    <result column="change_type_name" property="changeTypeName" jdbcType="VARCHAR" />
    <result column="change_content_type" property="changeContentType" jdbcType="VARCHAR" />
    <result column="change_content" property="changeContent" jdbcType="VARCHAR" />
    <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR" />
    <result column="merchant_name" property="merchantName" jdbcType="VARCHAR" />
    <result column="store_no" property="storeNo" jdbcType="VARCHAR" />
    <result column="store_name" property="storeName" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="create_no" property="createNo" jdbcType="VARCHAR" />
    <result column="create_name" property="createName" jdbcType="VARCHAR" />
    <result column="modify_no" property="modifyNo" jdbcType="VARCHAR" />
    <result column="modify_name" property="modifyName" jdbcType="VARCHAR" />
    <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP" />
    <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT" />
    <result column="company_no" property="companyNo" jdbcType="VARCHAR" />
    <result column="company_name" property="companyName" jdbcType="VARCHAR" />
    <result column="branch_no" property="branchNo" jdbcType="VARCHAR" />
    <result column="branch_name" property="branchName" jdbcType="VARCHAR" />
  </resultMap>
  <sql id="Base_Column_List" >
    id, cloud_pool_goods_no, change_type, change_type_name, change_content_type, change_content, 
    merchant_no, merchant_name, store_no, store_name, create_time, create_no, create_name, 
    modify_no, modify_name, modify_time, delete_flag, company_no, company_name, branch_no, 
    branch_name
  </sql>
  <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>
  <select id="selectByPrimaryParam" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.domain.CloudPoolApplyGoodsLogsDomain" >
    select 
    <include refid="Base_Column_List" />
    from cloud_pool_apply_goods_logs
    where
    <trim suffixOverrides="AND | OR" prefix="1=1">
      <if test="cloudPoolGoodsNo  != null and cloudPoolGoodsNo != ''">
        and cloud_pool_goods_no = #{cloudPoolGoodsNo}
      </if>
      <if test="changeType  != null and changeType != ''">
        and change_type = #{changeType}
      </if>
      <if test="companyNo  != null and companyNo  != ''">
        and company_no =  #{companyNo}
      </if>
      <if test="merchantNo  != null and merchantNo  != ''">
        and merchant_no = #{merchantNo}
      </if>
      <if test="storeNo  != null and storeNo  != ''">
        and store_no = #{storeNo}
      </if>
    </trim>
    order by create_time DESC
  </select>
</mapper>