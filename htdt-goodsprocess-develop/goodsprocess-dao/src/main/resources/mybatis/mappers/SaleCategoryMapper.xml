<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.SaleCategoryDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMapDomain" type="cn.htdt.goodsprocess.domain.SaleCategoryDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="category_no" property="categoryNo"/>
        <result column="category_name" property="categoryName"/>
        <result column="full_id_path" property="fullIdPath"/>
        <result column="full_name_path" property="fullNamePath"/>
        <result column="category_level" property="categoryLevel"/>
        <result column="modify_flag" property="modifyFlag"/>
        <result column="parent_no" property="parentNo"/>
        <result column="category_source" property="categorySource"/>
        <result column="first_category_no" property="firstCategoryNo"/>
        <result column="second_category_no" property="secondCategoryNo"/>
        <result column="third_category_no" property="thirdCategoryNo"/>
        <result column="picture_url" property="pictureUrl"/>
        <result column="sort_value" property="sortValue"/>
        <result column="mdm_category_no" property="mdmCategoryNo"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="mdm_disable_flag" property="mdmDisableFlag"/>
    </resultMap>

    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.vo.AtomResSaleCategoryVo">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="category_no" property="categoryNo"/>
        <result column="category_name" property="categoryName"/>
        <result column="full_id_path" property="fullIdPath"/>
        <result column="full_name_path" property="fullNamePath"/>
        <result column="category_level" property="categoryLevel"/>
        <result column="modify_flag" property="modifyFlag"/>
        <result column="parent_no" property="parentNo"/>
        <result column="category_source" property="categorySource"/>
        <result column="first_category_no" property="firstCategoryNo"/>
        <result column="second_category_no" property="secondCategoryNo"/>
        <result column="third_category_no" property="thirdCategoryNo"/>
        <result column="picture_url" property="pictureUrl"/>
        <result column="sort_value" property="sortValue"/>
        <result column="mdm_category_no" property="mdmCategoryNo"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="mdm_disable_flag" property="mdmDisableFlag"/>
        <result column="total" property="total"/>
        <result column="selNum" property="selNum"/>
        <result column="goodsNum" property="goodsNum"/>
        <result column="sc_sort_num" property="scSortNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        category_no,
        category_name,
        full_id_path,
        full_name_path,
        category_level,
        modify_flag,
        parent_no,
        category_source,
        first_category_no,
        second_category_no,
        third_category_no,
        picture_url,
        sort_value,
        mdm_category_no,
        disable_flag,
        mdm_disable_flag
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectOneSaleCategory" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        select
        <include refid="Base_Column_List"/>
        from
        sale_category
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="categoryNo != null and categoryNo != ''">
                    and category_no = #{categoryNo}
                </if>
                <if test="categoryName != null and categoryName != ''">
                    and category_name = #{categoryName}
                </if>
                <if test="categoryLevel != null and categoryLevel != ''">
                    and category_level = #{categoryLevel}
                </if>
                <if test="fullIdPath != null and fullIdPath != ''">
                    and full_id_path LIKE CONCAT (#{fullIdPath},'%')
                </if>
                <if test="fullNamePath != null and fullNamePath != ''">
                    and full_name_path LIKE CONCAT(CONCAT('%',#{fullNamePath}),'%')
                </if>
                <if test="disableFlag != null and disableFlag != ''">
                    and disable_flag = #{disableFlag}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <select id="selectSaleCategory" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        select
        <include refid="Base_Column_List"/>
        from
        sale_category
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="categoryNo != null and categoryNo != ''">
                    and category_no = #{categoryNo}
                </if>
                <if test="categoryNoList != null and categoryNoList.size() > 0">
                    and category_no IN
                    <foreach item="categoryNo" collection="categoryNoList" open="(" close=")" separator=",">
                        #{categoryNo}
                    </foreach>
                </if>
                <if test="categorySource != null and categorySource != ''">
                    and category_source = #{categorySource}
                </if>
                <if test="parentNo != null and parentNo != ''">
                    <choose>
                        <!--左右结构的话, 传进来的销售类目为二级销售目录, 需要根据二级销售类目, 查询出四级销售类目-->
                        <when test="goodsStyleType != null and goodsStyleType != '' and goodsStyleType == '1002'">
                            and second_category_no = #{parentNo}
                        </when>
                        <otherwise>
                            and parent_no = #{parentNo}
                        </otherwise>
                    </choose>
                </if>
                <if test="categoryName != null and categoryName != ''">
                    and category_name = #{categoryName}
                </if>
                <if test="categoryLevel != null and categoryLevel != ''">
                    and category_level = #{categoryLevel}
                </if>
                <if test="fullIdPath != null and fullIdPath != ''">
                    and full_id_path LIKE CONCAT (#{fullIdPath},'%')
                </if>
                <if test="fullNamePath != null and fullNamePath != ''">
                    and full_name_path LIKE CONCAT(CONCAT('%',#{fullNamePath}),'%')
                </if>
                <if test="mdmCategoryNo != null and mdmCategoryNo != ''">
                    and mdm_category_no = #{mdmCategoryNo}
                </if>
                <if test="disableFlag != null and disableFlag != ''">
                    and disable_flag = #{disableFlag}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        order by
        sort_value
    </select>

    <select id="selectPreviousSaleCategory" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.SaleCategoryDomain">
        select
        <include refid="Base_Column_List"/>
        from
        sale_category
        <where>
            parent_no = #{parentNo}
            and sort_value <![CDATA[ < ]]> #{sortValue}
            <include refid="Base_Column_Where"/>
        </where>
        order by
        sort_value DESC
        limit 0,1
    </select>

    <select id="selectNextSaleCategory" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.SaleCategoryDomain">
        select
        <include refid="Base_Column_List"/>
        from
        sale_category
        <where>
            parent_no = #{parentNo}
            and sort_value <![CDATA[ > ]]> #{sortValue}
            <include refid="Base_Column_Where"/>
        </where>
        order by
        sort_value
        limit 0,1
    </select>

    <select id="selectMaxSortValue" resultType="java.lang.Integer" parameterType="java.lang.String">
        select
        COALESCE (MAX(sort_value),0)
        from
        sale_category
        <where>
            parent_no = #{parentNo}
            <include refid="Base_Column_Where"/>
        </where>
    </select>

    <select id="selectFullSaleCategoryPath"
            parameterType="cn.htdt.goodsprocess.domain.SaleCategoryDomain" resultMap="BaseResultMapDomain">
        SELECT
        sc.category_no,
        sc.category_name,
        sc.parent_no,
        sc.full_id_path,
        sc.full_name_path,
        sc.category_level,
        sc.sort_value,
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no
        FROM
        sale_category sc
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="categoryNo != null and categoryNo != ''">
                    and category_no = #{categoryNo}
                </if>
            </trim>
        </where>
    </select>

    <select id="selectExhibitionSaleCategoryFullIdPath"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo" resultMap="BaseResultMapDomain">
        SELECT DISTINCT
        sc.category_no,
        sc.category_name,
        sc.parent_no,
        sc.full_id_path,
        sc.full_name_path,
        sc.category_level,
        sc.sort_value,
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no
        FROM
        sale_category sc
        INNER JOIN goods g ON g.category_no = sc.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsStatus != null and goodsStatus != ''">
                    AND g.goods_status = #{goodsStatus}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    AND g.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    AND g.store_no = #{storeNo}
                </if>
                <if test="goodsSourceType != null and goodsSourceType != ''">
                    AND g.goods_source_type = #{goodsSourceType}
                </if>
                <if test="fullNamePath != null and fullNamePath != ''">
                    and sc.full_name_path LIKE CONCAT(CONCAT('%',#{fullNamePath}),'%')
                </if>
                <if test="queryType==1004">
                    and g.goods_source_type = '1002'
                </if>
                <!--店铺身份查询列表（包括商家分发、店铺自建） -->
                <if test="queryType==1006">
                    and g.goods_source_type in ('1003','1005')
                </if>
                <if test="queryType==1007">
                    and g.goods_source_type in ('1006')
                </if>
                <if test="queryType==1009">
                    and g.goods_source_type in ('1003','1005','1006')
                </if>
                AND g.series_type != '1002'
                AND g.delete_flag = '1'
            </trim>
        </where>
        ORDER BY
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no,
        sc.sort_value
    </select>

    <select id="selectLevelTwoSaleCategory"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo" resultType="java.lang.String">
        SELECT
        sc.second_category_no
        FROM
        sale_category sc
        INNER JOIN goods_master_data g ON g.category_no = sc.category_no
        <if test="disableFlag!=null">
            AND sc.disable_flag = #{disableFlag}
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsSourceType != null and goodsSourceType != ''">
                    AND g.goods_source_type = #{goodsSourceType}
                </if>
                <if test="fullNamePath != null and fullNamePath != ''">
                    and sc.full_name_path LIKE CONCAT(CONCAT('%',#{fullNamePath}),'%')
                </if>
                AND sc.category_level = '1004'
                AND g.series_type != '1002'
                AND g.delete_flag = '1'
            </trim>
        </where>
        order by g.modify_time desc
    </select>


    <select id="selectLevelTwoSaleCategoryByGood"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo" resultType="java.lang.String">

        SELECT
            distinct sc.second_category_no as categoryNo
        FROM
        sale_category sc
        INNER JOIN goods g ON g.category_no = sc.category_no
        <if test="disableFlag!=null">
            AND sc.disable_flag = #{disableFlag}
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsSourceType != null and goodsSourceType != ''">
                    AND g.goods_source_type = #{goodsSourceType}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    AND g.store_no = #{storeNo}
                </if>
                AND sc.category_level = '1004'
                AND sc.delete_flag = '1'
                AND g.series_type != '1002'
                AND g.goods_type != '1002'
                AND g.delete_flag = '1'
                AND g.goods_source_type in('1003','1005')
                <if test="goodsStatus != null and goodsStatus != ''">
                    and g.goods_status = #{goodsStatus}
                </if>
                <if test="auditStatus != null and auditStatus != ''">
                    and g.audit_status = #{auditStatus}
                </if>
            </trim>
        </where>
    </select>

    <select id="getSortedLevelTwoSaleCategory" parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo" resultMap="BaseResultMap">
        select
            sc1.category_no,
            sc1.category_name
        from sale_category sc inner join sale_category sc1 on sc.category_no = sc1.parent_no
        where sc.delete_flag = 1
              and sc1.delete_flag = 1
            <if test="categoryNoList != null and categoryNoList.size() > 0">
                and sc1.category_no IN
                <foreach item="categoryNo" collection="categoryNoList" open="(" close=")" separator=",">
                    #{categoryNo}
                </foreach>
            </if>
            <if test="categoryNo != null and categoryNo != ''">
                and sc1.category_no = #{categoryNo}
            </if>
            <!--先按照一级类目排序, 再按照二级类目排序-->
            order by sc.sort_value, sc1.sort_value
    </select>


    <select id="selectSaleCategoryWithGoods"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo"
            resultType="cn.htdt.goodsprocess.domain.SaleCategoryDomain">
        SELECT
        sc.first_category_no firstCategoryNo,
        s.category_name categoryName,
        sum(g.sales_volume) totalVolume
        FROM
        sale_category sc
        INNER JOIN goods g ON g.category_no = sc.category_no
        left join sale_category s on sc.first_category_no = s.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsStatus != null and goodsStatus != ''">
                    AND g.goods_status = #{goodsStatus}
                </if>
                <if test="goodsSourceType != null and goodsSourceType != ''">
                    and g.goods_source_type = #{goodsSourceType}
                </if>
                <if test="distributeGoodsFlag != null">
                    and g.distribute_goods_flag = #{distributeGoodsFlag}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    AND g.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    AND g.store_no = #{storeNo}
                </if>
                <!--店铺身份查询列表（查询云池和分销） -->
                <if test="queryType == 1008">
                    AND (g.goods_source_type ='1006' or g.distribute_goods_flag = 2)
                </if>
                and g.distribution_status != '3004'
                and g.series_type !='1002'
                and g.delete_flag = 1
                and g.disable_flag = 2
            </trim>
        </where>
        group by
        s.category_name,
        sc.first_category_no
        order by totalVolume desc
    </select>

    <update id="updateByCategoryNo" parameterType="cn.htdt.goodsprocess.domain.SaleCategoryDomain">
        update
        sale_category
        <set>
            <if test="categoryName != null and categoryName != ''">
                category_name = #{categoryName,jdbcType=VARCHAR},
            </if>
            <if test="fullIdPath != null and fullIdPath != ''">
                full_id_path = #{fullIdPath,jdbcType=VARCHAR},
            </if>
            <if test="fullNamePath != null and fullNamePath != ''">
                full_name_path = #{fullNamePath,jdbcType=VARCHAR},
            </if>
            <if test="categoryLevel != null and categoryLevel != ''">
                category_level = #{categoryLevel,jdbcType=VARCHAR},
            </if>
            <if test="modifyFlag != null and modifyFlag != ''">
                modify_flag = #{modifyFlag,jdbcType=INTEGER},
            </if>
            <if test="parentNo != null and parentNo != ''">
                parent_no = #{parentNo,jdbcType=VARCHAR},
            </if>
            <if test="categorySource != null and categorySource != ''">
                category_source = #{categorySource,jdbcType=VARCHAR},
            </if>
            <if test="firstCategoryNo != null and firstCategoryNo != ''">
                first_category_no = #{firstCategoryNo,jdbcType=VARCHAR},
            </if>
            <if test="secondCategoryNo != null and secondCategoryNo != ''">
                second_category_no = #{secondCategoryNo,jdbcType=VARCHAR},
            </if>
            <if test="thirdCategoryNo != null and thirdCategoryNo != ''">
                third_category_no = #{thirdCategoryNo,jdbcType=VARCHAR},
            </if>
            <if test="pictureUrl != null and pictureUrl != ''">
                picture_url = #{pictureUrl,jdbcType=VARCHAR},
            </if>
            <if test="sortValue != null and sortValue != ''">
                sort_value = #{sortValue,jdbcType=INTEGER},
            </if>
            <if test="mdmCategoryNo != null and mdmCategoryNo != ''">
                mdm_category_no = #{mdmCategoryNo,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null and disableFlag != ''">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="mdmDisableFlag != null and mdmDisableFlag != ''">
                mdm_disable_flag = #{mdmDisableFlag,jdbcType=TINYINT},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="categoryNo != null and categoryNo != ''">
                    and category_no = #{categoryNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="batchUpdateDisableFlagByCategoryNo">
        update
        sale_category
        <set>
            <if test="domain.disableFlag != null and domain.disableFlag != ''">
                disable_flag = #{domain.disableFlag,jdbcType=TINYINT},
            </if>
            <if test="domain.modifyNo != null and domain.modifyNo != ''">
                modify_no = #{domain.modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="domain.modifyName != null and domain.modifyName != ''">
                modify_name = #{domain.modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            category_no IN
            <foreach item="categoryNo" collection="list" open="(" close=")" separator=",">
                #{categoryNo}
            </foreach>
        </where>
    </update>

    <update id="batchLogicDelete">
        update
        sale_category
        set
        <if test="domain.modifyNo != null and domain.modifyNo != ''">
            modify_no = #{domain.modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="domain.modifyName != null and domain.modifyName != ''">
            modify_name = #{domain.modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            category_no IN
            <foreach item="categoryNo" collection="list" open="(" close=")" separator=",">
                #{categoryNo}
            </foreach>
        </where>
    </update>

    <update id="batchUpdateSaleCategory" parameterType="java.util.List">
        <foreach collection="list" separator=";" item="item">
            update
            sale_category
            set
            <if test="item.sortValue != null and item.sortValue != ''">
                sort_value = #{item.sortValue,jdbcType=INTEGER},
            </if>
            <if test="item.fullNamePath != null and item.fullNamePath != ''">
                full_name_path = #{item.fullNamePath,jdbcType=INTEGER},
            </if>
            <if test="item.modifyNo != null and item.modifyNo != ''">
                modify_no = #{item.modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyName != null and item.modifyName != ''">
                modify_name = #{item.modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
            <where>
                category_no = #{item.categoryNo}
            </where>
        </foreach>
    </update>

    <update id="updateMdmCategoryNo" parameterType="cn.htdt.goodsprocess.domain.SaleCategoryDomain">
        update
        sale_category
        set
        mdm_category_no = #{mdmCategoryNo},
        <if test="modifyNo != null and modifyNo != ''">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null and modifyNo != ''">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW()
        <where>
            category_no = #{categoryNo}
        </where>
    </update>

    <delete id="deleteDataByTerm">
        delete from sale_category
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and category_no = #{categoryNo}
                and delete_flag = 2
            </trim>
        </where>
    </delete>

    <select id="selectSaleCategoryListByFullIdPath" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        SELECT
        sc.category_no,
        sc.category_name,
        sc.parent_no,
        sc.full_id_path,
        sc.full_name_path,
        sc.category_level,
        sc.sort_value,
        t.total,
        t.selNum
        FROM
        sale_category sc
        INNER JOIN (
        SELECT
        <if test=" categoryLevel== '1001'">
            ssc.first_category_no category_no,
        </if>
        <if test="categoryLevel == '1002'">
            ssc.second_category_no category_no,
        </if>
        <if test="categoryLevel == '1003'">
            ssc.third_category_no category_no,
        </if>
        <if test="categoryLevel == '1004'">
            ssc.category_no category_no,
        </if>
        COUNT( ssc.category_no ) total,
        COUNT( sscr.category_no ) selNum
        FROM
        sale_category ssc
        LEFT JOIN sale_category_relation sscr ON sscr.category_no = ssc.category_no
        AND sscr.merchant_no = #{merchantNo}
        AND sscr.store_no = #{storeNo}
        WHERE
        ssc.delete_flag = 1
        AND ssc.disable_flag = 2
--         AND ssc.mdm_category_no != ''
        AND ssc.category_level = '1004'
        <if test="fullIdPath != null and fullIdPath != ''">
            AND ssc.full_id_path LIKE CONCAT (#{fullIdPath},'%')
        </if>
        GROUP BY
        <if test="categoryLevel == '1001'">
            ssc.first_category_no
        </if>
        <if test="categoryLevel == '1002'">
            ssc.second_category_no
        </if>
        <if test="categoryLevel == '1003'">
            ssc.third_category_no
        </if>
        <if test="categoryLevel == '1004'">
            ssc.category_no
        </if>
        ) t ON t.category_no = sc.category_no
        ORDER BY
        sc.sort_value
    </select>

    <select id="selectSaleCategorySelectedListByFullIdPath" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        SELECT
        sc.category_no,
        sc.category_name,
        sc.parent_no,
        sc.full_id_path,
        sc.full_name_path,
        sc.category_level,
        sc.sort_value
        FROM
        sale_category sc
        INNER JOIN (
        SELECT
        <if test=" categoryLevel== '1001'">
            ssc.first_category_no category_no
        </if>
        <if test="categoryLevel == '1002'">
            ssc.second_category_no category_no
        </if>
        <if test="categoryLevel == '1003'">
            ssc.third_category_no category_no
        </if>
        <if test="categoryLevel == '1004'">
            ssc.category_no category_no
        </if>
        FROM
        sale_category ssc
        INNER JOIN sale_category_relation sscr ON sscr.category_no = ssc.category_no
        AND sscr.merchant_no = #{merchantNo}
        AND sscr.store_no = #{storeNo}
        WHERE
        ssc.delete_flag = 1
        AND ssc.disable_flag = 2
--         AND ssc.mdm_category_no != ''
        AND ssc.category_level = '1004'
        <if test="fullIdPath != null and fullIdPath != ''">
            AND ssc.full_id_path LIKE CONCAT (#{fullIdPath},'%')
        </if>
        GROUP BY
        <if test="categoryLevel == '1001'">
            ssc.first_category_no
        </if>
        <if test="categoryLevel == '1002'">
            ssc.second_category_no
        </if>
        <if test="categoryLevel == '1003'">
            ssc.third_category_no
        </if>
        <if test="categoryLevel == '1004'">
            ssc.category_no
        </if>
        ) t ON t.category_no = sc.category_no
        ORDER BY
        sc.sort_value
    </select>

    <select id="selectExhibitionSaleCategoryByFullIdPath" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        SELECT
        sc.category_no,
        sc.category_name,
        sc.parent_no,
        sc.full_id_path,
        sc.full_name_path,
        sc.category_level,
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no,
        sc.sort_value
        FROM
        sale_category sc
        INNER JOIN (
        SELECT
        <if test=" categoryLevel== '1001'">
            ssc.first_category_no category_no
        </if>
        <if test="categoryLevel == '1002'">
            ssc.second_category_no category_no
        </if>
        <if test="categoryLevel == '1003'">
            ssc.third_category_no category_no
        </if>
        <if test="categoryLevel == '1004'">
            ssc.category_no category_no
        </if>
        FROM
        sale_category ssc
        INNER JOIN goods g ON g.category_no = ssc.category_no
        AND g.merchant_no = #{merchantNo}
        AND g.store_no = #{storeNo}
        WHERE
        g.delete_flag = 1
        AND ssc.category_level = '1004'
        <if test="queryType == 1006">
            and g.goods_source_type in ('1003','1005')
        </if>
        <if test="fullIdPath != null and fullIdPath != ''">
            AND ssc.full_id_path LIKE CONCAT (#{fullIdPath},'%')
        </if>
        GROUP BY
        <if test="categoryLevel == '1001'">
            ssc.first_category_no
        </if>
        <if test="categoryLevel == '1002'">
            ssc.second_category_no
        </if>
        <if test="categoryLevel == '1003'">
            ssc.third_category_no
        </if>
        <if test="categoryLevel == '1004'">
            ssc.category_no
        </if>
        ) t ON t.category_no = sc.category_no
        ORDER BY
        sc.sort_value
    </select>

    <select id="selectSaleCategorySelectedListByFour" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        SELECT distinct
        sc.category_no,
        sc.category_name,
        sc.parent_no,
        sc.full_id_path,
        sc.full_name_path,
        sc.category_level,
        sc.sort_value,
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no
        FROM
        sale_category sc
        INNER JOIN sale_category_relation scr ON scr.category_no = sc.category_no
        <if test="merchantNo != null and merchantNo != ''">
        AND scr.merchant_no = #{merchantNo}
        </if>
        <if test="storeNo != null and storeNo != ''">
        AND scr.store_no = #{storeNo}
        </if>
        <where>
            sc.delete_flag = 1
            AND sc.disable_flag = 2
--             蛋品去掉mdm
--             AND sc.mdm_category_no != ''
            AND sc.category_level = '1004'
            <if test="secondCategoryNo != null and secondCategoryNo != ''">
                AND sc.second_category_no = #{secondCategoryNo}
            </if>
            <if test="fullNamePath != null and fullNamePath != ''">
                and sc.full_name_path LIKE CONCAT(CONCAT('%',#{fullNamePath}),'%')
            </if>
        </where>
        ORDER BY
        sc.parent_no,
        sc.sort_value
    </select>

    <select id="selectSaleCategoryListByGoodsMasterData" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        SELECT
        sc.category_no,
        sc.full_id_path,
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no
        FROM
        goods_master_data gmd
        INNER JOIN sale_category sc ON sc.category_no = gmd.category_no
        <where>
            gmd.delete_flag = 1
            <if test="disableFlag != null">
                AND sc.disable_flag = #{disableFlag}
            </if>
        </where>
        ORDER BY
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no
    </select>


    <select id="selectSaleCategoryNums" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        select
            sc.category_no ,
            sc.category_name ,
            t.goodsNum
        from
            sale_category sc
        inner join (
            select
                sc.first_category_no category_no ,
                count(1) goodsNum
            from
                goods g
            inner join sale_category sc on
                g.category_no = sc.category_no
            where
                g.goods_status = '1002'
                <if test="storeNo != null and storeNo != ''">
                    and g.store_no = #{storeNo}
                </if>
                and g.delete_flag = 1
                and g.disable_flag = 2
                and g.series_type != '1002'
                <if test="goodsSourceType != null and goodsSourceType != ''">
                    and g.goods_source_type = #{goodsSourceType}
                </if>
            group by
                sc.first_category_no
            )t on
            t.category_no = sc.category_no
                <if test="categoryName != null and categoryName != ''">
                    where sc.category_name  like CONCAT(CONCAT('%', #{categoryName}),'%')
                </if>
    </select>

    <select id="selectSaleCategoryNumsForPlatform" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        select
            sc.category_no ,
            sc.category_name ,
            t.goodsNum
        from
          sale_category sc
        inner join (
            select
                sc.first_category_no category_no ,
                count(1) goodsNum
            from
                cloud_pool_apply cpa
                inner join goods g on cpa.goods_no =g.goods_no
                inner join sale_category sc on g.category_no = sc.category_no
            where
                cpa.apply_status = '1002'
                and cpa.shelf_status = '1002'
                and cpa.disable_flag = 2
                and cpa.delete_flag = 1
                <if test="storeNo != null and storeNo != ''">
                    and g.store_no = #{storeNo}
                </if>
                and g.delete_flag = 1
                and g.disable_flag = 2
                and g.series_type != '1002'
                <if test="goodsSourceType != null and goodsSourceType != ''">
                    and g.goods_source_type = #{goodsSourceType}
                </if>
            group by
              sc.first_category_no
            )t on
        t.category_no = sc.category_no
        <if test="categoryName != null and categoryName != ''">
            where sc.category_name  like CONCAT(CONCAT('%', #{categoryName}),'%')
        </if>
    </select>
    <select id="selectSaleCategoryGoodsNumForStore" resultMap="BaseResultMap">
        select sc.category_no,sc.category_name,count(g.goods_no) goodsNum
        from sale_category sc
        inner join goods g
        on sc.category_no = g.category_no
        where 1=1 and sc.category_level ='1004'
        and g.goods_status = '1002'
        and g.series_type != '1002'
        and g.goods_source_type IN ('1003', '1005')
        <if test="storeNo != null and storeNo != ''">
            and g.store_no = #{storeNo}
        </if>
        and g.delete_flag = 1
        group by sc.category_no
    </select>
    <select id="selectSaleCategoryGoodsNumForPlatForm" resultMap="BaseResultMap">
        select sc.category_no,sc.category_name,count(g.goods_no) goodsNum
        from sale_category sc
        inner join goods g
        on sc.category_no = g.category_no
        inner join cloud_pool_apply cpa
        on cpa.goods_no = g.parent_goods_no
        where 1=1
        and sc.category_level ='1004'
        and g.series_type != '1002'
        and g.delete_flag = 1
        and cpa.apply_status = '1002'
        and cpa.shelf_status = '1002'
        and cpa.disable_flag = 2
        and cpa.delete_flag = 1
        group by sc.category_no
    </select>

    <select id="selectSecondCategoryByGoods" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo">
        select distinct
        sc2.category_no ,
        sc2.category_name ,
        sc2.full_id_path ,
        sc2.full_name_path ,
        sscs.sc_sort_num
        from
        goods g
        inner join sale_category sc on sc.category_no = g.category_no
        inner join sale_category sc2 on sc2.category_no = sc.second_category_no
        left join store_sale_category_sort sscs on sc2.category_no = sscs.category_no and g.store_no = sscs.store_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    and g.store_no = #{storeNo}
                </if>
                <!--店铺身份查询列表（包括商家分发、店铺自建） -->
                <if test="queryType== 1006">
                    and g.goods_source_type in ('1003','1005')
                </if>
                <if test="goodsType != null and goodsType != ''">
                    and g.goods_type = #{goodsType}
                </if>
                and g.foods_type != '1002'
                and g.delete_flag = 1
            </trim>
        </where>
        order by
        isnull(sscs.sc_sort_num),
        sscs.sc_sort_num,
        sc.full_id_path
    </select>
</mapper>
