<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsBrowseRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsBrowseRecordDomain">
        <result column="id" property="id" />
        <result column="create_time" property="createTime" />
        <result column="create_no" property="createNo" />
        <result column="create_name" property="createName" />
        <result column="modify_no" property="modifyNo" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="browse_no" property="browseNo" />
        <result column="fan_no" property="fanNo" />
        <result column="fan_name" property="fanName" />
        <result column="fan_phone" property="fanPhone" />
        <result column="ds_fan_phone" property="dsFanPhone" />
        <result column="share_agent_no" property="shareAgentNo" />
        <result column="goods_info_json" property="goodsInfoJson" />
        <result column="goods_no" property="goodsNo" />
        <result column="store_no" property="storeNo" />
        <result column="merchant_no" property="merchantNo" />
        <result column="goods_browse_source" property="goodsBrowseSource" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_time,
        create_no,
        create_name,
        modify_no,
        modify_name,
        modify_time,
        delete_flag,
        browse_no, fan_no, fan_name, fan_phone, share_agent_no, goods_info_json, goods_no, store_no, merchant_no, goods_browse_source
    </sql>

    <!--分页查询商品浏览记录列表-->
    <!--AND (gbr.fan_phone like concat('%',#{searchCondition},'%') or gbr.fan_name like concat('%',#{searchCondition},'%'))-->
    <select id="selectGoodsBrowseRecordByPage" parameterType="cn.htdt.goodsprocess.vo.GoodsBrowseRecordVO" resultType="cn.htdt.goodsprocess.vo.GoodsBrowseRecordVO">
        select
        gbr.browse_no, gbr.fan_no, gbr.fan_name, gbr.fan_phone, gbr.ds_fan_phone, gbr.goods_no, gbr.store_no, gbr.merchant_no, gbr.goods_browse_source, gbr.create_time, g.goods_name
        from goods_browse_record gbr
        LEFT JOIN goods g ON gbr.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    AND gbr.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    AND gbr.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsBrowseSource != null and goodsBrowseSource != ''">
                    AND gbr.goods_browse_source = #{goodsBrowseSource,jdbcType=VARCHAR}
                </if>
                <if test="searchCondition != null and  searchCondition != ''">
                    AND (gbr.fan_phone = #{searchCondition, typeHandler=cn.htdt.common.encry.handler.CipherHandler} or gbr.fan_name like
                    concat('%',#{searchCondition},'%'))
                </if>
                <if test="startBrowseTime != null">
                    <![CDATA[ AND gbr.create_time >= #{startBrowseTime} ]]>
                </if>
                <if test="endBrowseTime != null">
                    <![CDATA[ AND gbr.create_time <= #{endBrowseTime} ]]>
                </if>
            </trim>
        </where>
        order by gbr.create_time desc
    </select>

    <!--查询商品浏览记录统计列表-->
    <select id="selectGoodsBrowseCountList" parameterType="cn.htdt.goodsprocess.vo.GoodsBrowseRecordVO" resultType="cn.htdt.goodsprocess.vo.GoodsBrowseRecordVO">
        SELECT
            gbr.goods_no,
            COUNT(1) AS totalBrowseCount
        FROM
            goods_browse_record gbr
        WHERE
            gbr.delete_flag = 1
            <if test="storeNo != null and storeNo != ''">
                AND gbr.store_no = #{storeNo,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo != null and merchantNo != ''">
                AND gbr.merchant_no = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="goodsBrowseSource != null and goodsBrowseSource != ''">
                AND gbr.goods_browse_source = #{goodsBrowseSource,jdbcType=VARCHAR}
            </if>
            <if test="startBrowseTime != null">
                <![CDATA[ AND gbr.create_time >= #{startBrowseTime} ]]>
            </if>
            <if test="endBrowseTime != null">
                <![CDATA[ AND gbr.create_time <= #{endBrowseTime} ]]>
            </if>
        GROUP BY
            gbr.goods_no
        ORDER BY
            totalBrowseCount DESC
        LIMIT 0,5
    </select>
    <select id="selectGoodsBrowseRecordNum" parameterType="cn.htdt.goodsprocess.vo.GoodsBrowseRecordVO"
            resultType="java.lang.Integer">
        select
            COUNT(1)
        FROM
            goods_browse_record
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    AND store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    AND merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsBrowseSource != null and goodsBrowseSource != ''">
                    AND goods_browse_source = #{goodsBrowseSource,jdbcType=VARCHAR}
                </if>
                <if test="null != day and 1 == day">
                    AND to_days(create_time) = to_days(now())
                </if>
                <if test="null != day and 2 == day">
                    AND TO_DAYS( NOW( ) ) - TO_DAYS( create_time) = 1
                </if>
                <if test="null != day and 3 == day">
                    AND date(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                </if>
                <if test="null != day and 4 == day">
                    AND date(create_time) &gt;= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                </if>
                <if test="null != goodsNo and '' != goodsNo">
                    AND goods_no = #{goodsNo}
                </if>
                <if test="null != shareAgentNo and '' != shareAgentNo">
                    AND share_agent_no = #{shareAgentNo}
                </if>
            </trim>
        </where>
    </select>
</mapper>
