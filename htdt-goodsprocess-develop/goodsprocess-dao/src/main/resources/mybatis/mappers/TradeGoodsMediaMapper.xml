<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.TradeGoodsMediaDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.TradeGoodsMediaDomain">
        <result column="id" property="id" />
        <result column="create_no" property="createNo" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_no" property="modifyNo" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="media_no" property="mediaNo" />
        <result column="item_code" property="itemCode" />
        <result column="sku_code" property="skuCode" />
        <result column="sort_value" property="sortValue" />
        <result column="picture_url" property="pictureUrl" />
        <result column="sku_picture_url" property="skuPictureUrl" />
        <result column="media_type" property="mediaType" />
        <result column="main_picture_flag" property="mainPictureFlag" />
        <result column="picture_show_area" property="pictureShowArea" />
        <result column="video_url" property="videoUrl" />
        <result column="file_name" property="fileName" />
        <result column="file_size" property="fileSize" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="disable_flag" property="disableFlag" />
        <result column="ali_video_id" property="aliVideoId" />
        <result column="video_duration" property="videoDuration" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_no,
        create_name,
        create_time,
        modify_no,
        modify_name,
        modify_time,
        delete_flag,
        media_no, item_code, sku_code, sort_value, picture_url, sku_picture_url, media_type, main_picture_flag, picture_show_area, video_url, file_name, file_size, merchant_no, merchant_name, store_no, store_name, disable_flag, ali_video_id, video_duration
    </sql>

    <select id="selectTradeGoodsMediaBySkuCode" parameterType="cn.htdt.goodsprocess.domain.TradeGoodsMediaDomain"
            resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM trade_goods_media
        <where>
            sku_code = #{skuCode}
        </where>
    </select>

    <select id="selectCountByPicUrlAndSkuCode" resultType="java.lang.Integer">
        SELECT count(1) FROM trade_goods_media
        <where>
            sku_code = #{skuCode}
            <if test="skuPictureUrl != null and skuPictureUrl != ''">
                and sku_picture_url = #{skuPictureUrl}
            </if>
        </where>
    </select>

</mapper>
