<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsLossReportItemDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsLossReportItemDomain">
        <result column="id" property="id" />
        <result column="create_no" property="createNo" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_no" property="modifyNo" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="loss_report_item_no" property="lossReportItemNo" />
        <result column="loss_report_no" property="lossReportNo" />
        <result column="merchant_goods_no" property="merchantGoodsNo" />
        <result column="merchant_goods_name" property="merchantGoodsName" />
        <result column="goods_no" property="goodsNo" />
        <result column="goods_name" property="goodsName" />
        <result column="barcode" property="barcode" />
        <result column="unit_name" property="unitName" />
        <result column="standard_flag" property="standardFlag" />
        <result column="main_unit_num" property="mainUnitNum" />
        <result column="assist_unit_num" property="assistUnitNum" />
        <result column="calculation_unit_no" property="calculationUnitNo" />
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo" />
        <result column="warehouse_type" property="warehouseType" />
        <result column="warehouse_no" property="warehouseNo" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="batch_no" property="batchNo" />
        <result column="goods_price" property="goodsPrice" />
        <result column="first_attribute_name" property="firstAttributeName" />
        <result column="first_attribute_value_name" property="firstAttributeValueName" />
        <result column="second_attribute_name" property="secondAttributeName" />
        <result column="second_attribute_value_name" property="secondAttributeValueName" />
        <result column="third_attribute_name" property="thirdAttributeName" />
        <result column="third_attribute_value_name" property="thirdAttributeValueName" />
        <result column="loss_report_num" property="lossReportNum" />
        <result column="loss_report_amount" property="lossReportAmount" />
        <result column="picture_url" property="pictureUrl" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="disable_flag" property="disableFlag" />
    </resultMap>

    <resultMap id="VoBaseResultMap" type="cn.htdt.goodsprocess.vo.AtomGoodsLossReportItemVo" extends="BaseResultMap">
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_no,
        create_name,
        create_time,
        modify_no,
        modify_name,
        modify_time,
        delete_flag,
        loss_report_item_no, loss_report_no, merchant_goods_no, merchant_goods_name, goods_no, goods_name, barcode, unit_name, standard_flag, main_unit_num, assist_unit_num, calculation_unit_no, assist_calculation_unit_no, warehouse_type, warehouse_no, warehouse_name, batch_no, goods_price,
        first_attribute_name,
		first_attribute_value_name,
		second_attribute_name,
		second_attribute_value_name,
		third_attribute_name,
		third_attribute_value_name,
        loss_report_num, loss_report_amount, picture_url, merchant_no, merchant_name, store_no, store_name, disable_flag
    </sql>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List_R">
        ri.id,
        ri.create_no,
        ri.create_name,
        ri.create_time,
        ri.modify_no,
        ri.modify_name,
        ri.modify_time,
        ri.delete_flag,
        ri.loss_report_item_no, ri.loss_report_no, ri.merchant_goods_no, ri.merchant_goods_name, ri.goods_no, ri.goods_name, ri.barcode, ri.unit_name, ri.standard_flag, ri.main_unit_num, ri.assist_unit_num, ri.calculation_unit_no, ri.assist_calculation_unit_no, ri.warehouse_type, ri.warehouse_no, ri.warehouse_name, ri.batch_no, ri.goods_price,
        ri.first_attribute_name,
		ri.first_attribute_value_name,
		ri.second_attribute_name,
		ri.second_attribute_value_name,
		ri.third_attribute_name,
		ri.third_attribute_value_name,
        ri.loss_report_num, ri.loss_report_amount, ri.picture_url, ri.merchant_no, ri.merchant_name, ri.store_no, ri.store_name, ri.disable_flag
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <sql id="Base_Column_Where_R">
        and ri.delete_flag = 1
    </sql>

    <!--根据条件获取报损单明细list -->
    <select id="selectGoodsLossReportItems" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsLossReportItemVo" resultMap="VoBaseResultMap">
        select
        <include refid="Base_Column_List_R"/>
        from goods_loss_report_item ri
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="lossReportNo != null and lossReportNo != ''">
                    AND ri.loss_report_no = #{lossReportNo, jdbcType=VARCHAR}
                </if>
                <if test="lossReportNos != null and lossReportNos.size() > 0">
                    and ri.loss_report_no IN
                    <foreach collection="lossReportNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
        ORDER BY ri.modify_time desc
    </select>

    <!-- 根据条件获取报损单明细list-查询前n条 -->
    <select id="selectLimitGoodsLossReportItems" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsLossReportItemVo" resultMap="VoBaseResultMap">
        select
        <include refid="Base_Column_List_R"/>
        from goods_loss_report_item AS ri,
        (
            SELECT
                GROUP_CONCAT(id order by id desc) AS ids
            FROM goods_loss_report_item g
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="lossReportNos != null and lossReportNos.size() > 0">
                        and g.loss_report_no IN
                        <foreach collection="lossReportNos" item="item" index="index"
                                 open="(" separator="," close=")">
                            #{item}
                        </foreach>
                    </if>
                    <if test="lossReportNo != null and lossReportNo != ''">
                        AND g.loss_report_no = #{lossReportNo, jdbcType=VARCHAR}
                    </if>
                    <if test="merchantNo != null and merchantNo != ''">
                        AND g.merchant_no = #{merchantNo, jdbcType=VARCHAR}
                    </if>
                    <if test="storeNo != null and storeNo != ''">
                        AND g.store_no = #{storeNo, jdbcType=VARCHAR}
                    </if>
                    <include refid="Base_Column_Where"/>
                </trim>
            </where>
            GROUP BY loss_report_no
        ) AS b
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="lossReportNos != null and lossReportNos.size() > 0">
                    and ri.loss_report_no IN
                    <foreach collection="lossReportNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="preTotal != null and preTotal > 0">
                    AND FIND_IN_SET(ri.id, b.ids) BETWEEN 1 AND #{preTotal, jdbcType=INTEGER}
                </if>
                <if test="lossReportNo != null and lossReportNo != ''">
                    AND ri.loss_report_no = #{lossReportNo, jdbcType=VARCHAR}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    AND ri.merchant_no = #{merchantNo, jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    AND ri.store_no = #{storeNo, jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where_R"/>
            </trim>
        </where>
        ORDER BY ri.loss_report_no ASC, ri.id ASC
    </select>

</mapper>
