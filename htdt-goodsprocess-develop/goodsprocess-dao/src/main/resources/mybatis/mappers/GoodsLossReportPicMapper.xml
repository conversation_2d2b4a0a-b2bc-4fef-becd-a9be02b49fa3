<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsLossReportPicDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsLossReportPicDomain">
        <result column="id" property="id" />
        <result column="loss_report_pic_no" property="lossReportPicNo" />
        <result column="loss_report_no" property="lossReportNo" />
        <result column="sort_value" property="sortValue" />
        <result column="picture_url" property="pictureUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        loss_report_pic_no, loss_report_no, sort_value, picture_url
    </sql>

    <!--根据条件获取报损单明细list -->
    <select id="selectGoodsLossReportPics" parameterType="cn.htdt.goodsprocess.domain.GoodsLossReportPicDomain" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from goods_loss_report_pic
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="lossReportNo != null and lossReportNo != ''">
                    AND loss_report_no = #{lossReportNo, jdbcType=VARCHAR}
                </if>
            </trim>
        </where>
    </select>

</mapper>
