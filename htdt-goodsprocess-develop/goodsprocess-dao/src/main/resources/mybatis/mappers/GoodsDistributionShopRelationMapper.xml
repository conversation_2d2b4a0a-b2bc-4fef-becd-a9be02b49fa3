<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsDistributionShopRelationDao">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="cn.htdt.goodsprocess.vo.AtomGoodsDistributionShopRelationVo">
		<result column="id" property="id" />
		<result column="create_name" property="createName" />
		<result column="create_time" property="createTime" />
		<result column="modify_name" property="modifyName" />
		<result column="modify_time" property="modifyTime" />
		<result column="delete_flag" property="deleteFlag" />
		<result column="relation_no" property="relationNo" />
		<result column="goods_no" property="goodsNo" />
		<result column="goods_groups_no" property="goodsGroupsNo" />
		<result column="shop_goods_no" property="shopGoodsNo" />
		<result column="merchant_no" property="merchantNo" />
		<result column="merchant_name" property="merchantName" />
		<result column="company_no" property="companyNo" />
		<result column="company_name" property="companyName" />
		<result column="store_no" property="storeNo" />
		<result column="store_name" property="storeName" />
		<result column="branch_no" property="branchNo" />
		<result column="branch_name" property="branchName" />
		<result column="disable_flag" property="disableFlag" />
		<result column="create_no" property="createNo" />
		<result column="modify_no" property="modifyNo" />
		<result column="goods_name" property="goodsName" />
		<result column="goods_status" property="goodsStatus" />
		<result column="distribution_status" property="distributionStatus" />
		<result column="audit_status" property="auditStatus" />
		<result column="goods_distribution_type" property="goodsDistributionType" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		r.id,
		r.create_name,
		r.create_time,
		r.modify_name,
		r.modify_time,
		r.delete_flag,
		r.relation_no, r.goods_no, r.shop_goods_no,
		r.merchant_no,
		r.merchant_name, r.company_no,
		r.company_name, r.store_no,
		r.store_name,
		r.branch_no, r.branch_name,
		r.disable_flag, r.goods_distribution_type,
		r.create_no, r.modify_no,r.goods_groups_no
	</sql>

	<sql id="Base_Column_Where">
		and delete_flag = 1
	</sql>





	<!-- 逻辑删除分发店铺 -->
	<update id="logicDelete"
		parameterType="cn.htdt.goodsprocess.domain.GoodsDistributionShopRelationDomain">
		update
		goods_distribution_shop_relation
		set
		<if test="modifyNo != null">
			modify_no = #{modifyNo,jdbcType=VARCHAR},
		</if>
		<if test="modifyName != null">
			modify_name = #{modifyName,jdbcType=VARCHAR},
		</if>
		<if test="modifyTime != null">
			modify_time = #{modifyTime,jdbcType=TIMESTAMP},
		</if>
		delete_flag = 2
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="relationNo != null and relationNo != ''">
					and relation_no = #{relationNo}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</update>

	<!-- 批量删除 -->
	<update id="batchDelDistributionShop"
		parameterType="cn.htdt.goodsprocess.domain.GoodsDistributionShopRelationDomain">
		update
		goods_distribution_shop_relation
		set
		<if test="modifyNo != null">
			modify_no = #{modifyNo,jdbcType=VARCHAR},
		</if>
		<if test="modifyName != null">
			modify_name = #{modifyName,jdbcType=VARCHAR},
		</if>
		<if test="modifyTime != null">
			modify_time = #{modifyTime,jdbcType=TIMESTAMP},
		</if>
		delete_flag = 2
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				and relation_no in
				<foreach collection="relationNos" item="item" index="index"
					open="(" separator="," close=")">
					#{item}
				</foreach>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</update>



	<!-- 修改 -->
	<update id="updateDistributionShop"
		parameterType="cn.htdt.goodsprocess.vo.AtomGoodsDistributionShopRelationVo">
		update
		goods_distribution_shop_relation
		<set>
			<if test="goodsNo != null">
				goods_no = #{goodsNo,jdbcType=VARCHAR},
			</if>
			<if test="shopGoodsNo != null">
				shop_goods_no = #{shopGoodsNo,jdbcType=VARCHAR},
			</if>
			<if test="goodsGroupsNo != null and goodsGroupsNo != ''">
				 goods_groups_no = #{goodsGroupsNo,jdbcType=VARCHAR},
			</if>
			<if test="merchantNo != null">
				merchant_no = #{merchantNo,jdbcType=VARCHAR},
			</if>
			<if test="merchantName != null">
				merchant_name = #{merchantName,jdbcType=VARCHAR},
			</if>
			<if test="companyNo != null">
				company_no = #{companyNo,jdbcType=VARCHAR},
			</if>
			<if test="companyName != null">
				company_name = #{companyName,jdbcType=VARCHAR},
			</if>
			<if test="storeNo != null">
				store_no = #{storeNo,jdbcType=VARCHAR},
			</if>
			<if test="storeName != null">
				store_name = #{storeName,jdbcType=VARCHAR},
			</if>
			<if test="branchNo != null">
				branch_no = #{branchNo,jdbcType=VARCHAR},
			</if>
			<if test="branchName != null">
				branch_name = #{branchName,jdbcType=VARCHAR},
			</if>
			<if test="disableFlag != null">
				disable_flag = #{disableFlag,jdbcType=TINYINT},
			</if>
			<if test="createNo != null">
				create_no = #{createNo,jdbcType=VARCHAR},
			</if>
			<if test="createName != null">
				create_name = #{createName,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="modifyNo != null">
				modify_no = #{modifyNo,jdbcType=VARCHAR},
			</if>
			<if test="modifyName != null">
				modify_name = #{modifyName,jdbcType=VARCHAR},
			</if>
			<if test="modifyTime != null">
				modify_time = #{modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null">
				delete_flag = #{deleteFlag,jdbcType=TINYINT},
			</if>
		</set>
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="relationNo != null and relationNo != ''">
					and relation_no = #{relationNo}
				</if>
				<if test="goodsNo != null and goodsNo != ''">
					and goods_no = #{goodsNo}
				</if>
				<if test="goodsGroupsNo != null and goodsGroupsNo != ''">
					and goods_groups_no = #{goodsGroupsNo}
				</if>
				<if test="storeNoList != null and storeNoList.size() > 0">
					and store_no in
					<foreach collection="storeNoList" item="item" index="index"
							 open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="goodsNoList != null and goodsNoList.size() != ''">
					and goods_no in
					<foreach collection="goodsNoList" item="item" index="index"
							 open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="goodsGroupsNoList != null and goodsGroupsNoList.size() > 0">
					and goods_groups_no in
					<foreach collection="goodsGroupsNoList" item="item" index="index"
							 open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</update>



	<!-- 批量修改 -->
	<update id="batchUpdateDistributionShop" parameterType="java.util.List">
		<foreach collection="list" item="item" index="index" open=""
			close="" separator=";">

			update goods_distribution_shop_relation
			<set>
				<if test="goodsNo != null">
					goods_no = #{goodsNo,jdbcType=VARCHAR},
				</if>
				<if test="shopGoodsNo != null">
					shop_goods_no = #{shopGoodsNo,jdbcType=VARCHAR},
				</if>
				<if test="goodsGroupsNo != null and goodsGroupsNo != ''">
					goods_groups_no = #{goodsGroupsNo,jdbcType=VARCHAR},
				</if>
				<if test="merchantNo != null">
					merchant_no = #{merchantNo,jdbcType=VARCHAR},
				</if>
				<if test="merchantName != null">
					merchant_name = #{merchantName,jdbcType=VARCHAR},
				</if>
				<if test="companyNo != null">
					company_no = #{companyNo,jdbcType=VARCHAR},
				</if>
				<if test="companyName != null">
					company_name = #{companyName,jdbcType=VARCHAR},
				</if>
				<if test="storeNo != null">
					store_no = #{storeNo,jdbcType=VARCHAR},
				</if>
				<if test="storeName != null">
					store_name = #{storeName,jdbcType=VARCHAR},
				</if>
				<if test="branchNo != null">
					branch_no = #{branchNo,jdbcType=VARCHAR},
				</if>
				<if test="branchName != null">
					branch_name = #{branchName,jdbcType=VARCHAR},
				</if>
				<if test="disableFlag != null">
					disable_flag = #{disableFlag,jdbcType=TINYINT},
				</if>
				<if test="createNo != null">
					create_no = #{createNo,jdbcType=VARCHAR},
				</if>
				<if test="createName != null">
					create_name = #{createName,jdbcType=VARCHAR},
				</if>
				<if test="createTime != null">
					create_time = #{createTime,jdbcType=TIMESTAMP},
				</if>
				<if test="modifyNo != null">
					modify_no = #{modifyNo,jdbcType=VARCHAR},
				</if>
				<if test="modifyName != null">
					modify_name = #{modifyName,jdbcType=VARCHAR},
				</if>
				<if test="modifyTime != null">
					modify_time = #{modifyTime,jdbcType=TIMESTAMP},
				</if>
				<if test="deleteFlag != null">
					delete_flag = #{deleteFlag,jdbcType=TINYINT},
				</if>
			</set>
			<where>
				<trim suffixOverrides="AND | OR" prefix="1=1">
					<if test="relationNo != null and relationNo != ''">
						and relation_no = #{relationNo}
					</if>
					<include refid="Base_Column_Where" />
				</trim>
			</where>
		</foreach>
	</update>

	<select id="selectByParams" resultType="cn.htdt.goodsprocess.vo.AtomGoodsDistributionShopRelationVo">
		select
		<include refid="Base_Column_List" />,g.audit_status,g.goods_status,g.distribution_status
		from
		goods_distribution_shop_relation r
		left join goods g on r.goods_no = g.merchant_goods_no and r.store_no = g.store_no and g.delete_flag = 1
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="relationNo != null and relationNo != ''">
					and r.relation_no = #{relationNo}
				</if>
				<if test="goodsNo != null and goodsNo != ''">
					and r.goods_no = #{goodsNo}
				</if>
				<if test="goodsNoList != null and goodsNoList.size() != ''">
					and r.goods_no in
					<foreach collection="goodsNoList" item="item" index="index"
							 open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="goodsGroupsNo != null and goodsGroupsNo != ''">
					and r.goods_groups_no = #{goodsGroupsNo}
				</if>
				<if test="goodsGroupsNoList != null and goodsGroupsNoList.size() > 0">
					and r.goods_groups_no in
					<foreach collection="goodsGroupsNoList" item="item" index="index"
							 open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="shopGoodsNo != null and shopGoodsNo != ''">
					and r.shop_goods_no = #{shopGoodsNo}
				</if>
				AND r.delete_flag = 1
			</trim>
		</where>
	</select>

	<select id="selectGoodsDistributionShopRelation" resultType="cn.htdt.goodsprocess.vo.AtomGoodsDistributionShopRelationVo" >
		select
		<include refid="Base_Column_List"/>
		from
		goods_distribution_shop_relation r
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="relationNo != null and relationNo != ''">
					and r.relation_no = #{relationNo}
				</if>
				<if test="goodsNo != null and goodsNo != ''">
					and r.goods_no = #{goodsNo}
				</if>
				<if test="goodsNoList != null and goodsNoList.size() != ''">
					and r.goods_no in
					<foreach collection="goodsNoList" item="item" index="index"
							 open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="goodsGroupsNo != null and goodsGroupsNo != ''">
					and r.goods_groups_no = #{goodsGroupsNo}
				</if>
				<if test="goodsGroupsNoList != null and goodsGroupsNoList.size() > 0">
					and r.goods_groups_no in
					<foreach collection="goodsGroupsNoList" item="item" index="index"
							 open="(" separator="," close=")">
						#{item}
					</foreach>
				</if>
				<if test="goodsDistributionType != null and goodsDistributionType != ''">
					and r.goods_distribution_type = #{goodsDistributionType}
				</if>
				AND r.delete_flag = 1
			</trim>
		</where>
	</select>
	
	<!-- 根据商品编号删除分发店铺 -->
	<update id="delGoodsDistributionRelationByGoodsNo"
		parameterType="cn.htdt.goodsprocess.domain.GoodsDistributionShopRelationDomain">
		update
		goods_distribution_shop_relation
		set
		<if test="modifyNo != null">
			modify_no = #{modifyNo,jdbcType=VARCHAR},
		</if>
		<if test="modifyName != null">
			modify_name = #{modifyName,jdbcType=VARCHAR},
		</if>
		<if test="modifyTime != null">
			modify_time = #{modifyTime,jdbcType=TIMESTAMP},
		</if>
		delete_flag = 2
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="goodsNo != null and goodsNo != ''">
					and goods_no = #{goodsNo}
				</if>
				<if test="goodsGroupsNo != null and goodsGroupsNo != ''">
					and  goods_groups_no = #{goodsGroupsNo}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</update>

</mapper>
