<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsAssociationRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsAssociationRelationDomain">
        <result column="id" property="id" />
        <result column="create_no" property="createNo" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_no" property="modifyNo" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="goods_association_no" property="goodsAssociationNo" />
        <result column="goods_no" property="goodsNo" />
        <result column="association_goods_num" property="associationGoodsNum" />
        <result column="real_sale_price" property="realSalePrice" />
        <result column="disable_flag" property="disableFlag" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_no,
        create_name,
        create_time,
        modify_no,
        modify_name,
        modify_time,
        delete_flag,
        goods_association_no, goods_no, association_goods_num, real_sale_price, disable_flag, merchant_no, merchant_name, store_no, store_name
    </sql>
    <delete id="deleteByGoodsAssociationNo">
        delete from goods_association_relation where goods_association_no = #{goodsAssociationNo}
    </delete>
</mapper>
