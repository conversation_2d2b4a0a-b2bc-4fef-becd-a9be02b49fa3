<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.StoreCategoryChooseDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.StoreCategoryChooseDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="choose_type" property="chooseType"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        choose_type, merchant_no, merchant_name, store_no, store_name
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.StoreCategoryChooseDomain">
        select
        <include refid="Base_Column_List"/>
        from
        store_category_choose
        <where>
            store_no = #{storeNo}
            <include refid="Base_Column_Where"/>
        </where>
    </select>

    <update id="updateStoreCategoryChoose" parameterType="cn.htdt.goodsprocess.domain.StoreCategoryChooseDomain">
        update
        store_category_choose
        <set>
            <if test="chooseType != null and chooseType !=''">
                choose_type = #{chooseType,jdbcType=INTEGER},
            </if>
            <if test="modifyNo != null and modifyNo !=''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName !=''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            store_no = #{storeNo}
            <include refid="Base_Column_Where"/>
        </where>
    </update>
</mapper>
