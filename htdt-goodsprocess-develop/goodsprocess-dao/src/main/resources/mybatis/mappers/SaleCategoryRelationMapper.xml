<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.SaleCategoryRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.vo.AtomResSaleCategoryVo">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="category_no" property="categoryNo"/>
        <result column="category_name" property="categoryName"/>
        <result column="full_id_path" property="fullIdPath"/>
        <result column="full_name_path" property="fullNamePath"/>
        <result column="category_level" property="categoryLevel"/>
        <result column="modify_flag" property="modifyFlag"/>
        <result column="parent_no" property="parentNo"/>
        <result column="category_source" property="categorySource"/>
        <result column="first_category_no" property="firstCategoryNo"/>
        <result column="second_category_no" property="secondCategoryNo"/>
        <result column="third_category_no" property="thirdCategoryNo"/>
        <result column="picture_url" property="pictureUrl"/>
        <result column="sort_value" property="sortValue"/>
        <result column="mdm_category_no" property="mdmCategoryNo"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="mdm_disable_flag" property="mdmDisableFlag"/>
        <result column="selectFlag" property="selectFlag"/>
    </resultMap>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectSaleCategory" parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryVo"
            resultMap="BaseResultMap">
        SELECT distinct
        sc.category_no,
        sc.category_name,
        sc.full_id_path,
        sc.full_name_path,
        sc.category_level,
        sc.modify_flag,
        sc.parent_no,
        sc.category_source,
        sc.first_category_no,
        sc.second_category_no,
        sc.third_category_no,
        sc.picture_url,
        sc.sort_value,
        sc.mdm_category_no,
        sc.disable_flag,
        sc.mdm_disable_flag,
        IF
        ( scr.id, '2', '1' ) selectFlag
        FROM
        sale_category sc
        LEFT JOIN sale_category_relation scr
        ON scr.category_no = sc.category_no
        AND scr.merchant_no = #{merchantNo}
        AND scr.store_no = #{storeNo}
        <where>
            sc.delete_flag = 1
            <if test="categoryNo != null and categoryNo != ''">
                and sc.category_no = #{categoryNo}
            </if>
            <if test="disableFlag != null">
                and sc.disable_flag = 2
            </if>
            <if test="fullNamePath != null and fullNamePath != ''">
                and sc.full_name_path LIKE CONCAT(CONCAT('%',#{fullNamePath}),'%')
            </if>
        </where>
        order by
        sc.parent_no,
        sc.sort_value
    </select>

    <delete id="deleteSaleCategoryRelationMerchant"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryRelationVo">
        DELETE
        FROM
        sale_category_relation
        <where>
            category_type = #{categoryType}
            AND merchant_no = #{merchantNo}
            AND store_no = ''
            <if test="fullIdPath != null and fullIdPath != ''">
                AND category_no IN (
                SELECT
                category_no
                FROM
                sale_category
                WHERE
                full_id_path LIKE CONCAT (#{fullIdPath},'%')
                )
            </if>
            <if test="categoryNoList != null and categoryNoList.size() > 0">
                and category_no IN
                <foreach item="categoryNo" collection="categoryNoList" open="(" close=")" separator=",">
                    #{categoryNo}
                </foreach>
            </if>
        </where>
    </delete>

    <delete id="deleteSaleCategoryRelationStore" parameterType="cn.htdt.goodsprocess.vo.AtomReqSaleCategoryRelationVo">
        DELETE
        FROM
        sale_category_relation
        <where>
            category_type = #{categoryType}
            AND merchant_no = #{merchantNo}
            AND store_no = #{storeNo}
            <if test="fullIdPath != null and fullIdPath != ''">
                AND category_no IN (
                SELECT
                category_no
                FROM
                sale_category
                WHERE
                full_id_path LIKE CONCAT (#{fullIdPath},'%')
                )
            </if>
            <if test="categoryNoList != null and categoryNoList.size() > 0">
                and category_no IN
                <foreach item="categoryNo" collection="categoryNoList" open="(" close=")" separator=",">
                    #{categoryNo}
                </foreach>
            </if>
        </where>
    </delete>

</mapper>
