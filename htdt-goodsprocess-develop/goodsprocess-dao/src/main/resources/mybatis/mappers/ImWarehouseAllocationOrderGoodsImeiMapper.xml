<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.ImWarehouseAllocationOrderGoodsImeiDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsImeiDomain">
        <result column="id" property="id" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="allocation_order_no" property="allocationOrderNo" />
        <result column="goods_no" property="goodsNo" />
        <result column="out_warehouse_no" property="outWarehouseNo" />
        <result column="imei" property="imei" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
        <result column="company_no" property="companyNo" />
        <result column="company_name" property="companyName" />
        <result column="branch_no" property="branchNo" />
        <result column="branch_name" property="branchName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        allocation_order_no, goods_no, out_warehouse_no, imei, merchant_no, merchant_name, store_no, store_name, disable_flag, create_no, modify_no, company_no, company_name, branch_no, branch_name
    </sql>
    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsImeiDomain">
        insert into
            im_warehouse_allocation_order_goods_imei
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="allocationOrderNo != null" >
                allocation_order_no,
            </if>
            <if test="goodsNo != null" >
                goods_no,
            </if>
            <if test="outWarehouseNo != null" >
                out_warehouse_no,
            </if>
            <if test="imei != null" >
                imei,
            </if>
            <if test="merchantNo != null" >
                merchant_no,
            </if>
            <if test="merchantName != null" >
                merchant_name,
            </if>
            <if test="storeNo != null" >
                store_no,
            </if>
            <if test="storeName != null" >
                store_name,
            </if>
            <if test="disableFlag != null" >
                disable_flag,
            </if>
            <if test="createNo != null" >
                create_no,
            </if>
            <if test="createName != null" >
                create_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyNo != null" >
                modify_no,
            </if>
            <if test="modifyName != null" >
                modify_name,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="deleteFlag != null" >
                delete_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="allocationOrderNo != null" >
                #{allocationOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsNo != null" >
                #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="outWarehouseNo != null" >
                #{outWarehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="imei != null" >
                #{imei,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null" >
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null" >
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null" >
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null" >
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null" >
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null" >
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null" >
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null" >
                #{deleteFlag,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <delete id="trueDelete" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsImeiDomain">
        DELETE FROM
          im_warehouse_allocation_order_goods_imei
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="outWarehouseNo != null and outWarehouseNo != ''">
                    and out_warehouse_no = #{outWarehouseNo}
                </if>
            </trim>
        </where>

    </delete>
    <select id="selectByParams" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsImeiDomain" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from
    im_warehouse_allocation_order_goods_imei
    <where>
        <trim suffixOverrides="AND | OR" prefix="1=1">
            <if test="allocationOrderNo != null and allocationOrderNo != ''">
                and allocation_order_no = #{allocationOrderNo}
            </if>
            <if test="goodsNo != null and goodsNo != ''">
                and goods_no = #{goodsNo}
            </if>
            <if test="outWarehouseNo != null and outWarehouseNo != ''">
                and out_warehouse_no = #{outWarehouseNo}
            </if>
            <if test="storeNo != null and storeNo != ''">
                and store_no = #{storeNo}
            </if>
            <if test="merchantNo != null and merchantNo != ''">
                and merchant_no = #{merchantNo}
            </if>
        </trim>
    </where>
</select>
    <select id="getWarehouseAllocationOrderGoodsImeiList" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsImeiDomain" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from
        im_warehouse_allocation_order_goods_imei
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
            </trim>
        </where>
    </select>

</mapper>
