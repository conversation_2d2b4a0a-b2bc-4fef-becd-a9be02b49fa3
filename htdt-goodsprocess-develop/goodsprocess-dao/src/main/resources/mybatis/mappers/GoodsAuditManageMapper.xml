<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsAuditManageDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsAuditManageDomain">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="audit_type" property="auditType"/>
        <result column="goods_main_data_flag" property="goodsMainDataFlag"/>
        <result column="goods_audit_flag" property="goodsAuditFlag"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="audit_manage_no" property="auditManageNo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        audit_type, goods_main_data_flag, goods_audit_flag, disable_flag, create_no, modify_no,audit_manage_no
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.GoodsAuditManageDomain">
        select
        <include refid="Base_Column_List"/>
        from
        goods_audit_manage
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="auditManageNo != null and auditManageNo != ''">
                    and audit_manage_no = #{auditManageNo}
                </if>
                <if test="auditType != null and auditType != ''">
                    and audit_type = #{auditType}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        ORDER BY
        audit_type
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsAuditManageDomain">
        update
        goods_audit_manage
        <set>
            <if test="goodsMainDataFlag != null and goodsMainDataFlag != ''">
                goods_main_data_flag = #{goodsMainDataFlag,jdbcType=VARCHAR},
            </if>
            <if test="goodsAuditFlag != null and goodsAuditFlag != ''">
                goods_audit_flag = #{goodsAuditFlag,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="auditManageNo != null">
                    and audit_manage_no = #{auditManageNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.GoodsAuditManageDomain">
        update
        goods_audit_manage
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="auditManageNo != null">
                    and audit_manage_no = #{auditManageNo}
                </if>
            </trim>
        </where>
    </update>
</mapper>
