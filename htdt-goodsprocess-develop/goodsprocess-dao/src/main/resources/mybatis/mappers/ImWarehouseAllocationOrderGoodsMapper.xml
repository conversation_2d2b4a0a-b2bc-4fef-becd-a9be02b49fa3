<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.ImWarehouseAllocationOrderGoodsDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.vo.AtomWarehouseAllocationOrderVo">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="allocation_order_no" property="allocationOrderNo"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="goods_unit" property="goodsUnit"/>
        <result column="allocation_num" property="allocationNum"/>
        <result column="out_warehouse_stock_num" property="outWarehouseStockNum"/>
        <result column="out_warehouse_no" property="outWarehouseNo"/>
        <result column="out_warehouse_code" property="outWarehouseCode"/>
        <result column="out_warehouse_name" property="outWarehouseName"/>
        <result column="in_warehouse_stock_num" property="inWarehouseStockNum"/>
        <result column="in_warehouse_no" property="inWarehouseNo"/>
        <result column="in_warehouse_code" property="inWarehouseCode"/>
        <result column="in_warehouse_name" property="inWarehouseName"/>
        <result column="delivery_num" property="deliveryNum"/>
        <result column="receive_num" property="receiveNum"/>
        <result column="order_status" property="orderStatus"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="goodsNames" property="goodsNames"/>
        <result column="allocationNums" property="allocationNums"/>
        <result column="out_stock_num" property="outStockNum"/>
        <result column="wait_out_stock_num" property="waitOutStockNum"/>
        <result column="out_modify_time" property="outModifyTime"/>
        <result column="first_attribute_value_name" property="firstAttributeValueName"/>
        <result column="second_attribute_value_name" property="secondAttributeValueName"/>
        <result column="third_attribute_value_name" property="thirdAttributeValueName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        out_stock_num,
        out_modify_time,
        wait_out_stock_num,
        <!-- 2023-08-31蛋品-盛守武-调拨管理 -->
        allocation_order_no, goods_no, goods_name, goods_unit, allocation_num, out_warehouse_stock_num, out_warehouse_no, out_warehouse_code, out_warehouse_name, in_warehouse_stock_num, in_warehouse_no, in_warehouse_code, in_warehouse_name, delivery_num, receive_num, order_status, merchant_no, merchant_name, store_no, store_name, disable_flag, create_no, modify_no, raw_goods_no, multi_unit_allocation_num
    </sql>

    <sql id="Ware_Column_Goods_List">
        i.id,
        i.create_name,
        i.create_time,
        i.modify_name,
        i.modify_time,
        i.delete_flag,
        i.out_stock_num,
        i.out_modify_time,
        i.wait_out_stock_num,
        i.allocation_order_no, i.goods_no, i.goods_name, i.goods_unit, i.allocation_num, i.out_warehouse_stock_num, i.out_warehouse_no, i.out_warehouse_code, i.out_warehouse_name, i.in_warehouse_stock_num, i.in_warehouse_no, i.in_warehouse_code, i.in_warehouse_name, i.delivery_num, i.receive_num, i.order_status, i.merchant_no, i.merchant_name, i.store_no, i.store_name, i.disable_flag, i.create_no, i.modify_no
    </sql>

    <sql id="Goods_Column_Goods_List">
        g.barcode,g.goods_help_code,g.assist_calculation_unit_no,g.calculation_unit_no,
        g.imei_flag,g.warehouse_flag, g.conversion_rate,g.goods_name
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <sql id="Base_Column_Goods_Where">
        and i.delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain">
        select
        <include refid="Base_Column_List"/>
        from
        im_warehouse_allocation_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain">
        insert into
        im_warehouse_allocation_order_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="allocationOrderNo != null">
                allocation_order_no,
            </if>
            <if test="goodsNo != null">
                goods_no,
            </if>
            <if test="goodsName != null">
                goods_name,
            </if>
            <if test="goodsUnit != null">
                goods_unit,
            </if>
            <if test="allocationNum != null">
                allocation_num,
            </if>
            <if test="outWarehouseStockNum != null">
                out_warehouse_stock_num,
            </if>
            <if test="outWarehouseNo != null">
                out_warehouse_no,
            </if>
            <if test="outWarehouseCode != null">
                out_warehouse_code,
            </if>
            <if test="outWarehouseName != null">
                out_warehouse_name,
            </if>
            <if test="inWarehouseStockNum != null">
                in_warehouse_stock_num,
            </if>
            <if test="inWarehouseNo != null">
                in_warehouse_no,
            </if>
            <if test="inWarehouseCode != null">
                in_warehouse_code,
            </if>
            <if test="inWarehouseName != null">
                in_warehouse_name,
            </if>
            <if test="waitOutStockNum != null">
                wait_out_stock_num,
            </if>
            <if test="deliveryNum != null">
                delivery_num,
            </if>
            <if test="receiveNum != null">
                receive_num,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="merchantNo != null">
                merchant_no,
            </if>
            <if test="merchantName != null">
                merchant_name,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
            <if test="disableFlag != null">
                disable_flag,
            </if>
            <if test="createNo != null">
                create_no,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyNo != null">
                modify_no,
            </if>
            <if test="modifyName != null">
                modify_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <!--2023-08-31蛋品-盛守武-调拨管理-->
            <if test="multiUnitAllocationNum != null">
                multi_unit_allocation_num,
            </if>
            <if test="rawGoodsNo != null">
                raw_goods_no,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="allocationOrderNo != null">
                #{allocationOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsNo != null">
                #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsUnit != null">
                #{goodsUnit,jdbcType=VARCHAR},
            </if>
            <if test="allocationNum != null">
                #{allocationNum,jdbcType=DECIMAL},
            </if>
            <if test="outWarehouseStockNum != null">
                #{outWarehouseStockNum,jdbcType=DECIMAL},
            </if>
            <if test="outWarehouseNo != null">
                #{outWarehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="outWarehouseCode != null">
                #{outWarehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="outWarehouseName != null">
                #{outWarehouseName,jdbcType=VARCHAR},
            </if>
            <if test="inWarehouseStockNum != null">
                #{inWarehouseStockNum,jdbcType=DECIMAL},
            </if>
            <if test="inWarehouseNo != null">
                #{inWarehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="inWarehouseCode != null">
                #{inWarehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="inWarehouseName != null">
                #{inWarehouseName,jdbcType=VARCHAR},
            </if>
            <if test="waitOutStockNum != null">
                #{waitOutStockNum,jdbcType=DECIMAL},
            </if>
            <if test="deliveryNum != null">
                #{deliveryNum,jdbcType=DECIMAL},
            </if>
            <if test="receiveNum != null">
                #{receiveNum,jdbcType=DECIMAL},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="merchantNo != null">
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <!--2023-08-31蛋品-盛守武-调拨管理-->
            <if test="multiUnitAllocationNum != null">
                #{multiUnitAllocationNum,jdbcType=DECIMAL},
            </if>
            <if test="rawGoodsNo != null">
                #{rawGoodsNo,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain">
        update
        im_warehouse_allocation_order_goods
        <set>
            <if test="allocationOrderNo != null">
                allocation_order_no = #{allocationOrderNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsNo != null">
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="goodsUnit != null">
                goods_unit = #{goodsUnit,jdbcType=VARCHAR},
            </if>
            <if test="allocationNum != null">
                allocation_num = #{allocationNum,jdbcType=DECIMAL},
            </if>
            <if test="outWarehouseStockNum != null">
                out_warehouse_stock_num = #{outWarehouseStockNum,jdbcType=DECIMAL},
            </if>
            <if test="outWarehouseNo != null">
                out_warehouse_no = #{outWarehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="outWarehouseCode != null">
                out_warehouse_code = #{outWarehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="outWarehouseName != null">
                out_warehouse_name = #{outWarehouseName,jdbcType=VARCHAR},
            </if>
            <if test="inWarehouseStockNum != null">
                in_warehouse_stock_num = #{inWarehouseStockNum,jdbcType=DECIMAL},
            </if>
            <if test="inWarehouseNo != null">
                in_warehouse_no = #{inWarehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="inWarehouseCode != null">
                in_warehouse_code = #{inWarehouseCode,jdbcType=VARCHAR},
            </if>
            <if test="inWarehouseName != null">
                in_warehouse_name = #{inWarehouseName,jdbcType=VARCHAR},
            </if>
            <if test="deliveryNum != null">
                delivery_num = #{deliveryNum,jdbcType=DECIMAL},
            </if>
            <if test="receiveNum != null">
                receive_num = #{receiveNum,jdbcType=DECIMAL},
            </if>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=INTEGER},
            </if>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain">
        update
        im_warehouse_allocation_order_goods
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        </if>
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
            </trim>
        </where>
    </update>

    <sql id="Select_Base_Column_List">
        o.allocation_order_no,
        o.out_warehouse_name,
        o.in_warehouse_name,
        o.out_warehouse_no,
        o.in_warehouse_no,
        GROUP_CONCAT(g.goods_name,
            IF( gs.first_attribute_value_name != '', CONCAT( '-', gs.first_attribute_value_name ), '' ),
            IF( gs.second_attribute_value_name != '', CONCAT( '-', gs.second_attribute_value_name ), '' ),
            IF( gs.third_attribute_value_name != '', CONCAT( '-', gs.third_attribute_value_name ), '' )) goodsNames,
        SUM(g.allocation_num) allocationNums,
        o.order_status,
        o.create_no,
        o.create_name,
        o.create_time,
        o.modify_time
    </sql>

    <select id="selectWarehouseAllocationParams" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseAllocationOrderVo"
            resultMap="BaseResultMap">
        select
        <include refid="Select_Base_Column_List"/>
        from
        im_warehouse_allocation_order_goods g
        LEFT join
        im_warehouse_allocation_order o
        on
        g.allocation_order_no=o.allocation_order_no
        left join goods gs on g.goods_no = gs.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and o.allocation_order_no like concat(concat("%",#{allocationOrderNo,jdbcType=VARCHAR}),"%")
                </if>
                <if test="startTime != null and startTime != ''">
                    and o.create_time &gt;= #{startTime}
                </if>
                <if test="endTime != null and startTime != ''">
                    and o.create_time &lt;= #{endTime}
                </if>
                <if test="orderStatus != null">
                    and o.order_status = #{orderStatus,jdbcType=INTEGER}
                </if>
                <if test="outWarehouseNo != null and outWarehouseNo != ''">
                    and o.out_warehouse_no = #{outWarehouseNo,jdbcType=VARCHAR}
                </if>
                <if test="outWarehouseName != null and outWarehouseName != ''">
                    and o.out_warehouse_name = #{outWarehouseName,jdbcType=VARCHAR}
                </if>
                <if test="inWarehouseNo != null and inWarehouseNo != ''">
                    and o.in_warehouse_no = #{inWarehouseNo,jdbcType=VARCHAR}
                </if>
                <if test="inWarehouseName != null and inWarehouseName != ''">
                    and o.in_warehouse_name = #{inWarehouseName,jdbcType=VARCHAR}
                </if>
                <if test="allocationGoodsStr != null and allocationGoodsStr != ''">
                    and (o.allocation_order_no = #{allocationGoodsStr,jdbcType=VARCHAR} OR g.goods_name LIKE
                    CONCAT(CONCAT('%',#{allocationGoodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="goodsStr  != null and goodsStr  != ''">
                    and (g.goods_no like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%") OR g.goods_name LIKE
                    CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%'))
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and o.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and o.store_no = #{storeNo}
                </if>
                <if test="orderStatusList != null and orderStatusList.size() > 0">
                    and o.order_status IN
                    <foreach collection="orderStatusList" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and g.goods_no IN
                    <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="loginIdentity  != null and loginIdentity==1">
                    and gs.goods_source_type in ('1001','1002','1003')
                </if>
                <if test="loginIdentity  != null and loginIdentity==2">
                    and gs.goods_source_type in ('1002')
                </if>
                <if test="loginIdentity  != null and loginIdentity==4">
                    and gs.goods_source_type in ('1003')
                </if>
                <if test="loginIdentity  != null and loginIdentity==8">
                    and gs.goods_source_type in ('1003')
                </if>
                and o.delete_flag = 1
            </trim>
        </where>
        GROUP BY
        o.allocation_order_no,
        o.out_warehouse_name,
        o.in_warehouse_name,
        o.out_warehouse_no,
        o.in_warehouse_no,
        o.order_status,
        o.create_no,
        o.create_name,
        o.create_time,
        o.modify_time
        ORDER BY o.modify_time DESC
    </select>

    <delete id="trueDelete" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain">
        DELETE FROM
        im_warehouse_allocation_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
                <if test="outWarehouseName != null and outWarehouseName != ''">
                    and out_warehouse_name = #{outWarehouseName}
                </if>
                <if test="inWarehouseName != null and inWarehouseName != ''">
                    and in_warehouse_name = #{inWarehouseName}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
            </trim>
        </where>
    </delete>

    <update id="updateOutWarehouseAllocationOrderParams"
            parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseAllocationOrderVo">
        update
        im_warehouse_allocation_order_goods
        <set>
            <if test="stockNum != null">
                delivery_num = delivery_num + #{stockNum,jdbcType= DECIMAL},
                wait_out_stock_num = wait_out_stock_num - #{stockNum,jdbcType= DECIMAL},
                out_stock_num = out_stock_num + #{stockNum,jdbcType= DECIMAL},
                out_modify_time = #{outModifyTime,jdbcType=TIMESTAMP},
                order_status = 1,
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="outWarehouseNo != null and outWarehouseNo != ''">
                    and out_warehouse_no = #{outWarehouseNo}
                </if>
                <if test="inWarehouseNo != null and inWarehouseNo != ''">
                    and in_warehouse_no = #{inWarehouseNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="updateInWarehouseAllocationOrderParams"
            parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseAllocationOrderVo">
        update
        im_warehouse_allocation_order_goods
        <set>
            <if test="stockNum != null">
                out_stock_num = out_stock_num - #{stockNum,jdbcType= DECIMAL},
                receive_num = receive_num + #{stockNum,jdbcType= DECIMAL},
            </if>
            <if test="allocationNum.compareTo(receiveNum.add(stockNum)) == 0">
                order_status = 2,
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="outWarehouseNo != null and outWarehouseNo != ''">
                    and out_warehouse_no = #{outWarehouseNo}
                </if>
                <if test="inWarehouseNo != null and inWarehouseNo != ''">
                    and in_warehouse_no = #{inWarehouseNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>


    <update id="updateOutWarehouseAll" parameterType="java.util.List">
        <foreach collection="orderGoodsDomainList" item="item" index="index" open="" close="" separator=";">
            update
            im_warehouse_allocation_order_goods
            <set>
                <if test="item.stockNum != null">
                    delivery_num = delivery_num + #{item.stockNum},
                    out_stock_num = out_stock_num + #{item.stockNum},
                    out_modify_time = #{item.outModifyTime,jdbcType=TIMESTAMP},
                    wait_out_stock_num = 0,
                    order_status = 1 ,
                </if>
                <if test="item.modifyNo != null">
                    modify_no = #{item.modifyNo,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyName != null">
                    modify_name = #{item.modifyName,jdbcType=VARCHAR},
                </if>
            </set>
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.allocationOrderNo != null and item.allocationOrderNo != ''">
                        and allocation_order_no = #{item.allocationOrderNo}
                    </if>
                    <if test="item.goodsNo != null and item.goodsNo != ''">
                        and goods_no = #{item.goodsNo}
                    </if>
                    <if test="item.outWarehouseNo != null and item.outWarehouseNo != ''">
                        and out_warehouse_no = #{item.outWarehouseNo}
                    </if>
                    <if test="item.inWarehouseNo != null and item.inWarehouseNo != ''">
                        and in_warehouse_no = #{item.inWarehouseNo}
                    </if>
                </trim>
            </where>
        </foreach>
    </update>

    <update id="updateInWarehouseAll" parameterType="java.util.List">
        <foreach collection="orderGoodsDomainList" item="item" index="index" open="" close="" separator=";">
            update
            im_warehouse_allocation_order_goods
            <set>
                <if test="item.stockNum != null">
                    receive_num = receive_num + #{item.stockNum},
                    out_stock_num = 0,
                </if>
                <if test="item.allocationNum.compareTo(item.receiveNum.add(item.stockNum)) == 0">
                    order_status = 2,
                </if>
                <if test="item.modifyNo != null">
                    modify_no = #{item.modifyNo,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyName != null">
                    modify_name = #{item.modifyName,jdbcType=VARCHAR},
                </if>
            </set>
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.allocationOrderNo != null and item.allocationOrderNo != ''">
                        and allocation_order_no = #{item.allocationOrderNo}
                    </if>
                    <if test="item.goodsNo != null and item.goodsNo != ''">
                        and goods_no = #{item.goodsNo}
                    </if>
                    <if test="item.outWarehouseNo != null and item.outWarehouseNo != ''">
                        and out_warehouse_no = #{item.outWarehouseNo}
                    </if>
                    <if test="item.inWarehouseNo != null and item.inWarehouseNo != ''">
                        and in_warehouse_no = #{item.inWarehouseNo}
                    </if>
                </trim>
            </where>
        </foreach>
    </update>

    <update id="updateCompleteAllocationOrderGoods" parameterType="java.util.List">
        <foreach collection="orderGoodsDomainList" item="item" index="index" open="" close="" separator=";">
            update
            im_warehouse_allocation_order_goods
            <set>
                <if test="item.outStockNum != null">
                    wait_out_stock_num = wait_out_stock_num + #{item.outStockNum},
                    delivery_num = delivery_num - #{item.outStockNum},
                    out_stock_num = 0,
                    order_status = 2 ,
                </if>
                <if test="item.modifyNo != null">
                    modify_no = #{item.modifyNo,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyName != null">
                    modify_name = #{item.modifyName,jdbcType=VARCHAR},
                </if>
            </set>
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.allocationOrderNo != null and item.allocationOrderNo != ''">
                        and allocation_order_no = #{item.allocationOrderNo}
                    </if>
                    <if test="item.goodsNo != null and item.goodsNo != ''">
                        and goods_no = #{item.goodsNo}
                    </if>
                    <if test="item.outWarehouseNo != null and item.outWarehouseNo != ''">
                        and out_warehouse_no = #{item.outWarehouseNo}
                    </if>
                    <if test="item.inWarehouseNo != null and item.inWarehouseNo != ''">
                        and in_warehouse_no = #{item.inWarehouseNo}
                    </if>
                </trim>
            </where>
        </foreach>
    </update>

    <update id="updateRevertAllocationOrderGoods"
            parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain">
        update
        im_warehouse_allocation_order_goods
        <set>
            <if test="outStockNum != null">
                wait_out_stock_num = wait_out_stock_num + #{outStockNum},
                delivery_num = delivery_num - #{outStockNum},
                out_stock_num = 0,
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="outWarehouseNo != null and outWarehouseNo != ''">
                    and out_warehouse_no = #{outWarehouseNo}
                </if>
                <if test="inWarehouseNo != null and inWarehouseNo != ''">
                    and in_warehouse_no = #{inWarehouseNo}
                </if>
            </trim>
        </where>
    </update>

    <select id="getWarehouseAllocationOrder"
            parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain"
            resultType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain">
        select
        <include refid="Base_Column_List"/>
        from
        im_warehouse_allocation_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    AND allocation_order_no = #{allocationOrderNo,jdbcType=VARCHAR}
                </if>
                <if test="outWarehouseNo != null and outWarehouseNo != ''">
                    AND out_warehouse_no = #{outWarehouseNo,jdbcType=VARCHAR}
                </if>
                <if test="inWarehouseNo != null and inWarehouseNo != ''">
                    AND in_warehouse_no = #{inWarehouseNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    AND goods_no = #{goodsNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <select id="selectAllocationDetailWarehouseGoods" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseAllocationOrderVo">
        select
        <include refid="Ware_Column_Goods_List" />,
        <include refid="Goods_Column_Goods_List" />,
        (select count(d.id)  from im_warehouse_allocation_order_goods d where d.delete_flag=1 and d.allocation_order_no = #{allocationOrderNo}) as totalCount
        from
        im_warehouse_allocation_order_goods i
        left join goods g on i.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and i.allocation_order_no = #{allocationOrderNo}
                </if>
                <include refid="Base_Column_Goods_Where" />
            </trim>
        </where>
        order by i.create_time desc
    </select>

    <delete id="deleteAllocationOrderNo"
            parameterType="cn.htdt.goodsprocess.domain.ImWarehouseAllocationOrderGoodsDomain">
        DELETE FROM
        im_warehouse_allocation_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNo != null and allocationOrderNo != ''">
                    and allocation_order_no = #{allocationOrderNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </delete>

    <!-- 查询调拨的商品的数量-批量 -->
    <select id="selectBatchAllocationGoodsCount" resultType="cn.htdt.goodsprocess.vo.AtomWarehouseAllocationOrderVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomWarehouseAllocationOrderVo">
        select i.allocation_order_no, count(1) totalCount from im_warehouse_allocation_order_goods i
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="allocationOrderNos != null and allocationOrderNos.size() > 0">
                    and i.allocation_order_no IN
                    <foreach item="allocationOrderNo" collection="allocationOrderNos" open="(" close=")" separator=",">
                        #{allocationOrderNo}
                    </foreach>
                </if>
                <include refid="Base_Column_Goods_Where"/>
            </trim>
        </where>
        group by i.allocation_order_no
    </select>

</mapper>
