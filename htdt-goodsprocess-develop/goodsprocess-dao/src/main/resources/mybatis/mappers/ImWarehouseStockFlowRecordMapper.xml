<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.ImWarehouseStockFlowRecordDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.vo.AtomWareStockFlowVo">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="warehouse_no" property="warehouseNo"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="oper_type" property="operType"/>
        <result column="bill_type" property="billType"/>
        <result column="bill_code" property="billCode"/>
        <result column="sub_bill_code" property="subBillCode"/>
        <result column="current_stock_num" property="currentStockNum"/>
        <result column="stock_num" property="stockNum"/>
        <result column="calculation_unit_name" property="calculationUnitName"/>
        <result column="assist_calculation_unit_name" property="assistCalculationUnitName"/>
        <result column="conversion_rate" property="conversionRate"/>
        <result column="warehouse_flag" property="warehouseFlag"/>
        <result column="inventory_type" property="inventoryType"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
        <result column="purchase_num" property="purchaseNum"/>
        <result column="purchase_unit" property="purchaseUnit"/>
        <result column="purchase_unit_price" property="purchaseUnitPrice"/>
        <result column="returned_count" property="returnedCount"/>
        <result column="parent_goods_no" property="parentGoodsNo"/>
        <result column="original_goods_no" property="originalGoodsNo"/>
        <result column="goods_form" property="goodsForm"/>
        <result column="attribute_value_name" property="attributeValueName"/>
        <result column="return_code" property="returnCode"/>
        <result column="return_goods_code" property="returnGoodsCode"/>
        <result column="first_attribute_value_name" property="firstAttributeValueName"/>
        <result column="second_attribute_value_name" property="secondAttributeValueName"/>
        <result column="third_attribute_value_name" property="thirdAttributeValueName"/>
        <result column="multi_unit_type" property="multiUnitType"/>
        <result column="multi_unit_goods_no" property="multiUnitGoodsNo"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        d.id,
        d.create_name,
        d.create_time,
        d.modify_name,
        d.modify_time,
        d.warehouse_no, d.warehouse_name, d.goods_no, d.goods_name, d.calculation_unit_name,d.assist_calculation_unit_name,d.oper_type, d.bill_type, d.bill_code, d.sub_bill_code, d.current_stock_num, d.stock_num, d.conversion_rate, d.warehouse_flag,d.inventory_type, d.merchant_no, d.merchant_name, d.store_no, d.store_name, d.disable_flag, d.create_no, d.modify_no,
        d.parent_goods_no, d.original_goods_no, d.goods_form, d.attribute_value_name
    </sql>

    <sql id="Goods_Base_Column_List">
        s.first_attribute_value_name, s.second_attribute_value_name, s.third_attribute_value_name,s.multi_unit_type,s.multi_unit_goods_no
    </sql>

    <!-- 出入库报表结果列 -->
    <sql id="OutPut_Statement_Column_List">
        wsf.warehouse_no,
        wsf.bill_type,
        wsf.stock_num,
        wsf.create_time,
        wsf.goods_no,
        g.goods_name,
        g.barcode,
        g.first_attribute_value_name,
        g.second_attribute_value_name,
        g.third_attribute_value_name,
        (case when im.virtual_warehouse_flag = 1 then im.warehouse_name else '' end) as warehouseName
    </sql>

    <sql id="Base_Column_Where">
        <if test="inventoryType !=1 and storeNo  != null and storeNo  != ''">
            and s.store_no = #{storeNo,jdbcType=VARCHAR}
        </if>
        <if test="inventoryType !=1 and storeName != null and storeName != ''">
            and s.store_name LIKE CONCAT(CONCAT('%',#{storeName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="warehouseName != null and warehouseName != ''">
            and d.warehouse_name LIKE CONCAT(CONCAT('%',#{warehouseName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="inventoryType !=1 and merchantNo  != null and merchantNo  != ''">
            and s.merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="inventoryType !=1 and merchantName != null and merchantName != ''">
            and s.merchant_name = #{merchantName}
        </if>
        <if test="billCode != null and billCode != ''">
            and d.bill_code LIKE CONCAT(CONCAT('%',#{billCode,jdbcType=VARCHAR}),'%')
        </if>
        <if test="inventoryType !=1 and goodsNo != null and goodsNo != ''">
            and (s.goods_no = #{goodsNo} or s.parent_goods_no = #{goodsNo})
        </if>
        <if test="goodsName != null and goodsName != ''">
            and d.goods_name LIKE CONCAT(CONCAT('%',#{goodsName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="inventoryType !=1 and deleteFlag!=null">
            and s.delete_flag=#{deleteFlag}
        </if>
        <if test="billType != null and billType != ''">
            and d.bill_type = #{billType}
        </if>
        <if test="operType != null and operType != ''">
            and d.oper_type = #{operType}
        </if>

        <if test="inventoryType !=1 and loginIdentity  != null and loginIdentity==1">
            and s.goods_source_type in ('1001','1002','1003')
        </if>
        <if test="inventoryType !=1 and loginIdentity  != null and loginIdentity==2">
            and s.goods_source_type in ('1002')
        </if>
        <if test="inventoryType !=1 and loginIdentity  != null and loginIdentity==4">
            and s.goods_source_type in ('1003')
        </if>
        <if test="inventoryType !=1 and loginIdentity  != null and loginIdentity==8">
            and s.goods_source_type in ('1003')
        </if>

        <if test="inventoryType ==1 and storeNo  != null and storeNo  != ''">
            and gg.store_no = #{storeNo,jdbcType=VARCHAR}
        </if>
        <if test="inventoryType ==1 and storeName != null and storeName != ''">
            and gg.store_name LIKE CONCAT(CONCAT('%',#{storeName,jdbcType=VARCHAR}),'%')
        </if>
        <if test="inventoryType ==1 and merchantNo  != null and merchantNo  != ''">
            and gg.merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="inventoryType ==1 and merchantName != null and merchantName != ''">
            and gg.merchant_name = #{merchantName}
        </if>
        <if test="inventoryType ==1 and goodsNo != null and goodsNo != ''">
            and (gg.goods_groups_no = #{goodsNo})
        </if>
        <if test="inventoryType ==1 and deleteFlag!=null">
            and gg.delete_flag=#{deleteFlag}
        </if>
        <if test="inventoryType ==1 and loginIdentity  != null and loginIdentity==1">
            and gg.goods_source_type in ('1001','1002','1003','1005')
        </if>
        <if test="inventoryType ==1 and loginIdentity  != null and loginIdentity==2">
            and gg.goods_source_type in ('1002','1005')
        </if>
        <if test="inventoryType ==1 and loginIdentity  != null and loginIdentity==4">
            and gg.goods_source_type in ('1003','1005','2001')
        </if>
        <if test="inventoryType ==1 and loginIdentity  != null and loginIdentity==8">
            and gg.goods_source_type in ('1003','1005','2001')
        </if>

        <if test="totalStockBegin != null">
            <![CDATA[and d.stock_num >= #{totalStockBegin}]]>
        </if>
        <if test="totalStockEnd != null">
            <![CDATA[and d.stock_num < #{totalStockEnd}]]>
        </if>
        <if test="startTime != null and startTime != ''">
            <![CDATA[and  date_format(d.create_time, '%Y-%m-%d %H:%i:%s')  >= #{startTime}]]>
        </if>
        <if test="endTime != null and endTime != ''">
            <![CDATA[and date_format(d.create_time, '%Y-%m-%d %H:%i:%s') <= #{endTime}]]>
        </if>
        <choose>
            <when test="inventoryType != null and inventoryType != ''">
                AND d.inventory_type  = #{inventoryType}
            </when>
            <otherwise>
                AND d.inventory_type = 0
            </otherwise>
        </choose>
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.ImWarehouseStockFlowRecordDomain">
        select
        <include refid="Base_Column_List"/>
        from
        im_warehouse_stock_flow_record d
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and d.warehouse_no = #{warehouseNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>


    <select id="getCompletPurchaseReturnOrderGoodsByReturnCode" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomWareStockFlowVo">
        select
        <include refid="Base_Column_List"/>,
        p.purchase_num,
        p.returned_count,
        p.return_request_count,
        p.purchase_unit,
        p.purchase_unit_price,
        p.return_goods_code,
        p.return_code
        from
        im_warehouse_stock_flow_record d left join purchase_return_order_goods p on d.sub_bill_code=p.return_goods_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and d.bill_code =#{returnCode,jdbcType=VARCHAR} and d.delete_flag = 1
            </trim>
            <if test="storeNo  != null and storeNo  != ''">
                and d.store_no = #{storeNo,jdbcType=VARCHAR}
            </if>
            <if test="storeName != null and storeName != ''">
                and d.store_name = #{storeName}
            </if>
            <if test="merchantNo  != null and merchantNo  != ''">
                and d.merchant_no = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="merchantName != null and merchantName != ''">
                and d.merchant_name = #{merchantName}
            </if>
            <choose>
                <when test="inventoryType != null and inventoryType != ''">
                    AND d.inventory_type  = #{inventoryType}
                </when>
                <otherwise>
                    AND d.inventory_type = 0
                </otherwise>
            </choose>
        </where>
    </select>

    <!--一体机-经营报表-出入库报表-->
    <select id="selectOutPutStorageRecordForPage" resultType="cn.htdt.goodsprocess.vo.AtomResOutPutRecordVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqOutPutRecordVo">
        select
        <include refid="OutPut_Statement_Column_List"/>
        from
        im_warehouse_stock_flow_record wsf
        <if test="inventoryType != 1">
            LEFT JOIN goods g ON wsf.goods_no = g.goods_no
        </if>
        <if test="inventoryType == 1">
            LEFT JOIN goods_groups gg ON wsf.goods_no = gg.goods_groups_no
        </if>
        LEFT JOIN im_warehouse im ON wsf.warehouse_no = im.warehouse_no
        LEFT JOIN sale_category sc ON sc.category_no = g.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo  != null and storeNo  != ''">
                    and wsf.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and wsf.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="startTime != null and  endTime != null">
                    <![CDATA[and wsf.create_time  between #{startTime} and #{endTime}]]>
                </if>
                <choose>
                    <when test="inventoryType != null and inventoryType != ''">
                        AND wsf.inventory_type  = #{inventoryType}
                    </when>
                    <otherwise>
                        AND wsf.inventory_type = 0
                    </otherwise>
                </choose>
                <if test=" operType != null and operType != ''">
                    and wsf.oper_type = #{operType,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType != 1 and imeiFlag != null and imeiFlag != ''">
                    and g.imei_flag = #{imeiFlag,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType != 1 and brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType != 1 and goodsForm != null and goodsForm != ''">
                    and g.goods_form = #{goodsForm,jdbcType=VARCHAR}
                </if>
                <if test=" categoryNo != null and categoryNo != ''">
                    and sc.parent_no = #{categoryNo,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType != 1 and goodsStr != null and goodsStr != ''">
                    and (g.barcode like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%")
                    OR g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%')
                    or g.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsStr}),'%')
                    or g.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsStr}),'%'))
                </if>
                <if test=" inventoryType == 1 and goodsForm != null and goodsForm != ''">
                    and gg.goods_form = #{goodsForm,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType == 1 and goodsStr != null and goodsStr != ''">
                    and gg.goods_groups_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%')
                </if>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
            </trim>
            order by wsf.create_time DESC
        </where>
    </select>
    <!--一体机-经营报表-出入库报表-店铺自建类目-->
    <select id="selectStoreOutPutStorageRecordForPage" resultType="cn.htdt.goodsprocess.vo.AtomResOutPutRecordVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqOutPutRecordVo">
        select
        <include refid="OutPut_Statement_Column_List"/>
        from
        im_warehouse_stock_flow_record wsf
        <if test="inventoryType != 1">
            LEFT JOIN goods g ON wsf.parent_goods_no = g.goods_no
        </if>
        <if test="inventoryType == 1">
            LEFT JOIN goods_groups gg ON wsf.parent_goods_no = gg.goods_groups_no
        </if>
        LEFT JOIN im_warehouse im ON wsf.warehouse_no = im.warehouse_no
        LEFT JOIN store_category_goods_relation scg ON g.goods_no = scg.goods_no
        LEFT join store_category sc on scg.store_category_no = sc.store_category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo  != null and storeNo  != ''">
                    and wsf.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and wsf.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <choose>
                    <when test="inventoryType != null and inventoryType != ''">
                        AND wsf.inventory_type  = #{inventoryType}
                    </when>
                    <otherwise>
                        AND wsf.inventory_type = 0
                    </otherwise>
                </choose>
                <if test="startTime != null and  endTime != null">
                    <![CDATA[and wsf.create_time  between #{startTime} and #{endTime}]]>
                </if>
                <if test=" operType != null and operType != ''">
                    and wsf.oper_type = #{operType,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType != 1 and imeiFlag != null and imeiFlag != ''">
                    and g.imei_flag = #{imeiFlag,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType != 1 and brandNo != null and brandNo != ''">
                    and g.brand_no = #{brandNo,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType != 1 and goodsForm != null and goodsForm != ''">
                    and g.goods_form = #{goodsForm,jdbcType=VARCHAR}
                </if>
                <if test=" categoryNo != null and categoryNo != ''">
                    and sc.parent_no = #{categoryNo,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType != 1 and goodsStr != null and goodsStr != ''">
                    and (g.barcode like CONCAT(CONCAT("%",#{goodsStr,jdbcType=VARCHAR}),"%")
                    OR g.goods_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%')
                    or g.goods_name_full_pinyin like CONCAT(CONCAT('%', #{goodsStr}),'%')
                    or g.goods_name_full_initials like CONCAT(CONCAT('%', #{goodsStr}),'%'))
                </if>
                <if test=" inventoryType == 1 and goodsForm != null and goodsForm != ''">
                    and gg.goods_form = #{goodsForm,jdbcType=VARCHAR}
                </if>
                <if test=" inventoryType == 1 and goodsStr != null and goodsStr != ''">
                    and gg.goods_groups_name LIKE CONCAT(CONCAT('%',#{goodsStr,jdbcType=VARCHAR}),'%')
                </if>
                <if test="fullIdPath != null and fullIdPath!=''">
                    and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
                </if>
            </trim>
            order by wsf.create_time DESC
        </where>
    </select>
    <select id="getAllWareHouseStockFlowPageByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomWareStockFlowVo">
        select
        <include refid="Base_Column_List"/>
        <if test="inventoryType != 1">
            ,s.delete_flag,s.multi_unit_type,s.multi_unit_goods_no
        </if>
        <if test="inventoryType == 1">
            ,gg.delete_flag
        </if>
        from im_warehouse_stock_flow_record d
        <if test="inventoryType != 1">
            left join goods s on s.original_goods_no=d.original_goods_no and s.multi_unit_type != '1002'
        </if>
        <if test="inventoryType == 1">
            LEFT JOIN goods_groups gg ON d.goods_no = gg.goods_groups_no
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseFlag != null">
                    and d.warehouse_flag = #{warehouseFlag}
                </if>
                <if test="inventoryType != 1">
                    and s.goods_type != '1002'
                </if>
                <include refid="Base_Column_Where"/>
                order by d.modify_time desc, id desc
            </trim>
        </where>
    </select>
    <!-- 获取仓库库存流水信息-list-bossapp用根据商品编号分组查询最新一条记录 -->
    <select id="getLatestWareHouseStockFlowPageByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomWareStockFlowVo">
        select * from (
        select
        <include refid="Base_Column_List"/>,
        <if test="inventoryType != 1">
            <include refid="Goods_Base_Column_List"/>,s.delete_flag
        </if>
        <if test="inventoryType == 1">
            ,gg.delete_flag
        </if>
        from im_warehouse_stock_flow_record d
        <if test="inventoryType != 1">
            left join
            goods s on s.original_goods_no=d.original_goods_no
        </if>
        <if test="inventoryType == 1">
            LEFT JOIN goods_groups gg ON d.goods_no = gg.goods_groups_no
        </if>

        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and s.goods_type != '1002'
                <if test="warehouseFlag != null">
                    and d.warehouse_flag = #{warehouseFlag}
                </if>

                <include refid="Base_Column_Where"/>
                having 1
                order by d.create_time desc
            </trim>
        </where>
        ) a
        group by goods_no
        order by modify_time desc
    </select>

    <!-- 获取仓库库存流水信息是否包含有无仓数据 -->
    <select id="getWarehouseFlagByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomWareStockFlowVo">
        select
        <include refid="Base_Column_List"/>
        from im_warehouse_stock_flow_record d
        <if test="inventoryType != 1">
            left join goods s on s.original_goods_no=d.original_goods_no
        </if>
        <if test="inventoryType == 1">
            LEFT JOIN goods_groups gg ON d.goods_no = gg.goods_groups_no
        </if>

        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        group by d.warehouse_flag
    </select>

    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseStockFlowRecordDomain">
        insert into
        im_warehouse_stock_flow_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="warehouseName != null">
                warehouse_name,
            </if>
            <if test="goodsNo != null">
                goods_no,
            </if>
            <if test="goodsName != null">
                goods_name,
            </if>
            <if test="operType != null">
                oper_type,
            </if>
            <if test="billType != null">
                bill_type,
            </if>
            <if test="billCode != null">
                bill_code,
            </if>
            <if test="subBillCode != null">
                sub_bill_code,
            </if>
            <if test="currentStockNum != null">
                current_stock_num,
            </if>
            <if test="stockNum != null">
                stock_num,
            </if>
            <if test="calculationUnitName != null">
                calculation_unit_name,
            </if>
            <if test="conversionRate != null">
                conversion_rate,
            </if>
            <if test="warehouseFlag != null">
                warehouse_flag,
            </if>
            <if test="merchantNo != null">
                merchant_no,
            </if>
            <if test="merchantName != null">
                merchant_name,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
            <if test="disableFlag != null">
                disable_flag,
            </if>
            <if test="createNo != null">
                create_no,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyNo != null">
                modify_no,
            </if>
            <if test="modifyName != null">
                modify_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="parentGoodsNo != null">
                parent_goods_no,
            </if>
            <if test="originalGoodsNo != null">
                original_goods_no,
            </if>
            <if test="goodsForm != null">
                goods_form,
            </if>
            <if test="attributeValueName != null">
                attribute_value_name,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo},
            </if>
            <if test="warehouseName != null">
                #{warehouseName},
            </if>
            <if test="goodsNo != null">
                #{goodsNo},
            </if>
            <if test="goodsName != null">
                #{goodsName},
            </if>
            <if test="operType != null">
                #{operType},
            </if>
            <if test="billType != null">
                #{billType},
            </if>
            <if test="billCode != null">
                #{billCode},
            </if>
            <if test="subBillCode != null">
                #{subBillCode},
            </if>
            <if test="currentStockNum != null">
                #{currentStockNum},
            </if>
            <if test="stockNum != null">
                #{stockNum},
            </if>
            <if test="calculationUnitName != null">
                #{calculationUnitName},
            </if>
            <if test="conversionRate != null">
                #{conversionRate},
            </if>
            <if test="warehouseFlag != null">
                #{warehouseFlag},
            </if>
            <if test="merchantNo != null">
                #{merchantNo},
            </if>
            <if test="merchantName != null">
                #{merchantName},
            </if>
            <if test="storeNo != null">
                #{storeNo},
            </if>
            <if test="storeName != null">
                #{storeName},
            </if>
            <if test="disableFlag != null">
                #{disableFlag},
            </if>
            <if test="createNo != null">
                #{createNo},
            </if>
            <if test="createName != null">
                #{createName},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="modifyNo != null">
                #{modifyNo},
            </if>
            <if test="modifyName != null">
                #{modifyName},
            </if>
            <if test="modifyTime != null">
                #{modifyTime},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag},
            </if>
            <if test="originalGoodsNo != null">
                #{originalGoodsNo},
            </if>
            <if test="originalGoodsNo != null">
                #{originalGoodsNo},
            </if>
            <if test="goodsForm != null">
                #{goodsForm},
            </if>
            <if test="attributeValueName != null">
                #{attributeValueName},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="cn.htdt.goodsprocess.domain.ImWarehouseStockFlowRecordDomain">
        update
        im_warehouse_stock_flow_record
        <set>
            <if test="warehouseNo != null">
                warehouse_no = #{warehouseNo},
            </if>
            <if test="warehouseName != null">
                warehouse_name = #{warehouseName},
            </if>
            <if test="goodsNo != null">
                goods_no = #{goodsNo},
            </if>
            <if test="goodsName != null">
                goods_name = #{goodsName},
            </if>
            <if test="operType != null">
                oper_type = #{operType},
            </if>
            <if test="billType != null">
                bill_type = #{billType},
            </if>
            <if test="billCode != null">
                bill_code = #{billCode},
            </if>
            <if test="subBillCode != null">
                sub_bill_code = #{subBillCode},
            </if>
            <if test="currentStockNum != null">
                current_stock_num = #{currentStockNum},
            </if>
            <if test="stockNum != null">
                stock_num = #{stockNum},
            </if>
            <if test="calculationUnitName != null">
                calculation_unit_name = #{calculationUnitName},
            </if>
            <if test="conversionRate != null">
                conversion_rate = #{conversionRate},
            </if>
            <if test="warehouseFlag != null">
                warehouse_flag = #{warehouseFlag},
            </if>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo},
            </if>
            <if test="merchantName != null">
                merchant_name = #{merchantName},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo},
            </if>
            <if test="storeName != null">
                store_name = #{storeName},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag},
            </if>
            <if test="createNo != null">
                create_no = #{createNo},
            </if>
            <if test="createName != null">
                create_name = #{createName},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag},
            </if>
            <if test="parentGoodsNo != null">
                parent_goods_no = #{parentGoodsNo},
            </if>
            <if test="originalGoodsNo != null">
                original_goods_no = #{originalGoodsNo},
            </if>
            <if test="goodsForm != null">
                goods_form = #{goodsForm},
            </if>
            <if test="attributeValueName != null">
                attribute_value_name = #{attributeValueName},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.ImWarehouseStockFlowRecordDomain">
        update
        im_warehouse_stock_flow_record
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime},
        </if>
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
            </trim>
        </where>
    </update>

    <!-- 根据参数查询采购入库商品同仓库下入库的商品总数 -->
    <select id="selectPurchaseGoodsStockNum" resultType="java.lang.Integer"
            parameterType="cn.htdt.goodsprocess.domain.ImWarehouseStockFlowRecordDomain">
        select
        sum(stock_num)
        from im_warehouse_stock_flow_record
        where bill_code = #{billCode}
        and warehouse_no = #{warehouseNo}
        and goods_no = #{goodsNo}
        and delete_flag = 1
    </select>



    <!-- 根据参数查询商品同仓库下入库的商品总数 -->
    <select id="selectGoodsStockNum" resultType="java.math.BigDecimal"
            parameterType="cn.htdt.goodsprocess.domain.ImWarehouseStockFlowRecordDomain">
        select
            sum(d.stock_num)
        from
            im_warehouse_stock_flow_record d
        where
            d.goods_no = #{goodsNo}
          and d.oper_type = #{operType}
          and d.bill_type = #{billCode}
          and d.sub_bill_code = #{subBillCode}
    </select>
</mapper>
