<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.FreightTemplateDefaultDao">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap"
		type="cn.htdt.goodsprocess.domain.FreightTemplateDefaultDomain">
		<result column="id" property="id" />
		<result column="delete_flag" property="deleteFlag" />
		<result column="create_name" property="createName" />
		<result column="create_time" property="createTime" />
		<result column="modify_name" property="modifyName" />
		<result column="modify_time" property="modifyTime" />
		<result column="template_default_no" property="templateDefaultNo" />
		<result column="freight_template_no" property="freightTemplateNo" />
		<result column="merchant_no" property="merchantNo" />
		<result column="merchant_name" property="merchantName" />
		<result column="company_no" property="companyNo" />
		<result column="company_name" property="companyName" />
		<result column="store_no" property="storeNo" />
		<result column="store_name" property="storeName" />
		<result column="branch_no" property="branchNo" />
		<result column="branch_name" property="branchName" />
		<result column="template_default_flag" property="templateDefaultFlag" />
		<result column="create_no" property="createNo" />
		<result column="modify_no" property="modifyNo" />
		<result column="disable_flag" property="disableFlag" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		id,
		delete_flag,
		create_name,
		create_time,
		modify_name,
		modify_time,
		template_default_no, freight_template_no, merchant_no,
		merchant_name, company_no,
		company_name, store_no, store_name,
		branch_no, branch_name,
		template_default_flag, create_no, modify_no,
		disable_flag
	</sql>

	<sql id="Base_Column_Where">
		and delete_flag = 1
	</sql>


	<!-- 修改 -->
	<update id="updateByPrimaryKeySelective"
		parameterType="cn.htdt.goodsprocess.domain.FreightTemplateDefaultDomain">
		update
		freight_template_default
		<set>
			<if test="tagContent != null">
				freight_template_no =
				#{freightTemplateNo,jdbcType=VARCHAR},
			</if>
			<if test="merchantNo != null">
				merchant_no = #{merchantNo,jdbcType=VARCHAR},
			</if>
			<if test="merchantName != null">
				merchant_name = #{merchantName,jdbcType=VARCHAR},
			</if>
			<if test="storeNo != null">
				store_no = #{storeNo,jdbcType=VARCHAR},
			</if>
			<if test="storeName != null">
				store_name = #{storeName,jdbcType=VARCHAR},
			</if>
			<if test="companyNo != null">
				company_no = #{companyNo,jdbcType=VARCHAR},
			</if>
			<if test="companyName != null">
				company_name = #{companyName,jdbcType=VARCHAR},
			</if>
			<if test="branchNo != null">
				branch_no = #{branchNo,jdbcType=VARCHAR},
			</if>
			<if test="branchName != null">
				branch_name = #{branchName,jdbcType=VARCHAR},
			</if>
			<if test="templateDefaultFlag != null">
				template_default_flag = #{templateDefaultFlag,jdbcType=TINYINT},
			</if>
			<if test="disableFlag != null">
				disable_flag = #{disableFlag,jdbcType=TINYINT},
			</if>
			<if test="createNo != null">
				create_no = #{createNo,jdbcType=VARCHAR},
			</if>
			<if test="createName != null">
				create_name = #{createName,jdbcType=VARCHAR},
			</if>
			<if test="createTime != null">
				create_time = #{createTime,jdbcType=TIMESTAMP},
			</if>
			<if test="modifyNo != null">
				modify_no = #{modifyNo,jdbcType=VARCHAR},
			</if>
			<if test="modifyName != null">
				modify_name = #{modifyName,jdbcType=VARCHAR},
			</if>
			<if test="modifyTime != null">
				modify_time = #{modifyTime,jdbcType=TIMESTAMP},
			</if>
			<if test="deleteFlag != null">
				delete_flag = #{deleteFlag,jdbcType=TINYINT},
			</if>
		</set>
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="templateDefaultNo != null and templateDefaultNo != ''">
					and template_default_no = #{templateDefaultNo}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</update>

	<!-- 逻辑删除 -->
	<update id="logicDelete"
		parameterType="cn.htdt.goodsprocess.domain.FreightTemplateDefaultDomain">
		update
		freight_template_default
		set
		<if test="modifyNo != null">
			modify_no = #{modifyNo,jdbcType=VARCHAR},
		</if>
		<if test="modifyName != null">
			modify_name = #{modifyName,jdbcType=VARCHAR},
		</if>
		<if test="modifyTime != null">
			modify_time = #{modifyTime,jdbcType=TIMESTAMP},
		</if>
		delete_flag = 2
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="freightTemplateNo != null and freightTemplateNo != ''">
					and freight_template_no = #{freightTemplateNo}
				</if>
				<if test="templateDefaultNo != null and templateDefaultNo != ''">
					and template_default_no = #{templateDefaultNo}
				</if>
				<if test="storeNo != null and storeNo != ''">
					and store_no = #{storeNo}
				</if>
				<if test="merchantNo != null and merchantNo != ''">
					and merchant_no = #{merchantNo}
				</if>
				<if test="companyNo != null and companyNo != ''">
					and company_no = #{companyNo}
				</if>
				<include refid="Base_Column_Where" />
			</trim>
		</where>
	</update>
	
	<!-- 根据默认编号删除 -->
    <delete id="deleteDefaultByTemplateNo" parameterType="cn.htdt.goodsprocess.domain.FreightTemplateDefaultDomain">
        DELETE FROM
        freight_template_default
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
				and template_default_no = #{templateDefaultNo}
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </delete>

	<select id="selectByParams"
		resultType="cn.htdt.goodsprocess.domain.FreightTemplateDefaultDomain">
		select
			ftd.*
		from
			freight_template_default ftd left join freight_template ft on ftd.freight_template_no = ft.template_no
		<where>
			<trim suffixOverrides="AND | OR" prefix="1=1">
				<if test="freightTemplateNo != null and freightTemplateNo != ''">
					and ftd.freight_template_no = #{freightTemplateNo}
				</if>
				<if test="templateDefaultNo != null and templateDefaultNo != ''">
					and ftd.template_default_no = #{templateDefaultNo}
				</if>
				<if test="storeNo != null and storeNo != ''">
					and ftd.store_no = #{storeNo}
				</if>
				<if test="merchantNo != null and merchantNo != ''">
					and ftd.merchant_no = #{merchantNo}
				</if>
				<if test="companyNo != null and companyNo != ''">
					and ftd.company_no = #{companyNo}
				</if>
				<if test="templateDefaultFlag != null and templateDefaultFlag != ''">
					and ftd.template_default_flag = #{templateDefaultFlag}
				</if>
				<if test="templateSourceType != null and templateSourceType != ''">
					<choose>
			            <!--来源为1003的, 可能存在系统创建的来源为1004的模板-->
						<when test="templateSourceType == '1003'">
							and (ft.template_source_type = #{templateSourceType} or ft.template_source_type = '1004')
						</when>
						<otherwise>
							and ft.template_source_type = #{templateSourceType}
						</otherwise>
					</choose>
				</if>
				and ftd.delete_flag = 1
			</trim>
		</where>
	</select>


</mapper>
