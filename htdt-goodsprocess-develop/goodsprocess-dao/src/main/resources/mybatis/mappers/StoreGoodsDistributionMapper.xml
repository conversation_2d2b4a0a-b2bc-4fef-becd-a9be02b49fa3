<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.StoreGoodsDistributionDao">
  <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.StoreGoodsDistributionDomain">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="distribution_no" jdbcType="VARCHAR" property="distributionNo" />
    <result column="goods_no" jdbcType="VARCHAR" property="goodsNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="merchant_no" jdbcType="VARCHAR" property="merchantNo" />
    <result column="merchant_name" jdbcType="VARCHAR" property="merchantName" />
    <result column="store_no" jdbcType="VARCHAR" property="storeNo" />
    <result column="store_name" jdbcType="VARCHAR" property="storeName" />
    <result column="disable_flag" jdbcType="BIT" property="disableFlag" />
    <result column="create_no" jdbcType="VARCHAR" property="createNo" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_no" jdbcType="VARCHAR" property="modifyNo" />
    <result column="modify_name" jdbcType="VARCHAR" property="modifyName" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="delete_flag" jdbcType="BIT" property="deleteFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    d.id, d.distribution_no, d.goods_no,d.goods_name, d.merchant_no, d.merchant_name, d.store_no, d.store_name,
    d.disable_flag, d.create_no, d.create_name, d.create_time, d.modify_no, d.modify_name, d.modify_time,
    d.delete_flag
  </sql>

  <sql id="Goods_Column_Where">
    <if test="goodsName != null and goodsName != ''">
      and g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
    </if>
    <if test="goodsHelpCode != null and goodsHelpCode != ''">
      and g.goods_help_code LIKE CONCAT (CONCAT('%',#{goodsHelpCode}),'%')
    </if>
    <if test="goodsStatus != null and goodsStatus != ''">
      and g.goods_status = #{goodsStatus}
    </if>
    <if test="categoryNo != null and categoryNo != ''">
      and g.category_no = #{categoryNo}
    </if>
    <if test="brandNo != null and brandNo != ''">
      and g.brand_no = #{brandNo}
    </if>
    <if test="goodsType != null and goodsType != ''">
      and g.goods_type = #{goodsType}
    </if>
    <if test="stockFlag == 1">
      and exists (select 1 from goods_real_stock s
      where s.parent_goods_no = d.goods_no and s.available_stock_num > 0)
    </if>
    <if test="stockFlag == 2">
      and exists (select 1 from goods_real_stock s
      where s.parent_goods_no = d.goods_no and s.available_stock_num <![CDATA[ <= ]]>  0)
    </if>
  </sql>

  <sql id="GoodsShowStatus_Column_Where">
    <choose>
      <!--审核中或者审核失败 -->
      <when test="goodsShowStatus== 2002 or goodsShowStatus == 2004">
        <![CDATA[ and g.audit_status = #{goodsShowStatus}]]>
      </when>
      <!--已失效 -->
      <when test="goodsShowStatus == 3004">
        <![CDATA[ and g.distribution_status = #{goodsShowStatus}]]>
      </when>
      <!--未上架,必须是店铺自建或者商家分发，分发状态不等于已失效 ,审核状态不等于审核中和审核失败-->
      <when test="goodsShowStatus == 1001">
        <![CDATA[
                    and g.goods_status = '1001'
                    and g.audit_status not in ('2002','2004')
                    and g.goods_source_type in ('1003','1005')
                    and g.distribution_status not in ('3004')
                ]]>
      </when>
      <!--已上架,必须是店铺自建或者商家分发，分发状态分发成功且审核状态也是成功 -->
      <when test="goodsShowStatus == 1002">
        <![CDATA[
                    and g.goods_status = '1002'
                    and g.audit_status = '2003'
                    and g.goods_source_type in ('1003','1005')
                    and g.distribution_status not in ('3001','3002','3004')
                ]]>
      </when>
      <!--未分发,必须是商家自建，审核状态不等于审核中和审核失败-->
      <when test="goodsShowStatus == 3001">
        <![CDATA[
                    and g.audit_status not in ('2002','2004')
                    and g.goods_source_type in ('1002')
                    and g.distribution_status ='3001'
                ]]>
      </when>
      <!--已分发,必须是商家自建，审核状态等于审核成功 -->
      <when test="goodsShowStatus == 3003">
        <![CDATA[
                    and g.audit_status  in ('2003')
                    and g.goods_source_type in ('1002')
                    and g.distribution_status ='3003'
                ]]>
      </when>
    </choose>
  </sql>

  <select id="selectStoreGoodsDistributionList" parameterType="cn.htdt.goodsprocess.vo.StoreGoodsDistributionVo"
          resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from store_goods_distribution d
    left join goods g on d.goods_no = g.original_goods_no and g.goods_source_type in('1003','1005')
    LEFT JOIN sale_category sc ON g.category_no = sc.category_no
    <where>
      <trim suffixOverrides="AND | OR" prefix="1=1">
        <if test="goodsNo != null and goodsNo != ''">
          and d.goods_no = #{goodsNo}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
          and d.merchant_no = #{merchantNo}
        </if>
        <if test="storeNo != null and storeNo != ''">
          and d.store_no = #{storeNo}
        </if>
        <if test="disableFlag != null and disableFlag != ''">
          and d.disable_flag = #{disableFlag}
        </if>
        <if test="deleteFlag != null and deleteFlag != ''">
          and d.delete_flag = #{deleteFlag}
        </if>
        <if test="fullIdPath != null and fullIdPath!=''">
          and sc.full_id_path like CONCAT(CONCAT(#{fullIdPath}),'%')
        </if>
        <include refid="Goods_Column_Where"/>
        <include refid="GoodsShowStatus_Column_Where"/>
        and g.delete_flag = 1
      </trim>
    </where>
    order by modify_time desc
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from store_goods_distribution
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="batchDeleteStoreGoodsDistribution" parameterType="cn.htdt.goodsprocess.domain.StoreGoodsDistributionDomain">
    delete from store_goods_distribution where
    <foreach collection="list" item="emp" separator="or" >
      (goods_no = #{emp.goodsNo,jdbcType=VARCHAR} and store_no = #{emp.storeNo,jdbcType=VARCHAR})
    </foreach>

  </delete>

  <insert id="batchInsertStoreGoodsDistribution" parameterType="cn.htdt.goodsprocess.domain.StoreGoodsDistributionDomain">
    insert into store_goods_distribution (
      distribution_no, goods_no,goods_name,
      merchant_no, merchant_name, store_no,
      store_name, disable_flag, create_no,
      create_name, create_time, modify_no,
      modify_name, modify_time, delete_flag
      )
    values
    <foreach collection="list" item="emp" separator=",">
      (#{emp.distributionNo,jdbcType=VARCHAR}, #{emp.goodsNo,jdbcType=VARCHAR}, #{emp.goodsName,jdbcType=VARCHAR},
      #{emp.merchantNo,jdbcType=VARCHAR}, #{emp.merchantName,jdbcType=VARCHAR}, #{emp.storeNo,jdbcType=VARCHAR},
      #{emp.storeName,jdbcType=VARCHAR}, #{emp.disableFlag,jdbcType=BIT}, #{emp.createNo,jdbcType=VARCHAR},
      #{emp.createName,jdbcType=VARCHAR}, #{emp.createTime,jdbcType=TIMESTAMP}, #{emp.modifyNo,jdbcType=VARCHAR},
      #{emp.modifyName,jdbcType=VARCHAR}, #{emp.modifyTime,jdbcType=TIMESTAMP}, #{emp.deleteFlag,jdbcType=BIT}
      )
    </foreach>

  </insert>

  <insert id="insert" parameterType="cn.htdt.goodsprocess.domain.StoreGoodsDistributionDomain">
    insert into store_goods_distribution (id, distribution_no, goods_no,goods_name,
      merchant_no, merchant_name, store_no,
      store_name, disable_flag, create_no,
      create_name, create_time, modify_no,
      modify_name, modify_time, delete_flag
      )
    values (#{id,jdbcType=BIGINT}, #{distributionNo,jdbcType=VARCHAR}, #{goodsNo,jdbcType=VARCHAR},#{goodsName,jdbcType=VARCHAR},
      #{merchantNo,jdbcType=VARCHAR}, #{merchantName,jdbcType=VARCHAR}, #{storeNo,jdbcType=VARCHAR},
      #{storeName,jdbcType=VARCHAR}, #{disableFlag,jdbcType=BIT}, #{createNo,jdbcType=VARCHAR},
      #{createName,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP}, #{modifyNo,jdbcType=VARCHAR},
      #{modifyName,jdbcType=VARCHAR}, #{modifyTime,jdbcType=TIMESTAMP}, #{deleteFlag,jdbcType=BIT}
      )
  </insert>

  <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.StoreGoodsDistributionDomain">
    insert into store_goods_distribution
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="distributionNo != null">
        distribution_no,
      </if>
      <if test="goodsNo != null">
        goods_no,
      </if>
      <if test="merchantNo != null">
        merchant_no,
      </if>
      <if test="merchantName != null">
        merchant_name,
      </if>
      <if test="storeNo != null">
        store_no,
      </if>
      <if test="storeName != null">
        store_name,
      </if>
      <if test="disableFlag != null">
        disable_flag,
      </if>
      <if test="createNo != null">
        create_no,
      </if>
      <if test="createName != null">
        create_name,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyNo != null">
        modify_no,
      </if>
      <if test="modifyName != null">
        modify_name,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="deleteFlag != null">
        delete_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="distributionNo != null">
        #{distributionNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantNo != null">
        #{merchantNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantName != null">
        #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="storeNo != null">
        #{storeNo,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="disableFlag != null">
        #{disableFlag,jdbcType=BIT},
      </if>
      <if test="createNo != null">
        #{createNo,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyNo != null">
        #{modifyNo,jdbcType=VARCHAR},
      </if>
      <if test="modifyName != null">
        #{modifyName,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteFlag != null">
        #{deleteFlag,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.StoreGoodsDistributionDomain">
    update store_goods_distribution
    <set>
      <if test="distributionNo != null">
        distribution_no = #{distributionNo,jdbcType=VARCHAR},
      </if>
      <if test="goodsNo != null">
        goods_no = #{goodsNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantNo != null">
        merchant_no = #{merchantNo,jdbcType=VARCHAR},
      </if>
      <if test="merchantName != null">
        merchant_name = #{merchantName,jdbcType=VARCHAR},
      </if>
      <if test="storeNo != null">
        store_no = #{storeNo,jdbcType=VARCHAR},
      </if>
      <if test="storeName != null">
        store_name = #{storeName,jdbcType=VARCHAR},
      </if>
      <if test="disableFlag != null">
        disable_flag = #{disableFlag,jdbcType=BIT},
      </if>
      <if test="createNo != null">
        create_no = #{createNo,jdbcType=VARCHAR},
      </if>
      <if test="createName != null">
        create_name = #{createName,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyNo != null">
        modify_no = #{modifyNo,jdbcType=VARCHAR},
      </if>
      <if test="modifyName != null">
        modify_name = #{modifyName,jdbcType=VARCHAR},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime},
      </if>
      <if test="deleteFlag != null">
        delete_flag = #{deleteFlag,jdbcType=BIT},
      </if>
    </set>
    where
        goods_no = #{goodsNo,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="cn.htdt.goodsprocess.domain.StoreGoodsDistributionDomain">
    update store_goods_distribution
    set distribution_no = #{distributionNo,jdbcType=VARCHAR},
      goods_no = #{goodsNo,jdbcType=VARCHAR},
      merchant_no = #{merchantNo,jdbcType=VARCHAR},
      merchant_name = #{merchantName,jdbcType=VARCHAR},
      store_no = #{storeNo,jdbcType=VARCHAR},
      store_name = #{storeName,jdbcType=VARCHAR},
      disable_flag = #{disableFlag,jdbcType=BIT},
      create_no = #{createNo,jdbcType=VARCHAR},
      create_name = #{createName,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_no = #{modifyNo,jdbcType=VARCHAR},
      modify_name = #{modifyName,jdbcType=VARCHAR},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      delete_flag = #{deleteFlag,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>