<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsShelfBatchDao">
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsShelfBatchDomain">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="batch_no" property="batchNo"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="warehouse_flag" property="warehouseFlag"/>
        <result column="warehouse_no" property="warehouseNo"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="in_stock_num" property="inStockNum"/>
        <result column="left_stock_num" property="leftStockNum"/>
        <result column="return_stock_num" property="returnStockNum"/>
        <result column="production_date" property="productionDate"/>
        <result column="expiration_date" property="expirationDate"/>
        <result column="quality_guarantee_period" property="qualityGuaranteePeriod"/>
        <result column="shelf_life_unit" property="shelfLifeUnit"/>
        <result column="shelf_life_day" property="shelfLifeDay"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <sql id="Base_Column_List">
    gsb.id, gsb.batch_no, gsb.goods_no, gsb.goods_name, gsb.warehouse_flag, gsb.warehouse_no, gsb.warehouse_name, gsb.in_stock_num, gsb.left_stock_num, gsb.return_stock_num, gsb.production_date, gsb.expiration_date,
    gsb.quality_guarantee_period, gsb.shelf_life_unit, gsb.shelf_life_day, gsb.merchant_no, gsb.merchant_name, gsb.store_no, gsb.store_name, gsb.delete_flag, gsb.create_no,
    gsb.create_name, gsb.create_time, gsb.modify_no, gsb.modify_name, gsb.modify_time
  </sql>

    <sql id="Base_Column_Where">
    and gsb.delete_flag = 1
  </sql>

    <select id="selectByParams" resultType="cn.htdt.goodsprocess.vo.GoodsShelfBatchListVO"
            parameterType="cn.htdt.goodsprocess.dto.request.goodsshelf.ReqGoodsShelfBatchDTO">
        select
        <include refid="Base_Column_List"/>
        , datediff(gsb.expiration_date, now()) as expiringDayNum
        from
        goods_shelf_batch gsb
        left join store_category_goods_relation scgr on scgr.goods_no = gsb.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeCategoryNoList != null and storeCategoryNoList.size > 0">
                    and (scgr.store_category_no in
                    <foreach collection="storeCategoryNoList" index="index" item="storeCategoryNo" open="("
                             separator="," close=")">
                        #{storeCategoryNo}
                    </foreach>
                    <choose>
                        <!--当前类目集合, 是否包含无分类-->
                        <when test="noCategoryFlag != null">
                            or scgr.store_category_no is null)
                        </when>
                        <otherwise>
                            )
                        </otherwise>
                    </choose>
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and gsb.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null">
                    and gsb.store_no = #{storeNo}
                </if>
                <if test="storeNo == null or storeNo == ''">
                    and gsb.store_no = ''
                </if>
                <if test="batchNo != null and batchNo != ''">
                    and gsb.batch_no LIKE CONCAT(CONCAT('%',#{batchNo}),'%')
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and gsb.goods_name LIKE CONCAT(CONCAT('%',#{goodsName}),'%')
                </if>
                <if test="expirationDateStart != null">
                    <![CDATA[ and gsb.expiration_date >= #{expirationDateStart} ]]>
                </if>
                <if test="expirationDateEnd != null">
                    <![CDATA[ and gsb.expiration_date <= #{expirationDateEnd} ]]>
                </if>
                <if test="validityPeriodType != null and validityPeriodType != ''">
                    <!-- 取过期和临期 -->
                    <if test="validityPeriodType == '1001'">
                        <!-- 临期 -->
                        <![CDATA[ and datediff(gsb.expiration_date, now()) > 0 and floor(gsb.shelf_life_day * #{warningSettings}) >= datediff(gsb.expiration_date, now()) ]]>
                    </if>
                    <if test="validityPeriodType == '1002'">
                        <!-- 过期 -->
                        <![CDATA[ and datediff(gsb.expiration_date, now()) <= 0 ]]>
                    </if>
                    and gsb.left_stock_num > 0
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        <choose>
            <when test="validityPeriodType != null and validityPeriodType != ''">
                order by expiringDayNum, gsb.create_time
            </when>
            <otherwise>
                order by gsb.create_time desc
            </otherwise>
        </choose>
    </select>

    <select id="selectGoodsShelfBatchList" resultType="cn.htdt.goodsprocess.vo.GoodsShelfBatchListVO"
            parameterType="cn.htdt.goodsprocess.dto.request.goodsshelf.ReqGoodsShelfBatchDTO">
        select distinct
        <include refid="Base_Column_List"/>
        from
        goods_shelf_batch gsb
        left join goods_shelf_batch_flow gsbf on gsb.batch_no = gsbf.batch_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="batchNo != null and batchNo != ''">
                    and gsb.batch_no = #{batchNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and gsb.goods_no = #{goodsNo}
                </if>
                <if test="warehouseFlag != null and warehouseFlag != ''">
                    and gsb.warehouse_flag = #{warehouseFlag}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and gsb.warehouse_no = #{warehouseNo}
                </if>
                <if test="goodsNos != null and goodsNos.size > 0">
                    and gsb.goods_no in
                    <foreach collection="goodsNos" item="goodsNo" open="(" separator="," close=")">
                        #{goodsNo}
                    </foreach>
                </if>
                <if test="expirationDate != null">
                    <![CDATA[ and gsb.expiration_date >= #{expirationDate} ]]>
                </if>
                and gsb.left_stock_num > 0
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        order by gsb.expiration_date, gsb.create_time
    </select>

    <select id="selectGoodsShelfBatchFlow" resultType="cn.htdt.goodsprocess.vo.GoodsShelfBatchListVO"
            parameterType="cn.htdt.goodsprocess.dto.request.goodsshelf.ReqGoodsShelfBatchDTO">
        select
        <include refid="Base_Column_List"/>
        , gsbf.batch_flow_no
        <choose>
            <when test="billType != null and billType == '1006'">
                , gsbf.stock_num - gsb.return_stock_num as stock_num
            </when>
            <when test="billType != null and billType == '1002'">
                , gsbf.stock_num - gsbf.bill_return_stock_num as stock_num
            </when>
            <otherwise>
                , gsbf.stock_num
            </otherwise>
        </choose>
        from
        goods_shelf_batch gsb
        left join goods_shelf_batch_flow gsbf on gsb.batch_no = gsbf.batch_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size > 0">
                    and gsb.goods_no in
                    <foreach collection="goodsNos" item="goodsNo" open="(" separator="," close=")">
                        #{goodsNo}
                    </foreach>
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and gsb.goods_no = #{goodsNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and gsb.warehouse_no = #{warehouseNo}
                </if>
                <if test="warehouseFlag != null and warehouseFlag != ''">
                    and gsb.warehouse_flag = #{warehouseFlag}
                </if>
                <if test="expirationDate != null">
                    <![CDATA[ and gsb.expiration_date >= #{expirationDate} ]]>
                </if>
                <if test="billCode != null and billCode != ''">
                    and gsbf.bill_code = #{billCode}
                </if>
                <if test="billType != null and billType == '1002'">
                    and gsbf.stock_num - gsbf.bill_return_stock_num > 0
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        order by gsb.expiration_date desc, gsb.create_time
    </select>

    <select id="selectGoodsShelfByNo" resultType="cn.htdt.goodsprocess.domain.GoodsShelfBatchDomain">
        select
        <include refid="Base_Column_List"/>
        from goods_shelf_batch gsb
        where
        batch_no = #{batchNo}
        <include refid="Base_Column_Where"/>
    </select>

    <select id="countShelfLife" resultType="cn.htdt.goodsprocess.vo.GoodsShelfBatchVO"
            parameterType="cn.htdt.goodsprocess.vo.GoodsShelfBatchVO">
        select
        count(distinct(temp.goods_no), temp.B &lt;= 0 or null) expiringGoodsNum,
        count(distinct(temp.goods_no),(temp.B &gt; 0 and temp.A &gt;= temp.B) or null) expiringSoonGoodsNum
        from
        (select
        goods_no,
        floor(shelf_life_day * #{temporaryWarningSettings}) A,
        datediff(expiration_date, now()) B
        from
        goods_shelf_batch gsb
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="storeNo == null or storeNo == ''">
                    and store_no = ''
                </if>
                and left_stock_num > 0
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
        ) temp
    </select>

    <update id="batchUpdateLeftStockNum" parameterType="java.util.List">
        <foreach collection="list" item="item" open="" close="" separator=";">
            update
            goods_shelf_batch
            set
            <if test="item.operType != null and item.operType == '1001' and item.leftStockNum != null">
                left_stock_num = left_stock_num + #{item.leftStockNum},
            </if>
            <if test="item.operType != null and item.operType == '1002' and item.leftStockNum != null">
                left_stock_num = left_stock_num - if(#{item.leftStockNum} > left_stock_num, left_stock_num, #{item.leftStockNum}),
                <if test="item.returnStockNum != null">
                    return_stock_num = return_stock_num + #{item.returnStockNum},
                </if>
            </if>
            <if test="item.modifyNo != null and item.merchantNo != ''">
                modify_no = #{item.modifyNo},
            </if>
            <if test="item.modifyName != null and item.modifyName != ''">
                modify_name = #{item.modifyName},
            </if>
            modify_time = NOW()
            <where>
                batch_no = #{item.batchNo}
            </where>
        </foreach>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.vo.GoodsShelfBatchVO">
        update
        goods_shelf_batch
        set
        <if test="modifyNo != null and modifyNo !='' ">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null and modifyName !='' ">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and goods_no IN
                    <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                and delete_flag = 1
            </trim>
        </where>
    </update>
</mapper>