<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.VideoRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.VideoRelationDomain">
        <result column="id" property="id" />
        <result column="create_no" property="createNo" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_no" property="modifyNo" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="media_no" property="mediaNo" />
        <result column="vedio_id" property="vedioId" />
        <result column="vedio_url" property="vedioUrl" />
        <result column="type_from" property="typeFrom" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_no,
        create_name,
        create_time,
        modify_no,
        modify_name,
        modify_time,
        delete_flag,
        media_no, vedio_id, vedio_url, type_from
    </sql>

</mapper>
