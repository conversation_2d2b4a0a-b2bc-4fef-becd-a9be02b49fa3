<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.AttributeValueDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.AttributeValueDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="attribute_value_code" property="attributeValueCode"/>
        <result column="attribute_value_name" property="attributeValueName"/>
        <result column="attribute_code" property="attributeCode"/>
        <result column="disable_flag" property="disableFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        attribute_value_code, attribute_value_name, attribute_code, disable_flag
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.AttributeValueDomain">
        select
        <include refid="Base_Column_List"/>
        from
        attribute_value
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="attributeCode != null and attributeCode != ''">
                    and attribute_code = #{attributeCode}
                </if>
                <if test="attributeValueName != null and attributeValueName != ''">
                    and attribute_value_name = #{attributeValueName}
                </if>
                <if test="attributeValueCode != null and attributeValueCode != ''">
                    and attribute_value_code = #{attributeValueCode}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateByAttributeValueCode" parameterType="cn.htdt.goodsprocess.domain.AttributeValueDomain">
        update
        attribute_value
        <set>
            <if test="attributeValueName != null and attributeValueName !='' ">
                attribute_value_name = #{attributeValueName,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null and modifyNo !='' ">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName !='' ">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            attribute_value_code = #{attributeValueCode}
            <include refid="Base_Column_Where"/>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.AttributeValueDomain">
        update
        attribute_value
        set
        <if test="modifyNo != null and modifyNo !='' ">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null and modifyName !='' ">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="attributeCode != null and attributeCode != ''">
                    and attribute_code = #{attributeCode}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="batchLogicDelete">
        update
        attribute_value
        set
        <if test="domain.modifyNo != null and domain.modifyNo !='' ">
            modify_no = #{domain.modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="domain.modifyName != null and domain.modifyName !='' ">
            modify_name = #{domain.modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            1=1
            <if test="domain.attributeValueCode != null and domain.attributeValueCode !='' ">
                and attribute_value_code = #{domain.attributeValueCode}
            </if>
            <if test="list != null">
                and attribute_code IN
                <foreach collection="list" item="attributeCode" open="(" close=")" separator=",">
                    #{attributeCode}
                </foreach>
            </if>
            <include refid="Base_Column_Where"/>
        </where>
    </update>
</mapper>
