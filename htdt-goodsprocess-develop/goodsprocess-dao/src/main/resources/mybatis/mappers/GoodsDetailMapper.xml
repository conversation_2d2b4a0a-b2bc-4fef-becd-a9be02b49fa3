<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsDetailDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsDetailDomain">
        <result column="id" property="id" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="goods_no" property="goodsNo" />
        <result column="third_goods_no" property="thirdGoodsNo" />
        <result column="goods_form" property="goodsForm" />
        <result column="goods_type" property="goodsType" />
        <result column="category_no" property="categoryNo" />
        <result column="brand_no" property="brandNo" />
        <result column="goods_name" property="goodsName" />
        <result column="warehouse_type" property="warehouseType" />
        <result column="parent_goods_no" property="parentGoodsNo" />
        <result column="freight_template_no" property="freightTemplateNo" />
        <result column="sales_area_no" property="salesAreaNo" />
        <result column="source_type" property="sourceType" />
        <result column="goods_model" property="goodsModel" />
        <result column="goods_sale_description" property="goodsSaleDescription" />
        <result column="calculation_unit_no" property="calculationUnitNo" />
        <result column="calculation_unit_symbol" property="calculationUnitSymbol" />
        <result column="calculation_unit_name" property="calculationUnitName" />
        <result column="standard_flag" property="standardFlag" />
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo" />
        <result column="assist_calculation_unit_symbol" property="assistCalculationUnitSymbol" />
        <result column="assist_calculation_unit_name" property="assistCalculationUnitName" />
        <result column="conversion_rate" property="conversionRate" />
        <result column="barcode" property="barcode" />
        <result column="audit_status" property="auditStatus" />
        <result column="pay_type" property="payType" />
        <result column="delivery_type" property="deliveryType" />
        <result column="can_sale" property="canSale" />
        <result column="first_shelf_time" property="firstShelfTime" />
        <result column="audit_message" property="auditMessage" />
        <result column="main_flag" property="mainFlag" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        goods_no, third_goods_no, goods_form, goods_type, category_no, brand_no, goods_name, warehouse_type, parent_goods_no, freight_template_no, sales_area_no, source_type, goods_model, goods_sale_description, calculation_unit_no, calculation_unit_symbol, calculation_unit_name, standard_flag, assist_calculation_unit_no, assist_calculation_unit_symbol, assist_calculation_unit_name, conversion_rate, barcode, audit_status, pay_type, delivery_type, can_sale, first_shelf_time, audit_message, main_flag, merchant_no, merchant_name, store_no, store_name, disable_flag, create_no, modify_no
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.domain.GoodsDetailDomain">
        select
            <include refid="Base_Column_List" />
        from
            goods_detail
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </select>

    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.GoodsDetailDomain" >
        insert into
            goods_detail
        <trim prefix="(" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                id,
            </if>
            <if test="goodsNo != null" >
                goods_no,
            </if>
            <if test="thirdGoodsNo != null" >
                third_goods_no,
            </if>
            <if test="goodsForm != null" >
                goods_form,
            </if>
            <if test="goodsType != null" >
                goods_type,
            </if>
            <if test="categoryNo != null" >
                category_no,
            </if>
            <if test="brandNo != null" >
                brand_no,
            </if>
            <if test="goodsName != null" >
                goods_name,
            </if>
            <if test="warehouseType != null" >
                warehouse_type,
            </if>
            <if test="parentGoodsNo != null" >
                parent_goods_no,
            </if>
            <if test="freightTemplateNo != null" >
                freight_template_no,
            </if>
            <if test="salesAreaNo != null" >
                sales_area_no,
            </if>
            <if test="sourceType != null" >
                source_type,
            </if>
            <if test="goodsModel != null" >
                goods_model,
            </if>
            <if test="goodsSaleDescription != null" >
                goods_sale_description,
            </if>
            <if test="calculationUnitNo != null" >
                calculation_unit_no,
            </if>
            <if test="calculationUnitSymbol != null" >
                calculation_unit_symbol,
            </if>
            <if test="calculationUnitName != null" >
                calculation_unit_name,
            </if>
            <if test="standardFlag != null" >
                standard_flag,
            </if>
            <if test="assistCalculationUnitNo != null" >
                assist_calculation_unit_no,
            </if>
            <if test="assistCalculationUnitSymbol != null" >
                assist_calculation_unit_symbol,
            </if>
            <if test="assistCalculationUnitName != null" >
                assist_calculation_unit_name,
            </if>
            <if test="conversionRate != null" >
                conversion_rate,
            </if>
            <if test="barcode != null" >
                barcode,
            </if>
            <if test="auditStatus != null" >
                audit_status,
            </if>
            <if test="payType != null" >
                pay_type,
            </if>
            <if test="deliveryType != null" >
                delivery_type,
            </if>
            <if test="canSale != null" >
                can_sale,
            </if>
            <if test="firstShelfTime != null" >
                first_shelf_time,
            </if>
            <if test="auditMessage != null" >
                audit_message,
            </if>
            <if test="mainFlag != null" >
                main_flag,
            </if>
            <if test="merchantNo != null" >
                merchant_no,
            </if>
            <if test="merchantName != null" >
                merchant_name,
            </if>
            <if test="storeNo != null" >
                store_no,
            </if>
            <if test="storeName != null" >
                store_name,
            </if>
            <if test="disableFlag != null" >
                disable_flag,
            </if>
            <if test="createNo != null" >
                create_no,
            </if>
            <if test="createName != null" >
                create_name,
            </if>
            <if test="createTime != null" >
                create_time,
            </if>
            <if test="modifyNo != null" >
                modify_no,
            </if>
            <if test="modifyName != null" >
                modify_name,
            </if>
            <if test="modifyTime != null" >
                modify_time,
            </if>
            <if test="deleteFlag != null" >
                delete_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides="," >
            <if test="id != null" >
                #{id,jdbcType=BIGINT},
            </if>
            <if test="goodsNo != null" >
                #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="thirdGoodsNo != null" >
                #{thirdGoodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsForm != null" >
                #{goodsForm,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null" >
                #{goodsType,jdbcType=INTEGER},
            </if>
            <if test="categoryNo != null" >
                #{categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="brandNo != null" >
                #{brandNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null" >
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="warehouseType != null" >
                #{warehouseType,jdbcType=TINYINT},
            </if>
            <if test="parentGoodsNo != null" >
                #{parentGoodsNo,jdbcType=VARCHAR},
            </if>
            <if test="freightTemplateNo != null" >
                #{freightTemplateNo,jdbcType=VARCHAR},
            </if>
            <if test="salesAreaNo != null" >
                #{salesAreaNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null" >
                #{sourceType,jdbcType=TINYINT},
            </if>
            <if test="goodsModel != null" >
                #{goodsModel,jdbcType=VARCHAR},
            </if>
            <if test="goodsSaleDescription != null" >
                #{goodsSaleDescription,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitNo != null" >
                #{calculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitSymbol != null" >
                #{calculationUnitSymbol,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitName != null" >
                #{calculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="standardFlag != null" >
                #{standardFlag,jdbcType=TINYINT},
            </if>
            <if test="assistCalculationUnitNo != null" >
                #{assistCalculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="assistCalculationUnitSymbol != null" >
                #{assistCalculationUnitSymbol,jdbcType=VARCHAR},
            </if>
            <if test="assistCalculationUnitName != null" >
                #{assistCalculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="conversionRate != null" >
                #{conversionRate,jdbcType=DECIMAL},
            </if>
            <if test="barcode != null" >
                #{barcode,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null" >
                #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="payType != null" >
                #{payType,jdbcType=VARCHAR},
            </if>
            <if test="deliveryType != null" >
                #{deliveryType,jdbcType=VARCHAR},
            </if>
            <if test="canSale != null" >
                #{canSale,jdbcType=TINYINT},
            </if>
            <if test="firstShelfTime != null" >
                #{firstShelfTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditMessage != null" >
                #{auditMessage,jdbcType=VARCHAR},
            </if>
            <if test="mainFlag != null" >
                #{mainFlag,jdbcType=TINYINT},
            </if>
            <if test="merchantNo != null" >
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null" >
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null" >
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null" >
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null" >
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null" >
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null" >
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null" >
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null" >
                #{deleteFlag,jdbcType=TINYINT},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsDetailDomain" >
        update
            goods_detail
        <set >
            <if test="goodsNo != null" >
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="thirdGoodsNo != null" >
                third_goods_no = #{thirdGoodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsForm != null" >
                goods_form = #{goodsForm,jdbcType=INTEGER},
            </if>
            <if test="goodsType != null" >
                goods_type = #{goodsType,jdbcType=INTEGER},
            </if>
            <if test="categoryNo != null" >
                category_no = #{categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="brandNo != null" >
                brand_no = #{brandNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null" >
                goods_name = #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="warehouseType != null" >
                warehouse_type = #{warehouseType,jdbcType=TINYINT},
            </if>
            <if test="parentGoodsNo != null" >
                parent_goods_no = #{parentGoodsNo,jdbcType=VARCHAR},
            </if>
            <if test="freightTemplateNo != null" >
                freight_template_no = #{freightTemplateNo,jdbcType=VARCHAR},
            </if>
            <if test="salesAreaNo != null" >
                sales_area_no = #{salesAreaNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null" >
                source_type = #{sourceType,jdbcType=TINYINT},
            </if>
            <if test="goodsModel != null" >
                goods_model = #{goodsModel,jdbcType=VARCHAR},
            </if>
            <if test="goodsSaleDescription != null" >
                goods_sale_description = #{goodsSaleDescription,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitNo != null" >
                calculation_unit_no = #{calculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitSymbol != null" >
                calculation_unit_symbol = #{calculationUnitSymbol,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitName != null" >
                calculation_unit_name = #{calculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="standardFlag != null" >
                standard_flag = #{standardFlag,jdbcType=TINYINT},
            </if>
            <if test="assistCalculationUnitNo != null" >
                assist_calculation_unit_no = #{assistCalculationUnitNo,jdbcType=VARCHAR},
            </if>
            <if test="assistCalculationUnitSymbol != null" >
                assist_calculation_unit_symbol = #{assistCalculationUnitSymbol,jdbcType=VARCHAR},
            </if>
            <if test="assistCalculationUnitName != null" >
                assist_calculation_unit_name = #{assistCalculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="conversionRate != null" >
                conversion_rate = #{conversionRate,jdbcType=DECIMAL},
            </if>
            <if test="barcode != null" >
                barcode = #{barcode,jdbcType=VARCHAR},
            </if>
            <if test="auditStatus != null" >
                audit_status = #{auditStatus,jdbcType=INTEGER},
            </if>
            <if test="payType != null" >
                pay_type = #{payType,jdbcType=VARCHAR},
            </if>
            <if test="deliveryType != null" >
                delivery_type = #{deliveryType,jdbcType=VARCHAR},
            </if>
            <if test="canSale != null" >
                can_sale = #{canSale,jdbcType=TINYINT},
            </if>
            <if test="firstShelfTime != null" >
                first_shelf_time = #{firstShelfTime,jdbcType=TIMESTAMP},
            </if>
            <if test="auditMessage != null" >
                audit_message = #{auditMessage,jdbcType=VARCHAR},
            </if>
            <if test="mainFlag != null" >
                main_flag = #{mainFlag,jdbcType=TINYINT},
            </if>
            <if test="merchantNo != null" >
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null" >
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null" >
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null" >
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null" >
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null" >
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null" >
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null" >
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null" >
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.GoodsDetailDomain" >
        update
            goods_detail
        set
            <if test="modifyNo != null" >
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
      </update>
</mapper>
