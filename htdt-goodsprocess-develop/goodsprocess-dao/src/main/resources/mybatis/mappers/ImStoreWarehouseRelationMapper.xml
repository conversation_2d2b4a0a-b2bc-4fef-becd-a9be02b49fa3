<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.ImStoreWarehouseRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.ImStoreWarehouseRelationDomain">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="warehouse_no" property="warehouseNo"/>
        <result column="warehouse_code" property="warehouseCode"/>
        <result column="warehouse_name" property="warehouseName"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        warehouse_no, warehouse_code, warehouse_name, merchant_no, merchant_name, store_no, store_name, disable_flag, create_no, modify_no, company_no, company_name, branch_no, branch_name
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.ImStoreWarehouseRelationDomain">
        update
        im_store_warehouse_relation
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime},
        </if>
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="warehouseNo != null and warehouseNo != ''">
                    and warehouse_no = #{warehouseNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>


    <select id="getStoreWarehouse" resultType="cn.htdt.goodsprocess.dto.response.warehouse.ResWareHouseInfoDTO">
        select iw.warehouse_no,
               iw.warehouse_code,
               iw.warehouse_name,
               iw.warehouse_type,
               iw.self_warehouse_flag,
               iw.virtual_warehouse_flag
        from im_store_warehouse_relation iswr
                 left join im_warehouse iw on iswr.warehouse_no = iw.warehouse_no
        where iswr.store_no = #{storeNo}
          and virtual_warehouse_flag = 1
    </select>
</mapper>
