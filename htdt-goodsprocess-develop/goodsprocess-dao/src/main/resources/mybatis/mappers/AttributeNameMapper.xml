<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.AttributeNameDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.AttributeNameDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="attribute_code" property="attributeCode"/>
        <result column="attribute_name" property="attributeName"/>
        <result column="data_source_type" property="dataSourceType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        attribute_code, attribute_name
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" parameterType="cn.htdt.goodsprocess.domain.AttributeNameDomain"
            resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        attribute_name
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and merchant_no = #{merchantNo}
                and store_no = #{storeNo}
                <if test="attributeCode != null and attributeCode != ''">
                    and attribute_code = #{attributeCode}
                </if>
                <if test="attributeName != null and attributeName != ''">
                    and attribute_name = #{attributeName}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <select id="selectListByAttributeName" resultType="cn.htdt.goodsprocess.vo.AtomAttributeVo">
        select
        an.attribute_code attributeCode,
        an.attribute_name attributeName,
        an.data_source_type dataSourceType
        from attribute_name an
        <where>
            an.merchant_no = #{domain.merchantNo}
            and an.store_no = #{domain.storeNo}
            <if test="domain.attributeName != null and domain.attributeName != ''">
                and an.attribute_name LIKE CONCAT (CONCAT('%',#{domain.attributeName}),'%')
            </if>
            and an.delete_flag = 1
        </where>
        <choose>
            <when test="orderByColumn != null and orderByColumn == 'modifyTime'">
                ORDER BY an.modify_time DESC
            </when>
            <otherwise>
                ORDER BY an.attribute_code DESC
            </otherwise>
        </choose>
    </select>

    <select id="selectAttributeValueListByAttributeCode" resultType="cn.htdt.goodsprocess.domain.AttributeValueDomain">
        select
            id,
            attribute_value_code attributeValueCode,
            attribute_value_name attributeValueName,
            attribute_code attributeCode
        from attribute_value
        WHERE delete_flag = 1
            and attribute_code IN
            <foreach collection="attributeCodeList" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        ORDER BY id
    </select>

    <select id="selectInfoByAttributeCode" parameterType="cn.htdt.goodsprocess.domain.AttributeNameDomain"
            resultType="cn.htdt.goodsprocess.vo.AtomAttributeVo">
        select
        an.attribute_code attributeCode,
        an.attribute_name attributeName,
        av.attribute_value_code attributeValueCode,
        av.attribute_value_name attributeValueName,
        an.data_source_type dataSourceType
        from
        attribute_name an
        LEFT JOIN attribute_value av ON av.attribute_code = an.attribute_code and av.delete_flag = 1
        <where>
            an.attribute_code = #{attributeCode}
            and an.delete_flag = 1
        </where>
        ORDER BY
        av.attribute_value_code
    </select>

    <update id="updateByAttributeCode" parameterType="cn.htdt.goodsprocess.domain.AttributeNameDomain">
        update
        attribute_name
        <set>
            <if test="attributeName != null and attributeName != ''">
                attribute_name = #{attributeName,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
        </set>
        <where>
            attribute_code = #{attributeCode}
            <include refid="Base_Column_Where"/>
        </where>
    </update>

    <update id="batchLogicDelete">
        update
        attribute_name
        set
        <if test="domain.modifyNo != null and domain.modifyNo != ''">
            modify_no = #{domain.modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="domain.modifyName != null and domain.modifyName != ''">
            modify_name = #{domain.modifyName,jdbcType=VARCHAR},
        </if>
        modify_time = NOW(),
        delete_flag = 2
        <where>
            attribute_code IN
            <foreach collection="list" item="attributeCode" open="(" close=")" separator=",">
                #{attributeCode}
            </foreach>
            <include refid="Base_Column_Where"/>
        </where>
    </update>
</mapper>
