<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htdt.goodsprocess.dao.CloudPoolGoodsUpdateTimeDao">
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.CloudPoolGoodsUpdateTimeDomain">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="store_no" property="storeNo" jdbcType="VARCHAR"/>
        <result column="goods_update_time" property="goodsUpdateTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, store_no, goods_update_time
  </sql>
    <select id="selectByPrimaryKey" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.CloudPoolGoodsUpdateTimeDomain">
        select
        <include refid="Base_Column_List"/>
        from cloud_pool_goods_update_time
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
            </trim>
        </where>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.vo.AtomReqCloudPoolGoodsUpdateTimeVo">
        update
        cloud_pool_goods_update_time
        set
        goods_update_time = #{newGoodsUpdateTime,jdbcType=TIMESTAMP}
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="goodsUpdateTime != null">
                    and goods_update_time = #{goodsUpdateTime}
                </if>
            </trim>
        </where>
    </update>
</mapper>
