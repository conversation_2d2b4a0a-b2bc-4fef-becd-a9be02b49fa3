<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsImeiOrderRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsImeiOrderRelationDomain">
        <result column="id" property="id" />
        <result column="goods_no" property="goodsNo" />
        <result column="order_no" property="orderNo" />
        <result column="order_item_no" property="orderItemNo" />
        <result column="imei" property="imei" />
        <result column="warehouse_no" property="warehouseNo" />
        <result column="order_type" property="orderType" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="company_no" property="companyNo" />
        <result column="company_name" property="companyName" />
        <result column="branch_no" property="branchNo" />
        <result column="branch_name" property="branchName" />
    </resultMap>
    <resultMap id="VoBaseResultMap" type="cn.htdt.goodsprocess.vo.AtomGoodsImeiOrderRelationVo" extends="BaseResultMap">
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, goods_no, imei, order_no, order_item_no, order_type, disable_flag, create_no, create_name, create_time, modify_no, modify_name, modify_time, delete_flag, company_no, company_name, branch_no, branch_name
    </sql>

    <sql id="Base_Column_List_R">
        r.id, r.goods_no, r.imei, r.order_no, r.order_item_no, r.order_type, r.disable_flag, r.create_no, r.create_name, r.create_time, r.modify_no, r.modify_name, r.modify_time, r.delete_flag, r.company_no, r.company_name, r.branch_no, r.branch_name
    </sql>

    <sql id="Base_Column_Where">
        <if test="companyNo != null and companyNo != ''">
            and company_no = #{companyNo}
        </if>
        <if test="branchNo != null and branchNo != ''">
            and branch_no = #{branchNo}
        </if>
        <if test="orderNo != null and orderNo != ''">
            and order_no = #{orderNo}
        </if>
        <if test="orderItemNo != null and orderItemNo != ''">
            and order_item_no = #{orderItemNo}
        </if>
        <if test="goodsNo != null and goodsNo != ''">
            and goods_no = #{goodsNo}
        </if>
        <if test="warehouseNo != null and warehouseNo != ''">
            and warehouse_no = #{warehouseNo}
        </if>
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.domain.GoodsImeiOrderRelationDomain" >
        select
        <include refid="Base_Column_List" />
        from
        goods_imei_order_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="imei != null and imei != ''">
                    and imei = #{imei}
                </if>
                <if test="orderType != null and orderType != ''" >
                    and order_type = #{orderType,jdbcType=TINYINT}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </select>

    <select id="selectGoodsImeiOrderRelationR" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsImeiOrderRelationVo" >
        select
        <include refid="Base_Column_List_R" />
        from
        goods_imei_order_relation r
        left join goods_imei i on r.goods_no=i.goods_no and r.imei=i.imei
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and r.delete_flag = 1
                and i.delete_flag=1 and i.disable_flag=2 and i.sale_flag=1
                <if test="imei != null and imei != ''">
                    and r.imei = #{imei}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    and r.order_no = #{orderNo}
                </if>
                <if test="orderItemNo != null and orderItemNo != ''">
                    and r.order_item_no = #{orderItemNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and r.goods_no = #{goodsNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
            </trim>
        </where>
    </select>

    <select id="selectGoodsImeiOrderRelation" resultMap="VoBaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsImeiOrderRelationVo" >
        select
        <include refid="Base_Column_List_R" />
        from
        goods_imei_order_relation r
        left join goods_imei i on r.goods_no=i.goods_no and r.imei=i.imei and r.warehouse_no=i.warehouse_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and r.delete_flag = 1
                <if test="imei != null and imei != ''">
                    and r.imei = #{imei}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    and r.order_no = #{orderNo}
                </if>
                <if test="orderItemNo != null and orderItemNo != ''">
                    and r.order_item_no = #{orderItemNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and r.goods_no = #{goodsNo}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and r.warehouse_no = #{warehouseNo}
                </if>
            </trim>
        </where>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsImeiOrderRelationDomain" >
        update
          goods_imei_order_relation
        <set >
            <if test="goodsNo != null and goodsNo != ''" >
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="imei != null and imei != ''" >
                imei = #{imei,jdbcType=VARCHAR},
            </if>
            <if test="orderNo != null and orderNo != ''" >
                order_no = #{orderNo,jdbcType=VARCHAR},
            </if>
            <if test="orderItemNo != null and orderItemNo != ''" >
                order_item_no = #{orderItemNo,jdbcType=VARCHAR},
            </if>
            <if test="orderType != null and orderType != ''" >
                order_type = #{orderType,jdbcType=TINYINT},
            </if>
            <if test="disableFlag != null" >
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null" >
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null" >
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null" >
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null" >
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="warehouseNo != null and warehouseNo != ''">
                warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="imei != null and imei != ''">
                    and imei = #{imei}
                </if>
                <if test="orderType != null and orderType != ''" >
                    and order_type = #{orderType,jdbcType=TINYINT}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.GoodsImeiOrderRelationDomain" >
        update
          goods_imei_order_relation
        set
        <if test="modifyNo != null" >
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null" >
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        <if test="modifyTime != null" >
            modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        </if>
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="imei != null and imei != ''">
                    and imei = #{imei}
                </if>
                <if test="orderType != null and orderType != ''" >
                    and order_type = #{orderType,jdbcType=TINYINT}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>
</mapper>
