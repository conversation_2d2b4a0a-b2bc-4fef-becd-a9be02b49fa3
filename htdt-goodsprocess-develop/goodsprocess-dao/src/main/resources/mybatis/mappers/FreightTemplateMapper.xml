<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.FreightTemplateDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
               type="cn.htdt.goodsprocess.domain.FreightTemplateDomain">
        <result column="id" property="id"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="template_no" property="templateNo"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="company_no" property="companyNo"/>
        <result column="name" property="name"/>
        <result column="template_type" property="templateType"/>
        <result column="type" property="type"/>
        <result column="charge_way" property="chargeWay"/>
        <result column="default_flag" property="defaultFlag"/>
        <result column="channel_mode" property="channelMode"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="create_no" property="createNo"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
        <result column="distribution_code" property="distributionCode"/>
        <result column="distribution_type" property="distributionType"/>
        <result column="distribution_region" property="distributionRegion"/>
        <result column="template_first_piece" property="templateFirstPiece"/>
        <result column="template_first_amount" property="templateFirstAmount"/>
        <result column="template_next_piece" property="templateNextPiece"/>
        <result column="template_next_amount" property="templateNextAmount"/>
        <result column="template_fixed_amount" property="templateFixedAmount"/>
        <result column="store_no" property="storeNo"/>
        <result column="template_source_type" property="templateSourceType"/>
        <result column="template_free_flag" property="templateFreeFlag"/>
        <result column="template_free_amount" property="templateFreeAmount"/>
    </resultMap>

    <resultMap id="FreightTemplateDefaultMap" extends="BaseResultMap"
               type="cn.htdt.goodsprocess.vo.AtomFreightTemplateVo">
        <result column="template_default_flag" property="templateDefaultFlag"/>
    </resultMap>


    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
		id,
		create_name,
		create_time,
		modify_name,
		modify_time,
		delete_flag,
		template_no,merchant_no, company_no, name, template_type,
		type,
		charge_way, default_flag,
		channel_mode, disable_flag, create_no,
		modify_no, company_name,
		branch_no,
		branch_name,distribution_code,distribution_type,distribution_region,
		template_first_piece,template_first_amount,template_next_piece,template_next_amount,
		template_fixed_amount,store_no,template_source_type,template_free_flag,template_free_amount
	</sql>

    <sql id="T_Base_Column_List">
		t.id,
		t.create_name,
		t.create_time,
		t.modify_name,
		t.modify_time,
		t.delete_flag,
		t.template_no, t.merchant_no, t.company_no, t.name, t.template_type,
		t.type,
		t.charge_way, t.default_flag,
		t.channel_mode, t.disable_flag, t.create_no,
		t.modify_no, t.company_name,
		t.branch_no,
		t.branch_name,t.distribution_code,t.distribution_type,t.distribution_region,
		t.template_first_piece,t.template_first_amount,t.template_next_piece,t.template_next_amount,
		t.template_fixed_amount,t.store_no,t.template_source_type,t.template_free_flag,t.template_free_amount
	</sql>

    <sql id="Base_Column_Where">
		and delete_flag = 1
	</sql>

    <sql id="T_Base_Column_Where">
		and t.delete_flag = 1
	</sql>

    <sql id="Base_Column_Default_List">
		t.create_name,
		t.create_time,
		t.modify_name,
		t.modify_time,
		t.delete_flag,
		t.template_no,
		t.merchant_no,
		t.company_no,
		t.name,
		t.template_type,
		t.type,
		t.charge_way,
		t.default_flag,
		t.channel_mode,
		t.disable_flag,
		t.create_no,
		t.modify_no,
		t.company_name,
		t.branch_no,
		t.branch_name,
		t.distribution_code,
		t.distribution_type,
		t.distribution_region,
		t.template_first_piece,
		t.template_first_amount,
		t.template_next_piece,
		t.template_next_amount,
		t.template_fixed_amount,
		t.store_no,
		t.template_source_type,
		t.template_free_flag,
		t.template_free_amount,
		d.template_default_flag
  	</sql>

    <!-- 修改 -->
    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.FreightTemplateDomain">
        update
        freight_template
        <set>
            <if test="merchantNo != null">
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="companyNo != null">
                company_no = #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="templateType != null">
                template_type = #{templateType,jdbcType=VARCHAR},
            </if>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="chargeWay != null">
                charge_way = #{chargeWay,jdbcType=VARCHAR},
            </if>
            <if test="channelMode != null">
                channel_mode = #{channelMode,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                company_name = #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null">
                branch_no = #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null">
                branch_name = #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="defaultFlag != null">
                default_flag = #{defaultFlag,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="distributionCode != null">
                distribution_code = #{distributionCode,jdbcType=VARCHAR},
            </if>
            <if test="distributionType != null">
                distribution_type = #{distributionType,jdbcType=VARCHAR},
            </if>
            <if test="distributionType != null">
                distribution_region =
                #{distributionType,jdbcType=LONGVARCHAR},
            </if>
            <if test="templateFirstPiece != null">
                template_first_piece = #{templateFirstPiece,jdbcType=INTEGER},
            </if>
            <if test="templateFirstAmount != null">
                template_first_amount = #{templateFirstAmount,jdbcType=DECIMAL},
            </if>
            <if test="templateNextPiece != null">
                template_next_piece = #{templateNextPiece,jdbcType=INTEGER},
            </if>
            <if test="templateNextAmount != null">
                template_next_amount = #{templateNextAmount,jdbcType=DECIMAL},
            </if>
            <if test="templateFixedAmount != null">
                template_fixed_amount = #{templateFixedAmount,jdbcType=DECIMAL},
            </if>
            <if test="storeNo != null">
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="templateSourceType != null">
                template_source_type = #{templateSourceType,jdbcType=VARCHAR},
            </if>
            <if test="templateFreeFlag != null">
                template_free_flag = #{templateFreeFlag,jdbcType=INTEGER},
            </if>
            <if test="templateFreeAmount != null">
                template_free_amount = #{templateFreeAmount,jdbcType=DECIMAL},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="templateNo != null and templateNo != ''">
                    and template_no = #{templateNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <!-- 逻辑删除 -->
    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.FreightTemplateDomain">
        update
        freight_template
        set
        <if test="modifyNo != null">
            modify_no = #{modifyNo,jdbcType=VARCHAR},
        </if>
        <if test="modifyName != null">
            modify_name = #{modifyName,jdbcType=VARCHAR},
        </if>
        <if test="modifyTime != null">
            modify_time = #{modifyTime,jdbcType=TIMESTAMP},
        </if>
        delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="templateNo != null and templateNo != ''">
                    and template_no = #{templateNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <select id="selectByParams" resultType="cn.htdt.goodsprocess.domain.FreightTemplateDomain">
        select
        <include refid="Base_Column_List"/>
        from
        freight_template
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="templateNo != null and templateNo != ''">
                    and template_no = #{templateNo}
                </if>
                <if test="name != null and name != ''">
                    and name = #{name}
                </if>
                <if test="templateSourceType == '1003'">
                    <![CDATA[
						and (( store_no = #{storeNo}
						or ( merchant_no = #{merchantNo}
							and template_source_type = '1002'))
						or template_source_type = '1004')
					]]>
                </if>
                <if test="templateSourceType == '1002'">
                    and merchant_no = #{merchantNo}
                    and template_source_type = '1002'
                </if>
                <if test="templateSourceType == '1001'">
                    <!-- 目前平台只有1个，查询条件只有1001来源 -->
                    and template_source_type = '1001'
                </if>
                <include refid="Base_Column_Where"/>
                order by modify_time desc
            </trim>
        </where>
    </select>

    <!-- 根据运费模版类型统计运费模板个数,店铺或者单店新增模板来源为1004的系统模板 -->
    <select id="selectFreightTemplateCount" resultType="cn.htdt.goodsprocess.vo.AtomFreightTemplateVo"
            parameterType="cn.htdt.goodsprocess.domain.FreightTemplateDomain">
        SELECT
        t1.allTemplateCount,
        t2.merchantTemplateCount
        <choose>
            <when test="storeNo != null and storeNo != ''">
                ,t3.storeTemplateCount
            </when>
            <otherwise>
                ,0 AS storeTemplateCount
            </otherwise>
        </choose>
        FROM
        (
        SELECT
        COUNT( 1 ) AS allTemplateCount
        FROM
        freight_template t
        WHERE
        (
        <if test="storeNo != null and storeNo != ''">
            ((t.store_no = #{storeNo} AND t.template_source_type = '1003') or t.template_source_type = '1004') OR
        </if>
        (t.merchant_no = #{merchantNo} AND t.template_source_type = '1002')
        )
        AND t.delete_flag = 1
        ) t1,
        (
        SELECT
        COUNT( 1 ) AS merchantTemplateCount
        FROM
        freight_template t
        WHERE
        t.merchant_no = #{merchantNo} AND t.template_source_type = '1002'
        AND t.delete_flag = 1
        ) t2
        <if test="storeNo != null and storeNo != ''">
            ,
            (
            SELECT
            COUNT( 1 ) AS storeTemplateCount
            FROM
            freight_template t
            WHERE
            ((t.store_no = #{storeNo} AND t.template_source_type = '1003') or t.template_source_type = '1004')
            AND t.delete_flag = 1
            ) t3
        </if>
    </select>

    <!-- 分页查询运费模板列表 -->
    <select id="selectFreightTemplateListPage" resultMap="FreightTemplateDefaultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqFreightTemplateVo">
        select distinct
        <include refid="Base_Column_Default_List"/>
        from
        freight_template t
        LEFT JOIN freight_template_default d ON t.template_no = d.freight_template_no
        AND d.merchant_no = #{merchantNo}
        <if test="storeNo != null and storeNo != ''">
            <if test="templateSourceType == '1003'">
                AND d.store_no = #{storeNo}
            </if>
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="templateNo != null and templateNo != ''">
                    and t.template_no = #{templateNo}
                </if>
                <if test="templateNos != null and templateNos.size() > 0">
                    and template_no in
                    <foreach collection="templateNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <!--店铺或者单店, 模板来源新增1004, 系统创建-->
                <if test="templateSourceType == '1003'">
                    <![CDATA[
						and ( t.store_no = #{storeNo}
						or ( t.merchant_no = #{merchantNo}
							and t.template_source_type = '1002')
                        or t.template_source_type = '1004'
					  )
					]]>
                </if>
                <!--商家-->
                <if test="templateSourceType == '1002'">
                    and t.merchant_no = #{merchantNo}
                    and template_source_type = '1002'
                </if>
                <if test="templateSourceType == '1001'">
                    <!-- 目前平台只有1个，查询条件只有1001来源 -->
                    and t.template_source_type = '1001'
                </if>

                <!--bossapp使用, 0:全部模板, 模板来源新增1004, 系统创建-->
                <if test="templateSourceType == '0'.toString()">
                    AND
                    (
                    <if test="storeNo != null and storeNo != ''">
                        ((t.store_no = #{storeNo} AND t.template_source_type = '1003') or t.template_source_type = '1004') OR
                    </if>
                    (t.merchant_no = #{merchantNo} AND t.template_source_type = '1002')
                    )
                </if>
                <!--bossapp使用, 模板来源新增1004, 系统创建-->
                <if test="templateSourceType == '1'.toString()">
                    and ((t.store_no = #{storeNo} and template_source_type = '1003')
                    or t.template_source_type = '1004')
                </if>
                <!--bossapp使用, 商家模板-->
                <if test="templateSourceType == '2'.toString()">
                    and t.merchant_no = #{merchantNo}
                    and template_source_type = '1002'
                </if>
                <if test="name != null and name != ''">
                    <![CDATA[
						and t.name like CONCAT(CONCAT('%', #{name}),'%')
					]]>
                </if>
                <if test="templateDefaultFlag != null and templateDefaultFlag != ''">
                    and d.template_default_flag = #{templateDefaultFlag}
                </if>
                and t.delete_flag = 1
                order by t.modify_time desc
            </trim>
        </where>
    </select>

    <!-- 分页查询运费模板列表, 未添加系统创建的运费模板之前的逻辑-->
    <select id="selectFreightTemplateListForPage" resultMap="FreightTemplateDefaultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqFreightTemplateVo">
        select
        <include refid="Base_Column_Default_List"/>
        from
        freight_template t
        LEFT JOIN freight_template_default d ON t.template_no = d.freight_template_no
        AND t.merchant_no = #{merchantNo}
        <if test="storeNo != null and storeNo != ''">
            AND t.store_no = #{storeNo}
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="templateNo != null and templateNo != ''">
                    and t.template_no = #{templateNo}
                </if>
                <if test="templateNos != null and templateNos.size() > 0">
                    and template_no in
                    <foreach collection="templateNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="templateSourceType == '1003'">
                    <![CDATA[
						and ( t.store_no = #{storeNo}
						or ( t.merchant_no = #{merchantNo}
							and t.template_source_type = '1002'))
					]]>
                </if>
                <if test="templateSourceType == '1002'">
                    and t.merchant_no = #{merchantNo}
                    and template_source_type = '1002'
                </if>
                <if test="templateSourceType == '1001'">
                    <!-- 目前平台只有1个，查询条件只有1001来源 -->
                    and t.template_source_type = '1001'
                </if>
                <if test="templateSourceType == '0'.toString()">
                    AND
                    (
                    <if test="storeNo != null and storeNo != ''">
                        (t.store_no = #{storeNo} AND t.template_source_type = '1003') OR
                    </if>
                    (t.merchant_no = #{merchantNo} AND t.template_source_type = '1002')
                    )
                </if>
                <if test="templateSourceType == '1'.toString()">
                    and t.store_no = #{storeNo}
                    and template_source_type = '1003'
                </if>
                <if test="templateSourceType == '2'.toString()">
                    and t.merchant_no = #{merchantNo}
                    and template_source_type = '1002'
                </if>
                <if test="name != null and name != ''">
                    <![CDATA[
						and t.name like CONCAT(CONCAT('%', #{name}),'%')
					]]>
                </if>
                and t.delete_flag = 1
                order by t.modify_time desc
            </trim>
        </where>
    </select>

    <!-- 查询是否存在默认模板 -->
    <select id="getDefaultFreightTemplateList" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqFreightTemplateVo">
        select
        <include refid="Base_Column_Default_List"/>,
        d.template_default_no
        from
        freight_template t
        INNER JOIN freight_template_default d ON t.template_no = d.freight_template_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo != null and merchantNo != ''">
                    and d.merchant_no = #{merchantNo}
                </if>
                and d.store_no = #{storeNo}
                and d.template_default_flag = '1001'
                and t.delete_flag = 1
            </trim>
        </where>
    </select>


    <select id="selectFreightTemplateByNo" resultType="cn.htdt.goodsprocess.domain.FreightTemplateDomain">
        select
        <include refid="Base_Column_List"/>
        from
        freight_template
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and template_no = #{templateNo}
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>


    <select id="selectBytemplateNos" resultMap="BaseResultMap"
            parameterType="java.util.List">
        select
        <include refid="Base_Column_List"/>
        from
        freight_template
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and template_no in
                <foreach collection="templateNos" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
                <include refid="Base_Column_Where"/>
                order by template_fixed_amount desc
            </trim>
        </where>
    </select>

    <select id="selectDefaultByParams" resultType="cn.htdt.goodsprocess.domain.FreightTemplateDomain">
        select
        <include refid="T_Base_Column_List"/>
        from
        freight_template t
        LEFT JOIN freight_template_default d ON t.template_no = d.freight_template_no
        <if test="templateSourceType == '1003'">
            and d.store_no = #{storeNo}
        </if>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="defaultFlag != null and defaultFlag != ''">
                    and d.template_default_flag = #{defaultFlag}
                </if>
                <!--店铺或者单店, 模板来源新增1004, 系统创建-->
                <if test="templateSourceType == '1003'">
                    <![CDATA[
						and ( t.store_no = #{storeNo}
						or ( t.merchant_no = #{merchantNo}
							and t.template_source_type = '1002')
							or t.template_source_type = '1004')
					]]>
                </if>
                <if test="templateSourceType == '1002'">
                    and t.merchant_no = #{merchantNo}
                    and t.template_source_type = '1002'
                </if>
                <if test="templateSourceType == '1001'">
                    <!-- 目前平台只有1个，查询条件只有1001来源 -->
                    and t.template_source_type = '1001'
                </if>
                <include refid="T_Base_Column_Where"/>
                order by t.modify_time desc
            </trim>
        </where>
    </select>

    <select id="getSystemFreightTemplate" resultType="cn.htdt.goodsprocess.domain.FreightTemplateDomain">
        select
        <include refid="Base_Column_List"/>
        from
        freight_template
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="templateSourceType != null and templateSourceType != ''">
                    and template_source_type = #{templateSourceType}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

</mapper>
