<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.DifferenceOrderGoodsDao">
    <resultMap id="ResultMap" type="cn.htdt.goodsprocess.domain.DifferenceOrderGoodsDomain">
        <result column="id" property="id"/>
        <result column="difference_item_no" property="differenceItemNo"/>
        <result column="difference_no" property="differenceNo"/>
        <result column="merchant_goods_no" property="merchantGoodsNo"/>
        <result column="merchant_goods_name" property="merchantGoodsName"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="barcode" property="barcode"/>
        <result column="goods_help_code" property="goodsHelpCode"/>
        <result column="item_price" property="itemPrice"/>
        <result column="requisition_num" property="requisitionNum"/>
        <result column="requisition_unit" property="requisitionUnit"/>
        <result column="calculation_unit_no" property="calculationUnitNo"/>
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo"/>
        <result column="main_unit_num" property="mainUnitNum"/>
        <result column="assist_unit_num" property="assistUnitNum"/>
        <result column="standard_flag" property="standardFlag"/>
        <result column="difference_count" property="differenceCount"/>
        <result column="difference_amount" property="differenceAmount"/>
        <result column="no_storage_count" property="noStorageCount"/>
        <result column="in_storage_count" property="inStorageCount"/>
        <result column="company_no" property="companyNo"/>
        <result column="company_name" property="companyName"/>
        <result column="branch_no" property="branchNo"/>
        <result column="branch_name" property="branchName"/>
        <result column="platform_type" property="platformType"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="create_no" property="createNo"/>
        <result column="create_name" property="createName"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_no" property="modifyNo"/>
        <result column="modify_name" property="modifyName"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <resultMap id="DifferenceOrderGoodsAndWareHouseResultMap" type="cn.htdt.goodsprocess.dto.response.differenceorder.ResDifferenceOrderGoodsDTO" extends="ResultMap">
        <result column="warehouse_flag" property="warehouseFlag"/>
        <result column="validity_period_manage_flag" property="validityPeriodManageFlag"/>
        <result column="quality_guarantee_period" property="qualityGuaranteePeriod"/>
        <result column="shelf_life_unit" property="shelfLifeUnit"/>
        <result column="multi_unit_type" property="multiUnitType"/>
        <result column="multi_unit_goods_no" property="multiUnitGoodsNo"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,difference_item_no,difference_no,merchant_goods_no,merchant_goods_name,goods_no,goods_name,barcode,goods_help_code,item_price,requisition_num,requisition_unit,calculation_unit_no,assist_calculation_unit_no,main_unit_num,assist_unit_num,standard_flag,difference_count,difference_amount,no_storage_count,in_storage_count,company_no,company_name,branch_no,branch_name,platform_type,merchant_no,merchant_name,store_no,store_name,create_no,create_name,create_time,modify_no,modify_name,modify_time,delete_flag
    </sql>

    <sql id="Set_Column">
        <set>

            <if test="differenceItemNo != null">difference_item_no=#{differenceItemNo},</if>
            <if test="differenceNo != null">difference_no=#{differenceNo},</if>
            <if test="merchantGoodsNo != null">merchant_goods_no=#{merchantGoodsNo},</if>
            <if test="merchantGoodsName != null">merchant_goods_name=#{merchantGoodsName},</if>
            <if test="goodsNo != null">goods_no=#{goodsNo},</if>
            <if test="goodsName != null">goods_name=#{goodsName},</if>
            <if test="barcode != null">barcode=#{barcode},</if>
            <if test="goodsHelpCode != null">goods_help_code=#{goodsHelpCode},</if>
            <if test="itemPrice != null">item_price=#{itemPrice},</if>
            <if test="requisitionNum != null">requisition_num=#{requisitionNum},</if>
            <if test="requisitionUnit != null">requisition_unit=#{requisitionUnit},</if>
            <if test="calculationUnitNo != null">calculation_unit_no=#{calculationUnitNo},</if>
            <if test="assistCalculationUnitNo != null">assist_calculation_unit_no=#{assistCalculationUnitNo},</if>
            <if test="mainUnitNum != null">main_unit_num=#{mainUnitNum},</if>
            <if test="assistUnitNum != null">assist_unit_num=#{assistUnitNum},</if>
            <if test="standardFlag != null">standard_flag=#{standardFlag},</if>
            <if test="differenceCount != null">difference_count=#{differenceCount},</if>
            <if test="differenceAmount != null">difference_amount=#{differenceAmount},</if>
            <if test="noStorageCount != null">no_storage_count=#{noStorageCount},</if>
            <if test="inStorageCount != null">in_storage_count=#{inStorageCount},</if>
            <if test="companyNo != null">company_no=#{companyNo},</if>
            <if test="companyName != null">company_name=#{companyName},</if>
            <if test="branchNo != null">branch_no=#{branchNo},</if>
            <if test="branchName != null">branch_name=#{branchName},</if>
            <if test="platformType != null">platform_type=#{platformType},</if>
            <if test="merchantNo != null">merchant_no=#{merchantNo},</if>
            <if test="merchantName != null">merchant_name=#{merchantName},</if>
            <if test="storeNo != null">store_no=#{storeNo},</if>
            <if test="storeName != null">store_name=#{storeName},</if>
            <if test="createNo != null">create_no=#{createNo},</if>
            <if test="createName != null">create_name=#{createName},</if>
            <if test="createTime != null">create_time=#{createTime},</if>
            <if test="modifyNo != null">modify_no=#{modifyNo},</if>
            <if test="modifyName != null">modify_name=#{modifyName},</if>
            <if test="modifyTime != null">modify_time=#{modifyTime},</if>
            <if test="deleteFlag != null">delete_flag=#{deleteFlag},</if>
        </set>
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="getDifferenceOrderGoodsList" parameterType="cn.htdt.goodsprocess.vo.AtomDifferenceOrderGoodsVo" resultMap="ResultMap">
        select
            <include refid="Base_Column_List" />
        from difference_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="differenceItemNo != null and differenceItemNo != ''">
                    and difference_item_no = #{differenceItemNo}
                </if>
                <if test="differenceItemNos != null and differenceItemNos.size() > 0">
                    and difference_item_no in
                    <foreach collection="differenceItemNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="differenceNo != null and differenceNo != ''">
                    and difference_no = #{differenceNo}
                </if>
                <if test="differenceNos != null and differenceNos.size() > 0">
                    and difference_no in
                    <foreach collection="differenceNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="merchantGoodsNo != null and merchantGoodsNo != ''">
                    and merchant_goods_no = #{merchantGoodsNo}
                </if>
                <if test="merchantGoodsNos != null and originGoodsNos.size() > 0">
                    and merchant_goods_no in
                    <foreach collection="merchantGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and goods_no in
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and g.goods_name like CONCAT(CONCAT('%', #{goodsName}),'%')
                </if>
                <if test="goodsHelpCode != null and goodsHelpCode != ''">
                    and g.goods_help_code LIKE CONCAT (CONCAT('%',#{goodsHelpCode}),'%')
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="startCreateTime != null">
                    <![CDATA[ and create_time >= #{startCreateTime} ]]>
                </if>
                <if test="endCreateTime != null">
                    <![CDATA[ and create_time <= #{endCreateTime} ]]>
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
            order by create_time desc
        </where>
    </select>

    <select id="getDifferenceOrderGoodsListAndWarehouseInfo" parameterType="cn.htdt.goodsprocess.vo.AtomDifferenceOrderGoodsVo" resultMap="DifferenceOrderGoodsAndWareHouseResultMap">
        select
            dog.difference_item_no, dog.difference_no,
            dog.merchant_goods_no,dog.merchant_goods_name,
            dog.goods_no, dog.goods_name,
            dog.barcode, dog.goods_help_code,
            dog.item_price, dog.requisition_num, dog.requisition_unit,
            dog.calculation_unit_no, dog.assist_calculation_unit_no,
            dog.main_unit_num, dog.assist_unit_num,
            dog.standard_flag, dog.difference_count,
            dog.difference_amount, dog.no_storage_count,
            dog.in_storage_count, dog.store_no,
            dog.merchant_no, IFNULL(g.warehouse_flag, 1) warehouse_flag,
            g.validity_period_manage_flag, g.quality_guarantee_period,
            g.shelf_life_unit, g.multi_unit_type, g.multi_unit_goods_no
        from difference_order_goods dog left join goods g on dog.merchant_goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="differenceItemNo != null and differenceItemNo != ''">
                    and dog.difference_item_no = #{differenceItemNo}
                </if>
                <if test="differenceItemNos != null and differenceItemNos.size() > 0">
                    and dog.difference_item_no in
                    <foreach collection="differenceItemNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="differenceNo != null and differenceNo != ''">
                    and dog.difference_no = #{differenceNo}
                </if>
                <if test="differenceNos != null and differenceNos.size() > 0">
                    and dog.difference_no in
                    <foreach collection="differenceNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="merchantGoodsNo != null and merchantGoodsNo != ''">
                    and dog.merchant_goods_no = #{merchantGoodsNo}
                </if>
                <if test="merchantGoodsNos != null and originGoodsNos.size() > 0">
                    and dog.merchant_goods_no in
                    <foreach collection="merchantGoodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and dog.goods_no = #{goodsNo}
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and dog.goods_no in
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and dog.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and dog.store_no = #{storeNo}
                </if>
                and dog.delete_flag = 1
            </trim>
        </where>
    </select>

    <!--查询商品的仓库信息-->
    <select id="getMerchantGoodsWarehouse" parameterType="java.lang.String" resultType="cn.htdt.goodsprocess.dto.response.differenceorder.ResDifferenceGoodsWarehouseDTO">
        select
            iw.warehouse_no, iw.warehouse_name
        from im_warehouse_goods_relation iwgr left join im_warehouse iw on iwgr.warehouse_no = iw.warehouse_no
        where iwgr.goods_no = #{merchantGoodsNo}
              and iw.virtual_warehouse_flag = 1
    </select>

    <select id="getDifferenceOrderGoodsDetail" parameterType="cn.htdt.goodsprocess.vo.AtomDifferenceOrderGoodsVo" resultMap="ResultMap">
        select
        <include refid="Base_Column_List" />
        from difference_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="differenceItemNo != null and differenceItemNo != ''">
                    and difference_item_no = #{differenceItemNo}
                </if>
                <if test="differenceNo != null and differenceNo != ''">
                    and difference_no = #{differenceNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <select id="getDifferenceAmount" parameterType="cn.htdt.goodsprocess.vo.AtomDifferenceOrderGoodsVo" resultType="java.math.BigDecimal">
        select
            difference_amount
        from difference_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="differenceItemNo != null and differenceItemNo != ''">
                    and difference_item_no = #{differenceItemNo}
                </if>
                <if test="differenceNo != null and differenceNo != ''">
                    and difference_no = #{differenceNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <update id="updateDifferenceOrderGoods" parameterType="cn.htdt.goodsprocess.domain.DifferenceOrderGoodsDomain">
        update
        difference_order_goods
        <set>
            <if test="differenceCount != null">
                difference_count = #{differenceCount},
            </if>
            <if test="differenceAmount != null">
                difference_amount = #{differenceAmount},
            </if>
            <if test="noStorageCount != null">
                no_storage_count = #{noStorageCount},
            </if>
            <if test="inStorageCount != null">
                in_storage_count = #{inStorageCount},
            </if>
            <if test="merchantNo != null and merchantNo != ''">
                merchant_no = #{merchantNo},
            </if>
            <if test="storeNo != null and storeNo != ''">
                store_no = #{storeNo},
            </if>
            <if test="companyNo != null and companyNo != ''">
                company_no = #{companyNo},
            </if>
            <if test="companyName != null and companyName != ''">
                company_name = #{companyName},
            </if>
            <if test="branchNo != null and branchNo != ''">
                branch_no = #{branchNo},
            </if>
            <if test="platformType != null and platformType != ''">
                platform_type = #{platformType},
            </if>
            <if test="branchName != null and branchName != ''">
                branch_name = #{branchName}
            </if>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="differenceItemNo != null and differenceItemNo != ''">
                    and difference_item_no = #{differenceItemNo}
                </if>
                <if test="differenceNo != null and differenceNo != ''">
                    and difference_no = #{differenceNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>

    <update id="batchUpdateItemStorage" parameterType="cn.htdt.goodsprocess.domain.DifferenceOrderGoodsDomain">
        <foreach collection="differenceOrderGoodsList" item="item" index="index" open="" separator=";" close="">
            update difference_order_goods set
            <if test="item.inStorageCount != null">
                in_storage_count = in_storage_count + #{item.inStorageCount},
            </if>
            <choose>
                <!--优先获取未入库数量, 为空的话, 根据原有的未入库数量和入库数量计算-->
                <when test="item.noStorageCount != null">
                    no_storage_count = #{item.noStorageCount},
                </when>
                <when test="item.inStorageCount != null">
                    no_storage_count = no_storage_count - #{item.inStorageCount},
                </when>
            </choose>
            <if test="item.modifyNo != null and item.modifyNo != ''">
                modify_no = #{item.modifyNo},
            </if>
            <if test="item.modifyName != null and item.modifyName != ''">
                modify_name = #{item.modifyName},
            </if>
            modify_time = now()
            where difference_item_no = #{item.differenceItemNo}
            and goods_no = #{item.goodsNo}
        </foreach>

    </update>

</mapper>