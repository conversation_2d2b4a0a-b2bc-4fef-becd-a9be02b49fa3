<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.TradeOrderItemsRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.TradeOrderItemsRelationDomain">
        <result column="id" property="id" />
        <result column="order_item_no" property="orderItemNo"/>
        <result column="sku_code" property="skuCode" />
        <result column="goods_no" property="goodsNo" />
        <result column="stock_flag" property="stockFlag" />
        <result column="calculation_unit_name" property="calculationUnitName" />
        <result column="standard_flag" property="standardFlag" />
        <result column="warehouse_no" property="warehouseNo" />
        <result column="assist_calculation_unit_name" property="assistCalculationUnitName" />
        <result column="main_unit_num" property="mainUnitNum" />
        <result column="assist_unit_num" property="assistUnitNum" />
        <result column="conversion_rate" property="conversionRate" />
        <result column="warehouse_flag" property="warehouseFlag" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="htdt_goods_name" property="htdtGoodsName" />
        <result column="unit_relation_num" property="unitRelationNum" />
        <result column="unit_relation" property="unitRelation" />
        <result column="relation_buyer_code" property="relationBuyerCode" />

        <result column="goods_num" property="goodsNum" />
        <result column="production_date" property="productionDate" />
        <result column="multi_unit_type" property="multiUnitType" />
        <result column="multi_unit_goods_no" property="multiUnitGoodsNo" />
        <result column="order_no" property="orderNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_item_no,
        sku_code, goods_no, stock_flag, calculation_unit_name, standard_flag, warehouse_no, assist_calculation_unit_name, main_unit_num, assist_unit_num, conversion_rate, warehouse_flag, warehouse_name, htdt_goods_name, unit_relation_num, unit_relation, relation_buyer_code,
          goods_num,production_date,multi_unit_type,multi_unit_goods_no,order_no
    </sql>

    <select id="selectOneTradeOrderItemsRelation"
            parameterType="cn.htdt.goodsprocess.domain.TradeOrderItemsRelationDomain" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM trade_order_items_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="orderItemNo != null and orderItemNo != ''">
                    and order_item_no = #{orderItemNo}
                </if>
                <if test="skuCode != null and skuCode != ''">
                    and sku_code = #{skuCode}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="stockFlag != null">
                    and stock_flag = #{stockFlag}
                </if>
                <if test="relationBuyerCode != null and relationBuyerCode != ''">
                    and relation_buyer_code = #{relationBuyerCode}
                </if>
            </trim>
        </where>
        limit 1
    </select>

    <!-- 查询关联商品行列表信息 -->
    <select id="selectTradeOrderItemsRelations" resultType="cn.htdt.goodsprocess.domain.TradeOrderItemsRelationDomain">
        SELECT
        <include refid="Base_Column_List"/>
        FROM trade_order_items_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="orderItemNo != null and orderItemNo != ''">
                    and order_item_no = #{orderItemNo}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    and order_no = #{orderNo}
                </if>
                <if test="stockFlag != null and stockFlag != ''">
                    and stock_flag = #{stockFlag}
                </if>
            </trim>
        </where>
        order by id desc
    </select>

    <!-- 根据商品sku和是否入库更新关联千橙商品信息 -->
    <update id="updateOneItemsRelationByTrade" parameterType="cn.htdt.goodsprocess.domain.TradeOrderItemsRelationDomain">
        update
        trade_order_items_relation
        <set>
            <if test="goodsNo != null and goodsNo != ''">
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="standardFlag != null and standardFlag != ''">
                standard_flag = #{standardFlag,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitName != null and calculationUnitName != ''">
                calculation_unit_name = #{calculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="assistCalculationUnitName != null and assistCalculationUnitName != ''">
                assist_calculation_unit_name = #{assistCalculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="mainUnitNum != null">
                main_unit_num = #{mainUnitNum,jdbcType=DECIMAL},
            </if>
            <if test="assistUnitNum != null">
                assist_unit_num = #{assistUnitNum,jdbcType=DECIMAL},
            </if>
            <if test="conversionRate != null">
                conversion_rate = #{conversionRate,jdbcType=DECIMAL},
            </if>
            <if test="stockFlag != null">
                stock_flag = #{stockFlag,jdbcType=TINYINT},
            </if>
            <if test="warehouseFlag != null">
                warehouse_flag = #{warehouseFlag,jdbcType=TINYINT},
            </if>
            <if test="warehouseNo != null and warehouseNo != ''">
                warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null and warehouseName != ''">
                warehouse_name = #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="htdtGoodsName != null and htdtGoodsName != ''">
                htdt_goods_name = #{htdtGoodsName,jdbcType=VARCHAR},
            </if>
            <if test="unitRelationNum != null">
                unit_relation_num = #{unitRelationNum,jdbcType=DECIMAL},
            </if>
            <if test="unitRelation != null">
                unit_relation = #{unitRelation,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="orderItemNo != null and orderItemNo != ''">
                    and order_item_no = #{orderItemNo}
                </if>
                <if test="skuCode != null and skuCode != ''">
                    and sku_code = #{skuCode}
                </if>
                <if test="relationBuyerCode != null and relationBuyerCode != ''">
                    and relation_buyer_code = #{relationBuyerCode}
                </if>
            </trim>
        </where>
    </update>

    <!-- 根据商品sku和是否入库更新关联千橙商品信息-恢复默认 -->
    <update id="batchUpdateTradeOrderItemsRelation" parameterType="cn.htdt.goodsprocess.vo.AtomTradeOrderItemsRelationVo">
        update
        trade_order_items_relation
        <set>
            goods_no = '',
            standard_flag = 1,
            calculation_unit_name ='',
            assist_calculation_unit_name = '',
            main_unit_num = 1,
            assist_unit_num = 1,
            conversion_rate = 1.00,
            stock_flag = 1,
            warehouse_flag = 1,
            warehouse_no = '',
            warehouse_name = '',
            htdt_goods_name = '',
            unit_relation_num = 1,
            unit_relation=''
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size > 0">
                    and goods_no IN
                    <foreach collection="goodsNos" item="goodsNo" open="(" close=")" separator=",">
                        #{goodsNo}
                    </foreach>
                </if>
                <if test="stockFlag != null">
                    and stock_flag = #{stockFlag}
                </if>
                <if test="relationBuyerCode != null and relationBuyerCode != ''">
                    and relation_buyer_code = #{relationBuyerCode}
                </if>
            </trim>
        </where>
    </update>

    <!-- 根据商品sku和是否入库更新关联千橙商品信息-vo -->
    <update id="updateOneNotStockItemsRelationByTrade" parameterType="cn.htdt.goodsprocess.vo.AtomTradeOrderItemsRelationVo">
        update
        trade_order_items_relation
        <set>
            <if test="goodsNo != null and goodsNo != ''">
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="standardFlag != null and standardFlag != ''">
                standard_flag = #{standardFlag,jdbcType=VARCHAR},
            </if>
            <if test="calculationUnitName != null and calculationUnitName != ''">
                calculation_unit_name = #{calculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="assistCalculationUnitName != null and assistCalculationUnitName != ''">
                assist_calculation_unit_name = #{assistCalculationUnitName,jdbcType=VARCHAR},
            </if>
            <if test="mainUnitNum != null">
                main_unit_num = #{mainUnitNum,jdbcType=DECIMAL},
            </if>
            <if test="assistUnitNum != null">
                assist_unit_num = #{assistUnitNum,jdbcType=DECIMAL},
            </if>
            <if test="conversionRate != null">
                conversion_rate = #{conversionRate,jdbcType=DECIMAL},
            </if>
            <if test="stockFlag != null">
                stock_flag = #{stockFlag,jdbcType=TINYINT},
            </if>
            <if test="warehouseFlag != null">
                warehouse_flag = #{warehouseFlag,jdbcType=TINYINT},
            </if>
            <if test="warehouseNo != null and warehouseNo != ''">
                warehouse_no = #{warehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null and warehouseName != ''">
                warehouse_name = #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="htdtGoodsName != null and htdtGoodsName != ''">
                htdt_goods_name = #{htdtGoodsName,jdbcType=VARCHAR},
            </if>
            <if test="unitRelationNum != null">
                unit_relation_num = #{unitRelationNum,jdbcType=DECIMAL},
            </if>
            <if test="unitRelation != null">
                unit_relation = #{unitRelation,jdbcType=VARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="relationGoodsNo != null and relationGoodsNo != ''">
                    and goods_no = #{relationGoodsNo}
                </if>
                <if test="skuCode != null and skuCode != ''">
                    and sku_code = #{skuCode}
                </if>
                <if test="stockFlag != null">
                    and stock_flag = #{stockFlag}
                </if>
                <if test="relationBuyerCode != null and relationBuyerCode != ''">
                    and relation_buyer_code = #{relationBuyerCode}
                </if>
            </trim>
        </where>
    </update>

    <delete id="deleteTradeOrderItemsRelation">
        delete from trade_order_items_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="orderItemNo != null and orderItemNo != ''">
                    and order_item_no = #{orderItemNo}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    and order_no = #{orderNo}
                </if>
            </trim>
        </where>
    </delete>

</mapper>
