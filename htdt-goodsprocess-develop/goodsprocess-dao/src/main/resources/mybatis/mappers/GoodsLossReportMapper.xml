<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsLossReportDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsLossReportDomain">
        <result column="id" property="id" />
        <result column="create_no" property="createNo" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_no" property="modifyNo" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="loss_report_no" property="lossReportNo" />
        <result column="report_remark" property="reportRemark" />
        <result column="report_status" property="reportStatus" />
        <result column="compensate_method" property="compensateMethod" />
        <result column="compensate_amount" property="compensateAmount" />
        <result column="report_approve_remark" property="reportApproveRemark" />
        <result column="approve_flag" property="approveFlag" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="disable_flag" property="disableFlag" />
    </resultMap>

    <resultMap id="VoBaseResultMap" type="cn.htdt.goodsprocess.vo.AtomGoodsLossReportVo" extends="BaseResultMap">
        <result column="concat_goods_name" property="concatGoodsName" />
        <result column="concat_merchant_goods_no" property="concatMerchantGoodsNo" />
        <result column="loss_report_total_num" property="lossReportTotalNum" />
        <result column="loss_report_total_amount" property="lossReportTotalAmount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_no,
        create_name,
        create_time,
        modify_no,
        modify_name,
        modify_time,
        delete_flag,
        loss_report_no, report_remark, report_status, compensate_method, compensate_amount, report_approve_remark, approve_flag, merchant_no, merchant_name, store_no, store_name, disable_flag
    </sql>

    <!-- goods_loss_report表 -->
    <sql id="Base_Column_List_R">
        r.id,
        r.create_no,
        r.create_name,
        r.create_time,
        r.modify_no,
        r.modify_name,
        r.modify_time,
        r.delete_flag,
        r.loss_report_no, r.report_remark, r.report_status, r.compensate_method, r.compensate_amount, r.report_approve_remark, r.approve_flag, r.merchant_no, r.merchant_name, r.store_no, r.store_name, r.disable_flag
    </sql>

    <sql id="Base_Column_List_Other">

    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <sql id="Base_Column_Where_R">
        and r.delete_flag = 1
    </sql>

    <!--分页查询报损商品列表-pc-->
    <select id="selectGoodsLossReportByPage" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsLossReportVo" resultMap="VoBaseResultMap">
        select
        <include refid="Base_Column_List_R"/>,
        if(GROUP_CONCAT(DISTINCT  if(ri.goods_name='','',ri.goods_name) separator ',') is null, '', GROUP_CONCAT(DISTINCT  if(ri.goods_name='','',ri.goods_name) separator ',')) concat_goods_name,
        if(GROUP_CONCAT(DISTINCT  if(ri.merchant_goods_no='','',ri.merchant_goods_no) separator ',') is null, '', GROUP_CONCAT(DISTINCT  if(ri.merchant_goods_no='','',ri.merchant_goods_no) separator ',')) concat_merchant_goods_no,
        sum(ri.loss_report_num) loss_report_total_num,
          sum(loss_report_amount) loss_report_total_amount
        from goods_loss_report r
        left join goods_loss_report_item ri on r.loss_report_no = ri.loss_report_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="lossReportNo != null and lossReportNo != ''">
                    AND r.loss_report_no = #{lossReportNo, jdbcType=VARCHAR}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    AND r.merchant_no = #{merchantNo, jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    AND r.store_no = #{storeNo, jdbcType=VARCHAR}
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and r.store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="reportStatus != null">
                    AND r.report_status = #{reportStatus, jdbcType=INTEGER}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    AND ri.goods_name LIKE CONCAT (CONCAT('%',#{goodsName}),'%')
                </if>
                <if test="goodsNameAndLossNo != null and goodsNameAndLossNo != ''">
                    AND (ri.goods_name LIKE CONCAT (CONCAT('%',#{goodsNameAndLossNo}),'%') or r.loss_report_no = #{goodsNameAndLossNo})
                </if>
                <include refid="Base_Column_Where_R"/>
            </trim>
        </where>
        group by r.loss_report_no
        ORDER BY r.modify_time DESC
    </select>

    <!--分页查询报损商品列表-app-->
    <select id="selectAppGoodsLossReportByPage" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsLossReportVo" resultMap="VoBaseResultMap">
        select
        <include refid="Base_Column_List_R"/>, sum(ri.loss_report_num) loss_report_total_num, sum(loss_report_amount) loss_report_total_amount,
        if(GROUP_CONCAT(DISTINCT  if(ri.merchant_goods_no='','',ri.merchant_goods_no) separator ',') is null, '', GROUP_CONCAT(DISTINCT  if(ri.merchant_goods_no='','',ri.merchant_goods_no) separator ',')) concat_merchant_goods_no
        from goods_loss_report r
        left join goods_loss_report_item ri on r.loss_report_no = ri.loss_report_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="lossReportNo != null and lossReportNo != ''">
                    AND r.loss_report_no = #{lossReportNo, jdbcType=VARCHAR}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    AND r.merchant_no = #{merchantNo, jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    AND r.store_no = #{storeNo, jdbcType=VARCHAR}
                </if>
                <if test="reportStatus != null">
                    AND r.report_status = #{reportStatus, jdbcType=INTEGER}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    AND ri.goods_name LIKE CONCAT (CONCAT('%',#{goodsName}),'%')
                </if>
                <if test="goodsNameAndLossNo != null and goodsNameAndLossNo != ''">
                    AND (ri.goods_name LIKE CONCAT (CONCAT('%',#{goodsNameAndLossNo}),'%') or r.loss_report_no = #{goodsNameAndLossNo})
                </if>

                <if test="storeNoList != null and storeNoList.size() > 0">
                    and r.store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>

                <include refid="Base_Column_Where_R"/>
            </trim>
        </where>
        group by r.loss_report_no
        ORDER BY r.modify_time DESC
    </select>

    <!-- 根据条件获取单条报损单-有关联 -->
    <select id="selectOneGoodsLossReport" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsLossReportVo" resultMap="VoBaseResultMap">
        select
        <include refid="Base_Column_List_R"/>, sum(ri.loss_report_num) loss_report_total_num, sum(loss_report_amount) loss_report_total_amount
        from goods_loss_report r
        left join goods_loss_report_item ri on r.loss_report_no = ri.loss_report_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="lossReportNo != null and lossReportNo != ''">
                    AND r.loss_report_no = #{lossReportNo, jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where_R"/>
            </trim>
        </where>
        group by r.loss_report_no
    </select>

    <!-- 根据条件获取单条报损单-未关联 -->
    <select id="selectOneGoodsLossReportInfo" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsLossReportVo" resultMap="VoBaseResultMap">
        select
        <include refid="Base_Column_List_R"/>
        from goods_loss_report r
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="lossReportNo != null and lossReportNo != ''">
                    AND r.loss_report_no = #{lossReportNo, jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where_R"/>
            </trim>
        </where>
        group by r.loss_report_no
    </select>

    <!-- 修改 -->
    <update id="updateGoodsLossReportByTerm"
            parameterType="cn.htdt.goodsprocess.vo.AtomGoodsLossReportVo">
        update
        goods_loss_report
        <set>
            <if test="reportApproveRemark != null">
                report_approve_remark = #{reportApproveRemark, jdbcType=VARCHAR},
            </if>
            <if test="reportStatus != null">
                report_status = #{reportStatus, jdbcType=INTEGER},
            </if>
            <if test="approveFlag != null">
                approve_flag = #{approveFlag, jdbcType=INTEGER},
            </if>
            <if test="compensateMethod != null">
                compensate_method = #{compensateMethod, jdbcType=VARCHAR},
            </if>
            <if test="compensateAmount != null">
                compensate_amount = #{compensateAmount, jdbcType=DECIMAL},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="lossReportNo != null and lossReportNo != ''">
                    AND loss_report_no = #{lossReportNo, jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>

</mapper>
