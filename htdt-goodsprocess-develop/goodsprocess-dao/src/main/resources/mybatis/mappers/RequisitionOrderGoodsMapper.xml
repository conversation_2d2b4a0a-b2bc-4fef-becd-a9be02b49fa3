<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.RequisitionOrderGoodsDao">
    <resultMap id="ResultMap" type="cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionOrderGoodsDTO">
        <result column="requisition_item_no" property="requisitionItemNo"/>
        <result column="requisition_no" property="requisitionNo"/>
        <result column="merchant_goods_no" property="merchantGoodsNo"/>
        <result column="merchant_goods_name" property="merchantGoodsName"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="barcode" property="barcode"/>
        <result column="goods_help_code" property="goodsHelpCode"/>
        <result column="item_price" property="itemPrice"/>
        <result column="requisition_num" property="requisitionNum"/>
        <result column="requisition_unit" property="requisitionUnit"/>
        <result column="calculation_unit_no" property="calculationUnitNo"/>
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo"/>
        <result column="main_unit_num" property="mainUnitNum"/>
        <result column="assist_unit_num" property="assistUnitNum"/>
        <result column="standard_flag" property="standardFlag"/>
        <result column="out_storage_count" property="outStorageCount"/>
        <result column="out_storage_amount" property="outStorageAmount"/>
        <result column="in_storage_count" property="inStorageCount"/>
        <result column="in_storage_amount" property="inStorageAmount"/>
        <result column="raw_goods_no" property="rawGoodsNo"/>
        <result column="multi_unit_goods_no" property="multiUnitGoodsNo"/>
        <result column="store_name" property="storeName"/>
        <result column="warehouse_flag" property="warehouseFlag"/>
        <result column="validity_period_manage_flag" property="validityPeriodManageFlag"/>
        <result column="quality_guarantee_period" property="qualityGuaranteePeriod"/>
        <result column="shelf_life_unit" property="shelfLifeUnit"/>

    </resultMap>

    <resultMap id="collectionMap" extends="ResultMap" type="cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionOrderGoodsDTO">
        <collection property="merchantGoodsWarehouseList" select="getMerchantGoodsWarehouse"
                    column="{merchant_goods_no=merchant_goods_no,multi_unit_goods_no=multi_unit_goods_no}"
                    ofType="cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionGoodsWarehouseDTO">
            <result column="warehouse_no" property="warehouseNo"/>
            <result column="warehouse_name" property="warehouseName"/>
            <result column="virtual_warehouse_flag" property="virtualWarehouseFlag"/>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id,requisition_item_no,requisition_no,merchant_goods_no,merchant_goods_name,goods_no,goods_name,barcode,goods_help_code,item_price,requisition_num,requisition_unit,calculation_unit_no,assist_calculation_unit_no,main_unit_num,assist_unit_num,standard_flag,out_storage_count,out_storage_amount,in_storage_count,in_storage_amount,company_no,company_name,branch_no,branch_name,platform_type,merchant_no,merchant_name,store_no,store_name,create_no,create_name,create_time,modify_no,modify_name,modify_time,delete_flag,raw_goods_no
    </sql>

    <select id="getRequisitionOrderGoodsList"
            parameterType="cn.htdt.goodsprocess.dto.request.requisitionorder.ReqRequisitionOrderGoodsListDTO"
            resultMap="collectionMap">
        select
        rog.requisition_item_no,rog.requisition_no,rog.merchant_goods_no,rog.merchant_goods_name,rog.goods_no,rog.goods_name,
        rog.barcode,rog.goods_help_code,rog.requisition_num,rog.requisition_unit,rog.calculation_unit_no,rog.assist_calculation_unit_no,
        rog.main_unit_num,rog.assist_unit_num,rog.standard_flag,rog.out_storage_count,rog.out_storage_amount,rog.raw_goods_no,
        rog.in_storage_count,rog.in_storage_amount,rog.store_name,rog.item_price,g.warehouse_flag,g.multi_unit_goods_no,
        g.validity_period_manage_flag,
        g.quality_guarantee_period,
        g.shelf_life_unit,
        g.goods_no gg
        from requisition_order_goods rog
        <choose>
            <when test="null != loginIdentity and loginIdentity == 4">
                left join goods g on rog.goods_no = g.goods_no
            </when>
            <otherwise>
                left join goods g on rog.merchant_goods_no = g.goods_no
            </otherwise>
        </choose>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="requisitionNo != null and requisitionNo != ''">
                    and rog.requisition_no = #{requisitionNo}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and rog.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and rog.store_no = #{storeNo}
                </if>
            </trim>
        </where>
    </select>

    <select id="getMerchantGoodsWarehouse"
            resultType="cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionGoodsWarehouseDTO">
        select iw.warehouse_no, iw.warehouse_name,virtual_warehouse_flag
        from im_warehouse_goods_relation iwgr
                 left join im_warehouse iw on iwgr.warehouse_no = iw.warehouse_no
        where (iwgr.goods_no = #{merchant_goods_no} or iwgr.goods_no = #{multi_unit_goods_no})
          and iw.virtual_warehouse_flag = 1
          and iwgr.delete_flag = 1
          and iw.delete_flag = 1
    </select>

    <select id="getUnfinishedMerchantGoodsList"
            parameterType="cn.htdt.goodsprocess.dto.request.requisitionorder.ReqQueryUnfinishedGoodsDTO"
            resultType="cn.htdt.goodsprocess.dto.response.requisitionorder.ResUnfinishedGoodsDTO">
        select distinct rog.requisition_no, rog.merchant_goods_no, rog.merchant_goods_name
        from requisition_order_goods rog
        left join requisition_order ro on rog.requisition_no = ro.requisition_no
        <where>
            ro.status in (10, 11, 13, 14)
            <if test="merchantNo != null and merchantNo != ''">
                and ro.merchant_no = #{merchantNo}
            </if>
            <if test="goodsList != null and goodsList.size() > 0">
                and rog.goods_no in
                <foreach collection="goodsList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="exportRequisitionOrderGoods"
            parameterType="cn.htdt.goodsprocess.dto.request.requisitionorder.ReqRequisitionOrderListDTO"
            resultType="cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionOrderGoodsExportDTO">
        select rog.requisition_no,
        ro.status,
        rog.store_name,
        ro.remark,
        <if test="loginIdentity == 2">
            rog.merchant_goods_name goodsName,
        </if>
        <if test="loginIdentity == 4">
            rog.goods_name,
        </if>
        rog.requisition_unit,
        rog.requisition_num,
        rog.out_storage_count,
        rog.in_storage_count,
        rog.item_price,
        rog.in_storage_amount
        from requisition_order_goods rog
        left join requisition_order ro on rog.requisition_no = ro.requisition_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="requisitionNo != null and requisitionNo != ''">
                    and rog.requisition_no = #{requisitionNo}
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and rog.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and rog.store_no = #{storeNo}
                </if>
                <if test="storeNoList != null and storeNoList.size() > 0">
                    and rog.store_no in
                    <foreach collection="storeNoList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="status != null and status == 10">
                    and status in(10, 12)
                </if>
                <if test="status != null and status != 10">
                    and status = #{status}
                </if>
                <if test="createTimeStart != null">
                    <![CDATA[
                        and  date_format(ro.create_time, '%Y-%m-%d') >= #{createTimeStart}
                    ]]>
                </if>
                <if test="createTimeEnd != null">
                    <![CDATA[
                        and date_format(ro.create_time, '%Y-%m-%d') <= #{createTimeEnd}
                    ]]>
                </if>
            </trim>
        </where>
        order by ro.create_time desc, rog.id asc
    </select>

    <select id="getRequisitionOrderGoodsListByParam" parameterType="java.lang.String" resultMap="ResultMap">
        select
             rog.requisition_no, rog.out_storage_count
        from requisition_order_goods rog
        <where>
            <if test="requisitionNoList != null and requisitionNoList.size() > 0">
                and rog.requisition_no in
                <foreach collection="requisitionNoList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <update id="batchUpdateItemStorage" parameterType="cn.htdt.goodsprocess.domain.RequisitionOrderGoodsDomain">
        <foreach collection="requisitionOrderGoodsList" item="item" index="index" open="" separator=";" close="">
            update requisition_order_goods set
            <if test="item.itemPrice != null and item.itemPrice > 0">
                item_price = #{item.itemPrice},
            </if>
            <if test="item.outStorageCount != null">
                out_storage_count = if(isnull(out_storage_count), 0, out_storage_count) + #{item.outStorageCount},
            </if>
            <if test="item.outStorageAmount != null">
                out_storage_amount = if(isnull(out_storage_amount), 0, out_storage_amount) + #{item.outStorageAmount},
            </if>
            <if test="item.inStorageCount != null">
                in_storage_count = if(isnull(in_storage_count), 0, in_storage_count) + #{item.inStorageCount},
            </if>
            <if test="item.inStorageAmount != null">
                in_storage_amount = if(isnull(in_storage_amount), 0, in_storage_amount) + #{item.inStorageAmount},
            </if>
            <if test="item.modifyNo != null">
                modify_no = #{item.modifyNo},
            </if>
            <if test="item.modifyName != null">
                modify_name = #{item.modifyName},
            </if>
            modify_time = now()
            where requisition_no = #{item.requisitionNo}
            and merchant_goods_no = #{item.merchantGoodsNo}
            and goods_no = #{item.goodsNo}
            and merchant_no = #{item.merchantNo}
        </foreach>

    </update>

    <delete id="deleteOrderGoodsByRequisitionNo">
        delete
        from requisition_order_goods
        where requisition_no = #{requisitionNo}
    </delete>
</mapper>