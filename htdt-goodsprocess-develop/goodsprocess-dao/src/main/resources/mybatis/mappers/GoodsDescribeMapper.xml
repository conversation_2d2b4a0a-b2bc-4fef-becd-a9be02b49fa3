<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsDescribeDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsDescribeDomain">
        <result column="id" property="id" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="goods_no" property="goodsNo" />
        <result column="content" property="content" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        goods_no, content, merchant_no, merchant_name, store_no, store_name, disable_flag, create_no, modify_no
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsDescribeVo">
        select
            <include refid="Base_Column_List" />
        from
            goods_describe
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNos != null and goodsNos.size() > 0">
                and goods_no IN
                <foreach collection="goodsNos" item="item" index="index"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            </trim>
        </where>
    </select>

    <update id="updateByPrimaryKeySelective" parameterType="cn.htdt.goodsprocess.domain.GoodsDescribeDomain" >
        update
            goods_describe
        <set >
            <if test="goodsNo != null" >
                goods_no = #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null" >
                merchant_no = #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null" >
                merchant_name = #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null" >
                store_no = #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null" >
                source_type = #{sourceType,jdbcType=TINYINT},
            </if>
            <if test="storeName != null" >
                store_name = #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null" >
                disable_flag = #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null" >
                create_no = #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null" >
                create_name = #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null" >
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null" >
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="content != null" >
                content = #{content,jdbcType=LONGVARCHAR},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>

    <update id="logicDelete" parameterType="cn.htdt.goodsprocess.domain.GoodsDescribeDomain" >
        update
            goods_describe
        set
            <if test="modifyNo != null" >
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null" >
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null" >
                modify_time = #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
            </trim>
        </where>
    </update>

    <delete id="deleteByGoodsNo" parameterType="cn.htdt.goodsprocess.vo.AtomGoodsDescribeVo">
        delete from goods_describe
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="goodsNos != null and goodsNos.size() > 0">
                    and goods_no IN
                    <foreach collection="goodsNos" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
    </delete>

</mapper>
