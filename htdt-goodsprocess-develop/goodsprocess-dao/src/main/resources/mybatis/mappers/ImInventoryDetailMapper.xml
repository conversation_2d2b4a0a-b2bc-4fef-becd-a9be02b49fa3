<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.ImInventoryDetailDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.ImInventoryDetailDomain">
        <result column="id" property="id" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="inventory_code" property="inventoryCode" />
        <result column="goods_no" property="goodsNo" />
        <result column="stock_num" property="stockNum" />
        <result column="inventory_stock_num" property="inventoryStockNum" />
        <result column="warehouse_no" property="warehouseNo" />
        <result column="warehouse_code" property="warehouseCode" />
        <result column="warehouse_name" property="warehouseName" />
        <result column="confirm_no" property="confirmNo" />
        <result column="confirm_name" property="confirmName" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
        <result column="disable_flag" property="disableFlag" />
        <result column="create_no" property="createNo" />
        <result column="modify_no" property="modifyNo" />
        <result column="company_no" property="companyNo" />
        <result column="company_name" property="companyName" />
        <result column="branch_no" property="branchNo" />
        <result column="branch_name" property="branchName" />
    </resultMap>

    <resultMap id="BaseResultGoodsMap" type="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo" extends="BaseResultMap">
        <result column="standard_flag" property="standardFlag" />
        <result column="conversion_rate" property="conversionRate"/>
        <result column="calculation_unit_no" property="calculationUnitNo"/>
        <result column="calculation_unit_name" property="calculationUnitName"/>
        <result column="calculation_unit_symbol" property="calculationUnitSymbol"/>
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo" />
        <result column="assist_calculation_unit_name" property="assistCalculationUnitName"/>
        <result column="assist_calculation_unit_symbol" property="assistCalculationUnitSymbol"/>
        <result column="stock_num" property="realStockNum"/>
        <result column="freeze_stock_num" property="freezeStockNum"/>
        <result column="warehouse_flag" property="warehouseFlag"/>
        <result column="barcode" property="barcode"/>
        <result column="goods_help_code" property="goodsHelpCode"/>
        <result column="goods_name" property="goodsName" />
        <result column="goods_delete_flag" property="goodsDeleteFlag" />
        <result column="sales_volume" property="salesVolume" />
        <result column="retail_price" property="retailPrice" />
        <result column="multi_unit_type" property="multiUnitType"/>
        <result column="multi_unit_goods_no" property="multiUnitGoodsNo"/>
        <result column="validity_period_manage_flag" property="validityPeriodManageFlag"/>
        <result column="shelf_life_unit" property="shelfLifeUnit"/>
        <result column="quality_guarantee_period" property="qualityGuaranteePeriod"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_name,
        create_time,
        modify_name,
        modify_time,
        delete_flag,
        inventory_code, goods_no, stock_num, inventory_stock_num, warehouse_no, warehouse_code, warehouse_name, merchant_no, merchant_name, confirm_no, confirm_name, store_no, store_name, disable_flag, create_no, modify_no, company_no, company_name, branch_no, branch_name
    </sql>

    <sql id="Base_List">
        i.create_name,
        i.create_time,
        i.modify_name,
        i.modify_time,
        i.delete_flag,
        i.inventory_code, i.goods_no, i.inventory_stock_num,
        i.warehouse_no, i.warehouse_code, i.warehouse_name, i.confirm_no, i.confirm_name, i.merchant_no,
        i.company_no, i.company_name, i.branch_no, i.branch_name,
        i.merchant_name, i.store_no, i.store_name, i.disable_flag, i.create_no, i.modify_no,g.barcode,g.goods_help_code,
        g.imei_flag,g.warehouse_flag, g.conversion_rate, g.goods_name,g.assist_calculation_unit_no,g.calculation_unit_no,
        n.stock_num, s.freeze_stock_num, g.delete_flag as goods_delete_flag
    </sql>

    <sql id="Base_kuaizhao_List">
        i.create_name,
        i.create_time,
        i.modify_name,
        i.modify_time,
        i.delete_flag,
        i.inventory_code, i.goods_no, i.inventory_stock_num,
        i.warehouse_no, i.warehouse_code, i.warehouse_name, i.confirm_no, i.confirm_name, i.merchant_no,
        i.company_no, i.company_name, i.branch_no, i.branch_name,
        i.merchant_name, i.store_no, i.store_name, i.disable_flag, i.create_no, i.modify_no,g.barcode,g.goods_help_code,
        g.imei_flag,g.warehouse_flag, g.conversion_rate, g.goods_name,g.assist_calculation_unit_no,g.calculation_unit_no,
        i.stock_num, IFNULL(s.freeze_stock_num,0) + IFNULL(s.deliver_stock_num,0) freeze_stock_num, g.delete_flag as goods_delete_flag, g.sales_volume
        ,g.retail_price, n.stock_num canSaleStockNum, g.multi_unit_type, g.multi_unit_goods_no,g.quality_guarantee_period,g.shelf_life_unit,g.validity_period_manage_flag
    </sql>

    <sql id="Base_Column_Goods_List">
        i.create_name,
        i.create_time,
        i.modify_name,
        i.modify_time,
        i.delete_flag,
        i.inventory_code, i.goods_no, n.stock_num stock_num, i.inventory_stock_num,
        i.warehouse_no, i.warehouse_code, i.warehouse_name, i.confirm_no, i.confirm_name, i.merchant_no,
        i.company_no, i.company_name, i.branch_no, i.branch_name,
        i.merchant_name, i.store_no, i.store_name, i.disable_flag, i.create_no, i.modify_no,
        g.barcode,g.goods_help_code,g.assist_calculation_unit_no,g.calculation_unit_no,
        g.imei_flag,g.warehouse_flag, g.conversion_rate,g.goods_name,r.freeze_stock_num, g.multi_unit_type, g.multi_unit_goods_no, g.validity_period_manage_flag
    </sql>

    <sql id="Ware_Column_Goods_List">
        i.create_name,
        i.create_time,
        i.modify_name,
        i.modify_time,
        i.delete_flag,
        i.inventory_code, i.goods_no, i.inventory_stock_num,
        i.warehouse_no, i.warehouse_code, i.warehouse_name, i.confirm_no, i.confirm_name, i.merchant_no,
        i.company_no, i.company_name, i.branch_no, i.branch_name,
        i.merchant_name, i.store_no, i.store_name, i.disable_flag, i.create_no, i.modify_no,
        g.barcode,g.goods_help_code,g.assist_calculation_unit_no,g.calculation_unit_no,
        g.imei_flag,g.warehouse_flag, g.conversion_rate,g.goods_name,g.first_attribute_value_name firstAttributeValueName,g.second_attribute_value_name firstAttributeValueName,g.third_attribute_value_name firstAttributeValueName
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <sql id="Base_Column_Goods_Where">
        and i.delete_flag = 1
    </sql>

    <sql id="Base_Column_Goods_Detail_Where">
        and ii.delete_flag = 1
    </sql>

    <sql id="Base_Column_goods_relation_Where">
        and n.delete_flag = 1
    </sql>

    <select id="selectByDetailCode" resultMap="BaseResultGoodsMap" parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select
        <include refid="Base_kuaizhao_List" />
        from
        im_inventory_detail i
        left join goods g on i.goods_no = g.goods_no
        left join goods_real_stock s on i.goods_no = s.goods_no
        left join im_warehouse_goods_relation n on  i.goods_no=n.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.warehouse_no=n.warehouse_no and i.inventory_code = #{inventoryCode}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and (g.goods_no = #{goodsName,jdbcType=VARCHAR} or g.goods_name LIKE CONCAT(CONCAT('%',#{goodsName,jdbcType=VARCHAR}),'%'))
                </if>
                <include refid="Base_Column_Goods_Where" />
                <include refid="Base_Column_goods_relation_Where" />
            </trim>
        </where>
        order by i.create_time desc
    </select>

    <select id="selectGoodGroupsByDetailCode" resultMap="BaseResultGoodsMap" parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select
        i.create_name,
        i.create_time,
        i.modify_name,
        i.modify_time,
        i.delete_flag,
        i.inventory_code, i.goods_no, i.inventory_stock_num,
        i.warehouse_no, i.warehouse_code, i.warehouse_name, i.confirm_no, i.confirm_name, i.merchant_no,
        i.company_no, i.company_name, i.branch_no, i.branch_name,
        i.merchant_name, i.store_no, i.store_name, i.disable_flag, i.create_no, i.modify_no,
        g.warehouse_flag, g.goods_groups_name as goods_name,g.calculation_unit_no,
        i.stock_num, IFNULL(s.freeze_stock_num,0) + IFNULL(s.deliver_stock_num,0) freeze_stock_num, g.delete_flag as goods_delete_flag,
        n.stock_num canSaleStockNum
        from
        im_inventory_detail i
        left join goods_groups g on i.goods_no = g.goods_groups_no
        left join goods_real_stock s on i.goods_no = s.goods_no
        left join im_warehouse_goods_relation n on  i.goods_no=n.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.warehouse_no=n.warehouse_no and i.inventory_code = #{inventoryCode}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and (g.goods_no = #{goodsName,jdbcType=VARCHAR} or g.goods_name LIKE CONCAT(CONCAT('%',#{goodsName,jdbcType=VARCHAR}),'%'))
                </if>
                <include refid="Base_Column_Goods_Where" />
                <include refid="Base_Column_goods_relation_Where" />
            </trim>
        </where>
        order by i.create_time desc
    </select>

    <select id="selectInventoryChangeWarehouseGoods" resultMap="BaseResultGoodsMap" parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select
        <include refid="Ware_Column_Goods_List" />,
        (select count(d.id)  from im_inventory_detail d where d.delete_flag=1 and d.inventory_code = #{inventoryCode}) as totalCount
        from
        im_inventory_detail i
        left join goods g on i.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.warehouse_flag =1
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.inventory_code = #{inventoryCode}
                </if>
                <include refid="Base_Column_Goods_Where" />
            </trim>
        </where>
        order by i.create_time desc
    </select>

    <select id="selectInventoryChangeWarehouseGoodsGroups" resultMap="BaseResultGoodsMap" parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select
        i.create_name,
        i.create_time,
        i.modify_name,
        i.modify_time,
        i.delete_flag,
        i.inventory_code, i.goods_no, i.inventory_stock_num,
        i.warehouse_no, i.warehouse_code, i.warehouse_name, i.confirm_no, i.confirm_name, i.merchant_no,
        i.company_no, i.company_name, i.branch_no, i.branch_name,
        i.merchant_name, i.store_no, i.store_name, i.disable_flag, i.create_no, i.modify_no,
        g.calculation_unit_no,
        g.warehouse_flag,g.goods_groups_name as goods_name,
        (select count(d.id)  from im_inventory_detail d where d.delete_flag=1 and d.inventory_code = #{inventoryCode}) as totalCount
        from
        im_inventory_detail i
        left join goods_groups g on i.goods_no = g.goods_groups_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.warehouse_flag =1
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.inventory_code = #{inventoryCode}
                </if>
                <include refid="Base_Column_Goods_Where" />
            </trim>
        </where>
        order by i.create_time desc
    </select>

    <select id="selectInventoryDetailWarehouseGoods" resultMap="BaseResultGoodsMap" parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select
        <include refid="Ware_Column_Goods_List" />,
        (select count(d.id)  from im_inventory_detail d where d.delete_flag=1 and d.inventory_code = #{inventoryCode}) as totalCount
        from
        im_inventory_detail i
        left join goods g on i.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.inventory_code = #{inventoryCode}
                </if>
                <include refid="Base_Column_Goods_Where" />
            </trim>
        </where>
        order by i.create_time desc
    </select>

    <select id="selectByCode" resultMap="BaseResultGoodsMap" parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select
        <include refid="Base_List" />
        from
        im_inventory_detail i
        left join goods g on i.goods_no = g.goods_no
        --             left join calculation_unit c on g.calculation_unit_no = c.calculation_unit_no
        --             left join calculation_unit u on g.assist_calculation_unit_no = u.calculation_unit_no
        left join goods_real_stock s on i.goods_no = s.goods_no
        left join im_warehouse_goods_relation n on  i.goods_no=n.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.warehouse_no=n.warehouse_no and i.inventory_code = #{inventoryCode}
                </if>
                <if test="goodsName != null and goodsName != ''">
                    and (g.goods_no = #{goodsName,jdbcType=VARCHAR} or g.goods_name LIKE CONCAT(CONCAT('%',#{goodsName,jdbcType=VARCHAR}),'%'))
                </if>
                <include refid="Base_Column_Goods_Where" />
                <include refid="Base_Column_goods_relation_Where" />
            </trim>
        </where>
        order by i.create_time desc
    </select>

    <select id="selectGoodsByCode" resultMap="BaseResultGoodsMap" parameterType="java.lang.String">
        select
            <include refid="Base_Column_Goods_List" />
        from
            im_inventory_detail i
            left join goods g on i.goods_no = g.goods_no
            left join im_warehouse_goods_relation n on i.goods_no = n.goods_no
            left join goods_real_stock r on i.goods_no = r.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.warehouse_no=n.warehouse_no and i.inventory_code = #{inventoryCode}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and n.warehouse_no = #{warehouseNo}
                </if>
                <include refid="Base_Column_Goods_Where" />
                <include refid="Base_Column_goods_relation_Where" />
            </trim>
        </where>
        order by i.create_time desc
    </select>

    <select id="selectGoodsGroupsByCode" resultMap="BaseResultGoodsMap" parameterType="java.lang.String">
        select
        i.create_name,
        i.create_time,
        i.modify_name,
        i.modify_time,
        i.delete_flag,
        i.inventory_code, i.goods_no, n.stock_num stock_num, i.inventory_stock_num,
        i.warehouse_no, i.warehouse_code, i.warehouse_name, i.confirm_no, i.confirm_name, i.merchant_no,
        i.company_no, i.company_name, i.branch_no, i.branch_name,
        i.merchant_name, i.store_no, i.store_name, i.disable_flag, i.create_no, i.modify_no,
        g.calculation_unit_no, g.warehouse_flag, g.goods_groups_name as goods_name,
        r.freeze_stock_num
        from
        im_inventory_detail i
        left join goods_groups g on i.goods_no = g.goods_groups_no
        left join im_warehouse_goods_relation n on i.goods_no = n.goods_no
        left join goods_real_stock r on i.goods_no = r.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.warehouse_no=n.warehouse_no and i.inventory_code = #{inventoryCode}
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and n.warehouse_no = #{warehouseNo}
                </if>
                <include refid="Base_Column_Goods_Where" />
                <include refid="Base_Column_goods_relation_Where" />
            </trim>
        </where>
        order by i.create_time desc
    </select>

    <!-- 查盘点明细下的类目 -->
    <select id="selectInventoryDetailCategoryCode" resultType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select i.inventory_code, i.goods_no, sc.category_name, sc.full_name_path, g.first_attribute_value_name, g.second_attribute_value_name, g.third_attribute_value_name
        from im_inventory_detail i
        left join goods g on i.goods_no =g.goods_no
        left join sale_category sc on g.category_no =sc.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and i.inventory_code = #{inventoryCode}
                </if>
                <if test="goodsList != null and goodsList.size() > 0">
                    and i.goods_no IN
                    <foreach item="goodsNo" collection="goodsList" open="(" close=")" separator=",">
                        #{goodsNo}
                    </foreach>
                </if>
                <include refid="Base_Column_Goods_Where"/>
            </trim>
        </where>
        group by i.inventory_code, i.goods_no, sc.category_name
    </select>

    <!-- 判断是否存在商品正在盘点中-添加用 -->
    <select id="selectExistInventoryGoods" resultType="java.lang.Integer"
            parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select count(1) from im_inventory i left join im_inventory_detail ii on i.inventory_code=ii.inventory_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsList != null and goodsList.size() > 0">
                    and ii.goods_no in
                    <foreach collection="goodsList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and ii.warehouse_no = #{warehouseNo}
                </if>
                <if test="goodsNo != null and goodsNo !=''">
                    and ii.goods_no = #{goodsNo}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and i.company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and i.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and i.store_no = #{storeNo}
                </if>
                <if test="inventoryType != null">
                    and i.inventory_type = #{inventoryType}
                </if>
                and i.oper_type ='1002'
                <include refid="Base_Column_Goods_Where"/>
                <include refid="Base_Column_Goods_Detail_Where"/>
            </trim>
        </where>
    </select>

    <!-- 判断是否存在商品正在盘点中-修改用 -->
    <select id="selectExistInventory" resultType="java.lang.Integer"
            parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select count(1) from im_inventory i left join im_inventory_detail ii on i.inventory_code=ii.inventory_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and i.inventory_code != #{inventoryCode}
                and ii.goods_no IN
                (select goods_no from im_inventory_detail d where d.inventory_code=#{inventoryCode} and d.delete_flag=1)
                <if test="warehouseNo != null and warehouseNo != ''">
                    and ii.warehouse_no = #{warehouseNo}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and i.company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and i.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and i.store_no = #{storeNo}
                </if>
                and i.oper_type ='1002'
                <include refid="Base_Column_Goods_Where"/>
                <include refid="Base_Column_Goods_Detail_Where"/>
            </trim>
        </where>
    </select>

    <!-- 查询盘点的商品的数量 -->
    <select id="selectInventoryGoodsCount" resultType="java.lang.Integer"
            parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select count(1) from im_inventory_detail ii
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and ii.inventory_code != #{inventoryCode}
                <include refid="Base_Column_Goods_Detail_Where"/>
            </trim>
        </where>
    </select>

    <!-- 查询盘点的商品的数量-批量 -->
    <select id="selectBatchInventoryGoodsCount" resultType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select ii.inventory_code, count(1) totalCount from im_inventory_detail ii
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCodes != null and inventoryCodes.size() > 0">
                    and ii.inventory_code IN
                    <foreach item="inventoryCode" collection="inventoryCodes" open="(" close=")" separator=",">
                        #{inventoryCode}
                    </foreach>
                </if>
                <include refid="Base_Column_Goods_Detail_Where"/>
            </trim>
        </where>
        group by ii.inventory_code
    </select>

    <!-- 判断是否存在商品正在盘点中，存在则返回对应的商品信息-添加用 -->
    <select id="selectExistInventoryGoodsList" resultType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select
        g.goods_no,
        g.goods_name,
        g.goods_help_code,
        g.first_attribute_value_name,
        g.second_attribute_value_name,
        g.third_attribute_value_name,
        g.barcode
        from im_inventory i
        left join im_inventory_detail ii on i.inventory_code=ii.inventory_code
        left join goods g on g.goods_no = ii.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsList != null and goodsList.size() > 0">
                    and ii.goods_no in
                    <foreach collection="goodsList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="warehouseNo != null and warehouseNo != ''">
                    and ii.warehouse_no = #{warehouseNo}
                </if>
                <if test="goodsNo != null and goodsNo !=''">
                    and ii.goods_no = #{goodsNo}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and i.company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and i.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and i.store_no = #{storeNo}
                </if>
                and i.oper_type ='1002'
                <include refid="Base_Column_Goods_Where"/>
                <include refid="Base_Column_Goods_Detail_Where"/>
            </trim>
        </where>
    </select>

    <!-- 判断是否存在商品正在盘点中，存在则返回对应的商品信息-修改用 -->
    <select id="selectExistInventoryList" resultType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomInventoryDetailVo">
        select
        g.goods_no,
        g.goods_name,
        g.goods_help_code,
        g.first_attribute_value_name,
        g.second_attribute_value_name,
        g.third_attribute_value_name,
        g.barcode
        from im_inventory i
        left join im_inventory_detail ii on i.inventory_code=ii.inventory_code
        left join goods g on g.goods_no = ii.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and i.inventory_code != #{inventoryCode}
                and ii.goods_no IN
                (select goods_no from im_inventory_detail d where d.inventory_code=#{inventoryCode} and d.delete_flag=1)
                <if test="warehouseNo != null and warehouseNo != ''">
                    and ii.warehouse_no = #{warehouseNo}
                </if>
                <if test="companyNo  != null and companyNo  != ''">
                    and i.company_no = #{companyNo}
                </if>
                <if test="merchantNo  != null and merchantNo  != ''">
                    and i.merchant_no = #{merchantNo}
                </if>
                <if test="storeNo  != null and storeNo  != ''">
                    and i.store_no = #{storeNo}
                </if>
                and i.oper_type ='1002'
                <include refid="Base_Column_Goods_Where"/>
                <include refid="Base_Column_Goods_Detail_Where"/>
            </trim>
        </where>
    </select>

    <update id="logicDelete" parameterType="java.lang.String">
        update
            im_inventory_detail
        set
            delete_flag = 2
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="inventoryCode != null and inventoryCode != ''">
                    and inventory_code = #{inventoryCode}
                </if>
                <include refid="Base_Column_Where" />
            </trim>
        </where>
    </update>

    <update id="batchLogicDelete" parameterType="java.util.List">
        update
            im_inventory_detail
        set
            delete_flag = 2
        <where>
            delete_flag = 1
            and inventory_code in
            <foreach collection="list" item="item" index="index" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </where>
     </update>
</mapper>
