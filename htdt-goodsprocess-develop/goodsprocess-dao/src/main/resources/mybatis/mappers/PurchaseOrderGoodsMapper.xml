<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htdt.goodsprocess.dao.PurchaseOrderGoodsDao">
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.PurchaseOrderGoodsDomain">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="purchase_goods_code" property="purchaseGoodsCode" jdbcType="VARCHAR"/>
        <result column="purchase_code" property="purchaseCode" jdbcType="VARCHAR"/>
        <result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
        <result column="expect_receive_date" property="expectReceiveDate" jdbcType="TIMESTAMP"/>
        <result column="actual_receive_date" property="actualReceiveDate" jdbcType="TIMESTAMP"/>
        <result column="goods_no" property="goodsNo" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="purchase_unit" property="purchaseUnit" jdbcType="VARCHAR"/>
        <result column="category_no" property="categoryNo" jdbcType="VARCHAR"/>
        <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
        <result column="purchase_unit_price" property="purchaseUnitPrice" jdbcType="DECIMAL"/>
        <result column="purchase_num" property="purchaseNum" jdbcType="DECIMAL"/>
        <result column="calculation_unit_no" property="calculationUnitNo"/>
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo"/>
        <result column="main_unit_num" property="mainUnitNum"/>
        <result column="assist_unit_num" property="assistUnitNum"/>
        <result column="standard_flag" property="standardFlag"/>
        <result column="storage_count" property="storageCount" jdbcType="DECIMAL"/>
        <result column="purchase_price" property="purchasePrice" jdbcType="DECIMAL"/>
        <result column="warehouse_type" property="warehouseType" jdbcType="TINYINT"/>
        <result column="warehouse_no" property="warehouseNo" jdbcType="VARCHAR"/>
        <result column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="order_status" property="orderStatus" jdbcType="VARCHAR"/>
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="merchant_name" property="merchantName" jdbcType="VARCHAR"/>
        <result column="store_no" property="storeNo" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="disable_flag" property="disableFlag" jdbcType="TINYINT"/>
        <result column="create_no" property="createNo" jdbcType="VARCHAR"/>
        <result column="create_name" property="createName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_no" property="modifyNo" jdbcType="VARCHAR"/>
        <result column="modify_name" property="modifyName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="company_no" property="companyNo" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="branch_no" property="branchNo" jdbcType="VARCHAR"/>
        <result column="branch_name" property="branchName" jdbcType="VARCHAR"/>
        <result column="platform_type" property="platformType" jdbcType="VARCHAR"/>
        <result column="goodsName" property="goodsName"/>
        <result column="raw_goods_no" property="rawGoodsNo" jdbcType="VARCHAR"/>
        <result column="raw_goods_num" property="rawGoodsNum" jdbcType="DECIMAL"/>
        <result column="out_stock_num" property="outStockNum" jdbcType="DECIMAL"/>
        <result column="wait_out_stock_num" property="waitOutStockNum" jdbcType="DECIMAL"/>
    </resultMap>
    <resultMap id="VoBaseResultMap" type="cn.htdt.goodsprocess.vo.AtomPurchaseOrderGoodsVo" extends="BaseResultMap">
        <result property="warehouse_flag" column="warehouseFlag"/>
        <result property="goods_delete_flag" column="goodsDeleteFlag"/>
        <result property="purchase_order_status" column="purchaseOrderStatus"/>
        <result property="supplier_code" column="supplierCode"/>
        <result property="supplier_name" column="supplierName"/>
        <result property="purchase_date" column="purchaseDate"/>
        <result property="calculation_unit_no" column="calculationUnitNo"/>
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo"/>
        <result column="main_unit_num" property="mainUnitNum"/>
        <result column="assist_unit_num" property="assistUnitNum"/>
        <result column="standard_flag" property="standardFlag"/>
        <result property="conversion_rate" column="conversionRate"/>
        <result column="goods_form" property="goodsForm"/>
        <result column="first_attribute_value_name" property="firstAttributeValueName"/>
        <result column="second_attribute_value_name" property="secondAttributeValueName"/>
        <result column="third_attribute_value_name" property="thirdAttributeValueName"/>
    </resultMap>

    <resultMap id="OrderGoodsListResultMap" type="cn.htdt.goodsprocess.vo.AtomPurchaseOrderGoodsVo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="purchase_goods_code" property="purchaseGoodsCode" jdbcType="VARCHAR"/>
        <result column="purchase_code" property="purchaseCode" jdbcType="VARCHAR"/>
        <result column="source_type" property="sourceType" jdbcType="VARCHAR"/>
        <result column="expect_receive_date" property="expectReceiveDate" jdbcType="TIMESTAMP"/>
        <result column="actual_receive_date" property="actualReceiveDate" jdbcType="TIMESTAMP"/>
        <result column="goods_no" property="goodsNo" jdbcType="VARCHAR"/>
        <result column="goods_name" property="goodsName" jdbcType="VARCHAR"/>
        <result column="purchase_unit" property="purchaseUnit" jdbcType="VARCHAR"/>
        <result column="category_no" property="categoryNo" jdbcType="VARCHAR"/>
        <result column="category_name" property="categoryName" jdbcType="VARCHAR"/>
        <result column="purchase_unit_price" property="purchaseUnitPrice" jdbcType="DECIMAL"/>
        <result column="purchase_num" property="purchaseNum" jdbcType="DECIMAL"/>
        <result column="calculation_unit_no" property="calculationUnitNo"/>
        <result column="assist_calculation_unit_no" property="assistCalculationUnitNo"/>
        <result column="main_unit_num" property="mainUnitNum"/>
        <result column="assist_unit_num" property="assistUnitNum"/>
        <result column="standard_flag" property="standardFlag"/>
        <result column="storage_count" property="storageCount" jdbcType="DECIMAL"/>
        <result column="purchase_price" property="purchasePrice" jdbcType="DECIMAL"/>
        <result column="warehouse_type" property="warehouseType" jdbcType="TINYINT"/>
        <result column="warehouse_no" property="warehouseNo" jdbcType="VARCHAR"/>
        <result column="warehouse_name" property="warehouseName" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
        <result column="order_status" property="orderStatus" jdbcType="VARCHAR"/>
        <result column="merchant_no" property="merchantNo" jdbcType="VARCHAR"/>
        <result column="merchant_name" property="merchantName" jdbcType="VARCHAR"/>
        <result column="store_no" property="storeNo" jdbcType="VARCHAR"/>
        <result column="store_name" property="storeName" jdbcType="VARCHAR"/>
        <result column="disable_flag" property="disableFlag" jdbcType="TINYINT"/>
        <result column="create_no" property="createNo" jdbcType="VARCHAR"/>
        <result column="create_name" property="createName" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="modify_no" property="modifyNo" jdbcType="VARCHAR"/>
        <result column="modify_name" property="modifyName" jdbcType="VARCHAR"/>
        <result column="modify_time" property="modifyTime" jdbcType="TIMESTAMP"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
        <result column="company_no" property="companyNo" jdbcType="VARCHAR"/>
        <result column="company_name" property="companyName" jdbcType="VARCHAR"/>
        <result column="branch_no" property="branchNo" jdbcType="VARCHAR"/>
        <result column="branch_name" property="branchName" jdbcType="VARCHAR"/>
        <result column="platform_type" property="platformType" jdbcType="VARCHAR"/>
        <result column="goodsName" property="goodsName"/>
        <result column="goodsBaseName" property="goodsBaseName"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, purchase_goods_code, purchase_code, source_type, expect_receive_date, actual_receive_date,
        goods_no, goods_name, purchase_unit, category_no, category_name, purchase_unit_price,
        purchase_num, storage_count, purchase_price, warehouse_type, warehouse_no, warehouse_name,
        remark, order_status, merchant_no, merchant_name, store_no, store_name, disable_flag,
        create_no, create_name, create_time, modify_no, modify_name, modify_time, delete_flag,
        company_no, company_name, branch_no, branch_name,platform_type, calculation_unit_no,
        assist_calculation_unit_no, main_unit_num, assist_unit_num, standard_flag, raw_goods_no,
        raw_goods_num, out_stock_num, wait_out_stock_num
    </sql>
    <sql id="Base_Column_List_Goods">
        p.purchase_goods_code,
        p.purchase_code,
        p.source_type,
        p.expect_receive_date,
        p.actual_receive_date,
        p.goods_no,
        CONCAT_WS('-',
		    p.goods_name,
            IF
                ( g.first_attribute_value_name != '', g.first_attribute_value_name , null ),
            IF
                ( g.second_attribute_value_name != '', g.second_attribute_value_name , null ),
            IF
                ( g.third_attribute_value_name != '', g.third_attribute_value_name , null )) AS goodsName,
        p.goods_name as goodsBaseName,
        p.purchase_unit,
        p.category_no,
        p.category_name,
        p.purchase_unit_price,
        p.purchase_num,
        p.storage_count,
        p.purchase_price,
        p.warehouse_type,
        p.warehouse_no,
        p.warehouse_name,
        p.remark,
        p.order_status,
        p.merchant_no,
        p.merchant_name,
        p.store_no,
        p.store_name,
        p.disable_flag,
        p.create_no,
        p.create_name,
        p.create_time,
        p.modify_no,
        p.modify_name,
        p.modify_time,
        p.delete_flag,
        p.company_no,
        p.company_name,
        p.branch_no,
        p.branch_name,
        p.platform_type
    </sql>

    <sql id="Goods_Column_List">
    pog.purchase_goods_code AS purchaseGoodsCode,pog.purchase_code AS purchaseCode,pog.source_type AS sourceType,pog.expect_receive_date AS expectReceiveDate,pog.actual_receive_date AS actualReceiveDate,
    pog.goods_no AS goodsNo,
    CONCAT_WS('-',
		pog.goods_name,
        IF
            ( gs.first_attribute_value_name != '', gs.first_attribute_value_name , null ),
        IF
            ( gs.second_attribute_value_name != '', gs.second_attribute_value_name , null ),
        IF
        ( gs.third_attribute_value_name != '', gs.third_attribute_value_name , null )) AS goodsName,
    pog.goods_name as goodsBaseName,
    gs.first_attribute_value_name as firstAttributeValueName,
    gs.second_attribute_value_name as secondAttributeValueName,
    gs.third_attribute_value_name as thirdAttributeValueName,
    pog.purchase_unit AS purchaseUnit,pog.category_no AS categoryNo,pog.category_name AS categoryName,pog.purchase_unit_price AS purchaseUnitPrice,
    pog.purchase_num AS purchaseNum, pog.calculation_unit_no AS calculationUnitNo, pog.assist_calculation_unit_no AS assistCalculationUnitNo, pog.main_unit_num AS mainUnitNum, pog.assist_unit_num AS assistUnitNum, pog.standard_flag AS standardFlag, pog.storage_count AS storageCount,pog.purchase_price AS purchasePrice,pog.warehouse_type AS warehouseType,pog.warehouse_no AS warehouseNo,pog.warehouse_name AS warehouseName,
    pog.remark AS remark,pog.order_status AS orderStatus,pog.merchant_no AS merchantNo,pog.merchant_name AS merchantName,pog.store_no AS storeNo,pog.store_name AS storeName,pog.disable_flag AS disableFlag,
    pog.create_no AS createNo,pog.create_name AS createName,pog.create_time AS createTime,pog.modify_no AS modifyNo,pog.modify_name AS modifyName,pog.modify_time AS modifyTime,pog.delete_flag AS deleteFlag,
    pog.company_no AS companyNo,pog.company_name AS companyName,pog.branch_no AS branchNo,pog.branch_name AS branchName,
    pog.platform_type AS platformType,gs.warehouse_flag AS warehouseFlag,gs.delete_flag as goodsDeleteFlag,
    gs.calculation_unit_no AS curCalculationUnitNo,
    gs.assist_calculation_unit_no AS curAssistCalculationUnitNo,
    gs.main_unit_num AS curMainUnitNum,
    gs.assist_unit_num AS curAssistUnitNum, gs.validity_period_manage_flag AS validityPeriodManageFlag, gs.quality_guarantee_period AS qualityGuaranteePeriod, gs.shelf_life_unit AS shelfLifeUnit,
    gs.multi_unit_type AS multiUnitType, gs.multi_unit_goods_no AS multiUnitGoodsNo
    </sql>
    <sql id="VO_Goods_Column_List">
        pog.id,pog.purchase_goods_code,pog.purchase_code,pog.source_type,pog.expect_receive_date,pog.actual_receive_date,
        pog.goods_no,
        CONCAT_WS('-',
		pog.goods_name,
        IF
            ( g.first_attribute_value_name != '', g.first_attribute_value_name , null ),
        IF
            ( g.second_attribute_value_name != '', g.second_attribute_value_name , null ),
        IF
            ( g.third_attribute_value_name != '', g.third_attribute_value_name , null )) AS goodsName,
        pog.purchase_unit,pog.category_no,pog.category_name,pog.purchase_unit_price,
        pog.purchase_num, pog.calculation_unit_no, pog.assist_calculation_unit_no, pog.main_unit_num, pog.assist_unit_num, pog.standard_flag, pog.storage_count,pog.purchase_price,pog.warehouse_type,pog.warehouse_no,pog.warehouse_name,
        pog.remark,pog.order_status,pog.merchant_no,pog.merchant_name,pog.store_no,pog.store_name,pog.disable_flag,
        pog.create_no,pog.create_name,pog.create_time,pog.modify_no,pog.modify_name,pog.modify_time,pog.delete_flag,
        pog.company_no,pog.company_name,pog.branch_no,pog.branch_name,pog.platform_type,
        o.order_status as purchase_order_status, o.supplier_code, o.supplier_name, o.purchase_date, g.warehouse_flag, g.delete_flag as goods_delete_flag, g.conversion_rate, g.goods_form, g.first_attribute_value_name, g.second_attribute_value_name, g.third_attribute_value_name
    </sql>
    <!--状态标识-->
    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <sql id="Base_Column_Where_power">
        <if test="loginIdentity  != null and loginIdentity==1">
            and gs.goods_source_type in ('1001')
        </if>
        <if test="loginIdentity  != null and loginIdentity==2">
            and gs.goods_source_type in ('1002')
        </if>
        <if test="loginIdentity  != null and loginIdentity==4">
            and gs.goods_source_type in ('1003')
        </if>
        <if test="loginIdentity  != null and loginIdentity==8">
            and gs.goods_source_type in ('1003')
        </if>
    </sql>

    <!--根据采购单编码 查下面待入库的商品 -->
    <select id="selectPurchaseGoodsByPurchaseCode" resultMap="VoBaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderGoodsVo">
        select
        <include refid="Base_Column_List"/>
        from purchase_order_goods
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode  != null">
                    and purchase_code =#{purchaseCode,jdbcType=VARCHAR}
                </if>
                and delete_flag = 1
                and order_status != '1003'
            </trim>
        </where>
    </select>
    <!--根据采购单编码 查下面的商品 关联-->
    <select id="selectPurchaseOrderGoods" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderGoodsVo"
            parameterType="java.lang.String">
        select
        <include refid="Goods_Column_List"/>
        from purchase_order_goods pog left join goods gs on pog.goods_no = gs.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode  != null">
                    and pog.purchase_code =#{purchaseCode,jdbcType=VARCHAR}
                </if>
                and pog.delete_flag = 1
            </trim>
        </where>
    </select>
    <!--根据采购单编码 查下面的商品 关联-->
    <select id="selectPurchaseOrderGoodsGroups" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderGoodsVo"
            parameterType="java.lang.String">
        select
            pog.purchase_goods_code AS purchaseGoodsCode,pog.purchase_code AS purchaseCode,pog.source_type AS sourceType,pog.expect_receive_date AS expectReceiveDate,
            pog.actual_receive_date AS actualReceiveDate, pog.goods_no AS goodsNo, pog.goods_name AS goodsName, pog.goods_name as goodsBaseName,
            pog.purchase_unit AS purchaseUnit,pog.category_no AS categoryNo,pog.category_name AS categoryName,pog.purchase_unit_price AS purchaseUnitPrice,
            pog.purchase_num AS purchaseNum, pog.calculation_unit_no AS calculationUnitNo, pog.assist_calculation_unit_no AS assistCalculationUnitNo,
            pog.main_unit_num AS mainUnitNum, pog.assist_unit_num AS assistUnitNum, pog.standard_flag AS standardFlag, pog.storage_count AS storageCount,
            pog.purchase_price AS purchasePrice,pog.warehouse_type AS warehouseType,pog.warehouse_no AS warehouseNo,pog.warehouse_name AS warehouseName,
            pog.remark AS remark,pog.order_status AS orderStatus,pog.merchant_no AS merchantNo,pog.merchant_name AS merchantName,pog.store_no AS storeNo,
            pog.store_name AS storeName,pog.disable_flag AS disableFlag, pog.create_no AS createNo,pog.create_name AS createName,pog.create_time AS createTime,
            pog.modify_no AS modifyNo,pog.modify_name AS modifyName, pog.modify_time AS modifyTime,pog.delete_flag AS deleteFlag,
            pog.company_no AS companyNo,pog.company_name AS companyName,pog.branch_no AS branchNo,pog.branch_name AS branchName,
            pog.platform_type AS platformType,gs.warehouse_flag AS warehouseFlag,gs.delete_flag as goodsDeleteFlag,
            gs.calculation_unit_no AS curCalculationUnitNo
        from purchase_order_goods pog left join goods_groups gs on pog.goods_no = gs.goods_groups_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode  != null">
                    and pog.purchase_code =#{purchaseCode,jdbcType=VARCHAR}
                </if>
                and pog.delete_flag = 1
            </trim>
        </where>
    </select>

    <!--查询所有未到货的采购商品明细 order_status订单状态21:待签收 22:已签收 23:待入库 24:部分入库 25:已结案 26:完成 -->
    <select id="selectPurchaseOrderGoodsNoDelivery" resultMap="VoBaseResultMap"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderGoodsVo">
        select
        <include refid="VO_Goods_Column_List"/>
        from purchase_order_goods pog
        left join purchase_order o on pog.purchase_code=o.purchase_code
        left join goods g on pog.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="platformType != null and platformType != ''">
                    AND o.platform_type= #{platformType,jdbcType=VARCHAR}
                </if>
                <if test="supplierName != null and supplierName  != ''">
                    and (o.supplier_name like CONCAT(CONCAT('%', #{supplierName}),'%') or
                    o.supplier_code=#{supplierName,jdbcType=VARCHAR})
                </if>
                <if test="merchantNo != null and merchantNo != ''">
                    and pog.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and pog.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="barcode != null and barcode  != ''">
                    and (g.goods_no = #{barcode,jdbcType=VARCHAR} or g.barcode=#{barcode,jdbcType=VARCHAR})
                </if>
                <if test="sourceType != null and sourceType  != ''">
                    and pog.source_type = #{sourceType,jdbcType=VARCHAR}
                </if>
                <if test="purchaseCode != null and purchaseCode  != ''">
                    and pog.purchase_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <if test="startExpectReceiveDate != null">
                    <![CDATA[
                        and  pog.expect_receive_date >= #{startExpectReceiveDate}
                     ]]>
                </if>
                <if test="endExpectReceiveDate != null">
                    <![CDATA[
                        and pog.expect_receive_date <= #{endExpectReceiveDate}
                     ]]>
                </if>
                <if test="startPurchaseDate != null">
                    <![CDATA[
                        and  o.purchase_date >= #{startPurchaseDate}
                     ]]>
                </if>
                <if test="endPurchaseDate != null">
                    <![CDATA[
                        and o.purchase_date <= #{endPurchaseDate}
                     ]]>
                </if>
                <![CDATA[
                and pog.expect_receive_date < NOW()
                and o.order_status in (21, 22, 23, 24)
                and pog.delete_flag = 1
                 ]]>
            </trim>
        </where>
        order by
        pog.expect_receive_date DESC
    </select>

    <!--根据采购单编码集合 查下面所有的商品-->
    <select id="selectOrderGoodsListByPCodeArr" resultMap="OrderGoodsListResultMap" parameterType="java.util.ArrayList">
        select
        <include refid="Base_Column_List_Goods"/>
        from purchase_order_goods p
        INNER JOIN goods g on p.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCodeList  != null  and purchaseCodeList.size()>0">
                    and p.purchase_code in
                    <foreach collection="purchaseCodeList" item="item" index="index"
                             open="(" separator="," close=")" >
                        #{item}
                    </foreach>
                </if>
                and p.delete_flag = 1
            </trim>
        </where>
    </select>

    <!--根据采购单编码集合-->
    <select id="selectOrderGoodsList" resultMap="OrderGoodsListResultMap" parameterType="java.util.ArrayList">
        select
        <include refid="Base_Column_List"/>
        from purchase_order_goods p
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCodeList  != null  and purchaseCodeList.size()>0">
                    and p.purchase_code in
                    <foreach collection="purchaseCodeList" item="item" index="index"
                             open="(" separator="," close=")" >
                        #{item}
                    </foreach>
                </if>
                and p.delete_flag = 1
            </trim>
        </where>
    </select>

    <!--入库页面列表-->
    <select id="selectStorageOrderGoodsList" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderStorageVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderStorageVo">
        SELECT
        pog.purchase_code AS purchase_code,
        pog.purchase_goods_code AS purchaseGoodsCode,
        pog.goods_no AS goodsNo,
        pog.goods_name AS goodsName,
        CONCAT_WS('-',
        pog.goods_name,
        IF
        ( gs.first_attribute_value_name != '', gs.first_attribute_value_name , null ),
        IF
        ( gs.second_attribute_value_name != '', gs.second_attribute_value_name , null ),
        IF
        ( gs.third_attribute_value_name != '', gs.third_attribute_value_name , null )) AS showGoodsName,
        gs.first_attribute_value_name as firstAttributeValueName,
        gs.second_attribute_value_name as secondAttributeValueName,
        gs.third_attribute_value_name as thirdAttributeValueName,
        pog.purchase_unit AS purchaseUnit,
        pog.purchase_unit_price AS purchaseUnitPrice,
        pog.purchase_num AS purchaseNum,
        pog.storage_count AS storageCount,
        pog.order_status as orderStatus,
        pog.calculation_unit_no AS calculationUnitNo,
        pog.assist_calculation_unit_no AS assistCalculationUnitNo,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_unit_num AS assistUnitNum,
        pog.standard_flag AS standardFlag,
        gs.conversion_rate AS conversionRate,
        gs.warehouse_flag AS goodsWarehouseFlag,
        pog.warehouse_type AS warehouseFlag,
        pog.raw_goods_no AS rawGoodsNo,
        pog.raw_goods_num AS rawGoodsNum,
        pog.out_stock_num AS outStockNum,
        pog.wait_out_stock_num AS waitOutStockNum,
        gs.imei_flag as imeiFlag,
        gs.validity_period_manage_flag as validityPeriodManageFlag,
        gs.quality_guarantee_period as qualityGuaranteePeriod,
        gs.shelf_life_unit as shelfLifeUnit,
        gs.multi_unit_type AS multiUnitType,
        gs.multi_unit_goods_no AS multiUnitGoodsNo,
        grs.real_stock_num as realStockNum
        FROM
        purchase_order_goods pog
        LEFT JOIN goods gs ON pog.goods_no = gs.goods_no
        LEFT JOIN goods_real_stock grs ON (pog.raw_goods_no = grs.goods_no OR pog.goods_no = grs.goods_no)
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode  != null">
                    and pog.purchase_code =#{purchaseCode,jdbcType=VARCHAR}
                </if>
                AND pog.delete_flag = 1
                AND pog.order_status != '1003'
            </trim>
        </where>
    </select>

    <!--入库页面列表-->
    <select id="selectStorageOrderGoodsList2" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderStorageVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderStorageVo">
        SELECT
        pog.purchase_code AS purchase_code,
        pog.purchase_goods_code AS purchaseGoodsCode,
        pog.goods_no AS goodsNo,
        pog.goods_name AS goodsName,
        pog.purchase_unit AS purchaseUnit,
        pog.purchase_unit_price AS purchaseUnitPrice,
        pog.purchase_num AS purchaseNum,
        pog.storage_count AS storageCount,
        pog.order_status as orderStatus,
        pog.calculation_unit_no AS calculationUnitNo,
        pog.assist_calculation_unit_no AS assistCalculationUnitNo,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_unit_num AS assistUnitNum,
        pog.standard_flag AS standardFlag,
        pog.warehouse_type AS warehouseFlag,
        pog.raw_goods_no AS rawGoodsNo,
        pog.raw_goods_num AS rawGoodsNum,
        pog.out_stock_num AS outStockNum,
        pog.wait_out_stock_num AS waitOutStockNum,
        pog.warehouse_type_of_goods_groups as warehouseFlagOfGoodsGroups,
        grs.real_stock_num as realStockNum,
        po.purchase_type
        FROM
        purchase_order_goods pog
        LEFT JOIN goods_real_stock grs ON (pog.raw_goods_no = grs.goods_no OR pog.goods_no = grs.goods_no)
        left join purchase_order po on po.purchase_code = pog.purchase_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode  != null">
                    and pog.purchase_code =#{purchaseCode,jdbcType=VARCHAR}
                </if>
                AND pog.delete_flag = 1
                AND pog.order_status != '1003'
            </trim>
        </where>
    </select>

    <!--已入库页面列表-->
    <select id="selectAlreadyStorageOrderGoodsList"
            resultType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderAlreadyStorageVo"
            parameterType="java.lang.String">
        SELECT
        pog.purchase_code AS purchaseCode,
        pog.purchase_goods_code AS purchaseGoodsCode,
        wsf.warehouse_flag AS warehouseFlag,
        wsf.warehouse_no AS warehouseNo,
        wsf.warehouse_name AS warehouseName,
        wsf.create_time AS createTime,
        wsf.goods_no AS goodsNo,
        CONCAT_WS('-',
        wsf.goods_name,
        IF
        ( g.first_attribute_value_name != '', g.first_attribute_value_name , null ),
        IF
        ( g.second_attribute_value_name != '', g.second_attribute_value_name , null ),
        IF
        ( g.third_attribute_value_name != '', g.third_attribute_value_name , null )) AS goodsName,
        wsf.goods_name as goodsBaseName,
        g.first_attribute_value_name as firstAttributeValueName,
        g.second_attribute_value_name as secondAttributeValueName,
        g.third_attribute_value_name as thirdAttributeValueName,
        wsf.calculation_unit_name AS calculationUnitName,
        wsf.assist_calculation_unit_name AS assistCalculationUnitName,
        pog.calculation_unit_no AS calculationUnitNo,
        pog.assist_calculation_unit_no AS assistCalculationUnitNo,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_unit_num AS assistUnitNum,
        pog.standard_flag AS standardFlag,
        wsf.conversion_rate AS conversionRate,
        pog.purchase_unit AS purchaseUnit,
        pog.purchase_num AS purchaseNum,
        pog.purchase_unit_price AS purchaseUnitPrice,
        pog.out_stock_num as storageCount,
        wsf.stock_num AS stockNum,
        g.imei_flag AS imeiFlag,
        g.multi_unit_type AS multiUnitType,
        g.multi_unit_goods_no AS multiUnitGoodsNo
        FROM
        im_warehouse_stock_flow_record wsf
        LEFT JOIN purchase_order_goods pog ON wsf.sub_bill_code = pog.purchase_goods_code
        LEFT JOIN goods g on wsf.goods_no = g.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode  != null">
                    and wsf.bill_code =#{purchaseCode,jdbcType=VARCHAR}
                </if>
                AND wsf.delete_flag = 1
            </trim>
        </where>
    </select>

    <!--入库详情页面列表-->
    <select id="selectStorageOrderGoodsDetail" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderAlreadyStorageVo"
            parameterType="java.lang.String">
        SELECT
        pog.purchase_code AS purchaseCode,
        pog.purchase_goods_code AS purchaseGoodsCode,
        wsf.warehouse_flag AS warehouseFlag,
        wsf.warehouse_no AS warehouseNo,
        wsf.warehouse_name AS warehouseName,
        wsf.create_time AS createTime,
        wsf.goods_no AS goodsNo,
        wsf.goods_name AS goodsName,
        wsf.calculation_unit_name AS calculationUnitName,
        wsf.assist_calculation_unit_name AS assistCalculationUnitName,
        wsf.conversion_rate AS conversionRate,
        pog.purchase_unit AS purchaseUnit,
        pog.purchase_num AS purchaseNum,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_unit_num AS assistUnitNum,
        wsf.stock_num AS stockNum,
        gs.delete_flag AS goodsDelFlag
        FROM
        im_warehouse_stock_flow_record wsf
        LEFT JOIN purchase_order_goods pog ON wsf.sub_bill_code = pog.purchase_goods_code
        LEFT JOIN goods gs ON wsf.goods_no = gs.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode  != null">
                    and wsf.bill_code =#{purchaseCode,jdbcType=VARCHAR}
                </if>
                AND wsf.delete_flag = 1
            </trim>
        </where>
    </select>

    <!--采购单明细追踪页面查询sql selectPurchaseOrderTrackDetail-->
    <select id="selectPurchaseOrderTrackDetail" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderTrackDetailVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseTrackDetailDTO">
        SELECT
        po.purchase_date AS purchaseDate,
        po.purchase_code AS purchaseCode,
        po.source_type AS sourceType,
        po.supplier_code AS supplierCode,
        po.supplier_name AS supplierName,
        po.order_status AS orderStatus,
        gs.conversion_rate AS conversionRate,
        pog.goods_no AS goodsNo,
        CONCAT_WS('-',
        pog.goods_name,
        IF
        ( gs.first_attribute_value_name != '', gs.first_attribute_value_name , null ),
        IF
        ( gs.second_attribute_value_name != '', gs.second_attribute_value_name , null ),
        IF
        ( gs.third_attribute_value_name != '', gs.third_attribute_value_name , null )) AS goodsName,
        pog.category_no AS categoryNo,
        pog.category_name AS categoryName,
        pog.purchase_unit AS purchaseUnit,
        pog.calculation_unit_no AS calculationUnitNo,
        pog.assist_calculation_unit_no AS assistCalculationUnitNo,
        pog.main_unit_num AS mainUnitNum,
        pog.assist_unit_num AS assistUnitNum,
        pog.standard_flag AS standardFlag,
        pog.purchase_unit_price AS purchaseUnitPrice,
        pog.purchase_num AS purchaseNum,
        pog.storage_count AS storageCount,
        pog.purchase_price AS purchasePrice
        FROM
        purchase_order po
        LEFT JOIN purchase_order_goods pog ON po.purchase_code = pog.purchase_code
        LEFT JOIN goods gs ON pog.goods_no = gs.goods_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where_power"/>
                <if test="supplierKeyWord != null and supplierKeyWord  != ''">
                    and (po.supplier_name like CONCAT(CONCAT('%', #{supplierKeyWord}),'%') or po.supplier_code like
                    CONCAT(CONCAT('%', #{supplierKeyWord}),'%'))
                </if>
                <if test="platformType != null and platformType != ''">
                    AND po.platform_type= #{platformType,jdbcType=VARCHAR}
                </if>
                <if test="goodsKeyWord != null and goodsKeyWord  != ''">
                    and (pog.goods_no like CONCAT(CONCAT('%', #{goodsKeyWord}),'%') or pog.goods_name like
                    CONCAT(CONCAT('%', #{goodsKeyWord}),'%'))
                </if>
                <if test="purchaseCode != null and purchaseCode  != ''">
                    and po.purchase_code like CONCAT(CONCAT('%', #{purchaseCode}),'%')
                </if>
                <if test="sourceType != null and sourceType != ''">
                    and po.source_type =#{sourceType,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo != null">
                    and po.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null">
                    and po.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo != null and companyNo != ''">
                    and po.company_no = #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="startPurchaseDate != null and startPurchaseDate != ''">
                    <![CDATA[
                        and  po.purchase_date  >= #{startPurchaseDate}
                     ]]>
                </if>
                <if test="endPurchaseDate != null and endPurchaseDate != ''">
                    <![CDATA[
                        and po.purchase_date <= #{endPurchaseDate}
                     ]]>
                </if>
                <![CDATA[
                AND po.delete_flag = 1
                and pog.delete_flag = 1
                 ]]>
            </trim>
        </where>
        ORDER BY po.purchase_date DESC
    </select>

    <!--采购单明细追踪页面查询sql selectPurchaseOrderTrackDetail 自定义count-->
    <select id="selectPurchaseOrderTrackDetail_COUNT" resultType="Long" parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseTrackDetailDTO">
        SELECT count(1)
        FROM
        purchase_order po
        LEFT JOIN purchase_order_goods pog ON po.purchase_code = pog.purchase_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="supplierKeyWord != null and supplierKeyWord  != ''">
                    and (po.supplier_name like CONCAT(CONCAT('%', #{supplierKeyWord}),'%') or po.supplier_code like
                    CONCAT(CONCAT('%', #{supplierKeyWord}),'%'))
                </if>
                <if test="platformType != null and platformType != ''">
                    AND po.platform_type= #{platformType,jdbcType=VARCHAR}
                </if>
                <if test="goodsKeyWord != null and goodsKeyWord  != ''">
                    and (pog.goods_no like CONCAT(CONCAT('%', #{goodsKeyWord}),'%') or pog.goods_name like
                    CONCAT(CONCAT('%', #{goodsKeyWord}),'%'))
                </if>
                <if test="purchaseCode != null and purchaseCode  != ''">
                    and po.purchase_code like CONCAT(CONCAT('%', #{purchaseCode}),'%')
                </if>
                <if test="sourceType != null and sourceType != ''">
                    and po.source_type =#{sourceType,jdbcType=VARCHAR}
                </if>
                <if test="merchantNo != null">
                    and po.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null">
                    and po.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo != null and companyNo != ''">
                    and po.company_no = #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="startPurchaseDate != null and startPurchaseDate != ''">
                    <![CDATA[
                        and  po.purchase_date  >= #{startPurchaseDate}
                     ]]>
                </if>
                <if test="endPurchaseDate != null and endPurchaseDate != ''">
                    <![CDATA[
                        and po.purchase_date <= #{endPurchaseDate}
                     ]]>
                </if>
                <![CDATA[
                AND po.delete_flag = 1
                and pog.delete_flag = 1
                 ]]>
            </trim>
        </where>
    </select>

    <!--采购单明细追踪页面 退货商品信息查询sql selectPurchaseGoodsTrackDetail-->
    <select id="selectPurchaseGoodsTrackDetail" resultType="cn.htdt.goodsprocess.vo.AtomPurchaseGoodsTrackDetailVo"
            parameterType="java.util.List">
        SELECT
        pro.purchase_code AS purchaseCode,
        prog.goods_no AS goodsNo,
        prog.goods_name AS goodsName,
        SUM(prog.purchase_price) AS purchasePrice,
        SUM(prog.returned_count) AS storageCount,
        SUM(prog.return_request_count) AS returnRequestCount
        from
        purchase_return_order pro
        LEFT JOIN purchase_return_order_goods prog ON pro.return_code = prog.return_code
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCodeList  != null  and purchaseCodeList.size()>0">
                    and pro.purchase_code in
                    <foreach collection="purchaseCodeList" item="item" index="index"
                             open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                AND pro.delete_flag = 1
                AND prog.delete_flag = 1
            </trim>
        </where>
        GROUP BY
        pro.purchase_code,
        prog.goods_no,
        prog.goods_name
    </select>

    <insert id="insertSelective" parameterType="cn.htdt.goodsprocess.domain.PurchaseOrderGoodsDomain">
        insert into purchase_order_goods
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="purchaseGoodsCode != null">
                purchase_goods_code,
            </if>
            <if test="purchaseCode != null">
                purchase_code,
            </if>
            <if test="sourceType != null">
                source_type,
            </if>
            <if test="expectReceiveDate != null">
                expect_receive_date,
            </if>
            <if test="actualReceiveDate != null">
                actual_receive_date,
            </if>
            <if test="goodsNo != null">
                goods_no,
            </if>
            <if test="goodsName != null">
                goods_name,
            </if>
            <if test="purchaseUnit != null">
                purchase_unit,
            </if>
            <if test="categoryNo != null">
                category_no,
            </if>
            <if test="categoryName != null">
                category_name,
            </if>
            <if test="purchaseUnitPrice != null">
                purchase_unit_price,
            </if>
            <if test="purchaseNum != null">
                purchase_num,
            </if>
            <if test="storageCount != null">
                storage_count,
            </if>
            <if test="purchasePrice != null">
                purchase_price,
            </if>
            <if test="warehouseType != null">
                warehouse_type,
            </if>
            <if test="warehouseNo != null">
                warehouse_no,
            </if>
            <if test="warehouseName != null">
                warehouse_name,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="orderStatus != null">
                order_status,
            </if>
            <if test="merchantNo != null">
                merchant_no,
            </if>
            <if test="merchantName != null">
                merchant_name,
            </if>
            <if test="storeNo != null">
                store_no,
            </if>
            <if test="storeName != null">
                store_name,
            </if>
            <if test="disableFlag != null">
                disable_flag,
            </if>
            <if test="createNo != null">
                create_no,
            </if>
            <if test="createName != null">
                create_name,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="modifyNo != null">
                modify_no,
            </if>
            <if test="modifyName != null">
                modify_name,
            </if>
            <if test="modifyTime != null">
                modify_time,
            </if>
            <if test="deleteFlag != null">
                delete_flag,
            </if>
            <if test="companyNo != null">
                company_no,
            </if>
            <if test="companyName != null">
                company_name,
            </if>
            <if test="branchNo != null">
                branch_no,
            </if>
            <if test="branchName != null">
                branch_name,
            </if>
            <if test="platformType != null ">
                and platform_type
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="purchaseGoodsCode != null">
                #{purchaseGoodsCode,jdbcType=VARCHAR},
            </if>
            <if test="purchaseCode != null">
                #{purchaseCode,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="expectReceiveDate != null">
                #{expectReceiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="actualReceiveDate != null">
                #{actualReceiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="goodsNo != null">
                #{goodsNo,jdbcType=VARCHAR},
            </if>
            <if test="goodsName != null">
                #{goodsName,jdbcType=VARCHAR},
            </if>
            <if test="purchaseUnit != null">
                #{purchaseUnit,jdbcType=VARCHAR},
            </if>
            <if test="categoryNo != null">
                #{categoryNo,jdbcType=VARCHAR},
            </if>
            <if test="categoryName != null">
                #{categoryName,jdbcType=VARCHAR},
            </if>
            <if test="purchaseUnitPrice != null">
                #{purchaseUnitPrice,jdbcType=DECIMAL},
            </if>
            <if test="purchaseNum != null">
                #{purchaseNum,jdbcType=DECIMAL},
            </if>
            <if test="storageCount != null">
                #{storageCount,jdbcType=DECIMAL},
            </if>
            <if test="purchasePrice != null">
                #{purchasePrice,jdbcType=DECIMAL},
            </if>
            <if test="warehouseType != null">
                #{warehouseType,jdbcType=VARCHAR},
            </if>
            <if test="warehouseNo != null">
                #{warehouseNo,jdbcType=VARCHAR},
            </if>
            <if test="warehouseName != null">
                #{warehouseName,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="orderStatus != null">
                #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="merchantNo != null">
                #{merchantNo,jdbcType=VARCHAR},
            </if>
            <if test="merchantName != null">
                #{merchantName,jdbcType=VARCHAR},
            </if>
            <if test="storeNo != null">
                #{storeNo,jdbcType=VARCHAR},
            </if>
            <if test="storeName != null">
                #{storeName,jdbcType=VARCHAR},
            </if>
            <if test="disableFlag != null">
                #{disableFlag,jdbcType=TINYINT},
            </if>
            <if test="createNo != null">
                #{createNo,jdbcType=VARCHAR},
            </if>
            <if test="createName != null">
                #{createName,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="modifyNo != null">
                #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="modifyTime != null">
                #{modifyTime,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteFlag != null">
                #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="companyNo != null">
                #{companyNo,jdbcType=VARCHAR},
            </if>
            <if test="companyName != null">
                #{companyName,jdbcType=VARCHAR},
            </if>
            <if test="branchNo != null">
                #{branchNo,jdbcType=VARCHAR},
            </if>
            <if test="branchName != null">
                #{branchName,jdbcType=VARCHAR},
            </if>
            <if test="platformType != null ">
                #{platformType,jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>


    <!--根据采购单号 逻辑删除下面的采购商品-->
    <update id="updateDelGoodsByPurchaseCode" parameterType="cn.htdt.goodsprocess.domain.PurchaseOrderDomain">
        update purchase_order_goods
        <set>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null">
                    and purchase_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </update>
    <!--根据采购单号 强制完成-->
    <update id="updateGoodsStatusFinishByPurchaseCode"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderGoodsVo">
        update purchase_order_goods
        <set>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="purchaseCode != null">
                    and purchase_code = #{purchaseCode,jdbcType=VARCHAR}
                </if>
                <if test="purchaseCodeList != null and purchaseCodeList.size > 0">
                    and purchase_code in
                    <foreach collection="purchaseCodeList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
    </update>

    <!--根据采购单商品行号 强制完成-->
    <update id="updateGoodsStatusFinishByPurchaseGoodsCode"
            parameterType="cn.htdt.goodsprocess.vo.AtomPurchaseOrderGoodsVo">
        update purchase_order_goods
        <set>
            <if test="orderStatus != null">
                order_status = #{orderStatus,jdbcType=VARCHAR},
            </if>
            <if test="modifyNo != null">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null">
                modify_name = #{modifyName,jdbcType=VARCHAR}
            </if>
        </set>
        <where>
            purchase_goods_code in
                <foreach collection="purchaseGoodsCodeList" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
        </where>
    </update>

    <!--根据采购商品行号 修改已出入库数量和状态-->
    <update id="updateStorageOrderGoodsListStatus" parameterType="java.util.List">
        <foreach collection="storageGoodsList" item="item" index="index" open=""
                 close="" separator=";">
            update
            purchase_order_goods
            <set>
                <if test="item.storageCount != null">
                    storage_count = #{item.storageCount,jdbcType=DECIMAL},
                </if>
                <if test="item.purchaseNum != null">
                    purchase_num = #{item.purchaseNum,jdbcType=DECIMAL},
                </if>
                <if test="item.modifyNo != null">
                    modify_no = #{item.modifyNo,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyName != null">
                    modify_name = #{item.modifyName,jdbcType=VARCHAR},
                </if>
                <if test="item.orderStatus != null">
                    order_status = #{item.orderStatus,jdbcType=VARCHAR}
                </if>
            </set>
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.purchaseGoodsCode != null and item.purchaseGoodsCode != ''">
                        and purchase_goods_code = #{item.purchaseGoodsCode,jdbcType=VARCHAR}
                    </if>
                </trim>
            </where>
        </foreach>
    </update>

    <!--判断商品存在待支付/待发货订单或者存在未入库的采购单/未出库的采购退单用-->
    <!-- where pog.goods_no in ('21031110255605700000') and pog.order_status in('1001', '1002')-->
    <select id="selectPurchaseAndReturnGoodsCount" resultType="java.lang.Integer" parameterType="cn.htdt.goodsprocess.dto.request.purchaseorder.ReqPurchaseGoodsDTO">
        select IFNULL(sum(a.c), 0) from (
        select count(1) c from purchase_order_goods pog
        <where>
        <trim suffixOverrides="AND | OR" prefix="1=1">
            <if test="merchantNo != null and merchantNo != ''">
                and pog.merchant_no = #{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="storeNo != null and storeNo != ''">
                and pog.store_no = #{storeNo,jdbcType=VARCHAR}
            </if>
            <if test="companyNo != null and companyNo != ''">
                and pog.company_no = #{companyNo,jdbcType=VARCHAR}
            </if>
            <if test="goodsNo != null and goodsNo != ''">
                and pog.goods_no = #{goodsNo,jdbcType=VARCHAR}
            </if>
            <if test="orderStatusList != null and orderStatusList.size() > 0">
                AND pog.order_status IN
                <foreach item="orderStatus" collection="orderStatusList" open="(" close=")" separator=",">
                    #{orderStatus}
                </foreach>
            </if>
        </trim>
        </where>
        union
        select count(1) c from purchase_return_order_goods prog
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="merchantNo != null and merchantNo != ''">
                    and prog.merchant_no = #{merchantNo,jdbcType=VARCHAR}
                </if>
                <if test="storeNo != null and storeNo != ''">
                    and prog.store_no = #{storeNo,jdbcType=VARCHAR}
                </if>
                <if test="companyNo != null and companyNo != ''">
                    and prog.company_no = #{companyNo,jdbcType=VARCHAR}
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and prog.goods_no = #{goodsNo,jdbcType=VARCHAR}
                </if>
                <if test="orderStatusList != null and orderStatusList.size() > 0">
                    AND prog.order_status IN
                    <foreach item="orderStatus" collection="orderStatusList" open="(" close=")" separator=",">
                        #{orderStatus}
                    </foreach>
                </if>
            </trim>
        </where>
        ) a
    </select>

</mapper>
