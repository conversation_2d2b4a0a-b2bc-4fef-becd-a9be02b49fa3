<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="cn.htdt.goodsprocess.dao.CloudPoolGoodsDistributionRelationDao">
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.CloudPoolGoodsDistributionRelationDomain">
        <result column="goods_no" property="goodsNo" jdbcType="VARCHAR"/>
        <result column="store_no" property="storeNo" jdbcType="VARCHAR"/>
        <result column="user_no" property="userNo" jdbcType="VARCHAR"/>
        <result column="delete_flag" property="deleteFlag" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, goods_no, store_no, user_no, create_no, create_name, create_time, modify_no, modify_name, modify_time, delete_flag
    </sql>

    <sql id="Base_Column_Where">
        <if test="goodsNo != null">
            and goods_no = #{goodsNo,jdbcType=VARCHAR}
        </if>
        <if test="storeNo != null">
            and store_no = #{storeNo,jdbcType=VARCHAR}
        </if>
        <if test="userNo != null">
            and user_no = #{userNo,jdbcType=VARCHAR}
        </if>
        <if test="createNo != null">
            and create_no = #{createNo,jdbcType=VARCHAR}
        </if>
        <if test="createName != null">
            and create_name = #{createName,jdbcType=VARCHAR}
        </if>
        <if test="createTime != null">
            and create_time = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="modifyNo != null">
            and modify_no = #{modifyNo,jdbcType=VARCHAR}
        </if>
        <if test="modifyName != null">
            and modify_name = #{modifyName,jdbcType=VARCHAR}
        </if>
        <if test="modifyTime != null">
            and modify_time = #{modifyTime,jdbcType=TIMESTAMP}
        </if>
        <if test="deleteFlag != null">
            and delete_flag = #{deleteFlag,jdbcType=TINYINT}
        </if>
    </sql>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.CloudPoolGoodsDistributionRelationDomain">
        select
        <include refid="Base_Column_List"/>
        from cloud_pool_goods_distribution_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <select id="selectCloudPoolGoodsDistributionStoreNum"
            resultType="cn.htdt.goodsprocess.vo.AtomCloudPoolGoodsDistributionRelationVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomCloudPoolGoodsDistributionRelationVo">
        select
        goods_no goodsNo,
        COUNT( 1 ) distributionStoreTotal
        from cloud_pool_goods_distribution_relation
        <where>
            delete_flag = '1'
            and goods_no IN
            <foreach collection="goodsNos" item="goodsNo" open="(" close=")" separator=",">
                #{goodsNo}
            </foreach>
        </where>
        GROUP BY
        goods_no
    </select>

    <select id="selectStoreListByGoodsNos" resultType="string">
        SELECT
        store_no
        FROM
        cloud_pool_goods_distribution_relation
        WHERE
        delete_flag = '1'
        <if test="goodsNoList != null and goodsNoList.size() > 0">
            and goods_no IN
            <foreach collection="goodsNoList" item="item" index="index"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        store_no
    </select>

    <select id="selectCloudPoolDistributionStoreUpShelfNum"
            resultType="cn.htdt.goodsprocess.vo.AtomCloudPoolGoodsDistributionRelationVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomCloudPoolGoodsDistributionRelationVo">
        SELECT
        cpgdr.goods_no goodsNo,
        COUNT( 1 ) distributionStoreTotal
        FROM
        cloud_pool_goods_distribution_relation cpgdr
        INNER JOIN goods g ON g.cloud_pool_goods_no = cpgdr.goods_no
        AND g.store_no = cpgdr.store_no
        <where>
            cpgdr.delete_flag = '1'
            AND g.disable_flag = '2'
            AND g.goods_status = '1002'
            AND cpgdr.goods_no IN
            <foreach collection="goodsNos" item="goodsNo" open="(" close=")" separator=",">
                #{goodsNo}
            </foreach>
        </where>
        GROUP BY
        cpgdr.goods_no
    </select>


    <update id="batchUpdateByParam"
            parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
            update cloud_pool_goods_distribution_relation
            <set>
                <if test="item.goodsNo != null and item.goodsNo != ''">
                    goods_no = #{item.goodsNo,jdbcType=VARCHAR},
                </if>
                <if test="item.storeNo != null and item.storeNo != ''">
                    store_no = #{item.storeNo,jdbcType=VARCHAR},
                </if>
                <if test="item.userNo != null and item.userNo  != ''">
                    user_no = #{item.userNo,jdbcType=VARCHAR},
                </if>
                <if test="item.createNo != null and item.createNo != ''">
                    create_no = #{item.createNo,jdbcType=VARCHAR},
                </if>
                <if test="item.createName != null and item.createName  != ''">
                    create_name = #{item.createName,jdbcType=VARCHAR},
                </if>
                <if test="item.createTime != null and item.createTime  != ''">
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.modifyNo != null and item.modifyNo != ''">
                    modify_no = #{item.modifyNo,jdbcType=VARCHAR},
                </if>
                <if test="item.modifyName != null and item.modifyName != ''">
                    modify_name = #{item.modifyName,jdbcType=VARCHAR},
                </if>
                <if test="item.deleteFlag != null and item.deleteFlag != ''">
                    delete_flag = #{item.deleteFlag,jdbcType=TINYINT},
                </if>
                modify_time = NOW()
            </set>
            <where>
                <trim suffixOverrides="AND | OR" prefix="1=1">
                    <if test="item.goodsNo != null and item.goodsNo != ''">
                        and goods_no = #{item.goodsNo}
                    </if>
                    <if test="item.storeNo != null and item.storeNo != ''">
                        and store_no = #{item.storeNo}
                    </if>
                </trim>
            </where>
        </foreach>
    </update>

    <update id="modifyDeleteFlagRelationByParam"
            parameterType="cn.htdt.goodsprocess.vo.AtomCloudPoolGoodsDistributionRelationVo">
        update cloud_pool_goods_distribution_relation
        <set>
            <if test="modifyNo != null and modifyNo != ''">
                modify_no = #{modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="modifyName != null and modifyName != ''">
                modify_name = #{modifyName,jdbcType=VARCHAR},
            </if>
            <if test="deleteFlag != null">
                delete_flag = #{deleteFlag,jdbcType=TINYINT},
            </if>
            modify_time = NOW()
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="goodsNos  != null  and goodsNos.size()>0">
                    and goods_no in
                    <foreach collection="goodsNos" item="item" index="index"
                             open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
                <if test="storeNos  != null  and storeNos.size()>0">
                    and store_no in
                    <foreach collection="storeNos" item="item" index="index"
                             open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            </trim>
        </where>
    </update>
</mapper>
