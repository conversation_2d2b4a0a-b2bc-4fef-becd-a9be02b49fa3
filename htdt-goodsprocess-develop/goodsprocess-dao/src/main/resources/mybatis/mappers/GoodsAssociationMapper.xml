<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.GoodsAssociationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.GoodsAssociationDomain">
        <result column="id" property="id" />
        <result column="create_no" property="createNo" />
        <result column="create_name" property="createName" />
        <result column="create_time" property="createTime" />
        <result column="modify_no" property="modifyNo" />
        <result column="modify_name" property="modifyName" />
        <result column="modify_time" property="modifyTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="goods_association_no" property="goodsAssociationNo" />
        <result column="distribution_channel" property="distributionChannel" />
        <result column="association_goods_total" property="associationGoodsTotal" />
        <result column="association_price" property="associationPrice" />
        <result column="disable_flag" property="disableFlag" />
        <result column="merchant_no" property="merchantNo" />
        <result column="merchant_name" property="merchantName" />
        <result column="store_no" property="storeNo" />
        <result column="store_name" property="storeName" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        create_no,
        create_name,
        create_time,
        modify_no,
        modify_name,
        modify_time,
        delete_flag,
        goods_association_no, distribution_channel, association_goods_total, association_price, disable_flag, merchant_no, merchant_name, store_no, store_name
    </sql>
    <update id="modifyGoodsAssociation">
        update goods_association
        <set>
            <if test="associationGoodsTotal != null" >
                association_goods_total = #{associationGoodsTotal},
            </if>
            <if test="associationPrice != null" >
                association_price = #{associationPrice},
            </if>
        </set>
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    and store_no = #{storeNo}
                </if>
                <if test="goodsAssociationNo != null and goodsAssociationNo != ''">
                    and goods_association_no = #{goodsAssociationNo}
                </if>
            </trim>
        </where>
    </update>
</mapper>
