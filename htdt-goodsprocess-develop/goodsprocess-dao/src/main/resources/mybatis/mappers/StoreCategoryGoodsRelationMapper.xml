<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.htdt.goodsprocess.dao.StoreCategoryGoodsRelationDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.htdt.goodsprocess.domain.StoreCategoryGoodsRelationDomain">
        <result column="id" property="id"/>
        <result column="delete_flag" property="deleteFlag"/>
        <result column="store_category_no" property="storeCategoryNo"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="merchant_no" property="merchantNo"/>
        <result column="merchant_name" property="merchantName"/>
        <result column="store_no" property="storeNo"/>
        <result column="store_name" property="storeName"/>
        <result column="disable_flag" property="disableFlag"/>
    </resultMap>

    <resultMap id="atomResStoreCategoryNumVo" type="cn.htdt.goodsprocess.vo.AtomResStoreCategoryNumVo">
        <result column="store_category_no" property="storeCategoryNo"/>
        <result column="store_category_name" property="storeCategoryName"/>
        <result column="goodsNum" property="goodsNum"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        delete_flag,
        store_category_no, goods_no, merchant_no, merchant_name, store_no, store_name, disable_flag
    </sql>

    <sql id="Base_Column_Where">
        and delete_flag = 1
    </sql>

    <sql id="Base_Good_Column_Where">
        <!--平台身份查询全部列表（包括平台、商家、店铺自建） -->
        <if test="queryType==1001">
            and g.goods_source_type in ('1001','1002','1003')
        </if>
        <!--平台身份查询平台列表（包括平台自建） -->
        <if test="queryType==1002">
            and g.goods_source_type in ('1001')
        </if>
        <!--平台身份查询商家/店铺列表（包括商家、店铺自建） -->
        <if test="queryType==1003">
            and g.goods_source_type in ('1002','1003')
        </if>
        <!--商家身份查询商家列表（包括商家自建） -->
        <if test="queryType==1004">
            and g.goods_source_type in ('1002')
        </if>
        <!--商家身份查询店铺列表（包括店铺自建） -->
        <if test="queryType==1005">
            and g.goods_source_type in ('1003')
        </if>
        <!--店铺身份查询列表（包括商家分发、店铺自建） -->
        <if test="queryType== 1006">
            and g.goods_source_type in ('1003','1005')
        </if>
        <!--店铺身份查询列表（包括商家分发、店铺自建、云池） -->
        <if test="queryType == '1009'">
            and g.goods_source_type in ('1003','1005','1006')
        </if>
        <!--查询云池分销商品列表 -->
        <if test="queryType==1007">
            and g.goods_source_type in ('1006')
        </if>
        <!--平台身份查询全部列表（包括平台自建、商家自建、店铺自建、中台同步）-->
        <if test="queryType==1010">
            and g.goods_source_type in ('1001','1002','1003','1007')
        </if>
        <!--查询中台同步商品列表 -->
        <if test="queryType==1011">
            and g.goods_source_type in ('1007')
        </if>
    </sql>

    <select id="selectByParams" resultMap="BaseResultMap"
            parameterType="cn.htdt.goodsprocess.domain.StoreCategoryGoodsRelationDomain">
        select
        <include refid="Base_Column_List"/>
        from
        store_category_goods_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeCategoryNo != null and storeCategoryNo != ''">
                    and store_category_no = #{storeCategoryNo}
                </if>
                <include refid="Base_Column_Where"/>
            </trim>
        </where>
    </select>

    <resultMap id="atomResStoreCategoryGoodsVo" type="cn.htdt.goodsprocess.vo.AtomResStoreCategoryGoodsVo">
        <result column="store_category_no" property="storeCategoryNo"/>
        <result column="goods_no" property="goodsNo"/>
        <result column="goods_name" property="goodsName"/>
        <result column="full_name_path" property="fullNamePath"/>
        <result column="retail_price" property="retailPrice"/>
        <result column="market_price" property="marketPrice"/>
        <result column="purchase_price" property="purchasePrice"/>
        <result column="goods_status" property="goodsStatus"/>
        <result column="goods_form" property="goodsForm"/>
        <result column="series_type" property="seriesType"/>
        <result column="original_goods_no" property="originalGoodsNo"/>
    </resultMap>

    <resultMap id="atomResStoreCategoryGoodsNumVo" type="cn.htdt.goodsprocess.vo.AtomResStoreCategoryGoodsNumVo">
        <result column="store_category_no" property="storeCategoryNo"/>
        <result column="store_category_name" property="storeCategoryName"/>
        <result column="parent_no" property="parentNo"/>
    </resultMap>

    <select id="selectStoreCategoryGoods" resultMap="atomResStoreCategoryGoodsVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomResStoreCategoryGoodsVo">
        select
        scgr.store_category_no,
        g.goods_no,
        g.goods_name,
        sc.full_name_path,
        g.retail_price,
        g.market_price,
        g.purchase_price,
        g.goods_status,
        g.goods_form,
        g.series_type,
        g.original_goods_no
        from
        store_category_goods_relation scgr
        INNER JOIN goods g ON g.goods_no = scgr.goods_no AND g.delete_flag = 1
        INNER JOIN sale_category sc ON sc.category_no = g.category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeCategoryNo != null and storeCategoryNo != ''">
                    and scgr.store_category_no = #{storeCategoryNo}
                </if>
                <if test="storeCategoryNoList != null">
                    AND scgr.store_category_no IN
                    <foreach item="storeCategoryNo" collection="storeCategoryNoList" open="(" close=")" separator=",">
                        #{storeCategoryNo}
                    </foreach>
                </if>
                AND scgr.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="selectStoreCategoryAndGoodsNum" resultMap="atomResStoreCategoryGoodsNumVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomResStoreCategoryGoodsNumVo">
        select
        sc.store_category_no,
        sc.store_category_name,
        g.goods_no,
        g.goods_name
        from
        store_category_goods_relation scgr
        INNER JOIN goods g ON g.goods_no = scgr.goods_no and g.delete_flag = 1
        INNER JOIN store_category sc on sc.store_category_no = scgr.store_category_no and sc.delete_flag = 1
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeNo != null and storeNo != ''">
                    and sc.store_no = #{storeNo}
                    and g.store_no = #{storeNo}
                </if>
                AND scgr.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="selectStoreCategoryGoodsByParam" resultMap="atomResStoreCategoryGoodsVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomResPrintStoreCategoryGoodsVo">
        select
        scgr.store_category_no,
        g.goods_no,
        g.goods_name
        from
        store_category_goods_relation scgr
        INNER JOIN goods g ON g.parent_goods_no = scgr.goods_no
        INNER JOIN store_category sc on sc.store_category_no = scgr.store_category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <!--不同身份查询不同的商品-->
                <include refid="Base_Good_Column_Where"/>
                <if test="storeCategoryNo != null and storeCategoryNo != ''">
                    and scgr.store_category_no = #{storeCategoryNo}
                </if>
                <if test="storeCategoryNoList != null">
                    AND scgr.store_category_no IN
                    <foreach item="storeCategoryNo" collection="storeCategoryNoList" open="(" close=")" separator=",">
                        #{storeCategoryNo}
                    </foreach>
                </if>
                and g.delete_flag = 1
                and g.series_type !='1001'
                and g.goods_type != '1002'
                AND sc.delete_flag = 1
                AND scgr.delete_flag = 1
            </trim>
        </where>
    </select>

    <select id="selectStoreCategoryGoodsCount" resultMap="atomResStoreCategoryNumVo"
            parameterType="cn.htdt.goodsprocess.vo.AtomResStoreCategoryGoodsVo">
        select
        count (*) goodsNum
        scgr.store_category_no,
        from
        store_category_goods_relation scgr
        INNER JOIN goods g ON g.goods_no = scgr.goods_no AND g.delete_flag = 1
        INNER JOIN store_category sc ON sc.store_category_no = scgr.store_category_no
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                and g.series_type !='1001'
                and g.goods_type != '1002'
                and g.delete_flag = 1
                and scgr.delete_flag = 1
                and sc.delete_flag = 1
                <if test="storeCategoryNo != null and storeCategoryNo != ''">
                    and scgr.store_category_no = #{storeCategoryNo}
                </if>
                <if test="storeCategoryNoList != null">
                    AND scgr.store_category_no IN
                    <foreach item="storeCategoryNo" collection="storeCategoryNoList" open="(" close=")" separator=",">
                        #{storeCategoryNo}
                    </foreach>
                </if>
            </trim>
        </where>
    </select>

    <update id="batchDeleteStoreCategoryGoodsRelation"
            parameterType="cn.htdt.goodsprocess.vo.AtomReqStoreCategoryGoodsVo">
        DELETE
        FROM
        store_category_goods_relation
        <where>
            <trim suffixOverrides="AND | OR" prefix="1=1">
                <if test="storeCategoryNo != null and storeCategoryNo != ''">
                    and store_category_no = #{storeCategoryNo}
                </if>
                <if test="storeCategoryNoList != null">
                    AND store_category_no IN
                    <foreach item="storeCategoryNo" collection="storeCategoryNoList" open="(" close=")" separator=",">
                        #{storeCategoryNo}
                    </foreach>
                </if>
                <if test="goodsNo != null and goodsNo != ''">
                    and goods_no = #{goodsNo}
                </if>
                <if test="goodsNoList != null">
                    AND goods_no IN
                    <foreach item="goodsNo" collection="goodsNoList" open="(" close=")" separator=",">
                        #{goodsNo}
                    </foreach>
                </if>
            </trim>
        </where>
    </update>

    <select id="selectGoodsCategories" resultType="string">
        select
            store_category_no
        from
            store_category_goods_relation
        where
            store_no = #{storeNo}
            and goods_no = #{goodsNo}
    </select>
    <select id="selectGoodsSortNum" resultType="int">
        select
            sort_num
        from
            store_category_goods_relation
        where
            store_no = #{storeNo}
            and store_category_no = #{storeCategoryNo}
            and goods_no = #{goodsNo}
    </select>
    <update id="updateSortNum">
        update
            store_category_goods_relation
        set
            sort_num = #{endSortNum}
        where
            store_no = #{storeNo}
            and store_category_no = #{storeCategoryNo}
            and goods_no =#{goodsNo}
            and sort_num =#{startSortNum}
    </update>
    <update id="moveBetweenSortNum">
        update
            store_category_goods_relation
        set
            sort_num = sort_num+ #{moveValue}
        where
           store_no = #{storeNo}
            and store_category_no = #{storeCategoryNo}
            and sort_num between #{startMoveSortNum} and #{endMoveSortNum}
    </update>
    <select id="selectCategoryMaxSortNum" resultType="int">
        select
           IFNULL(max(sort_num),0)
        from
            store_category_goods_relation
        where
            store_no = #{storeNo}
            and store_category_no = #{storeCategoryNo}
    </select>

    <update id="batchUpdateByParam"
            parameterType="cn.htdt.goodsprocess.domain.StoreCategoryGoodsRelationDomain">
        <foreach collection="list" item="item" separator=";">
            update
            store_category_goods_relation
            set
            sort_num = #{item.sortNum},
            <if test="item.modifyNo != null and item.modifyNo != ''">
                modify_no = #{item.modifyNo,jdbcType=VARCHAR},
            </if>
            <if test="item.modifyName != null and item.modifyName != ''">
                modify_name = #{item.modifyName,jdbcType=VARCHAR},
            </if>
            modify_time = NOW()
            <where>
                goods_no = #{item.goodsNo}
                and store_no = #{item.storeNo}
                and store_category_no = #{item.storeCategoryNo}
            </where>
        </foreach>
    </update>
</mapper>
