package cn.htdt.app.platform.dto.response.officialwebsiteinformationcollection;

import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 官网信息收集 返回实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2021/11/1
 */
@Data
public class ResPlatformOfficialWebsiteInformationCollectionDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "官网入口")
    @ExcelProperty(value = "官网入口", index = 0)
    @ColumnWidth(20)
    private String officialWebsiteEntrance;

    @ApiModelProperty(value = "来源")
    @ExcelProperty(value = "来源", index = 1)
    @ColumnWidth(20)
    private String infoSources;

    @ApiModelProperty(value = "公司")
    @ExcelProperty(value = "公司", index = 2)
    @ColumnWidth(20)
    private String company;

    @ApiModelProperty(value = "姓名")
    @ExcelProperty(value = "姓名", index = 3)
    @ColumnWidth(20)
    private String name;

    @ApiModelProperty(value = "手机号")
    @ExcelProperty(value = "手机号", index = 4)
    @ColumnWidth(20)
    private String phone;

    @ApiModelProperty(value = "所在城市")
    @ExcelProperty(value = "所在城市", index = 5)
    @ColumnWidth(20)
    private String address;

    @ApiModelProperty(value = "目前行业")
    @ExcelProperty(value = "目前行业", index = 6)
    @ColumnWidth(20)
    private String industry;

    @ApiModelProperty(value = "创建时间（提交时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "提交时间", index = 7, converter = LocalDateTimeConverter.class)
    @ColumnWidth(20)
    private LocalDateTime createTime;

    @ApiModelProperty(value = "信息处理状态")
    @ExcelProperty(value = "信息处理状态", index = 8)
    @ColumnWidth(20)
    private String disposeStatusName;

    @ApiModelProperty(value = "最新信息处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty(value = "最新信息处理时间", index = 9,converter = LocalDateTimeConverter.class)
    @ColumnWidth(20)
    private LocalDateTime newestTime;

    @ApiModelProperty(value = "最新操作人")
    @ExcelProperty(value = "最新操作人", index = 10)
    @ColumnWidth(20)
    private String modifyName;

    @ApiModelProperty(value = "信息录入人")
    @ExcelProperty(value = "信息录入人", index = 11)
    @ColumnWidth(20)
    private String createName;

}
