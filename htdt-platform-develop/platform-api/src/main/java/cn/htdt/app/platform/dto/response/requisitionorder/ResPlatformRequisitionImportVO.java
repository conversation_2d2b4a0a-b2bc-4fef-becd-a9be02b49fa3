package cn.htdt.app.platform.dto.response.requisitionorder;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import lombok.Data;

/**
 * @description 要货单新增/编辑模板下载dto
 * 
 * <AUTHOR>
 * @date 2023/5/30 10:58
 **/
@Data
public class ResPlatformRequisitionImportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 0)
    @ColumnWidth(45)
    private String goodsName;
    /**
     * 商品编码
     */
    @ExcelProperty(value = "商品编码", index = 1)
    @ColumnWidth(30)
    private String goodsNo;

    /**
     * 计量单位
     */
    @ExcelProperty(value = "要货单位", index = 2)
    @ColumnWidth(15)
    private String calculationUnitName;

}
