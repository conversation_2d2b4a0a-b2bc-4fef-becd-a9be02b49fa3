package cn.htdt.app.platform.dto.response.content.course;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;

/**
 * <p>
 * 系列课程章节
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Data
@ApiModel(description = "系列课程章节返回实体类")
public class ResPlatformSeriesSectionDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(notes = "id")
    private BigInteger id;
    /**
     * 系列编号
     */
    @ApiModelProperty(notes = "系列编号")
    private String seriesNo;

    /**
     * 课程编号
     */
    @ApiModelProperty(notes = "课程编号")
    private String courseNo;

    /**
     * 课程内容
     */
    @ApiModelProperty(notes = "课程内容")
    private String content;

    /**
     * 课程标题
     */
    @ApiModelProperty(notes = "课程标题")
    private String title;

    /**
     * 排序值
     */
    @ApiModelProperty(notes = "排序值")
    private Integer sort;

    /**
     * 创建人
     */
    @ApiModelProperty(notes = "创建人")
    private String createNo;

    /**
     * 最后一次修改人
     */
    @ApiModelProperty(notes = "最后一次修改人")
    private String modifyNo;


}
