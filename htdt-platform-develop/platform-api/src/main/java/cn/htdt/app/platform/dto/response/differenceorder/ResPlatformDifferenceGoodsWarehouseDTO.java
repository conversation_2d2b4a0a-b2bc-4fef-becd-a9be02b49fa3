package cn.htdt.app.platform.dto.response.differenceorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 差异单商品仓库响应DTO
 *
 * <AUTHOR>
 * @since 2023-06-02
 */
@Data
public class ResPlatformDifferenceGoodsWarehouseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;
}
