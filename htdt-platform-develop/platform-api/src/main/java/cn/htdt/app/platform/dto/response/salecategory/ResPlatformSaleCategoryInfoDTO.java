package cn.htdt.app.platform.dto.response.salecategory;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 销售类目响应实体类
 *
 * <AUTHOR>
 * @date 2020-09-11
 */
@Data
public class ResPlatformSaleCategoryInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 自增主键ID
     */
    @ApiModelProperty(value = "自增主键ID")
    private Long id;

    /**
     * 类目id
     */
    @ApiModelProperty(value = "类目id")
    private String categoryNo;

    /**
     * 类目名称
     */
    @ApiModelProperty(value = "类目名称")
    private String categoryName;

    @ApiModelProperty(value = "商品类目id全路径")
    private String fullIdPath;

    @ApiModelProperty(value = "商品类目名称全路径")
    private String fullNamePath;

    /**
     * 层级(1001:一级 1002：二级 1003：三级 1004：四级)
     */
    @ApiModelProperty(value = "层级(1001:一级 1002：二级 1003：三级 1004：四级)")
    private String categoryLevel;

    /**
     * 父类目节点ID
     */
    @ApiModelProperty(value = "父类目节点ID")
    private String parentNo;

    /**
     * 数据来源类型(1001：MDM同步，1002：平台自建)
     */
    @ApiModelProperty(value = "数据来源类型(1001：MDM同步，1002：平台自建)")
    private String categorySource;

    /**
     * 图片URL
     */
    @ApiModelProperty(value = "图片URL")
    private String pictureUrl;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Integer sortValue;

    /**
     * 引用平台的类目id
     */
    @ApiModelProperty(value = "引用平台的类目id")
    private String mdmCategoryNo;

    /**
     * 是否可用:默认2，1：不可用，2：可用
     */
    @ApiModelProperty(value = "是否可用:默认2，1：不可用，2：可用")
    private Integer disableFlag;

    /**
     * 子目录
     */
    @ApiModelProperty(value = "子目录")
    private List<ResPlatformSaleCategoryInfoDTO> childCategory;

    /**
     * 选择标志（是否被选择） 1未选中 2已选中
     */
    @ApiModelProperty(value = "选择标志（是否被选择） 1未选中 2已选中")
    private String selectFlag;

    /**
     * 完整标志（是否完整） 1不完整 2完整
     */
    @ApiModelProperty(value = "选择标志（是否被选择） 1不完整 2完整")
    private String completeFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
