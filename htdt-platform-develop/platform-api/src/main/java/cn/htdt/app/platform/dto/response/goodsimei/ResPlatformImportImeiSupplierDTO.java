package cn.htdt.app.platform.dto.response.goodsimei;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

@ColumnWidth(value = 30)
@Data
public class ResPlatformImportImeiSupplierDTO implements Serializable {

    @ExcelProperty(value = "供应商名称", index = 0)
    private String supplierName;

    @ExcelProperty(value = "供应商编码", index = 1)
    private String supplierCode;

}
