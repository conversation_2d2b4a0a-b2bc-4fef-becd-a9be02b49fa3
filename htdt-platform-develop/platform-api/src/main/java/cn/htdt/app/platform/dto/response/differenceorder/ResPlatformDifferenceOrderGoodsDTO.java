package cn.htdt.app.platform.dto.response.differenceorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import cn.htdt.goodsprocess.dto.response.calculationunit.ResCalculationUnitGoodsDTO;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.request.ReqBaseDTO;
import cn.htdt.common.enums.goods.DifferenceOrderDeliveryStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 差异单商品行响应DTO
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ResPlatformDifferenceOrderGoodsDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 差异商品行编号
     */
    @ApiModelProperty(value = "差异商品行编号")
    private String differenceItemNo;

    /**
     * 差异单号
     */
    @ApiModelProperty(value = "差异单号")
    private String differenceNo;

    /**
     * 源商品编号（商家商品编号）
     */
    @ApiModelProperty(value = "商家商品编号")
    private String merchantGoodsNo;

    /**
     * 商家商品名称
     */
    @ApiModelProperty(value = "商家商品名称")
    private String merchantGoodsName;

    /**
     * 店铺商品编号
     */
    @ApiModelProperty(value = "店铺商品编号")
    private String goodsNo;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 条形码
     */
    @ApiModelProperty(value = "条形码")
    private String barcode;
    /**
     * 助记码
     */
    @ApiModelProperty(value = "助记码")
    private String goodsHelpCode;

    /**
     * 商家商品单价
     */
    @ApiModelProperty(value = "商家商品单价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal itemPrice;

    /**
     * 要货数量
     */
    @ApiModelProperty(value = "要货数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal requisitionNum;

    /**
     * 要货单位
     */
    @ApiModelProperty(value = "要货单位")
    private String requisitionUnit;

    /**
     * 计量单位ID
     */
    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    /**
     * 辅计量单位ID
     */
    @ApiModelProperty(value = "辅计量单位ID")
    private String assistCalculationUnitNo;

    @ApiModelProperty(value = "辅计量单位名称")
    private String assistCalculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    @ApiModelProperty(value = "主计量单位对应关系数值")
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    @ApiModelProperty(value = "辅计量单位对应关系数值")
    private BigDecimal assistUnitNum;

    /**
     * 是否使用辅计量单位采购:1-否;2-是
     */
    @ApiModelProperty(value = "是否使用辅计量单位采购: 1-否; 2-是")
    private Integer standardFlag;

    /**
     * 差异数量
     */
    @ApiModelProperty(value = "差异数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal differenceCount;

    /**
     * 差异金额
     */
    @ApiModelProperty(value = "差异金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal differenceAmount;

    /**
     * 未入库数量
     */
    @ApiModelProperty(value = "未入库数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal noStorageCount;

    /**
     * 已入库数量
     */
    @ApiModelProperty(value = "已入库数量")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal inStorageCount;

    /**
     * 是否入仓 1-否 2-是
     */
    @ApiModelProperty(value = "是否入仓: 1-否; 2-是")
    private Integer warehouseFlag;

    /**
     * 仓库编号
     */
    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;

    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 商品行入库状态 1001-待入库 1002-部分入库 1003-完成
     */
    @ApiModelProperty(value = "商品行入库状态: 1001-待入库; 1002-部分入库; 1003-完成")
    @Converter(enumClass = DifferenceOrderDeliveryStatusEnum.class, fieldName = "statusValue")
    private String itemStorageStatus;

    /**
     * 商品行入库状态 1001-待入库 1002-部分入库 1003-完成
     */
    @ApiModelProperty(value = "商品行入库状态: 1001-待入库; 1002-部分入库; 1003-完成")
    private String itemStorageStatusValue;

    /**
     * 归属平台编号
     */
    @ApiModelProperty(value = "归属平台编号")
    private String companyNo;

    /**
     * 归属平台名称
     */
    @ApiModelProperty(value = "归属平台名称")
    private String companyName;

    /**
     * 归属分部编号
     */
    @ApiModelProperty(value = "归属分部编号")
    private String branchNo;

    /**
     * 归属分部名称
     */
    @ApiModelProperty(value = "归属分部名称")
    private String branchName;

    /**
     * 归属平台类型(1001.运营 1002.商家 1003.店铺)
     */
    @ApiModelProperty(value = "归属平台类型(1001.运营 1002.商家 1003.店铺)")
    private String platformType;

    @ApiModelProperty("商家编号")
    private String merchantNo;

    @ApiModelProperty("商家名称")
    private String merchantName;

    @ApiModelProperty("店铺编号")
    private String storeNo;

    @ApiModelProperty("店铺名称")
    private String storeName;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "商家商品关联仓库信息")
    private List<ResPlatformDifferenceGoodsWarehouseDTO> merchantGoodsWarehouseList;

    /**
     * 2023-8-24蛋品-genghao-要货差异单-获取差异单商品列表
     */
    @ApiModelProperty(value = "是否启动效期管理标识(1:否 2:是)")
    private Integer validityPeriodManageFlag;

    @ApiModelProperty(value = "保质期,正整型")
    private Integer qualityGuaranteePeriod;

    @ApiModelProperty(value = "保质期单位")
    private String shelfLifeUnit;

    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     * <p>
     * 2023-08-25蛋品-genghao-采购管理-获取采购商品列表
     */
    @ApiModelProperty(value = "多单位商品类型")
    private String multiUnitType;

    /**
     * 多单位主品编号
     * <p>
     * 2023-08-25蛋品-genghao-采购管理-获取采购商品列表
     */
    @ApiModelProperty(value = "多单位主品编号")
    private String multiUnitGoodsNo;

    /**
     * 多单位商品单位集合
     * <p>
     * 2023-08-25蛋品-genghao-采购管理-获取采购商品列表
     */
    @ApiModelProperty(value = "多单位商品单位集合")
    private List<ResCalculationUnitGoodsDTO> calculationUnitGoodsDTOS;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
