package cn.htdt.app.platform.dto.response.officialwebsiteinformationcollection;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 官网信息收集管理表
 * </p>
 *
 * <AUTHOR>
 * @date 2022/04/24
 */
@Data
public class ResPlatformOfficialWebsiteManagementResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "信息处理编号")
    private String informationManagementNo;

    @ApiModelProperty(value = "信息收集编号")
    private String informationCollectionNo;

    @ApiModelProperty(value = "处理状态 1001:待处理 1002:已处理跟进中 1003：已处理，直接关闭 1004：已签约 1005：无合作意向")
    private String disposeStatus;

    @ApiModelProperty(value = "处理状态名称 1001:待处理 1002:已处理跟进中 1003：已处理，直接关闭 1004：已签约 1005：无合作意向")
    private String disposeStatusName;

    @ApiModelProperty(value = "处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime disposeTime;

    @ApiModelProperty(value = "处理人编码")
    private String disposeNo;

    @ApiModelProperty(value = "处理人姓名")
    private String disposeName;

    @ApiModelProperty(value = "处理备注")
    private String disposeExplain;

    @ApiModelProperty(value = "跟进时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime followTime;

    @ApiModelProperty(value = "跟进备注")
    private String followExplain;

}
