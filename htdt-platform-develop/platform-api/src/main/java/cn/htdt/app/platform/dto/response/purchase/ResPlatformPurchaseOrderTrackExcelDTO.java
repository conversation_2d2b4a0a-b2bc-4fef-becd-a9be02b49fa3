package cn.htdt.app.platform.dto.response.purchase;

import cn.htdt.app.platform.converter.PurchaseOrderStatusConverter;
import cn.htdt.app.platform.converter.PurchaseSourceTypeConverter;
import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *  采购明细跟踪查询Vo
 *
 * <AUTHOR>
 * @date 2020年11月09日
 */
@Data
public class ResPlatformPurchaseOrderTrackExcelDTO extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *商品编码
     */
    @ExcelProperty(value = "商品编码",index = 0)
    @ColumnWidth(15)
    private String goodsNo;
    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 1)
    @ColumnWidth(15)
    private String goodsName;
    /**
     *采购日期
     */
    @ExcelProperty(value = "采购日期",index = 2,converter = LocalDateTimeConverter.class)
    @ColumnWidth(15)
    private LocalDateTime purchaseDate;
    /**
     * 采购单编码
     */
    @ExcelProperty(value = "采购单号",index = 3)
    @ColumnWidth(15)
    private String purchaseCode;
    /**
     * 采购单状态：订单状态21:待签收 22:已签收 23:待入库 24:部分入库 25:已结案 26:完成
     */
    @ExcelProperty(value = "单据状态",index = 4, converter = PurchaseOrderStatusConverter.class)
    @ColumnWidth(15)
    private String orderStatus;
    /**
     *供应商编码
     */
    @ExcelProperty(value = "供应商编码",index = 5)
    @ColumnWidth(15)
    private String supplierCode;
    /**
     *供应商名称
     */
    @ExcelProperty(value = "供应商名称",index = 6)
    @ColumnWidth(15)
    private String supplierName;


    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "主计量单位",index = 7)
    @ColumnWidth(15)
    private String calculationUnitName;
    /**
     * 转换率(辅转主)
     */
    @ExcelProperty(value = "单位转换率",index = 8)
    @ColumnWidth(15)
    private BigDecimal conversionRate;
    /**
     * 采购单位
     */
    @ExcelProperty(value = "采购单位",index = 9)
    @ColumnWidth(15)
    private String purchaseUnit;

    /**
     * 采购数量
     */
    @ExcelProperty(value = "单位转换率",index = 10)
    @ColumnWidth(15)
    private BigDecimal purchaseNum;
    /**
     *交易金额
     */
    @ExcelProperty(value = "交易金额",index = 11)
    @ColumnWidth(15)
    private BigDecimal purchasePrice;
    /**
     * 已入库数量
     */
    @ExcelProperty(value = "入库数量",index = 12)
    @ColumnWidth(15)
    private BigDecimal storageCount;
    /**
     * 入库金额
     */
    @ExcelProperty(value = "入库金额",index = 13)
    @ColumnWidth(15)
    private BigDecimal putPurchasePrice;


    /**
     * 退货数量
     */
    @ExcelProperty(value = "退货数量",index = 14)
    @ColumnWidth(15)
    private BigDecimal returnRequestCount;
    /**
     * 退货金额
     */
    @ExcelProperty(value = "退货金额",index = 15)
    @ColumnWidth(15)
    private BigDecimal returnPurchasePrice;
    /**
     * 退货金额
     */
    @ExcelProperty(value = "退货金额",index = 16)
    @ColumnWidth(15)
    private BigDecimal outStorageCount;
    /**
     * 出库金额
     */
    @ExcelProperty(value = "出库金额",index = 17)
    @ColumnWidth(15)
    private BigDecimal outPurchasePrice;
    /**
     * 数据来源
     * */
    @ExcelProperty(value = "数据来源",index = 18, converter = PurchaseSourceTypeConverter.class)
    @ColumnWidth(15)
    private String sourceType;
    /**
     *商品类目名称
     */
    @ExcelProperty(value = "商品类目",index = 19)
    @ColumnWidth(15)
    private String categoryName;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
