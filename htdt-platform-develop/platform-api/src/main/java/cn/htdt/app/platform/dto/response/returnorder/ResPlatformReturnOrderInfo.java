package cn.htdt.app.platform.dto.response.returnorder;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.returnorder.ReturnSourceEnum;
import cn.htdt.common.enums.returnorder.ReturnStatusEnum;
import cn.htdt.common.enums.returnorder.ReturnTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退货信息
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformReturnOrderInfo implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 834509179962044757L;

    @ApiModelProperty(value = "售后来源")
    @Converter(enumClass = ReturnSourceEnum.class, fieldName = "sourceName")
    private String source;

    @ApiModelProperty(value = "售后来源")
    private String sourceName;

    @ApiModelProperty(value = "售后单编号")
    private String soReturnNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "售后类型")
    @Converter(enumClass = ReturnTypeEnum.class, fieldName = "returnTypeName")
    private String returnType;

    @ApiModelProperty(value = "售后类型Name")
    private String returnTypeName;

    @ApiModelProperty(value = "售后单状态")
    @Converter(enumClass = ReturnStatusEnum.class, fieldName = "returnStatusName")
    private String returnStatus;

    @ApiModelProperty(value = "售后单状态Name")
    private String returnStatusName;

    @ApiModelProperty(value = "申请退款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal applyReturnAmount;

    @ApiModelProperty(value = "实际退款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal actualReturnAmount;

    @ApiModelProperty(value = "创建日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime applyTime;

}
