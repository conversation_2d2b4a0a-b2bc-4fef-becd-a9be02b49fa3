package cn.htdt.app.platform.dto.response.payproject;

import cn.htdt.app.platform.converter.DealsStatusConverter;
import cn.htdt.app.platform.converter.DealsTypeConverter;
import cn.htdt.app.platform.converter.ProductLabellConverter;
import cn.htdt.app.platform.converter.WhetherConverter;
import cn.htdt.app.platform.converter.base.LocalDateConverter;
import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import cn.htdt.app.platform.converter.AgreementConfirmFlagConverter;
import cn.htdt.app.platform.converter.base.ReverseWhetherConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Description 套餐订单导出表
 **/

@Data
public class ResPlatformPayProjectDealsExcelDTO implements Serializable {

    @ExcelProperty(value = "订单编号", index = 0)
    @ColumnWidth(20)
    private String dealsNo;

    @ExcelProperty(value = "商家编号 ", index = 1)
    @ColumnWidth(20)
    private String merberCode;

    @ExcelProperty(value = "商家名称", index = 2)
    @ColumnWidth(20)
    private String merchantName;

    @ExcelProperty(value = "归属平台公司名称", index = 3)
    @ColumnWidth(20)
    private String companyName;

    @ExcelProperty(value = "归属分部名称", index = 4)
    @ColumnWidth(20)
    private String divisionName;

    @ExcelProperty(value = "订单标签", index = 5, converter = ProductLabellConverter.class)
    @ColumnWidth(20)
    private Integer productLabell;

    @ExcelProperty(value = "套餐名称", index = 6)
    @ColumnWidth(20)
    private String packageName;

    @ExcelProperty(value = "产品名称", index = 7)
    @ColumnWidth(20)
    private String projectName;

    @ExcelProperty(value = "是否试用", index = 8, converter = ReverseWhetherConverter.class)
    @ColumnWidth(20)
    private Integer packageRetail;

    @ExcelProperty(value = "应付金额", index = 9)
    @ColumnWidth(20)
    private BigDecimal originalPrice;

    @ExcelProperty(value = "实付金额", index = 10)
    @ColumnWidth(20)
    private BigDecimal payAmount;

    @ExcelProperty(value = "订单类型", index = 11, converter = DealsTypeConverter.class)
    @ColumnWidth(20)
    private Integer dealsType;

    @ExcelProperty(value = "订单状态", index = 12, converter = DealsStatusConverter.class)
    @ColumnWidth(20)
    private Integer dealsStatus;

    @ExcelProperty(value = "是否开发票", index = 13, converter = WhetherConverter.class)
    @ColumnWidth(20)
    private Integer needInvoiceFlag;

    @ExcelProperty(value = "付款时间", index = 14, converter = LocalDateTimeConverter.class)
    @ColumnWidth(30)
    private LocalDateTime payTime;

    @ExcelProperty(value = "创建时间", index = 15, converter = LocalDateTimeConverter.class)
    @ColumnWidth(30)
    private LocalDateTime createTime;

    @ExcelProperty(value = "套餐开始时间", index = 16, converter = LocalDateConverter.class)
    @ColumnWidth(20)
    private LocalDate projectStart;

    @ExcelProperty(value = "套餐结束时间", index = 17, converter = LocalDateConverter.class)
    @ColumnWidth(20)
    private LocalDate projectEnd;
    @ExcelProperty(value = "产品状态", index = 18)
    @ColumnWidth(20)
    private String projectStatusStr;

    @ExcelProperty(value = "推荐人名称", index = 19)
    @ColumnWidth(20)
    private String recommendName;

    @ExcelProperty(value = "是否确认", index = 20,converter = AgreementConfirmFlagConverter.class)
    @ColumnWidth(20)
    private Integer agreementConfirmFlag;

    @ExcelProperty(value = "确认时间", index = 21,converter = LocalDateTimeConverter.class)
    @ColumnWidth(25)
    private LocalDateTime confirmTime;

    @ExcelProperty(value = "确认人", index = 22)
    @ColumnWidth(20)
    private String confirmOperatorid;

    @ExcelProperty(value = "激活码", index = 23)
    @ColumnWidth(20)
    private String activationCode;

}
