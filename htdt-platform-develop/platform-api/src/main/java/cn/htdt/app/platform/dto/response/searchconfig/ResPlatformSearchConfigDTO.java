package cn.htdt.app.platform.dto.response.searchconfig;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 热门搜索配置 响应实体类
 *
 * <AUTHOR>
 * @date 2021/10/21
 **/
@Data
public class ResPlatformSearchConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "搜索关键词编号")
    private String searchKeywordNo;

    @ApiModelProperty(notes = "关键词")
    private String name;

    @ApiModelProperty(notes = "是否启用 1不启用 2启用")
    private Integer enable;

    @ApiModelProperty(notes = "类型 1 帮助中心 2老板学堂,逗号分隔")
    private String type;

    @ApiModelProperty(notes = "点击数")
    private Integer likeCount;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime createTime;
}
