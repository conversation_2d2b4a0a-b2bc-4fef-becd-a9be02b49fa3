package cn.htdt.app.platform.dto.response.purchasereturnorder;

import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 *
 * 功能描述: 采购退货单导出excel响应dto
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformPurchaseReturnOrderExcelDTO  implements Serializable {
    /**
     *退货单编码
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "退货单编码",index = 0)
    private String returnCode;
    /**
     *采购单编码
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "采购单编码",index = 1)
    private String purchaseCode;
    /**
     *供应商名称
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "供应商名称",index = 2)
    private String supplierName;
    /**
     *供应商编码
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "供应商编码",index = 3)
    private String supplierCode;
    /**
     *商品名称
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "商品名称",index = 4)
    private String goodName;
    /**
     *总退货数量
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "总退货数量",index = 5)
    private BigDecimal totalReturnNum;
    /**
     *交易金额
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "交易金额",index =6)
    private BigDecimal totalPurchasePrice;
    /**
     *退款金额
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "退款金额",index = 7)
    private BigDecimal totalReturnPrice;
    /**
     *收货联系人
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "收货联系人",index = 8)
    private String receiveContactName;
    /**
     *收货联系人手机号
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "收货联系人手机号",index = 9)
    private String receiveContactMobile;
    /**
     *发货联系人
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "发货联系人",index = 10)
    private String sendContactName;
    /**
     *发货联系人手机号
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "发货联系人手机号",index = 11)
    private String sendContactMobile;
    /**
     *创建人
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "创建人",index = 12)
    private String createName;

    /**
     *创建时间
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "创建时间",index = 13,converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     *退货状态形式VALUE 21:待签收 22:已签收 23:待入库 24:部分入库 25:已完成 26:已作废
     */
    @ExcelProperty(value = "退货状态",index = 14)
    private String returnStatusValue;

}
