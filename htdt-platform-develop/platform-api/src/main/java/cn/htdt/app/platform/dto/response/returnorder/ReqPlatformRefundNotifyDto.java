package cn.htdt.app.platform.dto.response.returnorder;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 退款异步回调入参
 *
 * <AUTHOR>
 */
@Data
public class ReqPlatformRefundNotifyDto implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 商户订单号
     */
    private String merchOrderNo;

    /**
     * 原商户订单号
     */
    private String origMerchOrderNo;

    /**
     * 支付交易号
     */
    private String tradeNo;

    /**
     * 交易金额
     */
    private String amount;

    /**
     * 交易类型
     */
    private String tradeType;

    /**
     * 交易状态
     */
    private String tradeStatus;

    /**
     * 相应状态
     */
    private String resultCode;

    /**
     * 响应消息
     */
    private String resultMessage;

    /**
     * 会话参数
     */
    private String context;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}