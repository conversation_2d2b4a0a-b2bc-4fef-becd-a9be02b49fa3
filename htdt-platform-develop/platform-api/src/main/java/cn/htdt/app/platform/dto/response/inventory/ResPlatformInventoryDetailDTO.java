package cn.htdt.app.platform.dto.response.inventory;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点详细 返回DTO
 **/
@Data
public class ResPlatformInventoryDetailDTO extends ReqComPageDTO implements Serializable {

    @ApiModelProperty(value = "盘点单号", name = "inventoryCode")
    private String inventoryCode;

    @ApiModelProperty(value = "商品编号", name = "goodsNo")
    private String goodsNo;

    @ApiModelProperty(value = "总库存数量", name = "stockNum")
    private BigDecimal stockNum;

    @ApiModelProperty(value = "盘点总库存数量", name = "inventoryStockNum")
    private BigDecimal inventoryStockNum;

    @ApiModelProperty(value = "仓库编号", name = "warehouseNo")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称", name = "warehouseName")
    private String warehouseName;

    @ApiModelProperty(value = "是否可用:默认2，1：不可用 2：可用", name = "disableFlag")
    private Integer disableFlag;

    @ApiModelProperty(value = "创建人no", name = "createNo")
    private String createNo;

    @ApiModelProperty(value = "创建人名称", name = "createName")
    private String createName;

    @ApiModelProperty(value = "修改人no", name = "modifyNo")
    private String modifyNo;

    @ApiModelProperty(value = "修改人名称", name = "modifyName")
    private String modifyName;

    @ApiModelProperty(value = "商家编号", name = "merchantNo")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称", name = "merchantName")
    private String merchantName;

    @ApiModelProperty(value = "门店编号", name = "storeNo")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称", name = "storeName")
    private String storeName;

    @ApiModelProperty(value = "平台编号", name = "companyNo")
    private String companyNo;

    @ApiModelProperty(value = "平台名称", name = "companyName")
    private String companyName;

    @ApiModelProperty(value = "分部编号", name = "branchNo")
    private String branchNo;

    @ApiModelProperty(value = "分部名称", name = "branchName")
    private String branchName;

    @ApiModelProperty(value = "计量单位符号", name = "calculationUnitSymbol")
    private String calculationUnitSymbol;

    @ApiModelProperty(value = "计量单位名称", name = "calculationUnitName")
    private String calculationUnitName;

    @ApiModelProperty(value = "是否有辅计量单位:1-否;2-是", name = "standardFlag")
    private Integer standardFlag;

    @ApiModelProperty(value = "辅计量单位符号", name = "assistCalculationUnitSymbol")
    private String assistCalculationUnitSymbol;

    @ApiModelProperty(value = "辅计量单位名称", name = "assistCalculationUnitName")
    private String assistCalculationUnitName;

    @ApiModelProperty(value = "转换率", name = "conversionRate")
    private BigDecimal conversionRate;

    @ApiModelProperty(value = "总库存数量", name = "realStockNum")
    private BigDecimal realStockNum;

    @ApiModelProperty(value = "冻结库存数量", name = "freezeStockNum")
    private BigDecimal freezeStockNum;

    @ApiModelProperty(value = "是否入仓(1:否 2:是)", name = "warehouseFlag")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "条形码", name = "barcode")
    private String barcode;

    @ApiModelProperty(value = "助记码", name = "goodsHelpCode")
    private String goodsHelpCode;

    @ApiModelProperty(value = "商品名称", name = "goodsName")
    private String goodsName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
