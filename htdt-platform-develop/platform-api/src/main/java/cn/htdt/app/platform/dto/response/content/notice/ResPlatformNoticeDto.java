package cn.htdt.app.platform.dto.response.content.notice;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * <p>
 * 公告表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Data
@ApiModel(description = "公告返回实体类")
public class ResPlatformNoticeDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(notes = "id")
    private BigInteger id;

    /**
     * 编号
     */
    @ApiModelProperty(notes = "编号")
    private String noticeNo;

    /**
     * 标题
     */
    @ApiModelProperty(notes = "标题")
    private String title;

    /**
     * 正文内容
     */
    @ApiModelProperty(notes = "正文内容")
    private String content;

    /**
     * 高亮状态 1 不高亮 2高亮
     */
    @ApiModelProperty(notes = "高亮状态 1 不高亮 2高亮")
    private Integer highlightStatus;

    /**
     * 发布状态 1 未发布 2发布
     */
    @ApiModelProperty(notes = "发布状态 1 未发布 2发布")
    private Integer releaseStatus;

    /**
     * 发布状态描述
     */
    @ApiModelProperty(notes = "发布状态描述")
    private String releaseStatusText;

    /**
     * 发布位置
     */
    @ApiModelProperty(notes = "发布位置")
    private String releasePosition;

    /**
     * 发布时间
     */
    @ApiModelProperty(notes = "发布时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime releaseTime;


    /**
     * 更新时间
     */
    @ApiModelProperty(notes = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime modifyTime;

    /**
     * 修改人编号
     */
    @ApiModelProperty("修改人编号")
    private String modifyNo;

    /**
     * 修改人名称
     */
    @ApiModelProperty("修改人名称")
    private String modifyName;

    /**
     * 生效时间-开始时间
     */
    @ApiModelProperty(notes = "生效时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectTimeStart;

    /**
     * 生效时间-结束时间
     */
    @ApiModelProperty(notes = "生效时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime effectTimeEnd;

}
