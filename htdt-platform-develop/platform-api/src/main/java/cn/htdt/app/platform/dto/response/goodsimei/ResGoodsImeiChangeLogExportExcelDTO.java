package cn.htdt.app.platform.dto.response.goodsimei;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 商品串码流水导出
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-23
 */
@Data
public class ResGoodsImeiChangeLogExportExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ExcelProperty(value = "店铺名称", index = 0)
    @ColumnWidth(20)
    private String storeName;

    @ExcelProperty(value = "自定义店铺编码", index = 1)
    @ColumnWidth(20)
    private String customStoreNo;

    @ExcelProperty(value = "交易类型", index = 2)
    @ColumnWidth(20)
    private String inventoryTypeStr; //TODO

    @ExcelProperty(value = "串码", index = 3)
    @ColumnWidth(20)
    private String imei;

    @ExcelProperty(value = "商品名称", index = 4)
    @ColumnWidth(20)
    private String goodsName;

    @ExcelProperty(value = "自定义商品编码", index = 5)
    @ColumnWidth(20)
    private String customGoodsNo;

    @ExcelProperty(value = "供应商", index = 6)
    @ColumnWidth(20)
    private String supplierName; //TODO

    @ExcelProperty(value = "交易人", index = 7)
    @ColumnWidth(20)
    private String createName;

    @ExcelProperty(value = "交易时间", index = 8)
    @ColumnWidth(20)
    private String createTimeStr; //TODO

    @ExcelProperty(value = "备注", index = 9)
    @ColumnWidth(20)
    private String remark;

    @ExcelProperty(value = "关联单号", index = 10)
    @ColumnWidth(20)
    private String customBatchNumber;

}
