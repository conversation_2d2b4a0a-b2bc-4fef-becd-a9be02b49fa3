package cn.htdt.app.platform.dto.response.storecategory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 店铺类目 响应DTO
 *
 * <AUTHOR>
 * @date 2020/10/22
 **/
@Data
public class ResPlatformStoreCategoryDTO implements Serializable {
    private static final long serialVersionUID = -4800024066309403545L;

    /**
     * 店铺类目编号
     */
    @ApiModelProperty(value = "店铺类目编号")
    private String storeCategoryNo;

    /**
     * 店铺类目名称
     */
    @ApiModelProperty(value = "店铺类目名称")
    private String storeCategoryName;

    /**
     * 层级(1001:一级，1002：二级)
     */
    @ApiModelProperty(value = "层级(1001:一级，1002：二级)")
    private String categoryLevel;

    /**
     * 排序值
     */
    @ApiModelProperty(value = "排序值")
    private Integer sortValue;

    /**
     * 父类目节点ID
     */
    @ApiModelProperty(value = "父类目节点ID")
    private String parentNo;

    /**
     * 图片URL
     */
    @ApiModelProperty(value = "图片URL")
    private String pictureUrl;

    /**
     * 类目关联的商品数量
     */
    @ApiModelProperty(value = "类目关联的商品数量")
    private Integer goodsNum;

    /**
     * 子类目
     */
    @ApiModelProperty(value = "子类目")
    private List<ResPlatformStoreCategoryDTO> childCategory;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
