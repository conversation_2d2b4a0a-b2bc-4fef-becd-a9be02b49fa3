package cn.htdt.app.platform.dto.response.payproject;

import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 套餐数据回显
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformPayProjectConfirmOrderDTO implements Serializable {

    private static final long serialVersionUID = -8354557156924339723L;

    @ApiModelProperty("订单编号")
    private String dealsNo;

    @ApiModelProperty("商户类型")
    private String traderType;

    @ApiModelProperty("发票抬头")
    private String invoiceNotify;

    @ApiModelProperty("纳税人识别号")
    private String taxManid;

    @ApiModelProperty("开户行名称")
    private String bankName;

    @ApiModelProperty("开户行账号")
    private String bankAccount;

    @ApiModelProperty("开户行账号加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "bankAccount")
    private String dsBankAccount;

    @ApiModelProperty("发票地址")
    private String invoiceAddress;

    @ApiModelProperty("发票地址加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "invoiceAddress")
    private String dsInvoiceAddress;

    @ApiModelProperty("联系电话")
    private String contactPhone;

    @ApiModelProperty("联系电话加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "contactPhone")
    private String dsContactPhone;

    @ApiModelProperty("付款人姓名")
    private String receiveName;

    @ApiModelProperty("付款人手机号")
    private String receivePhone;

    @ApiModelProperty("付款人手机号加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "receivePhone")
    private String dsReceivePhone;

    @ApiModelProperty("收货门店名称")
    private String receiveShopname;

    @ApiModelProperty("收货地址")
    private String receiveAddress;

    @ApiModelProperty("收货地址加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "receiveAddress")
    private String dsReceiveAddress;

    @ApiModelProperty("推荐会员店名称")
    private String recommendNo;

    @ApiModelProperty("发票类型")
    private String invoiceType;

    @ApiModelProperty("备注")
    private String bz;

    @ApiModelProperty("铁军服务")
    private String armynumber;

    @ApiModelProperty("是否已经删除，默认1未删除，其余已删除")
    private Integer disableFlag;

    @ApiModelProperty("商家编号")
    private String merchantNo;

    @ApiModelProperty("商家名称")
    private String merchantName;

    @ApiModelProperty("发票地址(省)编码")
    private String invoiceAddressProvinceCode;

    @ApiModelProperty("发票地址(市)编码")
    private String invoiceAddressCityCode;

    @ApiModelProperty("发票地址(区)编码")
    private String invoiceAddressAreaCode;

    @ApiModelProperty("发票地址(街道)编码")
    private String invoiceAddressStreetCode;

    @ApiModelProperty("发票地址(省)")
    private String invoiceAddressProvince;

    @ApiModelProperty("发票地址(市)")
    private String invoiceAddressCity;

    @ApiModelProperty("发票地址(区)")
    private String invoiceAddressArea;

    @ApiModelProperty("发票地址(街道)")
    private String invoiceAddressStreet;

    @ApiModelProperty("发票地址(街道后)")
    private String invoiceAddressSuffix;

    @ApiModelProperty("发票地址(街道后)加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "invoiceAddressSuffix")
    private String dsInvoiceAddressSuffix;

    @ApiModelProperty("合同模板返回值")
    List<ResPlatformContractUrlListDTO> resPayProjectContractDTOS = Collections.EMPTY_LIST;

    @ApiModelProperty("身份 1运营 2商家 4店铺 8单店")
    private Integer identity;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
