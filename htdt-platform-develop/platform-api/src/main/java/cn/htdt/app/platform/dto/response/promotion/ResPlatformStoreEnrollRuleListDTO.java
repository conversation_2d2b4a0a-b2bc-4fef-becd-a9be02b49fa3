package cn.htdt.app.platform.dto.response.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 店铺报名活动列表
 *
 * <AUTHOR>
 * @date 2023-02-17
 */
@Data
public class ResPlatformStoreEnrollRuleListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "活动店铺类型(1001:需店铺报名参与 1002:指定店铺直接参与)")
    private String storeType;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    @ApiModelProperty(value = "报名状态,1000:待报名;1002:平台受理中;1003:报名成功;1004平台已驳回")
    private String enrollStatus;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "更新人")
    private String modifyName;

    @ApiModelProperty(value = "店铺报名范围（1001:全部店铺 1002：指定区域 1003：导入店铺）")
    private String applyType;

    @ApiModelProperty(value = "报名区域范围")
    private String applyArea;

    @ApiModelProperty(value = "报名区域范围")
    private String applyAreaName;

    @ApiModelProperty(value = "报名说明")
    private String enrollExplain;

    @ApiModelProperty(value = "APP图片类型(1:默认图片 2:自定义图片)")
    private Integer appPictureType;

    @ApiModelProperty(value = "APP图片url")
    private String appPictureUrl;

    @ApiModelProperty(value = "图片类型(1:默认图片 2:自定义图片)")
    private Integer promotionPictureType;

    @ApiModelProperty(value = "图片url")
    private String promotionPictureUrl;

    @ApiModelProperty(value = "活动状态 0:草稿;1:活动未开始 2:活动进行中，3:活动已结束")
    private Integer promotionStatus;

    @ApiModelProperty(value = "报名时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime enrollTime;

    @ApiModelProperty(value = "历史受理记录")
    private List<ResPlatformEnrollDrawRecordDTO> enrollDrawRecordList;

}
