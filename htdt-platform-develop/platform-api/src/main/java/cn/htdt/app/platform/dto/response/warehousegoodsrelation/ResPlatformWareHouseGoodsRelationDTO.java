package cn.htdt.app.platform.dto.response.warehousegoodsrelation;

import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * 功能描述: 仓库库存响应dto
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformWareHouseGoodsRelationDTO implements Serializable {
    private static final long serialVersionUID = 2972813410807727946L;
    /**
     * 仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 仓库编号
     */
    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;


    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    /**
     * 商品串码数量
     */
    @ApiModelProperty(value = "商品串码数量")
    private Integer imeiCount;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;
    /**
     * 库存数量
     */
    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockNum;


    /**
     * 库存数量 (辅1)
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "库存数量(辅1)")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal stockOneNum;

    /**
     * 库存数量 (辅2)
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "库存数量(辅2)")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal stockTwoNum;

    /**
     * 转换率(辅转主)
     */
    @ApiModelProperty(value = "转换率")
    private BigDecimal conversionRate;

    /**
     * 计量单位名称
     */
    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    /**
     * 辅计量单位名称
     */
    @ApiModelProperty(value = "辅计量单位名称")
    private String assistCalculationUnitName;

    //20230809蛋品-wxb-库存管理
    @ApiModelProperty(value = "辅计量单位名称2")
    private String assistCalculationUnitNameTwo;


    /**
     * 转换率2(辅转主) 多单位时-辅计量单位2
     * //20230809蛋品-wxb-库存管理
     */
    private BigDecimal conversionRateTwo;

    /**
     * 删除状态码形式value
     */
    @ApiModelProperty(value = "删除状态码")
    private String deleteFlagValue;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "创建人")
    private String createName;

    @ApiModelProperty(value = "商品删除标识")
    private Integer goodsDeleteFlag;

    @ApiModelProperty(value = "是否入仓(1:否 2:是)")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "是否虚拟仓(1:否 2：是)")
    private Integer virtualWarehouseFlag;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "第一属性值编码")
    private String firstAttributeValueName;

    @ApiModelProperty(value = "第二属性值编码")
    private String secondAttributeValueName;

    @ApiModelProperty(value = "第三属性值编码")
    private String thirdAttributeValueName;

    @ApiModelProperty(value = "第一、二、三属性值编码，格式：first-second-third")
    private String attributeValueName;

    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "多单位商品类型 1001:主单位商品 1002:子")
    private String multiUnitType;

    /**
     * 多单位主品编号
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "多单位主品编号")
    private String multiUnitGoodsNo;

    public String getAttributeValueName() {
        attributeValueName = "";
        if(StringUtils.isNotEmpty(getFirstAttributeValueName())) {
            attributeValueName+= "-" + getFirstAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getSecondAttributeValueName())) {
            attributeValueName+= "-" + getSecondAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getThirdAttributeValueName())) {
            attributeValueName+= "-" + getThirdAttributeValueName();
        }
        if(StringUtils.isNotEmpty(attributeValueName)) {
            attributeValueName = attributeValueName.substring(1);
        }
        return attributeValueName;
    }

}
