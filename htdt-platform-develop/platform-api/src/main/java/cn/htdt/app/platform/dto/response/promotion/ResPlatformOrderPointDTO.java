package cn.htdt.app.platform.dto.response.promotion;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.constants.NumConstant;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单赠送的积分(新增抵扣积分信息)
 *
 * <AUTHOR>
 * @date 2020年9月14日
 */
@Data
public class ResPlatformOrderPointDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("订单赠送的积分")
    private Integer orderGivePoints;

    //账户剩余积分
    @ApiModelProperty(value = "会员账号积分")
    private Integer accountRemainPoints;

    @ApiModelProperty(value = "可用积分")
    private Integer canUsePoint;

    @ApiModelProperty(value = "可抵扣金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal canDeductionAmount;

    @ApiModelProperty(value = "抵扣还差金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal canDeductionLessAmount;

    /**
     * 是否可用:默认1，1：启用，2：禁用
     * 1、老板开启积分支付（抵扣）功能
     * 2、积分抵扣使用渠道包含
     */
    @ApiModelProperty(value = "是否可用:1：启用，2：禁用")
    private Integer disableFlag = NumConstant.TWO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
