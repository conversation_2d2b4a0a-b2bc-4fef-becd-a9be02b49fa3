package cn.htdt.app.platform.dto.response.promotion;

import cn.htdt.app.platform.dto.response.ResPlatformPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 限时购活动场次商品列表分页响应结果
 *
 * <AUTHOR>
 * @date 2021-11-04
 */
@Data
public class ResPlatformLimitTimeGoodsPageDTO implements Serializable {

    private static final long serialVersionUID = -1268735731069170029L;

    @ApiModelProperty(value = "活动场次编号")
    private String periodNo;

    @ApiModelProperty(value = "活动场次下的分页数据")
    private ResPlatformPageDTO<ResPlatformGoodsPromotionGoodsDTO> goodsPage;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
