package cn.htdt.app.platform.dto.response.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 商品活动规则响应的实体类
 *
 * <AUTHOR>
 * @date 2021-06-29
 */
@Data
public class ResPlatformGoodsPromotionRuleDTO implements Serializable {

    private static final long serialVersionUID = -47631012464347405L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动开始时间字符串，格式 yyyy-MM-dd")
    private String effectiveTimeStr;

    @ApiModelProperty(value = "活动结束时间字符串，格式 yyyy-MM-dd")
    private String invalidTimeStr;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "活动时间段信息")
    private List<ResPlatformGoodsPromotionPeriodDTO> goodsPromotionPeriodDTOList;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String userScope;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    @ApiModelProperty(value = "是否包邮(1:否 2:是)")
    private Integer freeDeliveryFlag;

    @ApiModelProperty(value = "配送方式是否以商品自身配置为主(1:否 2:是)")
    private Integer deliveryFlag;

    @ApiModelProperty(value = "配送方式(1000:自提 1100：配送;1000,1100:自提+配送)")
    private String deliveryWay;

    @ApiModelProperty(value = "支付方式(1000:网店支付 1001:到店支付;1000,1001:到店支付+网店支付)")
    private String paymentMethod;

    @ApiModelProperty(value = "单店总限购数量")
    private Integer storeTotalNum;

    @ApiModelProperty(value = "每人限购总数量")
    private Integer limitBuyTotalNum;

    @ApiModelProperty(value = "活动说明")
    private String promotionExplain;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
