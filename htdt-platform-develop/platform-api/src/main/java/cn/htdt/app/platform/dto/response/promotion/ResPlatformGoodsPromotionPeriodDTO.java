package cn.htdt.app.platform.dto.response.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalTime;

/**
 * 商品促销活动的时间段响应实体类
 *
 * <AUTHOR>
 * @date 2021-06-29
 */
@Data
public class ResPlatformGoodsPromotionPeriodDTO implements Serializable {

    private static final long serialVersionUID = -7261298574665592628L;

    @ApiModelProperty(value = "时间段编号")
    private String periodNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "每场开始时间字符串")
    private String startTimeStr;

    @ApiModelProperty(value = "每场结束时间字符串")
    private String endTimeStr;

    @ApiModelProperty(value = "每场开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime startTime;

    @ApiModelProperty(value = "每场结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime endTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
