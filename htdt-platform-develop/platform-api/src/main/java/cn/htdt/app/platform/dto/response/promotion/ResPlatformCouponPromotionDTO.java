package cn.htdt.app.platform.dto.response.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * @Program: platform
 * @Description: 优惠券活动配置出参
 * @Author： 刁阳
 * @CreateTime： 2021-04-15 15:05
 * @Version: 1.0
 */
@Data
public class ResPlatformCouponPromotionDTO implements Serializable {

    private static final long serialVersionUID = 3819481126532037297L;

    /***活动基本信息**/

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动短链接")
    private String shortLink;

    @ApiModelProperty(value = "图片类型(1:默认图片 2:自定义图片)")
    private Integer promotionPictureType;

    @ApiModelProperty(value = "图片url")
    private String promotionPictureUrl;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String userScope;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String joinUserScope;

    @ApiModelProperty(value = "活动说明")
    private String promotionExplain;

    @ApiModelProperty(value = "活动时间段类型 (1001:全天 1002:指定时间段)")
    private String effectivePeriodType;

    @ApiModelProperty(value = "活动开始时间")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "每日开始时间")
    private LocalTime dailyStartTime;

    @ApiModelProperty(value = "每日结束时间")
    private LocalTime dailyEndTime;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    @ApiModelProperty(value = "活动类型 1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    @ApiModelProperty(value = "促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券")
    private String promotionType;

    @ApiModelProperty(value = "活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;

    @ApiModelProperty(value = "活动有效期（1001：长期有效 1002：指定日期）")
    private String periodValidity;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺上下架状态（1：未上架 2：上架）")
    private Integer storeUpDownFlag;



    /***优惠券转商城券规则**/
    @ApiModelProperty(value = "优惠券编号")
    private String couponNo;

    @ApiModelProperty(value = "优惠券名称")
    private String mallCouponName;

    @ApiModelProperty(value = "优惠券描述")
    private String mallCouponDescribe;

    @ApiModelProperty(value = "优惠券发放方式（1001-自动发放，1002-会员领取，1003-触发返券）")
    private String mallCouponProvideType;

    @ApiModelProperty(value = "优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）")
    private String mallCouponType;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal mallDiscountThreshold;

    @ApiModelProperty(value = "折扣券单次使用百分比值")
    private Integer discountPercent;

    @ApiModelProperty(value = "券面额")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String couponPeriodValidity;

    @ApiModelProperty(value = "券开始时间")
    private LocalDateTime couponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "券使用范围(1001:POP 1002:自营 1003:归属平台公司)")
    private String mallCouponUseScope;


    @ApiModelProperty(value = "活动更新时间-开始时间")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "活动更新时间-结束时间")
    private LocalDateTime endTime;

    /**
     * 代理人数量
     */
    @ApiModelProperty(value = "代理人数量")
    private Integer agentNumber;
}
