package cn.htdt.app.platform.dto.response.markettask;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021-01-27
 * @Description 代理人酬劳管理信息响应DTO
 **/
@Data
public class ResPlatformAgentRewardManageDTO implements Serializable {

    /**
     * 序列化
     */
    private static final Long serialVersionUID = 1L;


    /**
     * 代理人的编码
     */
    @ApiModelProperty(value = "代理人的编码")
    private String agentNo;

    /**
     * 代理人手机号
     */
    @ApiModelProperty(value = "代理人手机号")
    private String agentPhone;

    /**
     * 代理人姓名
     */
    @ApiModelProperty(value = "代理人姓名")
    private String agentName;

    @ApiModelProperty(value = "有效时间")
    private String invalidTime;

    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "店铺编号")
    private String storeNo;
    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String storeName;
    /**
     * 分销商品数
     */

    @ApiModelProperty(value = "分销商品数")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int distributeNum;
    /**
     * 云池商品数
     */
    @ApiModelProperty(value = "云池商品数")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int cloudGoodsNum;
    /**
     * 任务总数
     */
    @ApiModelProperty(value = "任务总数")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int taskNum;

    /**
     * 佣金总和
     */
    @ApiModelProperty(value = "佣金总和")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal commission;
    /**
     * 礼品数量
     */
    @ApiModelProperty(value = "礼品数量")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int giftNum;
    /**
     * 服务券数量
     */
    @ApiModelProperty(value = "服务券数量")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int serviceCouponNum;
    /**
     * 现金券数量
     */
    @ApiModelProperty(value = "现金券数量")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int moneyCouponNum;
    /**
     * 汇金币数量
     */
    @ApiModelProperty(value = "汇金币数量")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int goldCoinNum;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public BigDecimal getCommission() {
        if (this.commission == null){
            return BigDecimal.ZERO;
        }
        return commission;
    }

    public void setCommission(BigDecimal commission) {
        this.commission = commission;
    }
}