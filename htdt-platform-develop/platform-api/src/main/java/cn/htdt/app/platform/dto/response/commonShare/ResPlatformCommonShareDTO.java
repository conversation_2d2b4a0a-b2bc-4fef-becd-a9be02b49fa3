package cn.htdt.app.platform.dto.response.commonShare;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 活动分享出参
 * <AUTHOR>
 * @date 2023/06/12
 */
@Data
public class ResPlatformCommonShareDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "通用编号")
    private String commonNo;

    @ApiModelProperty(value = "分享类型 枚举值:1001-生意经-服务中心-套餐列表 1002-生意经-服务中心-套餐详情页 " +
            "1003-生意经-课程-课程详情页 1004-生意经-咨询-咨询详情页 1005-生意经-案例-案例详情页 " +
            "1006-生意经-问答-问答详情页 2001-卖货-我要做活动-活动详情页 " +
            "2002-卖货-我的商品-商品详情页 2003-卖货-我要做活动-社群接龙详情页 3001-招募代理人-汇赚钱首页")
    private String shareType;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "分享文案")
    private String copyWriter;

    @ApiModelProperty(value = "落地页URL")
    private String landingUrl;

    @ApiModelProperty(value = "微信短链接URL")
    private String shareMiniProgramUrl;

    @ApiModelProperty(value = "微信短链接URL")
    private String shareH5Url;

    @ApiModelProperty(value = "活动图片地址")
    private String imgUrl;

    @ApiModelProperty(value = "二维码地址")
    private String QRUrl;

    @ApiModelProperty(value = "图片列表")
    private List<String> imageUrls;

    @ApiModelProperty(value = "售价范围")
    private String priceRange;

    @ApiModelProperty("小程序appId")
    private String appId;

    @ApiModelProperty("小程序名称")
    private String nickname;

    @ApiModelProperty("小程序头像")
    private String headImageUrl;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
