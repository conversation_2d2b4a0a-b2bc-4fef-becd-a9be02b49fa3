package cn.htdt.app.platform.dto.response.goodrealsstock;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * 功能描述: 商品库存导出响应dto
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformGoodsRealStockExcelDTO  implements Serializable {

    /**
     * 店铺名称，对应orgName
     */
    @ExcelProperty(value = "店铺名称",index = 0)
    @ColumnWidth(30)
    private String storeName;
    /**
     * 商品编号
     */
    @ExcelProperty(value = "商品编号",index = 1)
    @ColumnWidth(30)
    private String goodsNo;
    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 2)
    @ColumnWidth(30)
    private String goodsName;


    /**
     * 是否启用串码形式value
     */
    @ExcelProperty(value = "是否启用串码",index = 3)
    @ColumnWidth(20)
    private String imeiFlagValue;

    /**
     * 是否入仓形式value
     */
    @ExcelProperty(value = "是否入仓",index = 4)
    @ColumnWidth(20)
    private String warehouseFlagValue;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位",index = 5)
    @ColumnWidth(20)
    private String calculationUnitName;

    /**
     * 冻结库存数量，即预锁数量
     */
    @ExcelProperty(value = "预锁数量",index = 6)
    @ColumnWidth(20)
    private BigDecimal freezeStockNum;

    /**
     * 待发货数量
     */
    @ExcelProperty(value = "待发货数量",index = 7)
    @ColumnWidth(20)
    private BigDecimal deliverStockNum;

    /**
     * 可用库存数量，即可售库存
     */
    @ExcelProperty(value = "可售库存",index = 8)
    @ColumnWidth(20)
    private String availableStockNumStr;

    /**
     * 总库存数量，即实体库存
     */
    @ExcelProperty(value = "实体库存",index = 9)
    @ColumnWidth(20)
    private String realStockNumStr;

    /**
     *  商品状态形式value 1001-未上架;1004:已上架;
     */
    @ExcelProperty(value = "商品状态",index = 10)
    @ColumnWidth(20)
    private String goodsStatusValue;

    /**
     *  调拨在途数量
     */
    @ExcelProperty(value = "调拨在途数量",index = 11)
    @ColumnWidth(20)
    private BigDecimal allocationNum;

    /**
     *  已售数量
     */
    @ExcelProperty(value = "已售数量",index = 12)
    @ColumnWidth(20)
    private BigDecimal soldNum;

    /**
     * 转换率(辅转主)
     */
    @ExcelProperty(value = "辅计量单位1转换率",index = 13)
    @ColumnWidth(20)
    private BigDecimal conversionRate;


    /**
     * 辅计量单位id
     */
    @ExcelProperty(value = "辅计量单位1",index = 14)
    @ColumnWidth(20)
    private String assistCalculationUnitName;

    @ExcelProperty(value = "辅计量单位2转换率",index = 15)
    @ColumnWidth(20)
    private BigDecimal conversionRateTwo;


    /**
     * 辅计量单位id
     */
    @ExcelProperty(value = "辅计量单位2",index = 16)
    @ColumnWidth(20)
    private String assistCalculationUnitNameTwo;


    /**
     * 第一属性值编码
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "第一属性值",index = 17)
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "第二属性值",index = 18)
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "第三属性值",index = 19)
    private String thirdAttributeValueName;

    public String getGoodsName() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(goodsName);

        if (StringUtils.isNotEmpty(getFirstAttributeValueName())) {
            stringBuilder.append("-").append(getFirstAttributeValueName());
        }
        if (StringUtils.isNotEmpty(getSecondAttributeValueName())) {
            stringBuilder.append("-").append(getSecondAttributeValueName());
        }
        if (StringUtils.isNotEmpty(getThirdAttributeValueName())) {
            stringBuilder.append("-").append(getThirdAttributeValueName());
        }
        return stringBuilder.toString();
    }

}
