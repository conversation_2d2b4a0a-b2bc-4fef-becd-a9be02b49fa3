package cn.htdt.app.platform.dto.response.fileopt.goods;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @ClassName ReqAppSeriesGoodsImportErrorDTO
 * @Description 系列商品导入错误DTO
 * @date 2021/11/25 15:50
 */
@Data
public class ReqPlatformGoodsSeriesImportErrorDTO implements Serializable {
    /**
     * 序列化
     */
    private static final long serialVersionUID = 8382745965259598544L;

    /*@ExcelProperty(value = "*商品形式", index = 0)
    @ColumnWidth(14)
    private String importScene;*/

    @ExcelProperty(value = "*商品名称", index = 0)
    @ColumnWidth(40)
    private String goodsName;

    @ExcelProperty(value = "*属性名1", index = 1)
    @ColumnWidth(20)
    private String firstAttributeName;

    @ExcelProperty(value = "*属性值1", index = 2)
    @ColumnWidth(20)
    private String firstAttributeValueName;

    @ExcelProperty(value = "属性名2", index = 3)
    @ColumnWidth(20)
    private String secondAttributeName;

    @ExcelProperty(value = "属性值2", index = 4)
    @ColumnWidth(20)
    private String secondAttributeValueName;

    @ExcelProperty(value = "属性名3", index = 5)
    @ColumnWidth(20)
    private String thirdAttributeName;

    @ExcelProperty(value = "属性值3", index = 6)
    @ColumnWidth(20)
    private String thirdAttributeValueName;

    @ExcelProperty(value = "*零售价", index = 7)
    @ColumnWidth(14)
    private String retailPrice;

    @ExcelProperty(value = "计量单位", index = 8)
    @ColumnWidth(14)
    private String calculationUnitName;

    @ExcelProperty(value = "商品条形码", index = 9)
    @ColumnWidth(20)
    private String barcode;

    @ExcelProperty(value = "一级销售类目", index = 10)
    @ColumnWidth(20)
    private String firstCategoryName;

    @ExcelProperty(value = "二级销售类目", index = 11)
    @ColumnWidth(20)
    private String secondCategoryName;

    @ExcelProperty(value = "三级销售类目", index = 12)
    @ColumnWidth(20)
    private String thirdCategoryName;

    @ExcelProperty(value = "四级销售类目", index = 13)
    @ColumnWidth(20)
    private String fourCategoryName;

    @ExcelProperty(value = "末级店铺类目", index = 14)
    @ColumnWidth(20)
    private String storeCategoryName;

    @ExcelProperty(value = "商品品牌", index = 15)
    @ColumnWidth(20)
    private String brandName;

    @ExcelProperty(value = "是否入仓", index = 16)
    @ColumnWidth(12)
    private String warehouseFlag;

    @ExcelProperty(value = "采购价", index = 17)
    @ColumnWidth(9)
    private String purchasePrice;

    @ExcelProperty(value = "实体库存", index = 18)
    @ColumnWidth(12)
    private String realStockNum;

    @ExcelProperty(value = "供应商", index = 19)
    @ColumnWidth(40)
    private String supplierName;

    @ExcelProperty(value = "是否启用串码", index = 20)
    @ColumnWidth(20)
    private String imeiFlag;

    @ExcelProperty(value = "错误信息", index = 21)
    @ColumnWidth(30)
    private String errorMsg;
}
