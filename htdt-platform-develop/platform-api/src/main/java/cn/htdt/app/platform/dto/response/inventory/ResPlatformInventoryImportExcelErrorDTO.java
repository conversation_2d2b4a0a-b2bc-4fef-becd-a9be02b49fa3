package cn.htdt.app.platform.dto.response.inventory;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.math.BigDecimal;

import static org.apache.poi.ss.usermodel.Font.COLOR_RED;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点详细 导入error的DTO
 **/
@Data
public class ResPlatformInventoryImportExcelErrorDTO extends BaseRowModel {

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 0)
    private String goodsName;

    /**
     * 商品ID
     */
    @ExcelIgnore
    private String goodsNo;

    /**
     * 实盘数量
     */
    @ExcelProperty(value = "实盘数量",index = 1)
    private BigDecimal inventoryStockNum;

    @ExcelProperty(value = "错误信息", index = 2)
    @ColumnWidth(30)
    @HeadFontStyle(color = COLOR_RED)
    private String errorMsg;

}
