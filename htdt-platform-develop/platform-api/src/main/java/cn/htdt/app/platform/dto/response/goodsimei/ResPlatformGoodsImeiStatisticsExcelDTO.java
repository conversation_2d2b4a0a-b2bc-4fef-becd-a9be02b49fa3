package cn.htdt.app.platform.dto.response.goodsimei;

import cn.htdt.app.platform.converter.base.DateConverter;
import cn.htdt.app.platform.converter.base.WhetherConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品串码信息-统计导出DTO
 *
 * <AUTHOR>
 * @date 2020年9月11日
 */
@Data
public class ResPlatformGoodsImeiStatisticsExcelDTO implements Serializable {

    private static final long serialVersionUID = 5249406507084428508L;

    /**
     * 商品串码
     */
    @ExcelProperty(value = "串码", index = 0)
    @ColumnWidth(20)
    private String imei;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ExcelProperty(value = "商品编号", index = 1)
    @ColumnWidth(20)
    private String goodsNo;

    /**
     * 自定义商品编号，包含普通商品、套餐商品等所有商品
     */
    @ExcelProperty(value = "自定义商品编号", index = 2)
    @ColumnWidth(50)
    private String customGoodsNo;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 3)
    @ColumnWidth(40)
    private String goodsName;

    /**
     * 是否已销售(1:否 2:是)
     */
    @ExcelProperty(value = "是否已销售", index = 4, converter = WhetherConverter.class)
    @ColumnWidth(20)
    private Integer saleFlag;

    /**
     * 供应商
     */
    @ExcelIgnore
    private String supplierCode;
    @ExcelProperty(value = "供应商名称", index = 5)
    @ColumnWidth(20)
    private String supplierName;

    /**
     * 门店编号，对应orgId
     */
    @ExcelIgnore
    private String storeNo;
    @ExcelProperty(value = "门店名称", index = 6)
    @ColumnWidth(20)
    private String storeName;

    /**
     * 自定义店铺编码
     */
    @ExcelProperty(value = "自定义店铺编码", index = 7)
    @ColumnWidth(50)
    private String customStoreNo;

    /**
     * 是否在途(1:否 2:是)
     */
    @ExcelProperty(value = "是否在途", index = 8, converter = WhetherConverter.class)
    @ColumnWidth(20)
    private Integer transportFlag;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", index = 9)
    @ColumnWidth(40)
    private String createNo;

    /**
     * 创建时间
     * 是否在途(1:否 2:是)
     */
    @ExcelProperty(value = "创建时间", index = 10, converter = DateConverter.class)
    @ColumnWidth(40)
    private Date createTime;
}
