package cn.htdt.app.platform.dto.response.goodstrade;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 采购交易单
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformPurchaseTradeOrderDTO implements Serializable {

    private static final long serialVersionUID = -4861029642789973882L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "店铺编号")
    private String buyerCode;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "门店经营人")
    private String operatorName;

    @ApiModelProperty(value = "门店经营人手机号")
    private String operatorPhone;

    @ApiModelProperty(value = "采购订单数量")
    private Integer totalGoodsCount;

    @ApiModelProperty(value = "采购总金额")
    private BigDecimal totalGoodsAmount;

    @ApiModelProperty(value = "子订单编号")
    private String orderItemNo;

    @ApiModelProperty(value = "渠道编号")
    private String channelCode;

    @ApiModelProperty(value = "商品编码")
    private String itemCode;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "sku")
    private String skuCode;

    @ApiModelProperty(value = "sku图片信息")
    private String skuPictureUrl;

    @ApiModelProperty(value = "采购商品数量")
    private String goodsCount;

    @ApiModelProperty(value = "千橙掌柜商品编号")
    private String goodsNo;

    /**
     * 订单状态 10:待审核,20:待支付,21:审核通过待支付,31:已支付待拆单,32:已支付已拆单待下行ERP,40:待发货,50:已发货,61:买家收货,62:到期自动收货
     * 云原生状态 - 100:草稿 ,200:正式,400:卖家取消,401:卖家驳回,500:履约中,501:履约中-已确认收货,502:完成,80:已发起逆向[退款审核中],85:逆向完成[退款完成]
     */
    @ApiModelProperty(value = "订单状态 10:待审核,20:待支付,21:审核通过待支付,31:已支付待拆单,32:已支付已拆单待下行ERP,40:待发货,50:已发货,61:买家收货,62:到期自动收货" +
            "云原生状态 - 100:草稿 ,200:正式,400:卖家取消,401:卖家驳回,500:履约中,501:履约中-已确认收货,502:完成,80:已发起逆向[退款审核中],85:逆向完成[退款完成]")
    private String orderStatus;

    @ApiModelProperty(value = "计量单位编号")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "是否有辅计量单位:1-否;2-是;")
    private String standardFlag;

    @ApiModelProperty(value = "辅计量单位id")
    private String assistCalculationUnitNo;

    @ApiModelProperty(value = "辅计量单位名称")
    private String assistCalculationUnitName;

    @ApiModelProperty(value = "主计量单位对应关系数值")
    private String mainUnitNum;

    @ApiModelProperty(value = "辅计量单位对应关系数值")
    private String assistUnitNum;

    @ApiModelProperty(value = "转换率(辅转主)")
    private String conversionRate;

    @ApiModelProperty(value = "判断是否有库存 1：有 2：无\" 一体机专用")
    private String stockFlag;

    @ApiModelProperty(value = "千橙商品名称")
    private String htdtGoodsName;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "是否入仓 1：否 2：是")
    private Integer warehouseFlag;

    @ApiModelProperty("1007=千橙采购；3001=羊乃世家，见 BelongSystemEnum")
    private String dataSource;

    @ApiModelProperty("与采购单位对应关系值,0 < 对应关系 <= 999 之间的正整数")
    private BigDecimal unitRelationNum;

    @ApiModelProperty(value = "计量单位名称关联")
    private String unitRelation;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createOrderTime;

    @ApiModelProperty(value = "B2B供应商名称")
    private String sellerName;

}
