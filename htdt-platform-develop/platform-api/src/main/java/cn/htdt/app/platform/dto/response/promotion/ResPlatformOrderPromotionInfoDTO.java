package cn.htdt.app.platform.dto.response.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 订单的促销活动信息
 *
 * <AUTHOR>
 * @date 2021-07-13
 */
@Data
public class ResPlatformOrderPromotionInfoDTO implements Serializable {

    private static final long serialVersionUID = 1237193190438741324L;

    @ApiModelProperty(value = "序号")
    private Integer serialNum;

    @ApiModelProperty(value = "活动类型，2001:秒杀")
    private String promotionType;

    @ApiModelProperty(value = "促销活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "促销活动名称")
    private String promotionName;

    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
