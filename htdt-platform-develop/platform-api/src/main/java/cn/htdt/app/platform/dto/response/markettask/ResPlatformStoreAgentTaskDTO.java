package cn.htdt.app.platform.dto.response.markettask;


import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 *
 * 功能描述: 商家店铺平台任务响应dto
 * @author: 高繁
 * @date: 2021/01/21 14:51
 */
@Data
public class ResPlatformStoreAgentTaskDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 任务活动唯一编号
     */
    @ApiModelProperty(value = "任务活动唯一编号")
    private String taskNo;
    /**
     * 店铺编码
     */
    @ApiModelProperty(value = "店铺编码")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String storeName;
    /**
     * 任务活动类型，0001:拉新任务 枚举TaskTypeEnum
     */
    @ApiModelProperty(value = "店铺编码")
    private String taskType;


    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName;

    /**
     * 任务图片地址
     */
    @ApiModelProperty(value = "任务图片地址")
    private String taskImageUrl;

    /**
     * 任务开始时间
     */
    @ApiModelProperty(value = "任务开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate taskStartTime;

    /**
     * 任务结束时间
     */
    @ApiModelProperty(value = "任务结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate taskEndTime;

    /**
     * 任务上下架标识，默认0未上架 1上架 2下架
     */
    @ApiModelProperty(value = "任务上下架标识，默认1:未发布 2:上架 3:下架")
    private Integer statusFlag;

    /**
     * 酬劳类型
     */
    @ApiModelProperty(value = "酬劳类型")
    private String rewardSets;

    /**
     * 店铺代理人酬劳汇总
     */
    @ApiModelProperty(value = "店铺代理人酬劳汇总")
    private String agentRewards;


    /**
     * 参与代理人数
     */
    @ApiModelProperty(value = "参与代理人数")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int joinAgentNum;

    /**
     * 拉新总数
     */
    @ApiModelProperty(value = "拉新总数")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int memberNum;
    /**
     * 该任务在店铺的状态： 2:上架 3:下架
     */
    @ApiModelProperty(value = "该任务在店铺的状态： 2:上架 3:下架")
    private Integer status;


    /**
     * 平台任务状态： 1 未开始
     *  2 进行中
     *  3 已结束
     */
    @ApiModelProperty(value = "平台任务状态： 1 未开始 2 进行中 3 已结束")
    private Integer taskStatus;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
