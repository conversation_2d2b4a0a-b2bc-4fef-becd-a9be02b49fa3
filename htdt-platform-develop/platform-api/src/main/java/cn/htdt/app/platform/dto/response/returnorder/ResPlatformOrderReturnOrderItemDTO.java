package cn.htdt.app.platform.dto.response.returnorder;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.common.enums.ReturnReasonTypeEnum;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.returnorder.ReturnSourceEnum;
import cn.htdt.common.enums.returnorder.ReturnTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
  *@className:  ResOrderReturnOrderItemDTO
  * @Description : 售后详情
  * <AUTHOR>
  *@date : 2020/9/4/ 13:34
  */
 @Data
public class ResPlatformOrderReturnOrderItemDTO implements Serializable {

    @ApiModelProperty(value = "售后单来源 1000 粉丝发起 1100 店铺发起 1200 拒绝接单")
    @Converter(enumClass = ReturnSourceEnum.class, fieldName = "sourceName")
    private String source;

    @ApiModelProperty(value = "售后单来源描述")
    private String sourceName;

    @ApiModelProperty(value = "售后类型")
    @Converter(enumClass = ReturnTypeEnum.class)
    private String returnType;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    @ApiModelProperty(value = "商品购买数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal productItemNum;

    @ApiModelProperty(value = "售后商品数量")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal returnProductItemNum;

    @ApiModelProperty(value = "商品规格属性")
    private String extInfo;

    @ApiModelProperty(value = "售后商品购买金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal returnGoodsAmount;

    @ApiModelProperty(value = "申请退款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal applyReturnAmount;

    @ApiModelProperty(value = "退单申请时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "返回优惠券")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal discount;

    @ApiModelProperty(value = "退代金券金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal returnVoucherAmount;

    @ApiModelProperty(value = "返回积分")
    private int orderDepletePoints;

    @ApiModelProperty(value = "返回橙豆")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal virtualCoinDiscount;

    /**
     * 扣减积分
     */
    @ApiModelProperty(value = "扣减积分")
    private Integer orderGivePoints = 0;

    @ApiModelProperty(value = "积分抵扣")
    private Integer depPoints = NumConstant.ZERO;

    @ApiModelProperty(value = "抵扣积分优惠")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderDeductionPointDiscount = BigDecimal.ZERO;

    @ApiModelProperty(value = "渠道优惠金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal orderExtendChannelDiscount = BigDecimal.ZERO;
    @ApiModelProperty(value = "渠道优惠编码，如：美团")
    private String orderExtendChannelCode;
    @ApiModelProperty(value = "渠道优惠名称，如：美团")
    private String orderExtendChannelName;
    @ApiModelProperty(value = "渠道优惠类型：0=无需核销 1=整单核销 2=按金额核销")
    private Integer orderExtendChannelFlag = NumConstant.ZERO;

    @ApiModelProperty(value = "退单原因")
    @Converter(enumClass = ReturnReasonTypeEnum.class)
    private String returnReason;

    @ApiModelProperty(value = "审核原因")
    private String auditReason;

    @ApiModelProperty(value = "退单申请描述")
    private String returnRemark;

    @ApiModelProperty(value = "申请图片")
    private List<String> picPath;

    @ApiModelProperty(value = "商品主图片")
    private String productPicPath;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
