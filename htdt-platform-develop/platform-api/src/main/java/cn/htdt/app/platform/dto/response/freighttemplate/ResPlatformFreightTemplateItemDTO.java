package cn.htdt.app.platform.dto.response.freighttemplate;


import java.io.Serializable;
import java.math.BigDecimal;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运费模板请求实体
 *
 * <AUTHOR>
 * @date 2020年9月7日
 */
@Data
public class ResPlatformFreightTemplateItemDTO implements Serializable {


	/**
	 * 
	 */
	private static final long serialVersionUID = 4718765717319592838L;

	/**
	 * 模板详情编号
	 */
	@ApiModelProperty(value = "模板详情编号")
	private String templateItemNo;

    /**
     * 商家编号
     */
	@ApiModelProperty(value = "商家编号")
    private String merchantNo;

    /**
     * 归属平台编号
     */
	@ApiModelProperty(value = "归属平台编号")
    private String companyNo;

    /**
     * freight_template编号
     */
	@ApiModelProperty(value = "freight_template编号")
    private String freightTemplateNo;

    /**
     * 配送方式编码
     */
	@ApiModelProperty(value = "配送方式编码")
    private String distributionCode;

    /**
     * 类型区分 {1:基本配送方式; 2:指定条件包邮; }
     */
	@ApiModelProperty(value = "类型区分 {1001:基本配送方式; 1002:指定条件包邮;}")
    private String type;

    /**
     * 计费方式 包邮条件 { 10:按件数; 11:按重量; 12:按体积;  20:重量和金额; 21:件数和金额; 22:体积和金额;}
     */
	@ApiModelProperty(value = "计费方式 包邮条件 {1010:按件数; 1030:一口价}")
    private String chargeWay;

    /**
     * 配送区域
     */
	@ApiModelProperty(value = "配送区域")
    private String distributionRegion;

    /**
     * 是否已经删除，默认1未删除，其余已删除
     */
	@ApiModelProperty(value = "是否已经删除，默认1未删除，其余已删除")
    private Integer disableFlag;

    /**
     * 归属分部编号
     */
	@ApiModelProperty(value = "归属分部编号")
    private String branchNo;

    /**
     * 运费区域id
     */
    @ApiModelProperty(value = "运费区域id")
    private String areaId;
    
    /**
     * 首件
     */
    @ApiModelProperty(value = "首件")
    private Integer firstPiece;
    
    /**
     *  首费
     */
    @ApiModelProperty(value = "首费")
    private BigDecimal firstAmount;
    
    /**
     * 续件
     */
    @ApiModelProperty(value = "续件")
    private Integer nextPiece;
    
    /**
     *  续费
     */
    @ApiModelProperty(value = "续费")
    private BigDecimal nextAmount;
    
    /**
     * 一口价
     */
    @ApiModelProperty(value = "一口价")
    private BigDecimal fixedAmount;
    
	/**
	 * 运费区域名称
	 */
	@ApiModelProperty(value = "运费区域名称")
	private String distributionRegionName; 
    
}
