package cn.htdt.app.platform.dto.response.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 活动中奖记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
public class ResPlatformLotteryDrawRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录编号")
    private String recordNo;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "奖品类型（1001:实物礼品 1002:优惠券 1003:话费券 1004:话费 1005:汇金币 1006:积分  9999:谢谢惠顾）")
    private String rewardType;

    @ApiModelProperty(value = "奖品名称")
    private String rewardName;

    @ApiModelProperty(value = "券面额")
    private BigDecimal couponValue;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal discountThreshold;

    @ApiModelProperty(value = "券标识")
    private String couponIdentity;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String couponPeriodValidity;

    @ApiModelProperty(value = "券开始时间")
    private LocalDateTime couponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    private LocalDateTime couponInvalidTime;

    @ApiModelProperty(value = "券使用渠道 (1001:汇享购下单 1002:门店下单)")
    private String couponUseChannel;

    @ApiModelProperty(value = "券使用范围(1001:店铺全场通用 1002:云池商品通用 1003:指定云池商品)")
    private String couponUseScope;

    @ApiModelProperty(value = "券编号")
    private String couponNo;

    @ApiModelProperty(value = "券使用状态(1:未使用 2:已使用)")
    private Integer couponUseStatus;

    @ApiModelProperty(value = "配送方式 （1001:自提）")
    private String deliveryType;

    @ApiModelProperty(value = "奖品值 (话费、金币、积分)")
    private String rewardValue;

    @ApiModelProperty(value = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(value = "昵称")
    private String name;

    @ApiModelProperty(value = "粉丝备注名称（店铺）")
    private String storeFanName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "充值手机号")
    private String rechargePhone;

    @ApiModelProperty(value = "收货人")
    private String deliveryName;

    @ApiModelProperty(value = "收货人手机号")
    private String deliveryPhone;

    @ApiModelProperty(value = "收货人地址")
    private String deliveryAddress;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "发放/领取状态(1001:已领取 1002:未领取 1003:已发放 1004:未发放 1005:发放失败 )")
    private String sendReceiveStatus;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "充值手机号所属运营商")
    private String serviceProvider;

    /**
     * 中奖时间
     */
    @ApiModelProperty(value = "中奖时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "物流单号")
    private String trackingNumber;

    /**
     * 促销类型 1001:大转盘 1002:摇奖机1003:拆盲盒1004:套圈圈 1005:小猫钓鱼 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券 4001:报名活动
     */
    @ApiModelProperty(value = "促销类型 1001:大转盘 1002:摇奖机1003:拆盲盒1004:套圈圈 1005:小猫钓鱼 1006:膨胀红包")
    private String promotionType;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;
    @ApiModelProperty(value = "核销状态:1：待核销 2：已核销 3:已核销（不可核销）")
    private Integer writeOffStatus;

    /**
     * 蛋品, 商家抽奖订单配送方式(1101:自有配送, 1102: 快递配送), 参考DeliveryTypeSelectEnum
     */
    @ApiModelProperty(value = "商家抽奖订单配送方式, 1101: 自有配送, 1102: 快递配送")
    private String merchantLotteryDeliveryType;
}
