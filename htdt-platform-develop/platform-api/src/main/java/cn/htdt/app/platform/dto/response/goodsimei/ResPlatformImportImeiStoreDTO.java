package cn.htdt.app.platform.dto.response.goodsimei;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

@ColumnWidth(value = 30)
@Data
public class ResPlatformImportImeiStoreDTO implements Serializable {

    @ExcelProperty(value = "店铺名称", index = 0)
    private String storeName;

    @ExcelProperty(value = "店铺编码", index = 1)
    private String storeNo;

    public ResPlatformImportImeiStoreDTO() {
    }

    public ResPlatformImportImeiStoreDTO(String storeName, String storeNo) {
        this.storeName = storeName;
        this.storeNo = storeNo;
    }
}
