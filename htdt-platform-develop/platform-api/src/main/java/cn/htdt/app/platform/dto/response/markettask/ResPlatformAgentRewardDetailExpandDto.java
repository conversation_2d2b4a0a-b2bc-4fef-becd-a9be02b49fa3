package cn.htdt.app.platform.dto.response.markettask;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 代理人明细扩展内容
 */
@Data
public class ResPlatformAgentRewardDetailExpandDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 预计佣金集合
     */
    @ApiModelProperty(value = "预计佣金集合")
    private List<ResPlatformAgentRewardCommissionDto> predictCommissionList;

    @ApiModelProperty(value = "预计佣金总额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal predictCommission;


    /**
     * 预计服务券
     */
    @ApiModelProperty(value = "预计服务券")
    private List<ResPlatformAgentRewardGiftDto> predictServiceCouponList;

    /**
     * 预计现金券
     */
    @ApiModelProperty(value = "预计现金券")
    private List<ResPlatformAgentRewardGiftDto> predictMoneyCouponList;
    /**
     * 预计礼品
     */
    @ApiModelProperty(value = "预计礼品")
    private List<ResPlatformAgentRewardGiftDto> predictGiftList;


    /**
     * 佣金集合
     */
    @ApiModelProperty(value = "已获得佣金")
    private List<ResPlatformAgentRewardCommissionDto> commissionList;

    @ApiModelProperty(value = "已获得佣金总额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal commission;


    /**
     * 服务券
     */
    @ApiModelProperty(value = "已获得服务券")
    private List<ResPlatformAgentRewardGiftDto> serviceCouponList;

    /**
     * 现金券
     */
    @ApiModelProperty(value = "已获得现金券")
    private List<ResPlatformAgentRewardGiftDto> moneyCouponList;
    /**
     * 礼品
     */
    @ApiModelProperty(value = "已获得礼品")
    private List<ResPlatformAgentRewardGiftDto> giftList;
}
