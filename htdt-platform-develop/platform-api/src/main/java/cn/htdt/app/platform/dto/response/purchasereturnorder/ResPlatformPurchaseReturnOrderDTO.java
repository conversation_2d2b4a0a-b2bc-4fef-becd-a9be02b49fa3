package cn.htdt.app.platform.dto.response.purchasereturnorder;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 *
 * 功能描述: 采购退货单响应dto
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformPurchaseReturnOrderDTO  implements Serializable {
    private static final long serialVersionUID = -6485021349489492496L;
    /**
     *退货单编码
     */
    @ApiModelProperty(value = "退货单编码")
    private String returnCode;
    /**
     *采购单编码
     */
    @ApiModelProperty(value = "采购单编码")
    private String purchaseCode;
    /**
     *供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;
    /**
     *供应商编码
     */
    @ApiModelProperty(value = "供应商编码")
    private String supplierCode;
    /**
     *商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodName;
    /**
     *总退货数量
     */
    @ApiModelProperty(value = "总退货数量")
    private BigDecimal totalReturnNum;
    /**
     *交易金额
     */
    @ApiModelProperty(value = "交易金额")
    private BigDecimal totalPurchasePrice;
    /**
     *退款金额
     */
    @ApiModelProperty(value = "退款金额")
    private BigDecimal totalReturnPrice;
    /**
     *收货联系人
     */
    @ApiModelProperty(value = "收货联系人")
    private String receiveContactName;
    /**
     *收货联系人手机号
     */
    @ApiModelProperty(value = "收货联系人手机号")
    private String receiveContactMobile;
    /**
     *发货联系人
     */
    @ApiModelProperty(value = "发货联系人")
    private String sendContactName;
    /**
     *发货联系人手机号
     */
    @ApiModelProperty(value = "发货联系人手机号")
    private String sendContactMobile;
    /**
     *创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createName;

    /**
     *创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     *退货状态21:待提交 22:待审核 23:待出库24:部分出库25:已完成 26:已作废\n            27:审核不通过
     */
    private Integer returnStatus;
    /**
     *退货状态形式VALUE 21:待签收 22:已签收 23:待入库 24:部分入库 25:已完成 26:已作废
     */
    @ApiModelProperty(value = "退货状态")
    private String returnStatusValue;
    /**
     * 采购退货单对应的退货商品
     */
    @ApiModelProperty(value = "采购退货单对应的退货商品")
    private List<ResPlatformPurchaseReturnOrderGoodsDetailDTO> returnOrderGoodList;

    /**
     * 采购类型类型：0 商品；1 商品组
     */
    @ApiModelProperty(value = "采购类型类型：0 商品；1 商品组")
    private Integer purchaseType;


}
