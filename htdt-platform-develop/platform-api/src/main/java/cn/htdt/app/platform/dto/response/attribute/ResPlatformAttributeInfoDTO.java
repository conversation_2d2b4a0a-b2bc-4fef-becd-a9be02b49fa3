package cn.htdt.app.platform.dto.response.attribute;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * 属性详情查询响应DTO
 *
 * <AUTHOR>
 * @date 2020/10/10
 **/
@Data
public class ResPlatformAttributeInfoDTO implements Serializable {
    private static final long serialVersionUID = -1030020265994152908L;

    /**
     * 属性名称编码
     */
    @ApiModelProperty(value = "属性名称编码")
    private String attributeCode;

    /**
     * 属性名
     */
    @ApiModelProperty(value = "属性名")
    private String attributeName;

    /**
     * 属性值集合
     */
    @ApiModelProperty(value = "属性值集合")
    private List<ResPlatformAttributeValueDTO> attributeValueList;

    /**
     * 数据来源类型(默认1002。1001:同步MDM、1002:平台自创)
     */
    @ApiModelProperty(value = "数据来源类型(默认1002。1001:同步MDM、1002:平台自创)")
    private String dataSourceType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
