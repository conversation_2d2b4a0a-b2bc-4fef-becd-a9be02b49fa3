package cn.htdt.app.platform.dto.response.settlementorder;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.request.ReqBaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 结算单 响应DTO
 *
 * <AUTHOR>
 * @since 2023-05-30
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ResPlatformSettlementOrderDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("结算单号")
    private String settlementNo;

    @ApiModelProperty("关联单据号")
    private String associatedDocumentNo;

    /**
     * 关联单据类型, 参考枚举: AssociatedDocumentTypeEnum
     */
    @ApiModelProperty("关联单据类型")
    private String associatedDocumentType;

    @ApiModelProperty("结算状态 1-未结算 2-已结算")
    private Integer status;

    @ApiModelProperty("结算金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal settlementAmount;

    @ApiModelProperty("结算时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime settlementTime;

    @ApiModelProperty("结算人编号")
    private String settlementUserNo;

    @ApiModelProperty("结算人姓名")
    private String settlementUserName;

    @ApiModelProperty("商家编号")
    private String merchantNo;

    @ApiModelProperty("商家名称")
    private String merchantName;

    @ApiModelProperty("店铺ID, 对应orgId")
    private String storeNo;

    @ApiModelProperty("店铺名称, 对应orgName")
    private String storeName;

    @ApiModelProperty("创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty("修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
