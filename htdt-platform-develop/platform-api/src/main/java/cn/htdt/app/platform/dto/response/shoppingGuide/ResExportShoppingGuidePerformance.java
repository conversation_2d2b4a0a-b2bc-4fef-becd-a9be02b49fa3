package cn.htdt.app.platform.dto.response.shoppingGuide;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName ResExportShoppingGuidePerformance
 * <AUTHOR>
 * @create 2023/6/19 17:38
 */
@Data
public class ResExportShoppingGuidePerformance implements Serializable {
    private static final long serialVersionUID = -8572758992224493628L;


    @ExcelProperty(value = "导购员姓名",index = 0)
    @ColumnWidth(20)
    private String shoppingGuideName;

    @ExcelProperty(value = "订单数",index = 1)
    @ColumnWidth(20)
    private Integer orderNum;

    @ExcelProperty(value = "商品总额",index = 2)
    @ColumnWidth(20)
    private BigDecimal totalGoodsAmount;

    @ExcelProperty(value = "应付总额",index = 3)
    @ColumnWidth(20)
    private BigDecimal totalShouldAmount;

    @ExcelProperty(value = "提成比例",index = 4)
    @ColumnWidth(20)
    private String commissionRatio;

    @ExcelProperty(value = "提成金额",index = 5)
    @ColumnWidth(20)
    private BigDecimal commissionAmount;
}
