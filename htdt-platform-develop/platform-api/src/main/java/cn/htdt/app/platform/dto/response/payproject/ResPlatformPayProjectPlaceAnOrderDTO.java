package cn.htdt.app.platform.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 下单返回值
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-12
 */
@Data
public class ResPlatformPayProjectPlaceAnOrderDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品订单编号
     */
    @ApiModelProperty(value="收银台链接")
    private String cashierUrl = "";

    /**
     * 产品唯一编号
     */
    @ApiModelProperty(value="重复购买的增值服务")
    private Set<String> sameValueAdded;

}
