package cn.htdt.app.platform.dto.response.promotion;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.common.enums.market.PromotionDetailTypeEnum;
import cn.htdt.common.enums.market.PromotionOrderTypeEnum;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 订单促销活动信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-05-28
 */
@Data
public class ResPlatfromSoPromotionDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    /**
     * 父订单编号
     */
    @ApiModelProperty(value = "父订单编号")
    private String parentOrderNo;

    @ApiModelProperty(value = "so_promotion唯一编码")
    private String soPromotionCode;

    /**
     * 所属平台
     */
    @ApiModelProperty(value = "所属平台")
    private String companyNo;

    /**
     * 促销活动编号
     */
    @ApiModelProperty(value = "促销活动编号")
    private String promotionNo;

    /**
     * 促销活动类型  1001 优惠券 1002 议价 1003 满减
     */
    @ApiModelProperty(value = "促销活动类型  1001 优惠券 1002 议价 1003 满减")
    private String promotionType;

    @ApiModelProperty(value = "促销活动明细类型  1001 优惠券 1002 议价 1003 满减 1004 膨胀红包优惠券 1005 满折")
    private String promotionDetailType;

    @ApiModelProperty(value = "促销活动类型名称  1001 优惠券 1002 议价 1003 满减")
    private String promotionTypeName;

    @ApiModelProperty(value = "促销活动明细类型  1001 优惠券 1002 议价 1003 满减 1004 膨胀红包优惠券 1005 满折")
    private String promotionDetailTypeName;

    /**
     * 促销活动名称
     */
    @ApiModelProperty(value = "促销活动名称")
    private String promotionName;

    /**
     * 订单分摊金额
     */
    @ApiModelProperty(value = "订单分摊金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal amountSharePromotion;

    /**
     * 活动描述
     */
    @ApiModelProperty(value = "活动描述")
    private String promotionDesc;

    /**
     * toB标识，默认0未同步，1已同步
     */
    @ApiModelProperty(value = "toB标识，默认0未同步，1已同步")
    private String tobFlag;


}
