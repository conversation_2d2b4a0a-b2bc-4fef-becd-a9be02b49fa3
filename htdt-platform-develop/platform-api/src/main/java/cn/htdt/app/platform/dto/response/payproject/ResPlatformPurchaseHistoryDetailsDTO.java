package cn.htdt.app.platform.dto.response.payproject;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.PaymentChannelDetailsEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-08-10
 * @description 购买记录详情响应DTO
 **/
@Data
public class ResPlatformPurchaseHistoryDetailsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="订单编号")
    private String dealsNo;

    @ApiModelProperty("下单时间时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value="产品服务生效时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate serviceStart;

    @ApiModelProperty(value="产品服务失效时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate serviceEnd;

    @ApiModelProperty(value="商家编号")
    private String merchantNo;

    @ApiModelProperty(value="商家名称")
    private String merchantName;

    @ApiModelProperty(value="归属平台公司名称")
    private String companyName;

    @ApiModelProperty(value="归属分部名称")
    private String divisionName;

    @ApiModelProperty(value="订单类型1：线上 2：线下 3：赠送")
    private Integer dealsType;

    @ApiModelProperty(value="推荐人类型 1 服务商 2 铁军 3 服务商子账号 4 自定义")
    private String recommendType;

    @ApiModelProperty(value="推荐人编号")
    private String recommendNo;

    @ApiModelProperty(value="推荐人名称")
    private String recommendName;

    @ApiModelProperty(value="订单状态 1：待支付 2：交易成功 3：退款成功 4：交易关闭 5付款失败")
    private Integer dealsStatus;

    @ApiModelProperty(value="是否零售版 2：零售 1：免费")
    private Integer packageRetail;

    /**
     * 付款时间
     */
    @ApiModelProperty(value="付款时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;
    /**
     * 实付金额
     */
    @ApiModelProperty(value="实付金额")
    private BigDecimal payAmount;

    @ApiModelProperty("支付返回类型 (code)对应PaymentChannelDetailsEnum")
    @Converter(enumClass = PaymentChannelDetailsEnum.class, fieldName = "payTypeName")
    private String payType;

    @ApiModelProperty("支付返回类型名称")
    private String payTypeName;

    //支付状态

    @ApiModelProperty(value="订单商品详情")
    private List<ResPlatformPayProjectDealsItemDTO> projectDealsItemDTOS;

    @ApiModelProperty(value="发票信息")
    private ResPlatformPayProjectDealsDetailsDTO payProjectDealsDetailsDTO;

    @ApiModelProperty(value="返回成功的合同列表(套餐订单详情用)")
    List<ResPlatformPayDealsContractDTO> resPayProjectDealsContractDTOS;

    @ApiModelProperty(value = "合同状态 12:线上两方合同 13:线上3方合同 2:线下")
    private Integer contractState;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
