package cn.htdt.app.platform.dto.response.officialwebsiteinformationcollection;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 官网流量统计 返回实体类
 * </p>
 *
 * <AUTHOR>
 * @date 2021/11/4
 */
@Data
public class ResPlatformOfficialWebsiteFlowStatisticsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "今日官网实时浏览量")
    private Integer browseVolume;

    @ApiModelProperty(value = "今日官网实时访客数")
    private Integer visitorsNumber;
}
