package cn.htdt.app.platform.dto.response.requisitionorder;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import lombok.Data;

/**
 * @description 要货单出/入库模板下载dto
 * 
 * <AUTHOR>
 * @date 2023/5/30 10:58
 **/
@Data
public class ResPlatformStorageImportVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "仓库名称", index = 0)
    @ColumnWidth(45)
    private String warehouseName;
    /**
     * 商品编码
     */
    @ExcelProperty(value = "仓库编码", index = 1)
    @ColumnWidth(30)
    private String warehouseNo;

}
