package cn.htdt.app.platform.dto.response.content.protocol;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;

/**
 * 协议信息表
 *
 * <AUTHOR>
 * @since 2022-06-01
 */
@Data
@ApiModel(description = "协议信息返回实体类")
public class ResPlatformProtocolDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(notes = "id")
    private BigInteger id;

    /**
     * 协议编号
     */
    @ApiModelProperty(notes = "协议编号")
    private String protocolNo;

    /**
     * 标题
     */
    @ApiModelProperty(notes = "标题")
    private String title;

    /**
     * 协议类型，参照枚举ProtocolTypeEnum
     */
    @ApiModelProperty(notes = "协议类型:1001 隐私协议,1002 代理人协议,1003 云卖货协议")
    private String protocolType;

    /**
     * 协议类型，参照枚举ProtocolTypeEnum
     */
    @ApiModelProperty(notes = "协议类型描述")
    private String protocolTypeText;

    /**
     * 协议内容
     */
    @ApiModelProperty(notes = "协议内容")
    private String content;

    /**
     * 发布位置,枚举ReleasePositionEnum
     */
    @ApiModelProperty(notes = "发布位置:bossPc 千橙掌柜PC,bossApp 千橙掌柜APP,cash 千橙掌柜收银,hxg 汇享购微商城;多选时逗号分隔")
    private String releasePosition;

    /**
     * 发布位置,枚举ReleasePositionEnum
     */
    @ApiModelProperty(notes = "发布位置描述,逗号分隔")
    private String releasePositionText;

    /**
     * 创建人编号
     */
    @ApiModelProperty(notes = "创建人编号")
    private String createNo;

    /**
     * 创建人名称
     */
    @ApiModelProperty(notes = "创建人名称")
    private String createName;

    /**
     * 创建时间
     */
    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 修改人编号
     */
    @ApiModelProperty(notes = "修改人编号")
    private String modifyNo;

    /**
     * 修改人名称
     */
    @ApiModelProperty(notes = "修改人名称")
    private String modifyName;

    /**
     * 修改时间
     */
    @ApiModelProperty(notes = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

}
