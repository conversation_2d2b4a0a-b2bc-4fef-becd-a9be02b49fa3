package cn.htdt.app.platform.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 产品列表返回值
 * @Date 2021/8/2
 * @Param
 * @return
 **/
@Data
public class ResPlatformPayProjectKitListDTO extends ResPlatformPayProjectKitBaseListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("合同地址")
    private String projectContract;

    @ApiModelProperty("产品状态 0：删除 1：正常")
    private Integer projectStatus;

    @ApiModelProperty("同步给中台0：未同步 1：已经同步")
    private Integer updateFlag;

    @ApiModelProperty("产品有效天数")
    private Integer projectDays;

    @ApiModelProperty("是否试用 ：1否 2是")
    private Integer tryUseFlag;

    @ApiModelProperty("下拉框 ：1试用 2零售")
    private List<Integer> packageRetailList;

    @ApiModelProperty("是否存在免费试用订单 ：1否 2是")
    private Integer haveFreeDealsFlag;

    @ApiModelProperty("运维服务费")
    private String servicePrice;

    @ApiModelProperty("智能硬件押金")
    private String depositPrice;

    @ApiModelProperty("是否关联其他产品1：否 2：是")
    private Integer tryJoinFlag;

    @ApiModelProperty("是否有搭配产品 1:否 2:是")
    private Integer tryCollocationFlag;

    @ApiModelProperty("产品类别")
    private String productCode;

    @ApiModelProperty("产品标签：1->订阅套餐，2-> 增值套餐")
    private Integer productLabel;

    @ApiModelProperty("特殊关联场景code")
    private String sceneAssociationCode;

    @ApiModelProperty("产品上下架 1上架 2下架")
    private Integer projectOnline;

    @ApiModelProperty("是否是聚合服务 1否 2是")
    private Integer isPolymerization;

    @ApiModelProperty("是否为滴灌通套餐 1：否 2：是")
    private Integer mciFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
