package cn.htdt.app.platform.dto.response.payproject;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 服务市场产品聚合返回值
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-11
 */
@Data
public class ResPlatformPayProjectPolymerizationKitDTO implements Serializable {

    @ApiModelProperty("产品聚合唯一编码")
    private String polymerizationNo;

    @ApiModelProperty("聚合产品类型：1:会员套餐 2:增值服务  枚举:ProjectTypeEnum")
    private Integer projectType;

    @ApiModelProperty("聚合产品名称")
    private String polymerizationName;

    @ApiModelProperty("聚合产品图片地址")
    private String polymerizationImg;

    @ApiModelProperty("聚合产品简介")
    private String polymerizationIntroduction;

    @ApiModelProperty("聚合产品内层排序")
    private Integer showPolymerizationR;

    @ApiModelProperty("聚合产品上下架 1：上架 2：下架")
    private Integer polymerizationOnline;

    @ApiModelProperty("聚合产品最低价")
    private BigDecimal lowPrice;


}
