package cn.htdt.app.platform.dto.response.content.shopdeco;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(description = "设置装修模板返回实体类")
public class ResPlatformShopDecoFormworkDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "店铺装修模板编码")
    private String shopDecoFormworkNo;
}
