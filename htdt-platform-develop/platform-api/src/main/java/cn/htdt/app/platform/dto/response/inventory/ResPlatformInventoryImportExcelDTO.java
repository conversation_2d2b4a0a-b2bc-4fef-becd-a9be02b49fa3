package cn.htdt.app.platform.dto.response.inventory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点详细 导入DTO
 **/
@Data

public class ResPlatformInventoryImportExcelDTO extends BaseRowModel {

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 0)
    private String goodsName;

    /**
     * 商品ID
     */
    private String goodsNo;

    /**
     * 实盘数量
     */
    @ExcelProperty(value = "实盘数量",index = 1)
    private BigDecimal inventoryStockNum;

}
