package cn.htdt.app.platform.dto.response.warehousestockflow;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.time.LocalDateTime;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import com.fasterxml.jackson.annotation.JsonFormat;

import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import lombok.Data;

/**
 *
 * 功能描述: 仓库库存流水响应dto
 * 
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformWareHouseFlagStockFlowExcelDTO extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1350712595427501473L;
    /**
     * 仓库流水id
     */
    @ExcelIgnore
    private BigInteger id;
    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "商品编号", index = 0)
    private String goodsNo;
    /**
     * 商品名称
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "商品名称", index = 1)
    private String goodsName;
    /**
     * 计量单位名称
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "计量单位名称", index = 2)
    private String calculationUnitName;

    /**
     * 操作类型形式value(1001:加库 1002：减库)
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "操作类型", index = 3)
    private String operTypeValue;
    /**
     * 操作库存数量
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "操作库存数量", index = 4)
    private BigDecimal stockNum;

    /**
     * 当前库存数量
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "当前库存数量", index = 5)
    private BigDecimal currentStockNum;

    /**
     * 店铺名称，对应orgName
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "店铺名称", index = 6)
    private String storeName;

    /**
     * 来源单据类型形式value(1001:订单下单 1002:销售退货单 1003:调拨出库 1004:调拨入库 1005:采购入库 1006:采购退货出库 1007:盘点入库 1008:盘点出库 1009:发货出库)
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "来源单据类型", index = 7)
    private String billTypeValue;
    /**
     * 单据编号
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "单据编号", index = 8)
    private String billCode;
    /**
     * 转换率(辅转主)
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "转换率", index = 9)
    private BigDecimal conversionRate;

    /**
     * 辅计量单位
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "辅计量单位", index = 10)
    private String assistCalculationUnitName;

    /**
     * 删除状态码形式value
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "删除状态码", index = 11)
    private String deleteFlagValue;
    /**
     * 流水创建日期
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "流水创建日期", index = 12, converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "操作人", index = 13)
    private String modifyName;

}
