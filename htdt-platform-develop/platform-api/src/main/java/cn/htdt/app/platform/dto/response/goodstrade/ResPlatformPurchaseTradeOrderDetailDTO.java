package cn.htdt.app.platform.dto.response.goodstrade;

import cn.htdt.app.platform.dto.response.goods.ResPlatformGoodsPictureDTO;
import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 应用层出参
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformPurchaseTradeOrderDetailDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "条形码")
    private String barcode;

    @ApiModelProperty(value = "零售价")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "库存")
    private BigDecimal realStockNum;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "轮播图片集合")
    private List<ResPlatformGoodsPictureDTO> pictureList;

    @ApiModelProperty(value = "详情图片集合")
    private List<ResPlatformGoodsPictureDTO> detailPictureList;

    @ApiModelProperty(value = "商品SKU编码")
    private String skuCode;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

}
