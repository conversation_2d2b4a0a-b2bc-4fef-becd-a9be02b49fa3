package cn.htdt.app.platform.dto.response.fileopt.goods;

import cn.htdt.common.utils.ExcelUtil;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.HeadFontStyle;
import lombok.Data;

import java.io.Serializable;

import static org.apache.poi.ss.usermodel.Font.COLOR_RED;

/**
 * <AUTHOR>
 * @Date 2021-11-19
 * @Description 普通商品导出错误信息导出
 **/
@Data
public class ResPlatformGoodsCommonImportErrorDTO implements Serializable {
    /**
     * 序列化
     */
    private static final long serialVersionUID = 8382745965259598544L;

    /*@ExcelProperty(value = "*商品形式", index = 0)
    @ColumnWidth(14)
    @HeadFontStyle(color = COLOR_RED)
    @ExcelUtil.ExplicitConstraint(source = {"普通商品导入"})
    private String importScene;*/

    @ExcelProperty(value = "*商品名称", index = 0)
    @ColumnWidth(40)
    @HeadFontStyle(color = COLOR_RED)
    private String goodsName;

    @ExcelProperty(value = "*零售价", index = 1)
    @ColumnWidth(14)
    @HeadFontStyle(color = COLOR_RED)
    private String retailPrice;

    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位", index = 2)
    @ColumnWidth(14)
    @HeadFontStyle(color = COLOR_RED)
    private String calculationUnitName;

    @ExcelProperty(value = "商品条形码", index = 3)
    @ColumnWidth(20)
    private String barcode;

    @ExcelProperty(value = "一级销售类目", index = 4)
    @ColumnWidth(20)
    private String firstCategoryName;

    @ExcelProperty(value = "二级销售类目", index = 5)
    @ColumnWidth(20)
    private String secondCategoryName;

    @ExcelProperty(value = "三级销售类目", index = 6)
    @ColumnWidth(20)
    private String thirdCategoryName;

    @ExcelProperty(value = "四级销售类目", index = 7)
    @ColumnWidth(20)
    private String fourCategoryName;

    @ExcelProperty(value = "末级店铺类目名称", index = 8)
    @ColumnWidth(20)
    private String storeCategoryName;

    /**
     * 末级店铺类目编码
     */
    @ExcelIgnore
    private String storeCategoryNo;

    @ExcelProperty(value = "商品品牌名称", index = 9)
    @ColumnWidth(20)
    private String brandName;

    /**
     * 品牌编码
     */
    @ExcelIgnore
    private String brandNo;

    @ExcelProperty(value = "是否入仓", index = 10)
    @ColumnWidth(12)
    @ExcelUtil.ExplicitConstraint(source = {"是","否"},indexNum = 10)
    private String warehouseFlag;

    @ExcelProperty(value = "采购价", index = 11)
    @ColumnWidth(9)
    private String purchasePrice;

    /**
     * 总库存数量，即实体库存
     */
    @ExcelProperty(value = "实体库存", index = 12)
    @ColumnWidth(12)
    private String realStockNum;

    /**
     * 计量单位编码
     */
    @ExcelIgnore
    private String calculationUnitNo;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商", index = 13)
    @ColumnWidth(40)
    private String supplierName;

    /**
     * 供应商编码
     */
    @ExcelIgnore
    private String supplierNo;

    @ExcelProperty(value = "是否启用串码", index = 14)
    @ColumnWidth(20)
    @ExcelUtil.ExplicitConstraint(source = {"是","否"},indexNum = 14)
    private String imeiFlag;

    @ExcelProperty(value = "错误信息", index = 15)
    @ColumnWidth(30)
    @HeadFontStyle(color = COLOR_RED)
    private String errorMsg;
}