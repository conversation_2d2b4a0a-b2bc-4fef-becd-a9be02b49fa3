package cn.htdt.app.platform.dto.response.returnorder;

import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * @Date 2020-09-06
 * @Description 退货商家收货地址信息
 * <AUTHOR>
 **/
@Data
public class ResPlatformMerchantReturnAddressDTO implements Serializable {

    @ApiModelProperty(value = "退货商家收货人姓名")
    private String returnName;

    @ApiModelProperty(value = "退货商家收货人手机")
    private String returnMobile;

    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "returnMobile")
    @ApiModelProperty(value = "退货商家收货人手机")
    private String dsReturnMobile;

    @ApiModelProperty(value = "退货商家收货人地址")
    private String returnAddress;

    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene = DecipherScene.COMMON, fieldName = "returnAddress")
    @ApiModelProperty(value = "退货商家收货人地址")
    private String dsReturnAddress;

    @ApiModelProperty(value = "退货商家收货人省份")
    private String returnProvince;

    @ApiModelProperty(value = "退货商家收货人省份code")
    private String returnProvinceCode;

    @ApiModelProperty(value = "退货商家收货人城市")
    private String returnCity;

    @ApiModelProperty(value = "退货商家收货人城市code")
    private String returnCityCode;

    @ApiModelProperty(value = "退货商家收货人县区")
    private String returnCounty;

    @ApiModelProperty(value = "退货商家收货人县区code")
    private String returnCountyCode;

    @ApiModelProperty(value = "退货商家收货人区镇")
    private String returnArea;

    @ApiModelProperty(value = "退货商家收货人区镇code")
    private String returnAreaCode;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
