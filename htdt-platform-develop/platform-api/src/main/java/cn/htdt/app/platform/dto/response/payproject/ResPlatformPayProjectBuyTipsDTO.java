package cn.htdt.app.platform.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ResPlatformPayProjectBuyTipsDTO implements Serializable {
    private static final long serialVersionUID = -6973168097008638833L;

    @ApiModelProperty("提示信息")
    private String showMsg;

    @ApiModelProperty("按钮显示文案")
    private String buttonText;

    @ApiModelProperty("跳转链接")
    private String redirectUrl;
}
