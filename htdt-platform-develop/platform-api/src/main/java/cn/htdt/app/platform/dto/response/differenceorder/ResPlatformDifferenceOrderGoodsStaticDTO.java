package cn.htdt.app.platform.dto.response.differenceorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 差异单商品行统计DTO
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
public class ResPlatformDifferenceOrderGoodsStaticDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("差异品项数")
    private BigDecimal differenceCount;

    /**
     * 差异金额
     */
    @ApiModelProperty(value = "差异金额")
    private BigDecimal differenceAmount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
