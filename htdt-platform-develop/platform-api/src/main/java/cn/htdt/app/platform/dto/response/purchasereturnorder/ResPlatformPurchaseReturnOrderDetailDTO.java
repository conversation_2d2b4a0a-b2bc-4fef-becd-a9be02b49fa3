package cn.htdt.app.platform.dto.response.purchasereturnorder;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 *
 * 功能描述: 采购退货单响应dto
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformPurchaseReturnOrderDetailDTO implements Serializable {
    private static final long serialVersionUID = 5420048401295085859L;
    /**
     *退货单编码
     */
    @ApiModelProperty(value = "退货单编码")
    private String returnCode;
    /**
     *退货日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "退货日期")
    private LocalDateTime returnDate;
    /**
     *采购单编码
     */
    @ApiModelProperty(value = "采购单编码")
    private String purchaseCode;
    /**
     *供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    /**
     *采购日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "采购日期")
    private LocalDateTime purchaseDate;
    /**
     *附件名称
     */
    @ApiModelProperty(value = "附件名称")
    private String attachName;
    /**
     *附件路径
     */
    @ApiModelProperty(value = "附件路径")
    private String attachPath;
    /**
     *备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     *发货联系人
     */
    @ApiModelProperty(value = "发货联系人")
    private String sendContactName;
    /**
     *发货联系人手机号
     */
    @ApiModelProperty(value = "发货联系人手机号")
    private String sendContactMobile;
    /**
     *发货联系人手机号
     */
    @ApiModelProperty(value = "发货联系人手机号(加密)")
    private String dsSendContactMobile;
    /**
     *发货地址省份code
     */
    @ApiModelProperty(value = "发货地址省份code")
    private String sendProvinceCode;
    /**
     *发货地址省份名称
     */
    @ApiModelProperty(value = "发货地址省份名称")
    private String sendProvinceName;
    /**
     *发货地址城市code
     */
    @ApiModelProperty(value = "发货地址城市code")
    private String sendCityCode;
    /**
     *发货地址城市名称
     */
    @ApiModelProperty(value = "发货地址城市名称")
    private String sendCityName;
    /**
     *发货地址地区code
     */
    @ApiModelProperty(value = "发货地址地区code")
    private String sendDistrictCode;
    /**
     *发货地址地区名称
     */
    @ApiModelProperty(value = "发货地址地区名称")
    private String sendDistrictName;
    /**
     *发货地址镇code
     */
    @ApiModelProperty(value = "发货地址镇code")
    private String sendTownCode;
    /**
     *发货地址镇名称
     */
    @ApiModelProperty(value = "发货地址镇名称")
    private String sendTownName;
    /**
     *发货详细地址
     */
    @ApiModelProperty(value = "发货详细地址")
    private String sendDetailAddress;
    /**
     *发货详细地址
     */
    @ApiModelProperty(value = "发货详细地址(加密)")
    private String dsSendDetailAddress;
    /**
     *收货联系人
     */
    @ApiModelProperty(value = "收货联系人")
    private String receiveContactName;
    /**
     *收货联系人手机号
     */
    @ApiModelProperty(value = "收货联系人手机号")
    private String receiveContactMobile;
    /**
     *收货联系人手机号
     */
    @ApiModelProperty(value = "收货联系人手机号(加密)")
    private String dsReceiveContactMobile;
    /**
     *收货地址省份code
     */
    @ApiModelProperty(value = "收货地址省份code")
    private String receiveProvinceCode;
    /**
     *收货地址省份名称
     */
    @ApiModelProperty(value = "收货地址省份名称")
    private String receiveProvinceName;
    /**
     *收货地址城市code
     */
    @ApiModelProperty(value = "收货地址城市code")
    private String receiveCityCode;
    /**
     *收货地址城市名称
     */
    @ApiModelProperty(value = "收货地址城市名称")
    private String receiveCityName;
    /**
     *收货地址地区code
     */
    @ApiModelProperty(value = "收货地址地区code")
    private String receiveDistrictCode;
    /**
     *收货地址地区名称
     */
    @ApiModelProperty(value = "收货地址地区名称")
    private String receiveDistrictName;
    /**
     *收货地址镇code
     */
    @ApiModelProperty(value = "收货地址镇code")
    private String receiveTownCode;
    /**
     *收货地址镇名称
     */
    @ApiModelProperty(value = "收货地址镇名称")
    private String receiveTownName;
    /**
     *收货详细地址
     */
    @ApiModelProperty(value = "收货详细地址")
    private String receiveDetailAddress;
    /**
     *收货详细地址
     */
    @ApiModelProperty(value = "收货详细地址(加密)")
    private String dsReceiveDetailAddress;
    /**
     *商家ID
     */
    @ApiModelProperty(value = "商家ID")
    private String merchantNo;
    /**
     *商家名称
     */
    @ApiModelProperty(value = "商家名称")
    private String merchantName;
    /**
     *店铺ID，对应orgId
     */
    @ApiModelProperty(value = "店铺ID，对应orgId")
    private String storeNo;
    /**
     *店铺名称，对应orgName
     */
    @ApiModelProperty(value = "店铺名称，对应orgName")
    private String storeName;
    /**
     *归属平台编号
     */
    @ApiModelProperty(value = "归属平台编号")
    private String companyNo;
    /**
     *归属平台名称
     */
    @ApiModelProperty(value = "归属平台名称")
    private String companyName;
    /**
     *归属分部编号
     */
    @ApiModelProperty(value = "归属分部编号")
    private String branchNo;
    /**
     *归属分部名称
     */
    @ApiModelProperty(value = "归属分部名称")
    private String branchName;
    /**
     * 归属平台类型(1.运营 2.商家 3.店铺)
     */
    @ApiModelProperty(value = "归属平台类型(1.运营 2.商家 3.店铺)")
    private String platformType;
    /**
     * 可退商品数据
     */
    private List<ResPlatformAddReturnGoodsDTO> returnGoodsList;

    /**
     * 红字不可退商品数据
     */
    private List<ResPlatformAddReturnGoodsDTO> notReturnGoodsList;

}
