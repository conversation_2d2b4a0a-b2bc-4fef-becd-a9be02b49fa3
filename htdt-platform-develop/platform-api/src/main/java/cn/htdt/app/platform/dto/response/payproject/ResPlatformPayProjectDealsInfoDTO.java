package cn.htdt.app.platform.dto.response.payproject;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;


@Data
public class ResPlatformPayProjectDealsInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品类型：1:会员套餐 2:增值服务  枚举:ProjectTypeEnum
     */
    private Integer payProjectType;

    /**
     * 付费产品唯一标识，对应PAY_PROJECT_KIT表中的PROJECT_KIT_NO字段
     */
    private String payProjectKitNo;

    /**
     * 订单编号
     */
    private String dealsNo;

    /**
     * 订单类型1：线上 2：线下 3：赠送
     */
    private Integer dealsType;

    /**
     * 是否续费0：否 1：是
     */
    private Integer renewFee;

    /**
     * 订单状态 1：待支付 2：交易成功 3：退款成功 4：交易关闭 5付款失败
     */
    private Integer dealsStatus;

    /**
     * 套餐生效时间
     */
    private LocalDate packageStart;

    /**
     * 套餐失效时间
     */
    private LocalDate packageEnd;

    /**
     * 套餐类型 此字段作废
     */
    private Integer packageType;

    /**
     * 套餐金额
     */
    private BigDecimal packageAmount;

    /**
     * 是否零售版 0：零售 1：免费
     */
    private Integer packageRetail;

    /**
     * 实付金额
     */
    private BigDecimal payAmount;

    /**
     * 付款方式
     */
    private String payType;

    /**
     * 付款时间
     */
    private LocalDateTime payTime;

    /**
     * 收款银行0：招商银行南京珠江路支行 1：中国工商银行南京山西路支行 2：上海浦东发展银行南京城北支行
     */
    private Integer collectBank;

    /**
     * 合同地址
     */
    private String contract;

    /**
     * 会员编码
     */
    private String memberNo;

    /**
     * 店铺编码
     */
    private String storeNo;

    /**
     * 会员店名称
     */
    private String orgName;

    /**
     * 收货人姓名
     */
    private String represeNtative;

    /**
     * 收货人手机号
     */
    private String represeNtativePhone;

    /**
     * 归属平台公司
     */
    private String CompanyNo;

    /**
     * 归属分布
     */
    private String DivisionNo;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 下单设备 0：PC 1：IOS 2：Android
     */
    private Integer sourceType;

    /**
     * 短信条数
     */
    private Integer smsCount;

    /**
     * 0：未充值 1：无需充值 2：充值成功 3：充值失败
     */
    private Integer smsStatus;

    /**
     * 是否已经更新0：未更新 1：已经更新
     */
    private Integer updateFlag;

    /**
     * 是否开发票：0：不需要发票，1：需要开发票
     */
    private Integer needInvoiceFlag;

    /**
     * 会员服务费
     */
    private BigDecimal payMemberService;

    /**
     * 运维服务费
     */
    private BigDecimal payYwService;

    /**
     * 智能硬件押金
     */
    private BigDecimal payDepositService;

    /**
     * 产品服务生效时间
     */
    private LocalDate serviceStart;

    /**
     * 产品服务失效时间
     */
    private LocalDate serviceEnd;

    /**
     * 是否签订合同  1 ：是 0否
     */
    private String contractFlag;

    /**
     * 支付成功订单号
     */
    private String tradeNo;

    /**
     * 是否已发货 1:已发货 2:已签收
     */
    private Integer goodsStatus;

    /**
     * vip订单号，vip套餐7.0砖石版拆出来的订单
     */
    private String vipDealsNo;

    /**
     * 是否退款  1:已退款 0未退款
     */
    private Integer refundStatus;

    /**
     * 有实物邮寄产品是否确认收货 0：否 1：是
     */
    private Integer confirmReceiptFlag;

    /**
     * 是否已经删除，默认1未删除，其余已删除
     */
    private Integer disableFlag;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 产品标签：1->订阅套餐，2-> 增值套餐
     */
    private Integer productLabell;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 归属平台公司名称
     */
    private String companyName;

    /**
     * 归属分部名称
     */
    private String divisionName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
