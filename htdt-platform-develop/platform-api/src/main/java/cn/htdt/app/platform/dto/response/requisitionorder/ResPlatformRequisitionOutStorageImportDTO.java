package cn.htdt.app.platform.dto.response.requisitionorder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import cn.htdt.goodsprocess.dto.response.requisitionorder.ResRequisitionGoodsWarehouseDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 要货单出/入库商品行导入模板 字段
 * 
 * <AUTHOR>
 * @date 2023/5/30 15:52
 **/
@Data
public class ResPlatformRequisitionOutStorageImportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "要货单编号", required = true, position = 0)
    @ExcelProperty(value = "要货单编号", index = 0)
    @ColumnWidth(45)
    private String requisitionNo;

    @ApiModelProperty(value = "要货店铺", required = true, position = 1)
    @ExcelProperty(value = "要货店铺", index = 1)
    @ColumnWidth(45)
    private String storeName;

    @ApiModelProperty(value = "商家商品编号", required = true, position = 2)
    @ExcelProperty(value = "商品ID", index = 2)
    @ColumnWidth(30)
    private String merchantGoodsNo;

    @ApiModelProperty(value = "商家商品名称", position = 3)
    @ExcelProperty(value = "商品名称", index = 3)
    @ColumnWidth(45)
    private String merchantGoodsName;

    @ApiModelProperty(value = "店铺商品编号", required = true, position = 4)
    @ExcelIgnore
    private String goodsNo;

    @ApiModelProperty(value = "店铺商品名称", position = 5)
    @ExcelIgnore
    private String goodsName;

    @ApiModelProperty(value = "商品条码", position = 6)
    @ExcelProperty(value = "商品条码", index = 4)
    @ColumnWidth(20)
    private String barcode;

    @ApiModelProperty(value = "要货单位", position = 7)
    @ExcelProperty(value = "要货单位", index = 5)
    @ColumnWidth(20)
    private String requisitionUnit;

    @ApiModelProperty(value = "计量单位ID", position = 8)
    @ExcelIgnore
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位", position = 9)
    @ExcelIgnore
    private String calculationUnitName;

    @ApiModelProperty(value = "辅计量单位ID", position = 10)
    @ExcelIgnore
    private String assistCalculationUnitNo;

    @ApiModelProperty(value = "辅计量单位", position = 11)
    @ExcelIgnore
    private String assistCalculationUnitName;

    @ApiModelProperty(value = "主计量单位对应关系数值", position = 12)
    @ExcelIgnore
    private BigDecimal mainUnitNum;

    @ApiModelProperty(value = "辅计量单位对应关系数值", position = 13)
    @ExcelIgnore
    private BigDecimal assistUnitNum;

    @ApiModelProperty(value = "是否使用辅计量单位采购:1-否;2-是", position = 14)
    @ExcelIgnore
    private Integer standardFlag;

    @ApiModelProperty(value = "商家商品单价", position = 15)
    @ExcelIgnore
    private BigDecimal itemPrice;

    @ApiModelProperty(value = "要货数量", position = 16)
    @ExcelProperty(value = "要货数量", index = 6)
    @ColumnWidth(20)
    private BigDecimal requisitionNum;

    @ApiModelProperty(value = "已出库数量", position = 17)
    @ExcelProperty(value = "已出库数量", index = 7)
    @ColumnWidth(20)
    private BigDecimal outStorageCount;

    @ApiModelProperty(value = "未出库数量", position = 18)
    @ExcelProperty(value = "未出库数量", index = 8)
    @ColumnWidth(20)
    private BigDecimal treatOutStorageCount;

    @ApiModelProperty(value = "本次出库数量", position = 19)
    @ExcelProperty(value = "本次出库数量", index = 9)
    @ColumnWidth(20)
    private BigDecimal thisOutStorageCount;

    @ApiModelProperty(value = "本次出库金额", position = 20)
    @ExcelProperty(value = "本次出库金额", index = 10)
    @ColumnWidth(20)
    private BigDecimal thisOutStorageAmount;

    @ApiModelProperty(value = "是否出仓 1-否 2-是", position = 21)
    @ExcelIgnore
    private Integer warehouseFlag;

    @ApiModelProperty(value = "本次仓库编号", position = 22)
    @ExcelIgnore
    private String warehouseNo;

    @ApiModelProperty(value = "本次仓库名称", position = 23)
    @ExcelProperty(value = "本次出库仓库", index = 11)
    @ColumnWidth(20)
    private String warehouseName;

    @ApiModelProperty(value = "商家商品关联仓库信息", position = 24)
    @ExcelIgnore
    private List<ResRequisitionGoodsWarehouseDTO> merchantGoodsWarehouseList;

}
