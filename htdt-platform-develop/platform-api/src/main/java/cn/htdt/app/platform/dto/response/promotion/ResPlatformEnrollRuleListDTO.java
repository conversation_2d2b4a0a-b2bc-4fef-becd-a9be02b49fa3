package cn.htdt.app.platform.dto.response.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 报名活动列表
 * <AUTHOR>
 * @date 2021/4/1 16:16
 */
@Data
public class ResPlatformEnrollRuleListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String promotionName;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    /**
     * 活动店铺类型(1001:需店铺报名参与 1002:指定店铺直接参与)
     */
    @ApiModelProperty(value = "活动店铺类型(1001:需店铺报名参与 1002:指定店铺直接参与)")
    private String storeType;

    /**
     * 上下架状态（1：未上架 2：上架）
     */
    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;
    /**
     * 参与店铺数量
     */
    @ApiModelProperty(value = "参与店铺数量")
    private Integer joinStoreNum;

    /**
     * 已受理数量
     * */
    @ApiModelProperty(value = "已受理数量")
    private Integer applyNum;
    /**
     * 待处理数量
     */
    @ApiModelProperty(value = "待处理数量")
    private Integer waitNum;

    /**
     * 已交付数量
     */
    @ApiModelProperty(value = "已交付数量")
    private Integer deliveredNum;

    @ApiModelProperty(value = "1-未开始 2-进行中 3-已结束")
    private Integer promotionStatus;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;
    /**
     *  删除按钮是否展示  1 否  2 是
     */
    @ApiModelProperty(value = "删除按钮是否展示  1 否  2 是")
    private Integer delButtonFlag;

    @ApiModelProperty(value = "更新人")
    private String modifyName;

    /**
     * 店铺报名范围（1001:全部店铺 1002：指定区域 1003：导入店铺）
     */
    @ApiModelProperty(value = "店铺报名范围（1001:全部店铺 1002：指定区域 1003：导入店铺）")
    private String applyType ;
    /**
     * 报名区域范围
     */
    @ApiModelProperty(value = "报名区域范围")
    private String applyArea;

    /**
     * 报名区域范围
     */
    @ApiModelProperty(value = "报名区域范围")
    private String applyAreaName;

    /**
     * 报名说明
     */
    @ApiModelProperty(value = "报名说明")
    private String enrollExplain;
    /**
     * APP图片类型(1:默认图片 2:自定义图片)
     */
    @ApiModelProperty(value = "APP图片类型(1:默认图片 2:自定义图片)")
    private Integer appPictureType;

    /**
     * APP图片url
     */
    @ApiModelProperty(value = "APP图片url")
    private String appPictureUrl;

    @ApiModelProperty(value = "图片类型(1:默认图片 2:自定义图片)")
    private Integer promotionPictureType;

    @ApiModelProperty(value = "图片url")
    private String promotionPictureUrl;
    /**
     * 文件名称
     */
    @ApiModelProperty(value = "文件名称")
    private String fileName;
    /**
     * 文件大小
     */
    @ApiModelProperty(value = "文件大小")
    private String fileSize;

    @ApiModelProperty(value = "活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;
}
