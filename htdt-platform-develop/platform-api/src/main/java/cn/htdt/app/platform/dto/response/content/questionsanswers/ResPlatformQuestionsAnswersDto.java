package cn.htdt.app.platform.dto.response.content.questionsanswers;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 问答表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Data
public class ResPlatformQuestionsAnswersDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(notes = "id")
    private BigInteger id;

    /**
     * 问答编号
     */
    @ApiModelProperty(notes = "问答编号")
    private String questionsNo;

    /**
     * 标题搜索关键词
     */
    @ApiModelProperty(notes = "标题搜索关键词")
    private String titleSearchKey;

    /**
     * 标题
     */
    @ApiModelProperty(notes = "标题")
    private String title;

    /**
     * 正文内容
     */
    @ApiModelProperty(notes = "正文内容")
    private String content;

    /**
     * 一级分类
     */
    @ApiModelProperty(notes = "一级分类")
    private String firstCategory;

    /**
     * 一级分类名称
     */
    @ApiModelProperty(notes = "一级分类名称")
    private String firstCategoryName;

    /**
     * 二级分类名称
     */
    @ApiModelProperty(notes = "二级分类名称")
    private String secondCategoryName;

    /**
     * 二级分类
     */
    @ApiModelProperty(notes = "二级分类")
    private String secondCategory;

    /**
     * 分享状态 1不允许 2允许
     */
    @ApiModelProperty(notes = "分享状态 1不允许 2允许")
    private Integer shareFlag;

    /**
     * 下载状态 1不允许 2允许
     */
    @ApiModelProperty(notes = "下载状态 1不允许 2允许")
    private Integer downloadFlag;

    /**
     * 发布状态 1不允许 2允许
     */
    @ApiModelProperty(notes = "发布状态 1不允许 2允许")
    private Integer releaseStatus;

    /**
     * 发布位置
     */
    @ApiModelProperty(notes = "发布位置")
    private String releasePosition;

    /**
     * 发布位置集合
     */
    @ApiModelProperty(notes = "发布位置集合")
    private List<String> releasePositionList;

    /**
     * 发布时间
     */
    @ApiModelProperty(notes = "发布时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime releaseTime;

    /**
     * 评论数
     */
    @ApiModelProperty(notes = "评论数")
    private Integer commentCount;

    /**
     * 收藏数
     */
    @ApiModelProperty(value = "收藏数")
    private Integer collectCount;

    /**
     * 点赞数
     */
    @ApiModelProperty(notes = "点赞数")
    private Integer likeCount;

    /**
     * 分享数
     */
    @ApiModelProperty(notes = "分享数")
    private Integer shareCount;

    /**
     * 浏览数
     */
    @ApiModelProperty(notes = "浏览数")
    private Integer browseCount;

    /**
     * 创建人
     */
    @ApiModelProperty(notes = "创建人")
    private String createNo;

    /**
     * 最后一次修改人
     */
    @ApiModelProperty(notes = "最后一次修改人")
    private String modifyNo;

    /**
     * 发布时间
     */
    @ApiModelProperty(notes = "修改时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime modifyTime;

    @ApiModelProperty(notes = "点赞标识 1:未点赞 2:已点赞")
    private Integer likeFlag;

    @ApiModelProperty(notes = "收藏标识 1:未收藏 2:已收藏")
    private Integer collectFlag;

    @ApiModelProperty(notes = "首末行标识,1首行2末行")
    private Integer lineFlag = 0;

    /**
     * 最后一次运营编辑时间
     */
    @ApiModelProperty(notes = "最后一次运营编辑时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime updateTime;

}
