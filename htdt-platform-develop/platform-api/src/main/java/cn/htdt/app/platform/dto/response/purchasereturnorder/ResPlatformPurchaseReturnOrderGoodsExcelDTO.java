package cn.htdt.app.platform.dto.response.purchasereturnorder;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * 功能描述: 采购退货单待出库导出excel响应dto
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformPurchaseReturnOrderGoodsExcelDTO extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = -6700924265909584411L;
    /**
     *商品编码
     */
    @ExcelProperty(value = "商品编码",index = 0)
    @ColumnWidth(40)
    private String goodsNo;
    /**
     *商品名称
     */
    @ExcelProperty(value = "商品名称",index = 1)
    @ColumnWidth(40)
    private String goodsName;
    /**
     *采购单位
     */
    @ExcelProperty(value = "采购单位",index = 2)
    @ColumnWidth(40)
    private String purchaseUnit;

    /**
     *采购数量
     */
    @ExcelProperty(value = "采购数量",index = 3)
    @ColumnWidth(40)
    private BigDecimal purchaseNum;

    /**
     *本次退货数量
     */
    @ExcelProperty(value = "采购数量",index = 4)
    @ColumnWidth(40)
    private BigDecimal returnRequestCount;
    /**
     *是否入仓形式VALUE 1-有实体仓;2-无实体仓
     */
    @ExcelProperty(value = "是否入仓",index = 5)
    @ColumnWidth(40)
    private String warehouseTypeValue;
    /**
     * 待出库数量
     */
    @ExcelProperty(value = "待出库数量",index = 6)
    @ColumnWidth(40)
    private BigDecimal waitingWarehouseOut;

    /**
     *出库数量
     */
    @ExcelProperty(value = "已出库数量",index = 7)
    @ColumnWidth(40)
    private BigDecimal warehouseOutCount;
    /**
     *仓库名称
     */
    @ExcelProperty(value = "仓库名称",index = 8)
    @ColumnWidth(40)
    private String warehouseName;
    /**
     * 出库仓库可售库存
     */
    @ExcelProperty(value = "出库仓库可售库存",index = 9)
    @ColumnWidth(40)
    private BigDecimal availableStockNum;

    /**
     * 转换率(辅转主)
     */
    @ExcelProperty(value = "转换率",index = 10)
    @ColumnWidth(40)
    private BigDecimal conversionRate;

    /**
     * 计量单位ID
     */
    @ExcelProperty(value = "计量单位",index = 11)
    @ColumnWidth(40)
    private String calculationUnitName;


}
