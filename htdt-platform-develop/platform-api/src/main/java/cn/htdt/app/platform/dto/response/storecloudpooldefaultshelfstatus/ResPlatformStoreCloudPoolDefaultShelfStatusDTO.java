package cn.htdt.app.platform.dto.response.storecloudpooldefaultshelfstatus;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 店铺云池默认上架状态 响应DTO
 *
 * <AUTHOR>
 * @date 2021/2/18
 **/
@Data
public class ResPlatformStoreCloudPoolDefaultShelfStatusDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    /**
     * 是否默认上架，默认2，1否，2是
     */
    @ApiModelProperty(value = "是否默认上架，默认2，1否，2是")
    private Integer defaultOnShelves;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
