package cn.htdt.app.platform.dto.response.payproject;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021-08-11
 * @description 付费产品订单响应DTO
 **/
@Data
public class ResPlatformPayProjectDealsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="订单编号")
    private String dealsNo;

    @ApiModelProperty(value="商家编号")
    private String merchantNo;

    @ApiModelProperty(value="商家名称")
    private String merchantName;

    @ApiModelProperty(value="归属平台公司名称")
    private String companyName;

    @ApiModelProperty(value="归属分部名称")
    private String divisionName;

    @ApiModelProperty(value="订单标签：1->订阅套餐，2-> 增值套餐")
    private Integer productLabell;

    @ApiModelProperty(value="套餐名称")
    private String packageName;

    @ApiModelProperty(value="是否试用 1：否 2：是")
    private Integer packageRetail;

    @ApiModelProperty(value="实收金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal payAmount;

    @ApiModelProperty(value="应付金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal originalPrice;

    @ApiModelProperty(value="订单类型1：线上 2：线下 3：赠送")
    private Integer dealsType;

    @ApiModelProperty(value="订单状态 1：待支付 2：交易成功 3：退款成功 4：交易关闭 5付款失败")
    private Integer dealsStatus;

    @ApiModelProperty(value="是否开发票：1：不需要发票，2：需要开发票")
    private Integer needInvoiceFlag;

    @ApiModelProperty(value="付款时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime payTime;

    @ApiModelProperty(value="创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value="会员编码")
    private String merberCode;

    @ApiModelProperty(value="协议是否确认 1：否 2：是")
    private Integer agreementConfirmFlag;

    @ApiModelProperty(value="协议确认时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime confirmTime;

    @ApiModelProperty(value="协议确认人账号")
    private String confirmOperatorid;

    @ApiModelProperty(value="是否为滴灌通套餐订单 1：否 2：是")
    private Integer mciFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
