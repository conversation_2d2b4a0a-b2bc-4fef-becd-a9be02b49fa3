package cn.htdt.app.platform.dto.response.purchasereturnorder;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.metadata.BaseRowModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
/**
 *
 * 功能描述: 采购退货单待出库响应dto
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformPurchaseReturnOrderGoodsDTO extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = -6700924265909584411L;
    /**
     *退货单商品行编码
     */
    @ApiModelProperty(value = "退货单商品行编码")
    private String returnGoodsCode;
    /**
     *退货单编码
     */
    @ApiModelProperty(value = "退货单编码")
    private String returnCode;
    /**
     *商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     *是否入仓形式VALUE 1-有实体仓;2-无实体仓
     */
    @ApiModelProperty(value = "是否入仓")
    private String warehouseTypeValue;
    /**
     *商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String goodsNo;
    /**
     *采购单位
     */
    @ApiModelProperty(value = "采购单位")
    private String purchaseUnit;
    /**
     *采购数量
     */
    @ApiModelProperty(value = "采购数量")
    private BigDecimal purchaseNum;
    /**
     *已退货数量
     */
    @ApiModelProperty(value = "已退货数量")
    private BigDecimal returnedCount;

    /**
     *采购单价
     */
    @ApiModelProperty(value = "采购单价")
    private BigDecimal purchaseUnitPrice;

    /**
     * 待出库数量
     */
    @ApiModelProperty(value = "待出库数量")
    private BigDecimal waitingWarehouseOut;

    /**
     *出库数量
     */
    @ApiModelProperty(value = "出库数量")
    private BigDecimal warehouseOutCount;

    /**
     *本次退货数量
     */
    @ApiModelProperty(value = "本次退货数量")
    private BigDecimal returnRequestCount;
    /**
     * 出库仓库可售库存
     */
    @ApiModelProperty(value = "可售库存")
    private BigDecimal availableStockNum;

    /**
     * 出库仓库实体库存
     */
    @ApiModelProperty(value = "实体库存")
    private BigDecimal realStockNum;

    /**
     *仓库编号
     */
    @ApiModelProperty(value = "仓库编号")
    private String warehouseNo;
    /**
     *仓库名称
     */
    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    /**
     * 转换率(辅转主)
     */
    @ApiModelProperty(value = "转换率")
    private BigDecimal conversionRate;

    /**
     * 计量单位ID
     */
    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitName;



    /**
     *删除状态形式VALUE
     */
    @ApiModelProperty(value = "删除状态")
    private String deleteFlagValue;

    /**
     * 归属平台类型(1.运营 2.商家 3.店铺)
     */
    @ApiModelProperty(value = "归属平台类型(1.运营 2.商家 3.店铺)")
    private String platformType;

    /**
     *商家ID
     */
    @ApiModelProperty(value = "商家ID")
    private String merchantNo;
    /**
     *商家名称
     */
    @ApiModelProperty(value = "商家名称")
    private String merchantName;
    /**
     *店铺ID，对应orgId
     */
    @ApiModelProperty(value = "店铺ID，对应orgId")
    private String storeNo;
    /**
     *店铺名称，对应orgName
     */
    @ApiModelProperty(value = "店铺名称，对应orgName")
    private String storeName;

}
