package cn.htdt.app.platform.dto.response.advertiseinfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 广告通栏响应DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-01
 */
@Data
@ApiModel(description = "广告通栏响应实体类")
public class ResPlatformAdvertiseInfoDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 广告编号
     */
    @ApiModelProperty("广告编号")
    private String adNo;

    /**
     * 广告图片
     */
    @ApiModelProperty("广告图片")
    private String adPicUrl;

    /**
     * 链接URL
     */
    @ApiModelProperty("链接URL")
    private String linkAddress;
}
