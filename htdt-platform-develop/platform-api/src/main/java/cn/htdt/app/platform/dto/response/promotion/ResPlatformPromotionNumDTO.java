package cn.htdt.app.platform.dto.response.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
public class ResPlatformPromotionNumDTO implements Serializable {

    private static final long serialVersionUID = 1237193190438741324L;

    @ApiModelProperty(value = "促销活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "促销活动类型编码")
    private String promotionType;

    @ApiModelProperty(value = "促销活动类型名称")
    private String promotionTypeName;

    @ApiModelProperty(value = "促销活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动数量")
    private Integer promotionNum;

    @ApiModelProperty("活动有效期（1001：长期有效 1002：指定日期）")
    private String periodValidity;

    @ApiModelProperty(value = "活动时间段类型 (1001:全天 1002:指定时间段)")
    private String effectivePeriodType;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "每日开始时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime dailyStartTime;

    @ApiModelProperty(value = "每日结束时间")
    @JsonFormat(pattern = "HH:mm:ss")
    private LocalTime dailyEndTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);}
}
