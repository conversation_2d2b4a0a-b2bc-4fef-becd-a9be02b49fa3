package cn.htdt.app.platform.dto.response.returnorder;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 物流信息详情表
 * <AUTHOR>
 *
 */
@Data
public class ResPlatformOrderDeliveryDataDTO implements Serializable {

    @ApiModelProperty(value = "快递描述信息")
    private String remark;

    @ApiModelProperty(value = "快递记录对应的日期")
    private String deliveryDate;

    @ApiModelProperty(value = "快递记录对应的时间")
    private String deliveryTime;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
