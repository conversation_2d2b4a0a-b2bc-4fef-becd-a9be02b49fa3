package cn.htdt.app.platform.dto.response.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商促销活动参与店铺实体类
 *
 * <AUTHOR>
 * @date 2021-07-02
 */
@Data
public class ResPlatformGoodsPromotionStoreDTO implements Serializable {

    private static final long serialVersionUID = 6515445309405966997L;

    @ApiModelProperty(value = "规则编号")
    private String ruleNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动店铺类型(1001:全部店铺 1002:报名活动)")
    private String storeType;

    @ApiModelProperty(value = "报名活动编号")
    private String enrollNo;

    @ApiModelProperty(value = "店铺报名活动信息")
    ResPlatformEnrollRuleDTO resPlatformEnrollRuleDTO;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
