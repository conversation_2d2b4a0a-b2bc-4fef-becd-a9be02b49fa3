package cn.htdt.app.platform.dto.response.payproject;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2021-08-11
 * @description 分页查询购买列表响应DTO
 **/
@Data
public class ResPlatformPurchaseHistoryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单编号")
    private String dealsNo;

    @ApiModelProperty(value = "订单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "产品名称")
    private String packageName;

    @ApiModelProperty(value = "订单金额")
    private BigDecimal payAmount;

    @ApiModelProperty("订单状态 1：待支付 2：交易成功 3：退款成功 4：交易关闭 5付款失败")
    private Integer dealsStatus;

    @ApiModelProperty("产品状态 1：未开始 2：生效中 3：已过期")
    private Integer projectStatus;

    @ApiModelProperty("是否为滴灌通套餐 1：否 2：是")
    private Integer mciFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
