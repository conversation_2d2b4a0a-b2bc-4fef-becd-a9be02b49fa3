package cn.htdt.app.platform.dto.response.shoppingGuide;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @ClassName ResShoppingGuidePerformanceDTO
 * <AUTHOR>
 * @create 2023/6/19 10:36
 */
@Data
public class ResShoppingGuidePerformanceDTO implements Serializable {
    private static final long serialVersionUID = 7134814159377525983L;

    @ApiModelProperty(value = "导购员姓名")
    private String shoppingGuideName;

    @ApiModelProperty(value = "订单数")
    private Integer orderNum;

    @ApiModelProperty(value = "商品总额")
    private BigDecimal totalGoodsAmount;

    @ApiModelProperty(value = "优惠总额")
    private BigDecimal totalDiscountAmount;

    @ApiModelProperty("应付总额")
    private BigDecimal totalShouldAmount;

    @ApiModelProperty("已付总额")
    private BigDecimal totalRealAmount;

    @ApiModelProperty("橙豆支付总额")
    private BigDecimal totalCoinAmount;

    @ApiModelProperty("欠款总额")
    private BigDecimal totalArrearsAmount;

    @ApiModelProperty("提成比例")
    private BigDecimal commissionRatio;

    @ApiModelProperty("提成金额")
    private BigDecimal commissionAmount;
}
