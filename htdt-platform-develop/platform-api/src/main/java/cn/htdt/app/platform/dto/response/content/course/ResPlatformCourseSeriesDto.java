package cn.htdt.app.platform.dto.response.content.course;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigInteger;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 系列课程信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Data
@ApiModel(description = "系列课程响应实体类")
public class ResPlatformCourseSeriesDto implements Serializable{

    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @ApiModelProperty(notes = "id")
    private BigInteger id;

    /**
     * 系列编号
     */
    @ApiModelProperty(notes = "系列编号")
    private String seriesNo;

    /**
     * 章节数量
     */
    @ApiModelProperty(notes = "章节数量")
    private Integer sectionNum;

    /**
     * 标题
     */
    @ApiModelProperty(notes = "标题")
    private String title;

    /**
     * 封面地址
     */
    @ApiModelProperty(notes = "封面地址")
    private String coverUrl;

    /**
     * 分享状态 1不允许 2允许
     */
    @ApiModelProperty(notes = "分享状态 1不允许 2允许")
    private Integer shareFlag;

    /**
     * 下载状态 1不允许 2允许
     */
    @ApiModelProperty(notes = "下载状态 1不允许 2允许")
    private Integer downloadFlag;

    /**
     * 发布状态 1 未发布 2发布
     */
    @ApiModelProperty(notes = "发布状态 1 未发布 2发布")
    private Integer releaseStatus;

    /**
     * 发布位置 以json形式保存
     */
    @ApiModelProperty(notes = "发布位置 以json形式保存")
    private String releasePosition;

    /**
     * 发布时间
     */
    @ApiModelProperty(notes = "发布时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime releaseTime;

    /**
     * 修改时间
     */
    @ApiModelProperty(notes = "发布时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private LocalDateTime modifyTime;

    /**
     * 评论数
     */
    @ApiModelProperty(notes = "评论数")
    private Integer commentCount;

    /**
     * 点赞数
     */
    @ApiModelProperty(notes = "点赞数")
    private Integer likeCount;

    /**
     * 分享数
     */
    @ApiModelProperty(notes = "分享数")
    private Integer shareCount;

    /**
     * 浏览数
     */
    @ApiModelProperty(notes = "浏览数")
    private Integer browseCount;

    /**
     * 章节数
     */
    @ApiModelProperty(notes = "章节数")
    private Integer sectionCount;

    /**
     * 创建人
     */
    @ApiModelProperty(notes = "创建人")
    private String createNo;

    /**
     * 最后一次修改人
     */
    @ApiModelProperty(notes = "最后一次修改人")
    private String modifyNo;

    /**
     * 一级分类
     */
    @ApiModelProperty(notes = "一级分类")
    private String firstCategory;

    /**
     * 章节列表
     */
    @ApiModelProperty(notes = "章节列表")
    private List<ResPlatformSeriesSectionDto> sections;
}
