package cn.htdt.app.platform.dto.response.goodrealsstock;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *
 * 功能描述: 商品库存响应dto
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformGoodsRealStockDTO implements Serializable {

    /**
     * 店铺名称，对应orgName
     */
    @ApiModelProperty(value = "店铺名称")
    private String storeName;
    /**
     * 商品编号
     */
    @ApiModelProperty(value = "商品编号")
    private String goodsNo;
    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;


    /**
     * 是否启用串码形式value
     */
    @ApiModelProperty(value = "是否启用串码")
    private String imeiFlagValue;

    /**
     * 是否入仓形式value
     */
    @ApiModelProperty(value = "是否入仓")
    private String warehouseFlagValue;

    /**
     * 计量单位ID
     */
    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitName;

    /**
     * 冻结库存数量，即预锁数量
     */
    @ApiModelProperty(value = "预锁数量")
    private BigDecimal freezeStockNum;

    /**
     * 待发货数量
     */
    @ApiModelProperty(value = "待发货数量")
    private BigDecimal deliverStockNum;

    /**
     * 可用库存数量，即可售库存
     */
    @ApiModelProperty(value = "可售库存")
    private BigDecimal availableStockNum;

    /**
     * 可用库存数量，即可售库存 (辅1)
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "可用库存数量，即可售库存 (辅1)")
    private BigDecimal availableStockOneNum;

    /**
     * 可用库存数量，即可售库存 (辅2)
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "可用库存数量，即可售库存 (辅2)")
    private BigDecimal availableStockTwoNum;

    /**
     * 总库存数量，即实体库存
     */
    @ApiModelProperty(value = "实体库存")
    private BigDecimal realStockNum;


    /**
     * 总库存数量，即实体库存 (辅1)
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "总库存数量，即实体库存 (辅1)")
    private BigDecimal realStockOneNum;

    /**
     * 总库存数量，即实体库存 (辅2)
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "总库存数量，即实体库存 (辅2)")
    private BigDecimal realStockTwoNum;

    /**
     *  商品状态形式value 1001-未上架;1004:已上架;
     */
    @ApiModelProperty(value = "商品状态")
    private String goodsStatusValue;

    /**
     * 删除状态码形式value
     */
    @ApiModelProperty(value = "删除状态")
    private String deleteFlagValue;

    /**
     *  调拨在途数量
     */
    @ApiModelProperty(value = "调拨在途数量")
    private BigDecimal allocationNum;

    /**
     *  已售数量
     */
    @ApiModelProperty(value = "已售数量")
    private BigDecimal soldNum;

    /**
     * 转换率(辅转主)
     */
    @ApiModelProperty(value = "转换率")
    private BigDecimal conversionRate;

    //20230809蛋品-wxb-库存管理
    @ApiModelProperty(value = "转换率2(辅转主) 多单位时-辅计量单位2")
    private BigDecimal conversionRateTwo;


    /**
     * 辅计量单位id
     */
    @ApiModelProperty(value = "辅计量单位id")
    private String assistCalculationUnitName;

    /**
     * 辅计量单位名称2
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "辅计量单位名称2")
    private String assistCalculationUnitNameTwo;

    /**
     * 商品形式 1001-普通商品;1002-系列商品
     */
    private String goodsForm;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 第一、二、三属性值编码，格式：first-second-third
     */
    private String attributeValueName;


    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "多单位商品类型 1001:主单位商品 1002:子")
    private String multiUnitType;

    /**
     * 多单位主品编号
     * //20230809蛋品-wxb-库存管理
     */
    @ApiModelProperty(value = "多单位主品编号")
    private String multiUnitGoodsNo;

    public String getAttributeValueName() {
        attributeValueName = "";
        if(StringUtils.isNotEmpty(getFirstAttributeValueName())) {
            attributeValueName+= "-" + getFirstAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getSecondAttributeValueName())) {
            attributeValueName+= "-" + getSecondAttributeValueName();
        }
        if(StringUtils.isNotEmpty(getThirdAttributeValueName())) {
            attributeValueName+= "-" + getThirdAttributeValueName();
        }
        if(StringUtils.isNotEmpty(attributeValueName)) {
            attributeValueName = attributeValueName.substring(1);
        }
        return attributeValueName;
    }


}
