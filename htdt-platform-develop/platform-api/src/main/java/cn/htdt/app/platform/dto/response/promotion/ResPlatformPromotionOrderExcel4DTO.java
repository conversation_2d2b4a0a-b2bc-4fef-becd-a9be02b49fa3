package cn.htdt.app.platform.dto.response.promotion;

import cn.htdt.app.platform.converter.OrderStatusConverter;
import cn.htdt.app.platform.converter.base.DateConverter;
import cn.htdt.common.utils.excel.dto.RowRangeDto;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 平台角色导出的活动下的订单列表数据表头DTO
 *
 * <AUTHOR>
 * @date 2021-07-13
 */
@Data
@NoArgsConstructor
public class ResPlatformPromotionOrderExcel4DTO extends RowRangeDto implements Serializable {

    private static final long serialVersionUID = 6384307161692264989L;

    @ExcelProperty(value = "订单编号", index = 0)
    @ColumnWidth(30)
    private String orderNo;

    @ExcelProperty(value = "下单时间", index = 1, converter = DateConverter.class)
    @ColumnWidth(20)
    private Date orderCreateTime;

    @ExcelProperty(value = "商品ID", index = 2)
    @ColumnWidth(20)
    private String goodsNo;

    @ExcelProperty(value = "商品名称", index = 3)
    @ColumnWidth(20)
    private String goodsName;

    @ExcelProperty(value = "订单应付款", index = 4)
    @ColumnWidth(20)
    private BigDecimal shouldAmount;

    @ExcelProperty(value = "订单实付款", index = 5)
    @ColumnWidth(20)
    private BigDecimal realAmount;

    @ExcelProperty(value = "下单人姓名", index = 6)
    @ColumnWidth(20)
    private String buyerName;

    @ExcelProperty(value = "下单人手机号", index = 7)
    @ColumnWidth(20)
    private String buyerMobile;

    @ExcelProperty(value = "订单状态", index = 8, converter = OrderStatusConverter.class)
    @ColumnWidth(20)
    private String orderStatus;


    @ExcelProperty(value = "店铺编码", index = 9)
    @ColumnWidth(15)
    private String distributionStoreNo;

    @ExcelProperty(value = "店铺名称", index = 10)
    @ColumnWidth(20)
    private String distributionStoreName;

    /**
     * 商家编号
     */
    @ExcelIgnore
    private String distributionMerchantNo;

    @ExcelProperty(value = "商家会员编号", index = 11)
    @ColumnWidth(15)
    private String distributionMerberCode;

    @ExcelProperty(value = "商家名称", index = 12)
    @ColumnWidth(20)
    private String distributionMerchantName;


}
