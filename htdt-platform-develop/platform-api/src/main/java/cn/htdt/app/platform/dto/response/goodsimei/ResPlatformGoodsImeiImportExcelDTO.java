package cn.htdt.app.platform.dto.response.goodsimei;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 *  商品串码信息导入DTO
 *
 * <AUTHOR>
 * @date 2020年9月11日
 */
@Data
public class ResPlatformGoodsImeiImportExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 0)
    @ColumnWidth(40)
    private String goodsName;
    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    /**
     * 商品串码
     */
    @ExcelProperty(value = "商品串码",index = 1)
    @ColumnWidth(40)
    private String imei;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称",index = 2)
    @ColumnWidth(40)
    private String warehouseName;

    /**
     * 仓库编码
     */
    private String warehouseNo;

}
