package cn.htdt.app.platform.dto.response.purchase;

import cn.htdt.app.platform.dto.common.PlatformGoodsValidityPeriodDTO;
import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  采购单待入库Vo
 *
 * <AUTHOR>
 * @date 2020年9月15日
 */
@Data
public class ResPlatformPurchaseOrderStorageVO extends PlatformGoodsValidityPeriodDTO implements Serializable {
    private static final long serialVersionUID = -2183266402894644967L;
    /**
     *采购单编码
     */
    private String purchaseCode;
    /**
     *采购单商品行编码
     */
    private String purchaseGoodsCode;
    /**
     *商品编码
     */
    private String goodsNo;
    /**
     *商品名称
     */
    private String goodsName;
    /**
     * 商品名称 展示用带属性
     */
    private String showGoodsName;
    /**
     * 计量单位ID
     */
    private String calculationUnitNo;
    /**
     * 计量单位名称
     */
    private String calculationUnitName;
    /**
     *采购单位
     */
    private String purchaseUnit;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;
    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 转换率(辅转主)
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal conversionRate;
    /**
     * 是否入仓:1-否;2-是;
     */
    private Integer warehouseFlag;
    /**
     * 是否入仓:1-否;2-是;
     */
    private Integer warehouseFlagOfGoodsGroups;
    /**
     * 是否入仓
     */
    private String warehouseFlagStr;
    /**
     *采购单价
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal purchaseUnitPrice;
    /**
     *采购数量
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal purchaseNum;
    /**
     * 待入库数量
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal waitStorageCount;
    /**
     *已入库数量
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal storageCount;
    /**
     *本次入库数量
     */
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal storageNum;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
