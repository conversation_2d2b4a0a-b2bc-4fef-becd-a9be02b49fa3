package cn.htdt.app.platform.dto.response.requisitionorder;

import java.io.Serializable;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;

import lombok.Data;

/**
 * @description 要货单商品行数据导出
 * 
 * <AUTHOR>
 * @date 2023/5/30 15:52
 **/
@Data
public class ResPlatformRequisitionGoodsExportDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "订单编码", index = 0)
    @ColumnWidth(45)
    private String requisitionNo;

    @ExcelProperty(value = "订单状态", index = 1)
    @ColumnWidth(20)
    private String status;

    @ExcelProperty(value = "要货门店", index = 2)
    @ColumnWidth(45)
    private String storeName;

    @ExcelProperty(value = "订单备注", index = 3)
    @ColumnWidth(60)
    private String remark;

    @ExcelProperty(value = "商品名称", index = 4)
    @ColumnWidth(45)
    private String goodsName;

    @ExcelProperty(value = "要货单位", index = 5)
    @ColumnWidth(20)
    private String requisitionUnit;

    @ExcelProperty(value = "要货数量", index = 6)
    @ColumnWidth(20)
    private String requisitionNum;

    @ExcelProperty(value = "已发货数量", index = 7)
    @ColumnWidth(20)
    private String outStorageCount;

    @ExcelProperty(value = "已收货数量", index = 8)
    @ColumnWidth(20)
    private String inStorageCount;

    @ExcelProperty(value = "要货单价", index = 9)
    @ColumnWidth(20)
    private String itemPrice;

    @ExcelProperty(value = "已收货金额", index = 10)
    @ColumnWidth(20)
    private String inStorageAmount;

}
