package cn.htdt.app.platform.dto.response.goodsimei;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 串码历史库存 导出
 **/
@Data
public class ResPlatformImeiHistoryExportExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "日期",index = 0)
    @ColumnWidth(20)
    private String inventoryDateStr;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "串码",index = 1)
    @ColumnWidth(20)
    private String imei;

    /**
     * 实体库存
     */
    @ExcelProperty(value ="商品ID",index = 2)
    @ColumnWidth(20)
    private String goodsNo;

    /**
     * 实盘数量
     */
    @ExcelProperty(value = "自定义商品编码",index = 3)
    @ColumnWidth(20)
    private String customGoodsNo;

    /**
     * 差异数量
     */
    @ExcelProperty(value = "商品名称",index = 4)
    @ColumnWidth(20)
    private String goodsName;

    /**
     * 单位
     */
    @ExcelProperty(value = "供应商",index = 5)
    @ColumnWidth(20)
    private String supplierName;

    @ExcelProperty(value = "店铺名称",index = 6)
    @ColumnWidth(20)
    private String storeName;

    @ExcelProperty(value = "自定义店铺编码",index = 7)
    @ColumnWidth(20)
    private String customStoreNo;

    @ExcelProperty(value = "是否销售",index = 8)
    @ColumnWidth(20)
    private String saleFlagStr;

    @ExcelProperty(value = "是否在途",index = 9)
    @ColumnWidth(20)
    private String transportFlagStr;

}
