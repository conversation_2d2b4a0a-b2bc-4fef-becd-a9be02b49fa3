package cn.htdt.app.platform.dto.response.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 报名活动记录
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-01
 */
@Data
public class ResPlatformEnrollDrawRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 记录编号
     */
    private String recordNo;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    private String name;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话")
    private String phone;

    /**
     * 收货人
     */
    @ApiModelProperty(value = "收货人")
    private String deliveryName;

    /**
     * 收货人手机号
     */
    @ApiModelProperty(value = "收货人手机号")
    private String deliveryPhone;

    /**
     * 省编号
     */
    private String provinceCode;

    /**
     * 省名
     */
    private String provinceName;

    /**
     * 市编号
     */
    private String cityCode;

    /**
     * 市名
     */
    private String cityName;

    /**
     * 区域编号
     */
    private String regionCode;

    /**
     * 区域名称
     */
    private String regionName;

    /**
     * 街道编号
     */
    private String streetCode;

    /**
     * 街道名称
     */
    private String streetName;

    /**
     * 详细地址
     */
    @ApiModelProperty(value = "详细地址")
    private String detailAddress;


    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "登录账号")
    private String loginAccount;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty(value = "店铺编码")
    private String storeNo;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    /**
     * 报名时间
     */
    @ApiModelProperty(value = "报名时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    /**
     * 报名状态（1000：不用受理记录 1001：待报名
     * 1002：待受理 1003：已受理 1004：不受理）
     */
    @ApiModelProperty(value = "报名状态（1000：不用受理记录 1001：待报名 1002：待受理 1003：已受理 1004：不受理）")
    private String applyStatus;

    @ApiModelProperty(value = "报名状态名称")
    private String applyStatusName;

    @ApiModelProperty(value = "活动交付状态(1: 已完成)")
    private String taskStatus;

    /**
     * 受理备注
     */
    @ApiModelProperty(value = "受理备注")
    private String applyExplain;

    /**
     * 手机设备号
     */
    @ApiModelProperty(value = "手机设备号")
    private String deviceNumber;
    /**
     * 店铺报名备注
     */
    @ApiModelProperty(value = "店铺报名备注")
    private String storeEnrollExplain;
}
