package cn.htdt.app.platform.dto.response.returnorder;

import cn.htdt.app.platform.converter.OrderTypeConverter;
import cn.htdt.app.platform.converter.RefundmentStatusConverter;
import cn.htdt.app.platform.converter.ReturnSourceConverter;
import cn.htdt.app.platform.converter.ReturnStatusConverter;
import cn.htdt.app.platform.converter.ReturnTypeConverter;
import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 *
 * @Description 售后订单导出DTO
 **/
@Data
public class ResPlatformExportReturnOrderDTO implements Serializable {


    /**
     * 退单申请时间
     */
    @ExcelProperty(value = "申请售后时间",index = 0,converter = LocalDateTimeConverter.class)
    @ColumnWidth(20)
    private LocalDateTime applyTime;

    /**
     *
     * 退款单状态 字典REFUNDMENT_STATUS
     */
    @ExcelProperty(value = "退款单状态",index = 1, converter = RefundmentStatusConverter.class)
    @ColumnWidth(20)
    private String refundmentStatus;

    /**
     *  售后单状态 ,字典SO_RETURN_STATUS
     */
    @ExcelProperty(value = "售后单状态",index = 2, converter = ReturnStatusConverter.class)
    @ColumnWidth(20)
    private String returnStatus;

    /**
     * 售后编号
     */
    @ExcelProperty(value = "售后编号",index = 3)
    @ColumnWidth(20)
    private String soReturnNo;

    /**
     * 格式：150905xxxxxxxx2657 纯数字 6位日期+8位数字+1校验位+3位用户id
     */
    @ExcelProperty(value = "订单编号",index = 4)
    @ColumnWidth(20)
    private String orderNo;

    /**
     * 售后类型 1仅退款未发货 2仅退款已发货 3退款退货
     */
    @ExcelProperty(value = "售后类型",index = 5, converter = ReturnTypeConverter.class)
    @ColumnWidth(20)
    private String returnType;

    /**
     * 订单类型
     */
    @ExcelProperty(value = "订单类型",index = 6, converter = OrderTypeConverter.class)
    @ColumnWidth(20)
    private String orderType;

    /**
     * 商家名称
     */
    @ExcelProperty(value = "商家名称",index = 7)
    @ColumnWidth(20)
    private String merchantName;

    /**
     * 门店名称
     */
    @ExcelProperty(value = "门店名称",index = 8)
    @ColumnWidth(20)
    private String storeName;


    /**
     * 用户申请退款金额
     */
    @ExcelProperty(value = "用户申请退款金额",index = 9)
    @ColumnWidth(20)
    private BigDecimal applyReturnAmount;

    /**
     * 申请来源
     */
    @ExcelProperty(value = "申请来源",index = 10, converter = ReturnSourceConverter.class)
    @ColumnWidth(20)
    private String source;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 11)
    @ColumnWidth(20)
    private String goodsName;
    /**
     * 商品编码
     */
    @ExcelProperty(value = "商品编码",index = 12)
    @ColumnWidth(20)
    private String goodsNo;

    /**
     * 商品购买数量
     */
    @ExcelProperty(value = "商品购买数量",index = 13)
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    @ColumnWidth(20)
    private BigDecimal productItemNum;

    /**
     * 售后商品数量
     */
    @ExcelProperty(value = "售后商品数量",index = 14)
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    @ColumnWidth(20)
    private BigDecimal returnProductItemNum;

    /**
     * 规格属性
     */
    @ExcelProperty(value = "规格属性",index = 15)
    @ColumnWidth(20)
    private String extInfo;

    /**
     * 退还优惠券
     */
    @ExcelProperty(value = "退还优惠券",index = 16)
    @ColumnWidth(20)
    private BigDecimal discount;

    /**
     * 退还积分
     */
    @ExcelProperty(value = "退还积分",index = 17)
    @ColumnWidth(20)
    private int orderDepletePoints;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
