package cn.htdt.app.platform.dto.response.goodstrade;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @ClassName ResExportPurchaseTradeOrderDTO
 * <AUTHOR>
 * @create 2023/10/7 14:09
 */
@Data
public class ResExportPurchaseTradeOrderDetailDTO implements Serializable {

    @ExcelProperty(value = "B2B订单号",index = 0)
    @ColumnWidth(20)
    private String orderNo;

    @ExcelProperty(value = "B2B供货商",index = 1)
    @ColumnWidth(20)
    private String sellerName;

    @ExcelProperty(value = "采购金额",index = 2)
    @ColumnWidth(20)
    private BigDecimal totalGoodsAmount;

    @ExcelProperty(value = "采购时间",index = 3)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private String createTime;

    @ExcelProperty(value = "店铺名称",index = 4)
    @ColumnWidth(20)
    private String storeName;

    @ExcelProperty(value = "门店经营人",index = 5)
    @ColumnWidth(20)
    private String operatorName;

    @ExcelProperty(value = "手机号",index = 6)
    @ColumnWidth(20)
    private String operatorPhone;

}
