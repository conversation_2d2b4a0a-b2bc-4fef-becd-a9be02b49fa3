package cn.htdt.app.platform.dto.response.goodsimeihistory;


import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <p>
 * 商品串码历史查询请求Dto
 * </p>
 *
 * <AUTHOR>
 * @since 2023-07-20
 */
@EqualsAndHashCode
@Data
public class ResPlatformGoodsImeiHistoryDTO extends ReqComPageDTO implements Serializable {


    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String supplierName;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String supplierCode;

    /**
     * 商品串码
     */
    @ApiModelProperty(value = "商品串码")
    private String imei;

    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    /**
     * 门店名称
     */
    @ApiModelProperty(value = "门店名称")
    private String storeName;

    /**
     * 自定义商品编码
     */
    @ApiModelProperty(value = "自定义商品编码")
    private String customGoodsNo;

    /**
     * 自定义店铺编号
     */
    @ApiModelProperty(value = "自定义店铺编号")
    private String customStoreNo;

    /**
     * 是否在途:  1: 不在途 ; 2: 在途
     */
    @ApiModelProperty(value = "是否在途:  1: 不在途 ; 2: 在途")
    private Integer transportFlag;

    @ApiModelProperty(value = "是否销售:  1: 不在途 ; 2: 在途")
    private Integer saleFlag;

    /**
     * 出入库状态:  1表示已入库 ,  2表示已出库
     */
    @ApiModelProperty(value = "出入库状态:  1表示已入库 ,  2表示已出库")
    private Integer inventoryState;

    /**
     * 出入库方式 : 有采购入库、调拨入库、销售退货入库 以及调拨出库、销售出库、P200出库、O2O出库等
     */
    @ApiModelProperty(value = "出入库方式 : 有采购入库、调拨入库、销售退货入库 以及调拨出库、销售出库、P200出库、O2O出库等")
    private String inventoryType;



    /**
     * 库存日期
     */
    @ApiModelProperty(value = "库存日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate inventoryDate;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
