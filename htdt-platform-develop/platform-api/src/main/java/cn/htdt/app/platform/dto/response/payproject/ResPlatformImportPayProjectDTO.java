package cn.htdt.app.platform.dto.response.payproject;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

@ColumnWidth(value = 20)
public class ResPlatformImportPayProjectDTO implements Serializable {
    /**
     * 付款时间
     **/
    @ExcelProperty(value = "付款时间")
    private String payTime;
    /**
     * 推荐会员店
     **/
    @ExcelProperty(value = "推荐人")
    private String recommendName;
    /**
     * 会员店账号
     **/
    @ExcelProperty(value = "会员店账号")
    private String merberCode;
    /**
     * 归属分部/事业部
     **/
    @ExcelProperty(value = "归属分部/事业部")
    private String divisionName;
    /**
     * 套餐名称
     **/
    @ExcelProperty(value = "产品名称")
    private String payProjectKitName;
    /**
     * 实付金额
     **/
    @ExcelProperty(value = "实付金额")
    private String payAmount;

    /**
     * 开始时间
     **/
    @ExcelProperty(value = "开始时间")
    private String packageStart;
    /**
     * 结束时间
     **/
    @ExcelProperty(value = "结束时间")
    private String packageEnd;

    /**
     * 返回值
     **/
    @ExcelProperty(value = "返回值")
    private String resultMsg;
    /**
     * 套餐编号
     **/
    @ExcelProperty(value = "产品编号")
    private String dealsNo;

    public String getPayTime() {
        return payTime;
    }

    public void setPayTime(String payTime) {
        this.payTime = payTime;
    }

    public String getRecommendName() {
        return recommendName;
    }

    public void setRecommendName(String recommendName) {
        this.recommendName = recommendName;
    }

    public String getMerberCode() {
        return merberCode;
    }

    public void setMerberCode(String merberCode) {
        this.merberCode = merberCode;
    }

    public String getDivisionName() {
        return divisionName;
    }

    public void setDivisionName(String divisionName) {
        this.divisionName = divisionName;
    }

    public String getPayProjectKitName() {
        return payProjectKitName;
    }

    public void setPayProjectKitName(String payProjectKitName) {
        this.payProjectKitName = payProjectKitName;
    }

    public String getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(String payAmount) {
        this.payAmount = payAmount;
    }

    public String getPackageStart() {
        return packageStart;
    }

    public void setPackageStart(String packageStart) {
        this.packageStart = packageStart;
    }

    public String getPackageEnd() {
        return packageEnd;
    }

    public void setPackageEnd(String packageEnd) {
        this.packageEnd = packageEnd;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getDealsNo() {
        return dealsNo;
    }

    public void setDealsNo(String dealsNo) {
        this.dealsNo = dealsNo;
    }
}
