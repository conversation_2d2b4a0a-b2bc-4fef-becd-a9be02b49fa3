package cn.htdt.app.platform.dto.response.returnorder;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.PaymentChannelEnum;
import cn.htdt.common.enums.returnorder.RefundmentStatusEnum;
import cn.htdt.common.enums.returnorder.RefundmentWayEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 退款明细
 * <AUTHOR>
 */
@Data
public class ResPlatformOrderRefundmentDetailDTO implements Serializable {

    @ApiModelProperty(value = "平台退款单号对应退款明细编码")
    private String refundmentNo;

    @ApiModelProperty(value = "退款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal amount;

    @ApiModelProperty(value = "退款渠道")
    @Converter(enumClass = PaymentChannelEnum.class, fieldName = "refundmentChannelName")
    private String refundmentChannel;

    @ApiModelProperty(value = "退款渠道描述")
    private String refundmentChannelName;

    @ApiModelProperty(value = "退款单状态")
    @Converter(enumClass = RefundmentStatusEnum.class, fieldName = "refundmentStatusName")
    private String refundmentStatus;

    @ApiModelProperty(value = "退款单状态描述")
    private String refundmentStatusName;

    @ApiModelProperty(value = "退单方式")
    @Converter(enumClass = RefundmentWayEnum.class, fieldName = "refundmentWayName")
    private String refundmentWay;

    @ApiModelProperty(value = "退单方式描述")
    private String refundmentWayName;

    @ApiModelProperty(value = "退款时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime refundmentTime;

    @ApiModelProperty(value = "1:订单取消退款 2:退货退款 3:仅退款 4:删除商品 5:换货")
    private String refundmentType;

    @ApiModelProperty(value = "售后类型 1000仅退款未发货 1001仅退款已发货 1002退款退货")
    private String returnType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
