package cn.htdt.app.platform.dto.response.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 粉丝、代理人详情信息
 * <AUTHOR>
 * @date 2021/4/1 16:16
 */
@Data
public class ResPlatformFanAgentInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "代理人编号")
    private String agentNo;

    @ApiModelProperty(value = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "电话")
    private String phone;

    @ApiModelProperty(value = "店铺信息")
    private List<StoreInfo> storeInfoList;

    @Data
    public static class StoreInfo implements Serializable{

        private static final long serialVersionUID = 7473442692560005191L;

        /*******代理人*******/
        @ApiModelProperty(value = "店铺编号")
        private String storeNo;

        @ApiModelProperty(value = "店铺名称")
        private String storeName;

        @ApiModelProperty(value = "商家编号")
        private String merchantNo;

        @ApiModelProperty(value = "商家名称")
        private String merchantName;
    }
}
