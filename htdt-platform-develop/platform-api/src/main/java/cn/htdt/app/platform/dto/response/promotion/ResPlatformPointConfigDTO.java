package cn.htdt.app.platform.dto.response.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 抽奖积分配置
 *
 * <AUTHOR>
 * @date 2020年9月14日
 */
@Data
public class ResPlatformPointConfigDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *抽奖积分配置 1:启用 2:禁用
     */
    @ApiModelProperty(value = "抽奖积分配置 1:启用 2:禁用")
    private Integer pointsConfig;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
