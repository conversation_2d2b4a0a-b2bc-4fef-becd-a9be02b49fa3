package cn.htdt.app.platform.dto.response.promotion;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 活动中奖记录核销
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
public class ResPlatformWriteOffRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "记录编号")
    private String recordNo;

    @ApiModelProperty(value = "奖品编号")
    private String rewardNo;
    @ApiModelProperty(value = "商品编号")
    private String goodsNo;
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "奖品名称")
    private String rewardName;

    @ApiModelProperty(value = "活动类型 1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    @ApiModelProperty(value = "奖品值 (话费、金币、积分)")
    private String rewardValue;

    @ApiModelProperty(value = "粉丝编号")
    private String fanNo;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，2:作废")
    private Integer disableFlag;
    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @ApiModelProperty(value = "核销状态:1：待核销 2：已核销 3:已核销（不可核销）")
    private Integer writeOffStatus;


    @ApiModelProperty(value = "是否可核销:1：可核销 2：不可核销")
    private Integer writeOffFlag;

    @ApiModelProperty("是否启用串码：1->否；2->是")
    private Integer  imeiFlag;
    @ApiModelProperty("是否入仓：1->否；2->是")
    private Integer  warehouseFlag;
    /**
     * 商品仓库列表
     */
    private List<GoodsWarehouse> goodsWarehouseList;
    /**
     * 商品仓库信息
     */
    @Data
    public class GoodsWarehouse implements Serializable {

        private static final long serialVersionUID = 3452193286745066611L;

        /**
         * 仓库编码
         */
        private String warehouseNo;

        /**
         * 仓库名称
         */
        private String warehouseName;
    }
    /**
     * 中奖时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "中奖时间")
    private LocalDateTime createTime;

    @ApiModelProperty("来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    public String getRewardValue() {
        if (StringUtils.isBlank(this.rewardValue)){
            return "1";
        }
        return rewardValue;
    }

    public void setRewardValue(String rewardValue) {
        if (StringUtils.isBlank(rewardValue)){
            rewardValue = "1";
        }
        this.rewardValue = rewardValue;
    }
}