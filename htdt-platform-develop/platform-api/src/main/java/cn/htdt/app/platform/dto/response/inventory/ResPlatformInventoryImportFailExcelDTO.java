package cn.htdt.app.platform.dto.response.inventory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点详细 导入失败DTO
 **/
@Data
public class ResPlatformInventoryImportFailExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 0)
    @ColumnWidth(20)
    private String goodsName;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID",index = 1)
    @ColumnWidth(20)
    private String goodsNo;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称",index = 2)
    @ColumnWidth(20)
    private String warehouseName;

    /**
     * 仓库编码
     */
    @ExcelProperty(value = "仓库编码",index = 3)
    @ColumnWidth(20)
    private String warehouseNo;

    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因",index = 4)
    @ColumnWidth(20)
    private String reason;
}
