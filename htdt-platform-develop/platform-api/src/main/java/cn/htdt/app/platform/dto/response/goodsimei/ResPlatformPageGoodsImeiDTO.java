package cn.htdt.app.platform.dto.response.goodsimei;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 商品串码
 * @Date 2023/7/31
 * <AUTHOR>
 **/
@Data
public class ResPlatformPageGoodsImeiDTO implements Serializable {

    private static final long serialVersionUID = 6564188352972515221L;

    @ApiModelProperty(value = "商品编号，包含普通商品、套餐商品等所有商品")
    private String goodsNo;

    @ApiModelProperty("自定义商品编码")
    private String customGoodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "第一属性值编码")
    private String firstAttributeValueName;

    @ApiModelProperty(value = "第二属性值编码")
    private String secondAttributeValueName;

    @ApiModelProperty(value = "第三属性值编码")
    private String thirdAttributeValueName;

    @ApiModelProperty(value = "实际库存(串码数量)")
    private Integer imeiCount;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称，对应orgName")
    private String storeName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
