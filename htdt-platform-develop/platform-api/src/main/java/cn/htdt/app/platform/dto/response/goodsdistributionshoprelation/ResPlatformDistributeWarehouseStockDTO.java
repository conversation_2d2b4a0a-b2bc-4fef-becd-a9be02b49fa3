package cn.htdt.app.platform.dto.response.goodsdistributionshoprelation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2020/12/7 20:53
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ResPlatformDistributeWarehouseStockDTO implements Serializable {

    private static final long serialVersionUID = -7475823820587231390L;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "库存数量")
    private BigDecimal stockNum;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
