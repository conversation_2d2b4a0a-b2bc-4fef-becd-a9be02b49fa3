package cn.htdt.app.platform.dto.response.promotion;

import cn.htdt.app.platform.converter.*;
import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 活动中奖记录excel导出
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
public class ResPlatformLotteryDrawRecordExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ColumnWidth(40)
    @ExcelProperty(value = "中奖ID")
    private String recordNo;


    @ExcelProperty(value = "店铺Id")
    private String storeNo;

    @ExcelProperty(value = "店铺名称")
    private String storeName;

    /**
     * 商家编号
     */
    @ExcelIgnore
    private String merchantNo;

    @ExcelProperty(value = "商家会员编号")
    private String merberCode;

    @ExcelProperty(value = "商家名称")
    private String merchantName;

    @ExcelProperty(value = "姓名")
    private String name;

    @ExcelProperty(value = "手机号")
    private String phone;

    @ExcelProperty(value = "奖品名称")
    private String rewardName;

    @ExcelProperty(value = "奖品类型", converter = LotteryRewardTypeConverter.class)
    private String rewardType;

    @ExcelProperty(value = "奖品状态", converter = LotteryRewardStatusConverter.class)
    private Integer disableFlag;

    @ExcelProperty(value = "中奖时间",converter = LocalDateTimeConverter.class)
    private LocalDateTime createTime;

    @ExcelProperty(value = "充值手机号")
    private String rechargePhone;


    @ExcelProperty(value = "归属运营商")
    private String serviceProvider;


    @ExcelProperty(value = "收货人")
    private String deliveryName;

    @ExcelProperty(value = "收货人手机号")
    private String deliveryPhone;

    @ExcelProperty(value = "收货人地址")
    private String deliveryAddress;

    @ExcelProperty(value = "订单编号")
    private String orderNo;

    @ExcelProperty(value = "订单状态", converter = OrderStatusConverter.class)
    private String orderStatus;

    @ExcelProperty(value = "发放/领取状态", converter = SendReceiveStatusConverter.class)
    private String sendReceiveStatus;

    @ExcelProperty(value = "券编号")
    private String couponNo;

    @ExcelProperty(value = "使用状态",converter =CouponUseStatusConverter.class)
    private Integer couponUseStatus;

}
