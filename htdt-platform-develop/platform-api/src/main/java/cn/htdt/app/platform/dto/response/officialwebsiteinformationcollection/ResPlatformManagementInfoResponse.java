package cn.htdt.app.platform.dto.response.officialwebsiteinformationcollection;

import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 官网信息收集
 * </p>
 *
 * <AUTHOR>
 * @date 2021/10/29
 */
@Data
public class ResPlatformManagementInfoResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 信息收集编号
     */
    @ApiModelProperty(value = "信息收集编号")
    private String informationCollectionNo;

    @ApiModelProperty(value = "官网入口")
    private String officialWebsiteEntrance;


    @ApiModelProperty(value = "官网入口Code")
    private String officialWebsiteEntranceCode;

    @ApiModelProperty(value = "来源")
    private String infoSources;

    @ApiModelProperty(value = "公司")
    private String company;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "手机号加密")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.WEBSITE, fieldName = "phone")
    private String dsPhone;

    @ApiModelProperty(value = "所在城市")
    private String address;

    @ApiModelProperty(value = "目前行业")
    private String industry;

    @ApiModelProperty(value = " 合作意向 备注")
    private String informationExplain;

    @ApiModelProperty(value = "创建时间（提交时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "1001：待处理 1002：已处理，跟进中 1003：已处理，直接关闭 1004：已签约 1005：无合作意向")
    private String disposeStatus;

    @ApiModelProperty(value = "1001：待处理 1002：已处理，跟进中 1003：已处理，直接关闭 1004：已签约 1005：无合作意向")
    private String disposeStatusName;


    @ApiModelProperty(notes = "登录数量")
    private Integer totalLoginCount;

    @ApiModelProperty(notes = "登录IP")
    private String loginIp;

    @ApiModelProperty(notes = "登录时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime loginTime;

    @ApiModelProperty(notes = "注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime registerTime;

    @ApiModelProperty(notes = "最长一次停留时长")
    private Long useTime;

    @ApiModelProperty(value = "处理管理信息")
    private ResPlatformOfficialWebsiteManagementResponse managementInfo;
}
