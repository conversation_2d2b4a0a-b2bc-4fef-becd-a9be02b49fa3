package cn.htdt.app.platform.dto.response.purchase;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 供应商操作请求Dto
 *
 * <AUTHOR>
 * @date 2020年9月9日
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResPlatformPurchaseSupplierDTO extends ReqComPageDTO implements Serializable {


    private static final long serialVersionUID = -2753259915602294725L;
    /**
     * 供应商编号
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商简称
     */
    private String supplierShortName;

    /**
     * 供应商助记码
     */
    private String supplierHelpCode;
    /**
     * 供应商品牌名称
     */
    private String supplierBrandName;
    /**
     * 联系人
     */
    private String linkManName;
    /**
     * 联系人手机号
     */
    private String linkManTelphone;
    /**
     * 联系人手机号-加密
     */
    private String dsLinkManTelphone;

    /**
     * 备注
     */
    private String remark;
    /**
     * 企业名称
     */
    private String enterpriseName;


    /**
     * 企业税号
     */
    private String enterpriseTaxCode;

    /**
     * 法定代表人
     */
    private String enterpriseLegalRepresentative;
    /**
     * 营业执照号码
     */
    private String enterpriseLicenseNumber;
    /**
     * 营业执照号码
     */
    private LocalDateTime enterpriseLicenseDate;
    /**
     * 法定代表人证件反面照附件路径
     */
    private LocalDateTime enterpriseLegalDate;
    /**
     * 营业执照图片
     */
    private String enterpriseLicenseAttachPath;
    /**
     * 法定代表人证件正面附件路径
     */
    private String enterpriseLegalFrontAttachPath;
    /**
     * 法定代表人证件反面照附件路径
     */
    private String enterpriseLegalReverseAttachPath;
    /**
     * 供应商地址省份id
     */
    private String supplierProvinceCode;
    /**
     * 供应商地址省份名称
     */
    private String supplierProvinceName;
    /**
     * 供应商地址城市code
     */
    private String supplierCityCode;
    /**
     * 供应商地址城市名称
     */
    private String supplierCityName;
    /**
     * 供应商地址地区code
     */
    private String supplierDistrictCode;
    /**
     * 供应商地址地区名称
     */
    private String supplierDistrictName;
    /**
     * 供应商地址镇code
     */
    private String supplierTownCode;
    /**
     * 供应商地址镇名称
     */
    private String supplierTownName;
    /**
     * 供应商详细地址
     */
    private String supplierDetailAddress;
    /**
     * 供应商详细地址-加密
     */
    private String dsSupplierDetailAddress;
    /**
     * 主营品类
     */
    private String supplierMajorBusiness;
    /**
     *数据来源 1001:手动添加 1002：采购商城添加
     */
    private String sourceType;
    /**
     *数据来源名称
     */
    private String sourceTypeName;
    /**
     * 商家ID
     */
    private String merchantNo;
    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 店铺ID，对应orgId
     */
    private String storeNo;
    /**
     * 店铺名称，对应orgName
     */
    private String storeName;
    /**
     * '是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    /**
     * 归属平台编号
     */
    private String companyNo;
    /**
     * 归属平台名称
     */
    private String companyName;
    /**
     * 归属分部编号
     */
    private String branchNo;

    /**
     * 归属分部名称
     */
    private String branchName;
    /**
     * 平台名称编码
     */
    private String platformType;
    /**
     * 平台名称
     */
    private String platformName;
    /**
     * 删除标记 1001 不可删除  1002 可删除
     */
    private String delButtonFlag;
    /**
     * 创建时间
     * */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
