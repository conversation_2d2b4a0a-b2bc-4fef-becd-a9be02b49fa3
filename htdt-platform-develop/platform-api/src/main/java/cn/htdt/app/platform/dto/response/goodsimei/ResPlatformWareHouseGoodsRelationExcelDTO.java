package cn.htdt.app.platform.dto.response.goodsimei;

import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import cn.htdt.app.platform.converter.base.WhetherConverter;
import cn.htdt.common.enums.constants.NumConstant;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 功能描述: 商品串码信息导出DTO
 *
 * @author: 王磊
 * @date: 2021/01/27
 */
@Data
public class ResPlatformWareHouseGoodsRelationExcelDTO implements Serializable {
    private static final long serialVersionUID = 2972813410807727946L;
    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ExcelProperty(value = "商品编号", index = 0)
    @ColumnWidth(40)
    private String goodsNo;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称", index = 1)
    @ColumnWidth(40)
    private String goodsName;

    /**
     * 是否入仓(1:否 2:是)
     */
    @ExcelProperty(value = "是否入仓商品", index = 2, converter = WhetherConverter.class)
    @ColumnWidth(20)
    private Integer warehouseFlag;

    /**
     * 商品删除标识
     */
    @ExcelProperty(value = "商品删除标识", index = 3, converter = WhetherConverter.class)
    @ColumnWidth(20)
    private Integer goodsDeleteFlag;

    /**
     * 实体库存数量
     */
    @ExcelProperty(value = "实体库存数量", index = 4)
    @ColumnWidth(20)
    private BigDecimal stockNum;

    /**
     * 仓库编码
     */
    @ExcelIgnore
    private String warehouseNo;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称", index = 5)
    @ColumnWidth(40)
    private String warehouseName;

    /**
     * 商品串码数量
     */
    @ExcelProperty(value = "商品串码数量", index = 6)
    @ColumnWidth(40)
    private Integer imeiCount = NumConstant.ZERO;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人", index = 7)
    @ColumnWidth(40)
    private String createName;

    /**
     * 创建时间 , converter = DateConverter.class
     */
    @ExcelProperty(value = "创建时间", index = 8, converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ColumnWidth(40)
    private LocalDateTime createTime;

    /**
     * 第一属性值编码
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "第一属性值",index = 9)
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "第二属性值",index = 10)
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "第三属性值",index = 11)
    private String thirdAttributeValueName;

    public String getGoodsName() {
        StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append(goodsName);

        if (StringUtils.isNotEmpty(getFirstAttributeValueName())) {
            stringBuilder.append("-").append(getFirstAttributeValueName());
        }
        if (StringUtils.isNotEmpty(getSecondAttributeValueName())) {
            stringBuilder.append("-").append(getSecondAttributeValueName());
        }
        if (StringUtils.isNotEmpty(getThirdAttributeValueName())) {
            stringBuilder.append("-").append(getThirdAttributeValueName());
        }
        return stringBuilder.toString();
    }
    public String getWarehouseName(){
        String name = warehouseName;
        if(warehouseFlag == 1){
            name = "";
        }
        return name;
    }


}
