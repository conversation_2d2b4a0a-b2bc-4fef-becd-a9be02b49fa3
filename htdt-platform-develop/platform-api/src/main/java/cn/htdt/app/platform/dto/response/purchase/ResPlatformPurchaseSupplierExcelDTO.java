package cn.htdt.app.platform.dto.response.purchase;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;

import java.io.Serializable;

/**
 *  供应商信息导出Vo
 *
 * <AUTHOR>
 * @date 2020年9月11日
 */
@Data
public class ResPlatformPurchaseSupplierExcelDTO extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 供应商编号
     */
    @ExcelProperty(value = "供应商编号",index = 0)
    @ColumnWidth(40)
    private String supplierCode;

    /**
     * 供应商名称
     */
    @ExcelProperty(value = "供应商名称",index = 1)
    @ColumnWidth(40)
    private String supplierName;
    /**
     * 供应商助记码
     */
    @ExcelProperty(value ="供应商助记码",index = 2)
    @ColumnWidth(40)
    private String supplierHelpCode;
    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人",index = 3)
    @ColumnWidth(20)
    private String linkManName;
    /**
     * 联系人手机号
     */
    @ExcelProperty(value = "联系人手机号",index = 4)
    @ColumnWidth(20)
    private String linkManTelphone;
}
