package cn.htdt.app.platform.dto.response.promotion;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Data
public class ResPlatformLotteryRuleDTO extends ReqComPageDTO {

    /***活动基本信息**/
    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动名称")
    private String promotionName;

    @ApiModelProperty(value = "活动短链接")
    private String shortLink;

    @ApiModelProperty(value = "图片类型(1:默认图片 2:自定义图片)")
    private Integer promotionPictureType;

    @ApiModelProperty(value = "图片url")
    private String promotionPictureUrl;

    @ApiModelProperty(value = "图片活动名称")
    private String promotionActityName;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String userScope;

    @ApiModelProperty(value = "活动对象 1001: 不限制 1002: 仅限老用户 1003: 仅限新用户")
    private String joinUserScope;

    @ApiModelProperty(value = "活动说明")
    private String promotionExplain;

    @ApiModelProperty(value = "活动时间段类型 (1001:全天 1002:指定时间段)")
    private String effectivePeriodType;

    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime effectiveTime;

    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDateTime invalidTime;

    @ApiModelProperty(value = "每日开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime dailyStartTime;

    @ApiModelProperty(value = "每日结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HH:mm:ss", timezone = "GMT+8")
    private LocalTime dailyEndTime;

    @ApiModelProperty(value = "来源 1001：平台 1002：商家 1003：店铺")
    private String sourceType;

    @ApiModelProperty(value = "活动类型 1000:抽奖  2000:商品促销 3000:优惠券")
    private String activityType;

    @ApiModelProperty(value = "促销类型 1001:大转盘 1002:套圈圈 1003:打地鼠 1004:扭蛋机 1005:刮刮乐 1006:膨胀红包 2001:秒杀 2002:拼团 2003:预售 2004:单品促销 2005:满额促销 2006:满量促销 2007:满额赠 2008:套餐 3001:平台-手动发代理人券 3002::平台-手动发粉丝券 3003:平台-网店代理人领券 3004:平台-网店粉丝领券 3005:店铺-手动发粉丝券 3006:店铺-网店粉丝领券 3007:膨胀红包 3100:抽奖券")
    private String promotionType;

    @ApiModelProperty(value = "活动状态 1001:活动未开始 1002:活动进行中，1003:活动已结束")
    private String status;

    @ApiModelProperty(value = "活动有效期（1001：长期有效 1002：指定日期）")
    private String periodValidity;

    @ApiModelProperty(value = "上下架状态（1：未上架 2：上架）")
    private Integer upDownFlag;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "店铺上下架状态（1：未上架 2：上架）")
    private Integer storeUpDownFlag;


    /***抽奖规则**/
    @ApiModelProperty(value = "抽奖类型 1001:直接抽奖 1002:订单抽奖")
    private String lotteryType;

    @ApiModelProperty(value = "抽奖上架地点 1001:网店抽奖活动专区 1002:专属链接")
    private String lotteryShowLocation;

    @ApiModelProperty(value = "用户每日抽奖次数")
    private Integer userDailyDrawTimes;

    @ApiModelProperty(value = "用户每日中奖次数")
    private Integer userDailyWinningTimes;

    @ApiModelProperty(value = "用户总抽奖次数")
    private Integer userTotalDrawTimes;

    @ApiModelProperty(value = "用户总中奖次数")
    private Integer userTotalWinningTimes;

    @ApiModelProperty(value = "店铺每日中奖次数")
    private Integer storeDailyWinningTimes;

    @ApiModelProperty(value = "店铺总中奖次数")
    private Integer storeTotalWinningTimes;

    @ApiModelProperty(value = "是否有分享次数（1:否 2:是）")
    private Integer shareTimesLimitFlag;

    @ApiModelProperty(value = "每次分享获得额外抽奖次数")
    private Integer shareGainDrawTimes;

    @ApiModelProperty(value = "分享最高可获抽奖次数")
    private Integer shareMaxGainDrawTimes;

    @ApiModelProperty(value = "是否转换成商城券（1:否 2:是）")
    private Integer changePurchaseCouponFlag;

    @ApiModelProperty(value = "是否有金币抽奖次数")
    private Integer goldTimesLimitFlag;

    @ApiModelProperty(value = "每次消耗金币数")
    private Integer goldNum;

    @ApiModelProperty(value = "金币最高可获抽奖次数")
    private Integer goldMaxGainDrawTimes;

    @ApiModelProperty(value = "单笔订单满额")
    private BigDecimal orderAmount;

    @ApiModelProperty(value = "单笔订单满额获得抽奖次数")
    private Integer orderNum;

    @ApiModelProperty(value = "是否有积分抽奖次数")
    private Integer scoreTimesLimitFlag;

    @ApiModelProperty(value = "每次消耗积分数")
    private Integer scoreNum;

    @ApiModelProperty(value = "积分最高可获抽奖次数")
    private Integer scoreMaxGainDrawTimes;

    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    @ApiModelProperty(value = "创建ID")
    private String createNo;

    @ApiModelProperty(value = "创建名称")
    private String createName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改ID")
    private String modifyNo;

    @ApiModelProperty(value = "修改名称")
    private String modifyName;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "数据逻辑删除标识，默认1未删除，其余已删除")
    private Integer deleteFlag = 1;


    /***优惠券转商城券规则**/
    @ApiModelProperty(value = "优惠券编号")
    private String mallCouponNo;

    @ApiModelProperty(value = "优惠券名称")
    private String mallCouponName;

    @ApiModelProperty(value = "优惠券描述")
    private String mallCouponDescribe;

    @ApiModelProperty(value = "优惠券发放方式（1001-自动发放，1002-会员领取，1003-触发返券）")
    private String mallCouponProvideType;

    @ApiModelProperty(value = "优惠券类型 （1001:满减券 1002:折扣券 1003:现金券）")
    private String mallCouponType;

    @ApiModelProperty(value = "使用门槛阈值（满减时表示满多少元）")
    private BigDecimal mallDiscountThreshold;

    @ApiModelProperty(value = "折扣券单次使用百分比值")
    private Integer mallDiscountPercent;

    @ApiModelProperty(value = "券面额")
    private BigDecimal mallCouponValue;

    @ApiModelProperty(value = "券有效期（1001：长期有效 1002：指定日期 1003:自领取后天数有效）")
    private String mallCouponPeriodValidity;

    @ApiModelProperty(value = "券开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime mallCouponEffectiveTime;

    @ApiModelProperty(value = "券结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime mallCouponInvalidTime;

    @ApiModelProperty(value = "券使用范围(1001:POP 1002:自营 1003:归属平台公司 1004:全部)")
    private String mallCouponUseScope;

    @ApiModelProperty(value = "项目编号")
    private String projectCode;

    @ApiModelProperty(value = "项目名称")
    private String projectName;


    @ApiModelProperty(value = "有效期-开始时间")
    private String effectiveTimeStr;

    @ApiModelProperty(value = "有效期-结束时间")
    private String invalidTimeStr;

    @ApiModelProperty(value = "活动店铺类型(1001:全部店铺 1002:报名活动)")
    private String storeType;

    @ApiModelProperty(value = "中奖记录数")
    private Integer recordCount;

    @ApiModelProperty(value = "报名活动的店铺是否显示上下架")
    private Integer showShelf = 0;

    @ApiModelProperty(value = "总计中奖数量")
    private Integer allLotteryNum;

    @ApiModelProperty(value = "创建来源 1000:PC  2000:APP")
    private String createSource;

}
