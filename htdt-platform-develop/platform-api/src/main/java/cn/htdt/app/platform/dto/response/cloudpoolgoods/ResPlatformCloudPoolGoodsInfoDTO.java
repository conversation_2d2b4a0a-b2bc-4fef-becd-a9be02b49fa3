package cn.htdt.app.platform.dto.response.cloudpoolgoods;

import cn.htdt.app.platform.dto.response.goods.ResPlatformSeriesGoodsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 云池申请商品列表查询响应DTO
 *
 * <AUTHOR>
 * @date 2021/1/20
 **/
@Data
public class ResPlatformCloudPoolGoodsInfoDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "商品编号，包含普通商品、套餐商品等所有商品")
    private String goodsNo;

    @ApiModelProperty(value = "云池商品状态: 1001未上架，1002已上架，1003已删除，1004已失效")
    private String cloudPoolGoodsStatus;

    @ApiModelProperty(value = "云池商品状态Value")
    private String cloudPoolGoodsStatusValue;

    @ApiModelProperty(value = "售卖状态：1001可售卖，1002不可售卖")
    private String goodsSaleStatus;

    @ApiModelProperty(value = "售卖状态Value")
    private String goodsSaleStatusValue;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "商品形式value")
    private String goodsFormValue;

    @ApiModelProperty(value = "商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品")
    private String goodsType;

    @ApiModelProperty(value = "商品类型value")
    private String goodsTypeValue;

    @ApiModelProperty(value = "商品类目ID")
    private String categoryNo;

    @ApiModelProperty(value = "商品类目名称")
    private String categoryName;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "商品品牌ID")
    private String brandNo;

    @ApiModelProperty(value = "商品品牌名称")
    private String brandName;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @ApiModelProperty(value = "商品来源类型1001-平台自建;1002-商家自建;1003-店铺自建;1004:一键代发;1005:商家分发")
    private String goodsSourceType;

    @ApiModelProperty(value = "商品来源类型值")
    private String goodsSourceTypeValue;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "最小零售价")
    private BigDecimal minRetailPrice;

    @ApiModelProperty(value = "最大零售价")
    private BigDecimal maxRetailPrice;

    @ApiModelProperty(value = "市场价")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "最小市场价")
    private BigDecimal minMarketPrice;

    @ApiModelProperty(value = "最大市场价")
    private BigDecimal maxMarketPrice;

    @ApiModelProperty(value = "采购价")
    private BigDecimal purchasePrice;

    @ApiModelProperty(value = "最小采购价")
    private BigDecimal minPurchasePrice;

    @ApiModelProperty(value = "最大采购价")
    private BigDecimal maxPurchasePrice;

    @ApiModelProperty(value = "可售库存")
    private BigDecimal canSaleStockNum;

    @ApiModelProperty(value = "实际库存")
    private BigDecimal realStockNum;

    @ApiModelProperty(value = "商品主图")
    private String mainPictureUrl;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店编号")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "分销店总数")
    private Integer distributionStoreTotal;

    @ApiModelProperty(value = "分销店上架总数")
    private Integer distributionStoreUpShelfTotal;

    @ApiModelProperty(value = "系列商品")
    private ResPlatformSeriesGoodsDTO seriesGoods;

    @ApiModelProperty(value = "云池供货价")
    private BigDecimal cloudPoolSupplyPrice;

    @ApiModelProperty(value = "最小云池供货价")
    private BigDecimal minCloudPoolSupplyPrice;

    @ApiModelProperty(value = "最大云池供货价")
    private BigDecimal maxCloudPoolSupplyPrice;

    @ApiModelProperty(value = "代理人佣金比例（%）")
    private BigDecimal agentCommissionRatio;

    @ApiModelProperty(value = "最小代理人佣金比例（%）")
    private BigDecimal minAgentCommissionRatio;

    @ApiModelProperty(value = "最大代理人佣金比例（%）")
    private BigDecimal maxAgentCommissionRatio;

    @ApiModelProperty(value = "代理人佣金")
    private BigDecimal agentCommission;

    @ApiModelProperty(value = "最小代理人佣金")
    private BigDecimal minAgentCommission;

    @ApiModelProperty(value = "最大代理人佣金")
    private BigDecimal maxAgentCommission;

    @ApiModelProperty(value = "分销店佣金比例（%）")
    private BigDecimal distributionStoreCommissionRatio;

    @ApiModelProperty(value = "最小分销店佣金比例（%）")
    private BigDecimal minDistributionStoreCommissionRatio;

    @ApiModelProperty(value = "最大分销店佣金比例（%）")
    private BigDecimal maxDistributionStoreCommissionRatio;

    @ApiModelProperty(value = "分销店佣金")
    private BigDecimal distributionStoreCommission;

    @ApiModelProperty(value = "最小分销店佣金")
    private BigDecimal minDistributionStoreCommission;

    @ApiModelProperty(value = "最大分销店佣金")
    private BigDecimal maxDistributionStoreCommission;

    @ApiModelProperty(value = "平台服务费")
    private BigDecimal platformServiceCommission;

    @ApiModelProperty(value = "最小平台服务费")
    private BigDecimal minPlatformServiceCommission;

    @ApiModelProperty(value = "最大平台服务费")
    private BigDecimal maxPlatformServiceCommission;

    @ApiModelProperty(value = "销量")
    private BigDecimal salesVolume;

    @ApiModelProperty(value = "审核原因")
    private String auditMessage;

    @ApiModelProperty(value = "失败类型 1001-商品基础信息审核不通过, 1002-该商品不适合作为云池商品推广")
    private String failureType;

    @ApiModelProperty(value = "云池零售价")
    private BigDecimal cloudPoolRetailPrice;

    @ApiModelProperty(value = "最小云池零售价")
    private BigDecimal minCloudPoolRetailPrice;

    @ApiModelProperty(value = "最大云池零售价")
    private BigDecimal maxCloudPoolRetailPrice;

    @ApiModelProperty(value = "云池市场价")
    private BigDecimal cloudPoolMarketPrice;

    @ApiModelProperty(value = "最小云池市场价")
    private BigDecimal minCloudPoolMarketPrice;

    @ApiModelProperty(value = "最大云池市场价")
    private BigDecimal maxCloudPoolMarketPrice;

    @ApiModelProperty(value = "上架状态 1001-未上架;1002-已上架")
    private String shelfStatus;

    @ApiModelProperty(value = "商家账号")
    private String merberCode;

    /**
     * 商品上下架类型, 用于不同的区域查询不同上架类型的云池商品, 1001: 正常上架 1002: 仅上架至活动区域, 参考CloudPoolGoodsShelfTypeEnum枚举
     */
    @ApiModelProperty(value = "上架类型: 1001-正常上架 1002-仅上架至活动区域")
    private String shelfType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
