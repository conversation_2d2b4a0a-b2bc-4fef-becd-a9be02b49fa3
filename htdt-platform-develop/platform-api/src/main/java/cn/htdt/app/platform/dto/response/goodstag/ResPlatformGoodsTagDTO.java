package cn.htdt.app.platform.dto.response.goodstag;

import cn.htdt.common.dto.request.ReqComPageDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 商品标签request请求DTO
 *
 * <AUTHOR>
 * @date 2020年9月3日
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ResPlatformGoodsTagDTO extends ReqComPageDTO implements Serializable {

    private static final long serialVersionUID = 4726617595942024708L;
    /**
     * 标签id
     */
    @ApiModelProperty(value = "标签编号")
    private String tagNo;

    /**
     * 标签名称
     */
    @ApiModelProperty(value = "标签名称")
    private String tagName;

    /**
     * 标签文案
     */
    @ApiModelProperty(value = "标签文案")
    private String tagContent;

    /**
     * 标签类型(1001:商品角标;1002:服务标签;1003:促销标签)
     */
    @ApiModelProperty(value = "标签类型(1001:商品角标;1002:服务标签;1003:促销标签)")
    private String tagType;


    /**
     * 促销活动类型(1001:秒杀 1002:满赠 1003:满折)
     */
    @ApiModelProperty(value = "促销活动类型(1001:秒杀 1002:满赠 1003:满折)")
    private String promotionType;

    /**
     * 标签图片类型(1:默认图片 2:自定义图片)
     */
    @ApiModelProperty(value = "标签图片类型(1:默认图片 2:自定义图片)")
    private Boolean tagPictureType;

    /**
     * 标签图片url
     */
    @ApiModelProperty(value = "标签图片url")
    private String tagPictureUrl;

    /**
     * 显示类型(1:列表 2:详情 多选逗号分隔)
     */
    @ApiModelProperty(value = "显示类型(1:列表 2:详情 多选逗号分隔)")
    private String showType;

    /**
     * 显示位置(1:主图左上角 2:主图右上角 3:主图左下角 4:主图右下角 5:商品名称前面 6：商品名称后面)
     */
    @ApiModelProperty(value = "显示位置(1:主图左上角 2:主图右上角 3:主图左下角 4:主图右下角 5:商品名称前面 6：商品名称后面)")
    private Integer showPosition;

    /**
     * 优先级
     */
    @ApiModelProperty(value = "优先级")
    private Integer priorityLevel;

    /**
     * 有效期类型(1:长期有效 2:自定义)
     */
    @ApiModelProperty(value = "有效期类型(1:长期有效 2:自定义)")
    private Integer timeValidity;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private LocalDateTime beginTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private LocalDateTime endTime;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 商家编号
     */
    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    /**
     * 商家名称
     */
    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    @ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    @ApiModelProperty(value = "店铺名称，对应orgName")
    private String storeName;

    /**
     * 是否可用:默认1，1：可用，其余不可用
     */
    @ApiModelProperty(value = "是否可用:默认1，1：可用，其余不可用")
    private Integer disableFlag;

    /**
     * 创建人ID
     */
    @ApiModelProperty(value = "创建人ID")
    private String createNo;

    /**
     * 最后一次修改人ID
     */
    @ApiModelProperty(value = "最后一次修改人ID")
    private String modifyNo;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "最后一次操作时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
