package cn.htdt.app.platform.dto.response.storecategory;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 店铺类目选择方式 响应DTO
 *
 * <AUTHOR>
 * @date 2020/10/22
 **/
@Data
public class ResPlatformStoreCategoryChooseDTO implements Serializable {
    private static final long serialVersionUID = 8080097434177355327L;

    /**
     * 选择方式(1001:自建店铺类目树 1002:引用销售类目树 )
     */
    @ApiModelProperty(value = "选择方式(1001:自建店铺类目树 1002:引用销售类目树 )")
    private String chooseType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
