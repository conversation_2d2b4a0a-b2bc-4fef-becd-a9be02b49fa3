package cn.htdt.app.platform.dto.response.purchasereturnorder;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 *
 * 功能描述: 采购退货单响应dto
 * @author: luh
 * @date: 2020/10/13
 */
@Data
public class ResPlatformAddReturnGoodsDTO implements Serializable {
    private static final long serialVersionUID = -2483928607848010891L;
    /**
     *退货单商品行编码
     */
    private String returnGoodsCode;
    /**
     * 商品id
     * */
    private String goodsNo;
    /**
     * 商品名称
     * */
    private String goodsName;
    /**
     *单价单位
     */
    private String purchaseUnit;
    /**
     * 入库数量
     * */
    private BigDecimal purchaseNum;
    /**
     *单价
     */
    private BigDecimal purchaseUnitPrice;

    /**
     *采购价
     */
    private BigDecimal purchasePrice;
    /**
     *是否入仓 1-有实体仓;2-无实体仓
     */
    private Integer warehouseType;
    /**
     * 是否入库形式Value 1-有实体仓;2-无实体仓
     * */
    private String warehouseTypeValue;
    /**
     * 仓库编码
     * */
    private String warehouseNo;
    /**
     * 仓库名称
     * */
    private String warehouseName;
    /**
     * 总入库数量
     * */
    private BigDecimal storageCount;
    /**
     * 商品删除状态
     * */
    private String goodsDeleteFlag;
    /**
     * 剩余可退货数量
     * */
    private BigDecimal applyStorageNum;
    /**
     * 退货中数量
     * */
    private BigDecimal returningCount;
    /**
     * 已退货数量
     * */
    private BigDecimal returnedCount;

    /**
     *本次退货数量
     */
    private BigDecimal returnRequestCount;

    /**
     * 计量单位ID-返回
     */
    @ApiModelProperty("计量单位ID")
    private String calculationUnitNo;

    /**
     * 计量单位名称
     */
    @ApiModelProperty("计量单位名称")
    private String calculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    @ApiModelProperty("主计量单位对应关系数值")
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位id
     */
    @ApiModelProperty("辅计量单位id")
    private String assistCalculationUnitNo;

    /**
     * 辅计量单位名称
     */
    @ApiModelProperty("辅计量单位名称")
    private String assistCalculationUnitName;

    /**
     * 辅计量单位对应关系数值
     */
    @ApiModelProperty("辅计量单位对应关系数值")
    private BigDecimal assistUnitNum;

}
