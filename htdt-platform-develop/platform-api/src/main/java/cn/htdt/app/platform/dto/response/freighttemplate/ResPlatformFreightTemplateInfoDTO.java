package cn.htdt.app.platform.dto.response.freighttemplate;



import java.io.Serializable;
import java.util.List;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
public class ResPlatformFreightTemplateInfoDTO implements Serializable {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 3506836397300961939L;

	
	/**
	 * 模板编号
	 */
	@ApiModelProperty(value = "模板编号")
	private String templateNo;
	
	
	/**
	 * 模板名称
	 */
	@ApiModelProperty(value = "模板名称")
	private String name;
	
	/**
	 * 运费模板主表
	 */
	private ResPlatformFreightTemplateDTO freightTemplateDTO;
	
	/**
	 * 运费模板详情子表
	 */
	private List<ResPlatformFreightTemplateItemDTO> freightTemplateItemList;
}
