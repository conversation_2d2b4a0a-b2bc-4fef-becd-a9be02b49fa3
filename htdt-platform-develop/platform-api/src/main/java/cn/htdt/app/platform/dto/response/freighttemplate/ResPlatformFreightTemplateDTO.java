package cn.htdt.app.platform.dto.response.freighttemplate;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运费模板请求实体
 *
 * <AUTHOR>
 * @date 2020年9月10日
 */
@Data
public class ResPlatformFreightTemplateDTO implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2595308602600781761L;

	/**
	 * 模板编号
	 */
	@ApiModelProperty(value = "模板编号")
	private String templateNo;

	/**
	 * 商家编号
	 */
	@ApiModelProperty(value = "商家编号")
	private String merchantNo;

	/**
	 * 归属平台编号
	 */
	@ApiModelProperty(value = "归属平台编号")
	private String companyNo;

	/**
	 * 运费模板名称
	 */
	@ApiModelProperty(value = "运费模板名称")
	private String name;

	/**
	 * 配送方式编码
	 */
	@ApiModelProperty(value = "配送方式编码")
	private String distributionCode;

	/**
	 * 配送区域类型 0:默认全国; -1:非默认，自定义区域
	 */
	@ApiModelProperty(value = "配送区域类型 0:默认全国;  -1:非默认，自定义区域")
	private String distributionType;

	/**
	 * 类型区分 {1010:自定义运费; 1011:卖家承担运费; }
	 */
	@ApiModelProperty(value = "类型区分 {1001:自定义运费; 1002:卖家承担运费;}")
	private String type;

	/**
	 * 计费方式 包邮条件 { 计费方式 1010:按件数; 1011:按重量; 1012:按体积; 1013:按金额;}
	 */
	@ApiModelProperty(value = "计费方式 包邮条件 {计费方式  1010:按件数; 1030:一口价}")
	private String chargeWay;

	/**
	 * 配送区域
	 */
	@ApiModelProperty(value = "配送区域")
	private String distributionRegion;

	/**
	 * 是否已经删除，默认1未删除，其余已删除
	 */
	@ApiModelProperty(value = "是否已经删除，默认1未删除，其余已删除")
	private Integer disableFlag;


	/**
	 * 首件
	 */
	@ApiModelProperty(value = "首件,模板默认")
	private Integer templateFirstPiece;

	/**
	 * 首费
	 */
	@ApiModelProperty(value = "首费,模板默认")
	private BigDecimal templateFirstAmount;

	/**
	 * 续件
	 */
	@ApiModelProperty(value = "续件,模板默认")
	private Integer templateNextPiece;

	/**
	 * 续费
	 */
	@ApiModelProperty(value = "续费,模板默认")
	private BigDecimal templateNextAmount;

	/**
	 * 一口价
	 */
	@ApiModelProperty(value = "一口价,模板默认")
	private BigDecimal templateFixedAmount;

	/**
	 * 门店编号，对应orgId
	 */
	@ApiModelProperty(value = "门店编号，对应orgId")
	private String storeNo;

	/**
	 * 模板来源 1001-平台自建;1002-商家自建;1003-店铺自建
	 */
	@ApiModelProperty(value = "模板来源 1001-平台自建;1002-商家自建;1003-店铺自建")
	private String templateSourceType;

	/**
	 * 是否默认 1001:默认; 1002:非默认;
	 */
	@ApiModelProperty(value = "是否默认模板   1001:默认;  1002:非默认;")
	private String templateDefaultFlag;


	/**
	 * 是否满额免运费，默认1否，2是
	 */
	@ApiModelProperty(value = "是否满额免运费，默认1否，2是")
	private Integer templateFreeFlag;

	/**
	 * 免运费金额限制
	 */
	@ApiModelProperty(value = "免运费金额限制")
	private BigDecimal templateFreeAmount;
    /**
     * 修改时间
     */
	
	@ApiModelProperty(value = "最后操作时间")
	@JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;
}
