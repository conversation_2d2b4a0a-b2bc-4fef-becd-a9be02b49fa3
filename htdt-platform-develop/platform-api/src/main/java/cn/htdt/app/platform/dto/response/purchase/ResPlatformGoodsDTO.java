package cn.htdt.app.platform.dto.response.purchase;


import cn.htdt.goodsprocess.dto.response.calculationunit.ResCalculationUnitGoodsDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 功能描述: 商品响应dto
 *
 * @author: 张宇
 * @date: 2020/9/11 14:51
 */
@Data
public class ResPlatformGoodsDTO implements Serializable {

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    private String goodsNo;

    private String originalGoodsNo;
    /**
     * 第三方ID
     */
    private String thirdGoodsId;

    /**
     * 商品形式 1-普通商品;2-系列商品;3-称重商品
     */
    private String goodsForm;

    /**
     * 商品形式value
     */
    private String goodsFormValue;

    /**
     * 商品类型 1001-实物商品;1002-虚拟商品;1003-服务商品（仅供平台使用）;1004-称重商品
     */
    private String goodsType;

    /**
     * 商品类型value
     */
    private String goodsTypeValue;

    /**
     * 商品助记码
     */
    private String goodsHelpCode;

    /**
     * 商品类目ID
     */
    private String categoryId;

    /**
     * 商品类目名称
     */
    private String categoryName;

    /**
     * 商品品牌ID
     */
    private String brandId;

    /**
     * 商品品牌名称
     */
    private String brandName;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 是否入仓:1-否;2-是;
     */
    private Integer warehouseFlag;

    /**
     * 是否入仓value
     */
    private String wareHouseFlagValue;

    /**
     * 系列商品父ID
     */
    private String parentGoodsId;

    /**
     * 运费模板ID
     */
    private String freightTemplateId;

    /**
     * 销售区域ID
     */
    private String salesAreaId;

    /**
     * 商品来源类型(1-平台自建;2-商家自建;3-店铺自建;4-一键代发)
     */
    private Integer sourceType;

    /**
     * 商品型号
     */
    private String goodsModel;

    /**
     * 卖点描述
     */
    private String goodsSaleDescription;

    /**
     * 计量单位ID
     */
    private String calculationUnitId;

    /**
     * 计量单位符号
     */
    private String calculationUnitSymbol;

    /**
     * 计量单位名称
     */
    private String calculationUnitName;

    /**
     * 是否有辅计量单位:1-否;2-是;
     */
    private Integer standardFlag;

    /**
     * 辅计量单位id
     */
    private String assistCalculationUnitId;

    /**
     * 辅计量单位符号
     */
    private String assistCalculationUnitSymbol;

    /**
     * 辅计量单位名称
     */
    private String assistCalculationUnitName;

    /**
     * 主计量单位对应关系数值
     */
    private BigDecimal mainUnitNum;

    /**
     * 辅计量单位对应关系数值
     */
    private BigDecimal assistUnitNum;

    /**
     * 转换率(辅转主)
     */
    private BigDecimal conversionRate;

    /**
     * 保修期（天）
     */
    private Integer guaranteeDays;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 商品状态1-未上架;2-审核中;3-审核失败;4:已上架;5:未分发;6:分发成功;7:已失效;
     */
    private String goodsStatus;

    /**
     * 商品状态value
     */
    private String goodsStatusValue;

    /**
     * 支付方式(1:到店支付 2：网店支付; 多个逗号分隔)
     */
    private String payType;

    /**
     * 配送方式(1:自提 2：配送)
     */
    private String deliveryType;

    /**
     * 首次上架时间
     */
    private LocalDateTime firstShelfTime;

    /**
     * 审核原因
     */
    private String auditMessage;

    /**
     * 是否纳入商品主数据库(1:否 2:是)
     */
    private Integer mainDataFlag;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 归属平台编码
     */
    private String companyNo;

    /**
     * 归属平台名称
     */
    private String companyName;

    /**
     * 归属分部编码
     */
    private String branchNo;

    /**
     * 归属分部名称
     */
    private String branchName;

    /**
     * 供应商编码
     */
    private String supplierCode;

    /**
     * 供应商名称
     */
    private String supplierName;

    /**
     * 供应商手机号
     */
    private String supplierTelphone;

    /**
     * 限价
     */
    private BigDecimal limitPrice;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 零售价
     */
    private BigDecimal retailPrice;

    /**
     * 商品主图
     */
    private String mainPictureUrl;
    /**
     * 可售库存
     */
    private BigDecimal canSaleStockNum;

    /**
     * 实际库存
     */
    private BigDecimal realStockNum;

    /**
     * 关联仓库
     */
    private String collectWarehouseName;

    /**
     * 是否可用:默认2，1：不可用2:可用
     */
    private Integer disableFlag;

    /**
     * 第一属性值编码
     */
    private String firstAttributeValueName;

    /**
     * 第二属性值编码
     */
    private String secondAttributeValueName;

    /**
     * 第三属性值编码
     */
    private String thirdAttributeValueName;

    /**
     * 是否启用串码(1:否 2:是)
     */
    private Integer imeiFlag;

    /**
     * 2023-09-15蛋品-genghao-采购商品导入
     * 计量单位ID
     */
    private String calculationUnitNo;

    /**
     * 2023-09-15蛋品-genghao-采购商品导入
     * 辅计量单位id
     */
    private String assistCalculationUnitNo;

    /**
     * 多单位商品类型 1001:主单位商品 1002:子
     *
     * 2023-08-25蛋品-genghao-采购管理-获取采购商品列表
     */
    @ApiModelProperty(value = "多单位商品类型")
    private String multiUnitType;

    /**
     * 多单位主品编号
     * <p>
     * 2023-08-25蛋品-genghao-采购管理-获取采购商品列表
     */
    @ApiModelProperty(value = "多单位主品编号")
    private String multiUnitGoodsNo;

    /**
     * 多单位商品单位集合
     *
     * 2023-08-25蛋品-genghao-采购管理-获取采购商品列表
     */
    @ApiModelProperty(value = "多单位商品单位集合")
    private List<ResCalculationUnitGoodsDTO> calculationUnitGoodsDTOS;


    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
