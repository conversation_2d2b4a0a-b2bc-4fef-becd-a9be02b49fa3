package cn.htdt.app.platform.dto.response.promotion;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.OrderStatusEnum;
import cn.htdt.common.enums.goods.DeliveryWayEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 秒杀活动的订单列表响应参数
 *
 * <AUTHOR>
 * @since 2021-07-12
 */
@Data
public class ResPlatformSoPromotionDTO implements Serializable {

    private static final long serialVersionUID = 6660683361026295096L;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "下单时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime orderCreateTime;

    @ApiModelProperty("拼接商品编号")
    private String goodsNoConcat;

    @ApiModelProperty(value = "拼接商品名称")
    private String goodsNameConcat;

    @ApiModelProperty(value = "订单应收总金额")
    private BigDecimal shouldAmount;

    @ApiModelProperty(value = "订单实付金额")
    private BigDecimal realAmount;

    @ApiModelProperty(value = "下单人名称")
    private String buyerName;

    @ApiModelProperty(value = "下单人联系号码")
    private String buyerMobile;

    @ApiModelProperty(value = "订单状态，1010-待支付、1030-待确认、1050-待发货、1060-待收货、1061-部分发货、1999-交易成功、9000-交易关闭")
    @Converter(enumClass = OrderStatusEnum.class, fieldName = "orderStatusName")
    private String orderStatus;

    @ApiModelProperty(value = "订单状态描述")
    private String orderStatusName;

    @ApiModelProperty("分销店对应商户编码")
    private String distributionMerchantNo;

    @ApiModelProperty("分销店对应商户名称")
    private String distributionMerchantName;

    @ApiModelProperty("分销店铺编号")
    private String distributionStoreNo;

    @ApiModelProperty("分销店铺名称")
    private String distributionStoreName;

    @ApiModelProperty(value = "配送方式")
    @Converter(enumClass = DeliveryWayEnum.class, fieldName = "orderDeliveryWayName")
    private String orderDeliveryWay;

    @ApiModelProperty(value = "配送方式名称")
    private String orderDeliveryWayName;

    @ApiModelProperty(value = "下单人编号")
    private String buyerNo;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
