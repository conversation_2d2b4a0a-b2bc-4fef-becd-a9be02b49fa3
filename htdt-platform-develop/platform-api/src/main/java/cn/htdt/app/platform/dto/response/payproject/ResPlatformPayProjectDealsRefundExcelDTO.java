package cn.htdt.app.platform.dto.response.payproject;

import cn.htdt.app.platform.converter.ProductLabellConverter;
import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 *    退款单导出
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-23
 */
@Data
public class ResPlatformPayProjectDealsRefundExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty(value = "订单编号", index = 0)
    @ColumnWidth(20)
    private String dealsNo;

    @ExcelProperty(value = "商家编号", index = 1)
    @ColumnWidth(20)
    private String merberCode;

    @ExcelProperty(value = "商家名称", index = 2)
    @ColumnWidth(20)
    private String merchantName;

    @ExcelProperty(value = "归属平台公司名称", index = 3)
    @ColumnWidth(20)
    private String companyName;

    @ExcelProperty(value = "归属分部名称", index = 4)
    @ColumnWidth(20)
    private String divisionName;

    @ExcelProperty(value = "订单标签", index = 5, converter = ProductLabellConverter.class)
    @ColumnWidth(20)
    private Integer productLabell;

    @ExcelProperty(value = "产品名称", index = 6)
    @ColumnWidth(20)
    private String packageName;

    @ExcelProperty(value = "退款金额", index = 7)
    @ColumnWidth(20)
    private BigDecimal refundPrice;

    @ExcelProperty(value = "付款时间", index = 8, converter = LocalDateTimeConverter.class)
    @ColumnWidth(20)
    private LocalDateTime payTime;

    @ExcelProperty(value = "退款时间", index = 9, converter = LocalDateTimeConverter.class)
    @ColumnWidth(20)
    private LocalDateTime createTime;



}
