package cn.htdt.app.platform.dto.response.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 活动中奖记录数量
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-03
 */
@Data
public class ResPlatformLotteryDrawNumDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "今日中奖数量")
    private Integer todayDrawNum;

    @ApiModelProperty(value = "总计中奖数量")
    private Integer allDrawNum;
}
