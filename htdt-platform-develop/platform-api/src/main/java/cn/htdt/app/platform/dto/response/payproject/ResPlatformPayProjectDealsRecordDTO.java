package cn.htdt.app.platform.dto.response.payproject;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 产品订单操作记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-10
 */
@Data
public class ResPlatformPayProjectDealsRecordDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="操作人手机号")
    private String userTel;

    @ApiModelProperty(value="操作内容")
    private String content;

    @ApiModelProperty(value="附件")
    private String enclosure;

    @ApiModelProperty(value="商家编号")
    private String merchantNo;

    @ApiModelProperty(value="订单编号")
    private String dealsNo;

    @ApiModelProperty(value="操作人姓名")
    private String createName;
    private String createNo;

    @ApiModelProperty(value="操作时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value="退款金额")
    private BigDecimal refundPrice;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
