package cn.htdt.app.platform.dto.response.warehousestockflow;

import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *
 * 功能描述: 商品组仓库库存流水响应dto
 * 
 */
@Data
public class ResPlatformGoodsGroupsWareHouseStockFlowDExcelDTO extends BaseRowModel implements Serializable {

    private static final long serialVersionUID = 1350712595427501473L;
    /**
     * 单据编号
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "单据编号", index = 9)
    private String billCode;
    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "商品组编号", index = 0)
    private String goodsNo;
    /**
     * 商品名称
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "商品组名称", index = 1)
    private String goodsName;
    /**
     * 计量单位名称
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "计量单位名称", index = 2)
    private String calculationUnitName;

    /**
     * 操作类型形式value(1001:加库 1002：减库)
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "操作类型", index = 3)
    private String operTypeValue;

    /**
     * 仓库编号
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "仓库编号", index = 6)
    private String warehouseNo;

    /**
     * 仓库名称
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "仓库名称", index = 7)
    private String warehouseName;
    /**
     * 操作库存数量
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "操作库存数量", index = 4)
    private BigDecimal stockNum;

    /**
     * 当前库存数量
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "当前库存数量", index = 5)
    private BigDecimal currentStockNum;

    /**
     * 来源单据类型形式value(1001:订单下单 1002:销售退货单 1003:调拨出库 1004:调拨入库 1005:采购入库 1006:采购退货出库 1007:盘点入库 1008:盘点出库 1009:发货出库)
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "来源单据类型", index = 8)
    private String billTypeValue;

    /**
     * 流水创建日期
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "流水创建日期", index = 13, converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 操作人
     */
    @ColumnWidth(40)
    @ExcelProperty(value = "操作人", index = 14)
    private String modifyName;

}
