package cn.htdt.app.platform.dto.response.goodsimei;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 *  商品串码信息导出DTO
 *
 * <AUTHOR>
 * @date 2020年9月11日
 */
@Data
public class ResPlatformWarehouseExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 仓库编码
     */
    @ExcelProperty(value = "仓库编码",index = 1)
    @ColumnWidth(40)
    private String warehouseNo;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称",index = 0)
    @ColumnWidth(40)
    private String warehouseName;

}
