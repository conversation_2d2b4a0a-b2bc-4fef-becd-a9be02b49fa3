package cn.htdt.app.platform.dto.response.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商促销活动参与商品实体类
 *
 * <AUTHOR>
 * @date 2021-07-02
 */
@Data
public class ResPlatformGoodsPromotionGoodsDTO implements Serializable {

    private static final long serialVersionUID = 7797722075946536167L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "系列商品主品商品编号，非系列商品商品编号")
    private String parentGoodsNo;

    @ApiModelProperty(value = "是否子品(1:否 2:是)")
    private Integer subGoodsFlag;

    @ApiModelProperty(value = "商品形式 1001-普通商品;1002-系列商品")
    private String goodsForm;

    @ApiModelProperty(value = "规格属性")
    private String attributeNames;

    @ApiModelProperty(value = "商品品牌编号")
    private String brandNo;

    @ApiModelProperty(value = "商品品牌")
    private String brandName;

    @ApiModelProperty(value = "销售类目编号")
    private String categoryNo;

    @ApiModelProperty(value = "销售类目")
    private String categoryName;

    @ApiModelProperty(value = "供货价")
    private BigDecimal cloudPoolSupplyPrice;

    @ApiModelProperty(value = "零售价")
    private BigDecimal retailPrice;

    @ApiModelProperty(value = "云池零售价")
    private BigDecimal cloudPoolRetailPrice;

    @ApiModelProperty(value = "活动价格（秒杀价、拼团价、预售价、搭配价）")
    private BigDecimal promotionPrice;

    @ApiModelProperty(value = "活动费用")
    private BigDecimal activityExpenses;

    @ApiModelProperty(value = "云池可售库存")
    private BigDecimal canSaleStockNum;

    @ApiModelProperty(value = "活动设置库存")
    private Integer settingStockNum;

    @ApiModelProperty(value = "活动剩余库存")
    private Integer remainStockNum;

    @ApiModelProperty(value = "限购数")
    private Integer limitBuyNum;

    @ApiModelProperty(value = "代理人佣金比例（%）")
    private BigDecimal agentCommissionRatio;

    @ApiModelProperty(value = "代理人佣金")
    private BigDecimal agentCommission;

    @ApiModelProperty(value = "分销店佣金比例（%）")
    private BigDecimal distributionStoreCommissionRatio;

    @ApiModelProperty(value = "分销店佣金")
    private BigDecimal distributionStoreCommission;

    @ApiModelProperty(value = "平台服务费比例（%）")
    private BigDecimal platformServiceCommissionRatio;

    @ApiModelProperty(value = "平台服务费")
    private BigDecimal platformServiceCommission;

    @ApiModelProperty(value = "平台净利润")
    private BigDecimal grossProfit;

    @ApiModelProperty(value = "供货店铺id")
    private String storeNo;

    @ApiModelProperty(value = "供货店铺名称")
    private String storeName;

    @ApiModelProperty(value = "酬劳类型：1佣金 2汇金币 3礼品 4专享现金券 5服务券 6话费券")
    private Integer rewardType;

    @ApiModelProperty(value = "酬劳类型")
    private String rewardTypeValue;

    @ApiModelProperty(value = "佣金百分比")
    private BigDecimal yjPercent;

    @ApiModelProperty(value = "分销酬劳")
    private String reward;

    @ApiModelProperty(value = "商品展示的状态值")
    private String goodsShowStatusValue;

    @ApiModelProperty(value = "是否互斥商品的标识，1-否，2-是")
    private Integer unAvailableFlag;

    // 20230928蛋品 lixiang  商品管理 多单位商品
    @ApiModelProperty(value = "多单位商品类型 1001:主单位商品 1002:子")
    private String multiUnitType;

    @ApiModelProperty(value = "多单位主品编号")
    private String multiUnitGoodsNo;

    @ApiModelProperty(value = "计量单位ID")
    private String calculationUnitNo;

    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
