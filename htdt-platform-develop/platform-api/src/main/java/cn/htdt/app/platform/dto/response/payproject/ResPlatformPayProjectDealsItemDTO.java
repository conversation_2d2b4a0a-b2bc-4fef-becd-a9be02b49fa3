package cn.htdt.app.platform.dto.response.payproject;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.enums.order.DealsProjectStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2021-08-10
 * @description 付费产品订单产品详情响应DTO
 **/
@Data
public class ResPlatformPayProjectDealsItemDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("产品名称")
    private String projectName;

    @ApiModelProperty("产品原价格")
    private BigDecimal originalPrice;

    @ApiModelProperty("产品价格")
    private BigDecimal payAmount;

    @ApiModelProperty(value="产品服务生效时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate projectStart;

    @ApiModelProperty(value="产品服务失效时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate projectEnd;

    @ApiModelProperty("订单产品状态 1000 未开始 1001 生效中 1002 已过期 1003 已失效")
    @Converter(enumClass = DealsProjectStatusEnum.class, fieldName = "dealsProjectStatusName")
    private String dealsProjectStatus;

    @ApiModelProperty("订产品状态名称")
    private String dealsProjectStatusName;

    @ApiModelProperty("产品类型：1:会员套餐 2:增值服务")
    private Integer projectType;

    @ApiModelProperty("产品唯一编码")
    private String projectKitNo;

    @ApiModelProperty("产品简介")
    private String projectIntroduction;

    @ApiModelProperty("核销码")
    private String activationCode;

    /**
     * 赠送权益内容
     **/
    @ApiModelProperty(value="赠送权益内容")
    private String giftRightsContent;

    /**
     * 赠送权益状态
     **/
    @ApiModelProperty(value="赠送权益状态")
    private String giftRightsStatus;

    /**
     * 收回短信条数
     **/
    @ApiModelProperty(value="收回短信条数")
    private Integer regainSmsNum;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
