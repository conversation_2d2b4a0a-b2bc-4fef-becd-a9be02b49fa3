package cn.htdt.app.platform.dto.response.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 报名活动统计DTO
 * <AUTHOR>
 * @date 2021/4/1 16:16
 */
@Data
public class ResPlatformEnrollApplyCountDTO  implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "报名统计")
    private Integer acceptCount;

    @ApiModelProperty(value = "受理统计")
    private Integer applyCount;

}
