package cn.htdt.app.platform.dto.response.inventory;

import cn.htdt.common.dto.request.ReqComPageDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点 返回DTO
 **/
@Data
public class ResPlatformInventoryDTO extends ReqComPageDTO implements Serializable {

    @ApiModelProperty(value = "盘点单号", name = "inventoryCode")
    private String inventoryCode;

    @ApiModelProperty(value = "仓库编号", name = "warehouseNo")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称", name = "warehouseName")
    private String warehouseName;

    @ApiModelProperty(value = "盘点状态:1001-创建;1002-盘点中;1003-关闭", name = "operType")
    private String operType;

    @ApiModelProperty(value = "是否可用:默认2，1：不可用 2：可用", name = "disableFlag")
    private Integer disableFlag;

    @ApiModelProperty(value = "创建人no", name = "createNo")
    private String createNo;

    @ApiModelProperty(value = "创建人名称", name = "createName")
    private String createName;

    @ApiModelProperty(value = "修改人no", name = "modifyNo")
    private String modifyNo;

    @ApiModelProperty(value = "修改人名称", name = "modifyName")
    private String modifyName;

    @ApiModelProperty(value = "商家编号", name = "merchantNo")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称", name = "merchantName")
    private String merchantName;

    @ApiModelProperty(value = "门店编号", name = "storeNo")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称", name = "storeName")
    private String storeName;

    @ApiModelProperty(value = "平台编号", name = "companyNo")
    private String companyNo;

    @ApiModelProperty(value = "平台名称", name = "companyName")
    private String companyName;

    @ApiModelProperty(value = "分部编号", name = "branchNo")
    private String branchNo;

    @ApiModelProperty(value = "分部名称", name = "branchName")
    private String branchName;

    @ApiModelProperty(value = "创建开始时间", name = "createTimeStartStr")
    private String createTimeStartStr;

    @ApiModelProperty(value = "创建结束时间", name = "createTimeEndStr")
    private String createTimeEndStr;

    @ApiModelProperty(value = "盘点详细内容", name = "detailDTOList")
    private List<ResPlatformInventoryDetailDTO> detailDTOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
