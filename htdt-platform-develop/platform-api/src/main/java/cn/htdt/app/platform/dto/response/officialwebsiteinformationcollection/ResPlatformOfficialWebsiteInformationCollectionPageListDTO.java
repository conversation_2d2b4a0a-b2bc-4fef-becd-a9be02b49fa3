package cn.htdt.app.platform.dto.response.officialwebsiteinformationcollection;

import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 获取列表DTO
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformOfficialWebsiteInformationCollectionPageListDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "信息收集编号")
    private String informationCollectionNo;

    @ApiModelProperty(value = "官网入口")
    private String officialWebsiteEntrance;

    @ApiModelProperty(value = "来源")
    private String infoSources;

    @ApiModelProperty(value = "公司")
    private String company;

    @ApiModelProperty(value = "姓名")
    private String name;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "手机号")
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.WEBSITE, fieldName = "phone")
    private String dsPhone;

    @ApiModelProperty(value = "所在城市")
    private String address;

    @ApiModelProperty(value = "合作意向")
    private String informationExplain;

    @ApiModelProperty(value = "目前行业")
    private String industry;

    @ApiModelProperty(value = "创建时间（提交时间）")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "信息录入人")
    private String createName;

    @ApiModelProperty(value = "最新修改人")
    private String modifyName;

    @ApiModelProperty(value = "信息管理状态 1001：待处理 1002：已处理，跟进中 1003：已处理，直接关闭 1004：已签约 1005：无合作意向")
    private String disposeStatus;

    @ApiModelProperty(value = "信息管理状态 1001：待处理 1002：已处理，跟进中 1003：已处理，直接关闭 1004：已签约 1005：无合作意向")
    private String disposeStatusName;

    @ApiModelProperty(value = "最新处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime newestTime;
}
