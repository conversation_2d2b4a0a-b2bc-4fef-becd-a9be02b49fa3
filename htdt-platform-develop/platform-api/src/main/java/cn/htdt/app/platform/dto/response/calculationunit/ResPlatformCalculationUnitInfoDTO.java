package cn.htdt.app.platform.dto.response.calculationunit;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 计量单位信息
 *
 * <AUTHOR>
 * @date 2020-09-16
 */
@Data
public class ResPlatformCalculationUnitInfoDTO implements Serializable {

    /**
     * 计量单位id
     */
    @ApiModelProperty(value = "计量单位id")
    private String calculationUnitNo;

    /**
     * 计量单位名称
     */
    @ApiModelProperty(value = "计量单位名称")
    private String calculationUnitName;

    /**
     * 计量单位符号
     */
    @ApiModelProperty(value = "计量单位符号")
    private String calculationUnitSymbol;

    /**
     * 数据来源类型(1:MDM同步 2:平台自建)
     */
    @ApiModelProperty(value = "数据来源类型(1001:MDM同步 1002:平台自建)")
    private String dataSourceType;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
