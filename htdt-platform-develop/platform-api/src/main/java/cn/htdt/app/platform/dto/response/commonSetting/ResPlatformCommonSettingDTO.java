package cn.htdt.app.platform.dto.response.commonSetting;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 公共设置表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-22
 */
@Data
public class ResPlatformCommonSettingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "设置类型SettingTypeEnum 10001:商品常用字段显示;10002:抹零设置;10003:价签打印模板设置;")
    private String settingType;

    @ApiModelProperty(value = "设置值")
    private String settingValue;

    /**
     * 数据来源
     */
    private String dataSourceType;

    /**
     * 商家id
     */
    private String merchantNo;

    /**
     * 店铺id
     */
    private String storeNo;

    public ResPlatformCommonSettingDTO(String settingType, String settingValue) {
        this.settingType = settingType;
        this.settingValue = settingValue;
    }
}
