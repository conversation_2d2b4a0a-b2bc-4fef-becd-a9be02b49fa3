package cn.htdt.app.platform.dto.response.goodsimei;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

@ColumnWidth(value = 20)
@Data
public class ResPlatformImportImeiWarehousingDTO implements Serializable {
    /**
     * 付款时间
     **/
    @ExcelProperty(value = "*入库类型")
    private String inventoryTypeStr;

    @ExcelProperty(value = "商品名称")
    private String goodsName;
    /**
     * 会员店账号
     **/
    @ExcelProperty(value = "*自定义商品编码")
    private String customGoodsNo;
    /**
     * 归属分部/事业部
     **/
    @ExcelProperty(value = "*串码")
    private String imei;

    @ExcelProperty(value = "供应商")
    private String supplierName;

    @ExcelProperty(value = "备注")
    private String remark;

    @ExcelProperty(value = "商品编码")
    private String goodsNo;

    @ExcelProperty(value = "供应商编码")
    private String supplierCode;

    @ExcelProperty(value = "校验结果")
    private String resultMsg;
}
