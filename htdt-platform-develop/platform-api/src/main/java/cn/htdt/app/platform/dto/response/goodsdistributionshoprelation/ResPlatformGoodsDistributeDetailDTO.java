package cn.htdt.app.platform.dto.response.goodsdistributionshoprelation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 详细说明.分发商品库存信息
 * <p>
 * Copyright: Copyright (c) 2020/12/7 20:50
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ResPlatformGoodsDistributeDetailDTO   implements Serializable {

    private static final long serialVersionUID = 7113624524066844404L;

    @ApiModelProperty(value = "原始商品编号")
    private String goodsNo;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "是否入仓 1：否 2: 是")
    private Integer warehouseFlag;

    @ApiModelProperty(value = "库存")
    private BigDecimal stockNum;

    @ApiModelProperty(value = "有仓库存信息")
    private List<ResPlatformDistributeWarehouseStockDTO> warehouseStockList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
