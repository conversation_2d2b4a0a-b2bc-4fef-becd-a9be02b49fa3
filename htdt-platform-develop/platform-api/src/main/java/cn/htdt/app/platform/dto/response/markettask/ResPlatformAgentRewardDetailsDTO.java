package cn.htdt.app.platform.dto.response.markettask;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021-2-2
 * @Description 代理人酬劳管理酬劳明细DTO
 **/
@Data
public class ResPlatformAgentRewardDetailsDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;


    /**
     * 代理人的编码
     */
    @ApiModelProperty(value = "代理人的编码")
    private String agentNo;

    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "店铺编号")
    private String storeNo;
    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    @ApiModelProperty(value = "营销活动类型")
    private String marketType;

    /**
     * 分销商品个数
     */
    @ApiModelProperty(value = "分销商品个数")
    private int distributeGoodsNum;
    /**
     * 云池商品个数
     */
    @ApiModelProperty(value = "云池商品个数")
    private int cloudGoodsNum;
    /**
     * 预估佣金
     */
    @ApiModelProperty(value = "预估佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal predictCommission;
    /**
     * 预估汇金币
     */
    @ApiModelProperty(value = "预估汇金币")
    private int predictGoldCoins;
    /**
     * 预估服务券
     */
    @ApiModelProperty(value = "预估服务券")
    private String predictServiceCoupon;
    /**
     * 预估服务券数量
     */
    @ApiModelProperty(value = "预估服务券数量")
    private int predictServiceCouponNum;

    /**
     * 预估现金券
     */
    @ApiModelProperty(value = "预估现金券")
    private String predictMoneyCoupon;
    /**
     * 预估现金券数量
     */
    @ApiModelProperty(value = "预估现金券数量")
    private int predictMoneyCouponNum;
    /**
     * 预估礼品
     */
    @ApiModelProperty(value = "预估礼品")
    private String predictGift;
    /**
     * 预估礼品数量
     */
    @ApiModelProperty(value = "预估礼品数量")
    private int predictGiftNum;

    /**
     * 实际获得佣金
     */
    @ApiModelProperty(value = "实际获得佣金")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal commission;
    /**
     * 汇金币
     */
    @ApiModelProperty(value = "汇金币")
    private int goldCoins;
    /**
     * 实际获得服务券
     */
    @ApiModelProperty(value = "实际获得服务券")
    private String serviceCoupon;
    /**
     * 实际获得服务券数量
     */
    @ApiModelProperty(value = "实际获得服务券数量")
    private int serviceCouponNum;

    /**
     * 实际获得现金券
     */
    @ApiModelProperty(value = "实际获得现金券")
    private String moneyCoupon;
    /**
     * 实际获得现金券数量
     */
    @ApiModelProperty(value = "实际获得现金券数量")
    private int moneyCouponNum;
    /**
     * 实际获得礼品
     */
    @ApiModelProperty(value = "实际获得礼品")
    private String gift;
    /**
     * 实际获得礼品数量
     */
    @ApiModelProperty(value = "实际获得礼品数量")
    private int giftNum;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

    public BigDecimal getPredictCommission() {
        if (this.predictCommission == null){
            return BigDecimal.ZERO;
        }
        return predictCommission;
    }

    public void setPredictCommission(BigDecimal predictCommission) {
        this.predictCommission = predictCommission;
    }

    public BigDecimal getCommission() {
        if (this.commission == null){
            return BigDecimal.ZERO;
        }
        return commission;
    }

    public void setCommission(BigDecimal commission) {
        this.commission = commission;
    }
}