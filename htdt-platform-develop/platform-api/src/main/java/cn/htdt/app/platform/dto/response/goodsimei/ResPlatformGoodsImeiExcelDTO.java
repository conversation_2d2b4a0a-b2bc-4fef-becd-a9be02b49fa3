package cn.htdt.app.platform.dto.response.goodsimei;

import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import cn.htdt.app.platform.converter.base.WhetherConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 *  商品串码信息导出DTO
 *
 * <AUTHOR>
 * @date 2020年9月11日
 */
@Data
public class ResPlatformGoodsImeiExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品编号，包含普通商品、套餐商品等所有商品
     */
    @ExcelProperty(value = "商品编号",index = 0)
    @ColumnWidth(40)
    private String goodsNo;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 1)
    @ColumnWidth(40)
    private String goodsName;

    /**
     * 是否入仓(1:否 2:是)
     */
    @ExcelProperty(value = "是否入仓商品",index = 2, converter = WhetherConverter.class)
    @ColumnWidth(20)
    private Integer warehouseFlag;

    /**
     * 实体库存数量
     */
    @ExcelProperty(value = "实体库存数量",index = 3)
    @ColumnWidth(20)
    private BigDecimal availableStockNum;

    /**
     * 仓库编码
     */
    @ExcelIgnore
    private String warehouseNo;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称",index = 4)
    @ColumnWidth(40)
    private String warehouseName;

    /**
     * 商品串码数量
     */
    @ExcelProperty(value = "商品串码数量",index = 5)
    @ColumnWidth(40)
    private Integer imeiCount;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人",index = 6)
    @ColumnWidth(40)
    private String createNo;

    /**
     * 创建时间 , converter = DateConverter.class
     */
    @ExcelProperty(value = "创建时间", index = 7, converter = LocalDateTimeConverter.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ColumnWidth(40)
    private LocalDateTime createTime;

}
