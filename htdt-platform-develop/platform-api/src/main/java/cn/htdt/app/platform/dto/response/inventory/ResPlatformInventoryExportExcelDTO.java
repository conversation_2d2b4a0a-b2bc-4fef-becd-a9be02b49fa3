package cn.htdt.app.platform.dto.response.inventory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点详细 导出DTO
 **/
@Data
public class ResPlatformInventoryExportExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品名称",index = 0)
    @ColumnWidth(20)
    private String goodsName;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品ID",index = 1)
    @ColumnWidth(20)
    private String goodsNo;

    /**
     * 实体库存
     */
    @ExcelProperty(value ="实体库存",index = 2)
    @ColumnWidth(20)
    private BigDecimal stockNum;

    /**
     * 实盘数量
     */
    @ExcelProperty(value = "实盘数量",index = 3)
    @ColumnWidth(20)
    private BigDecimal inventoryStockNum;

    /**
     * 差异数量
     */
    @ExcelProperty(value = "差异数量",index = 4)
    @ColumnWidth(20)
    private BigDecimal difStockNum;

    /**
     * 单位
     */
    @ExcelProperty(value = "单位",index = 5)
    @ColumnWidth(20)
    private String symbol;
}
