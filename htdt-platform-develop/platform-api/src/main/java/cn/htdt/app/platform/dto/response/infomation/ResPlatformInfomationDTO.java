package cn.htdt.app.platform.dto.response.infomation;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 资讯信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-10-30
 */
@Data
@ApiModel(description = "资讯响应实体类")
public class ResPlatformInfomationDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(notes = "资讯编号")
    private String infoNo;

    @ApiModelProperty(notes = "标题")
    private String title;

    @ApiModelProperty(notes = "正文内容")
    private String content;

    @ApiModelProperty(notes = "封面")
    private String coverUrl;

    @ApiModelProperty(notes = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

}
