package cn.htdt.app.platform.dto.response.inventory;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点 下载模板DTO
 **/
@Data
public class ResPlatformInventoryGoodsGroupsDownLoadExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品组名称",index = 0)
    @ColumnWidth(25)
    private String goodsName;

    /**
     * 仓库名称
     */
    @ExcelProperty(value = "仓库名称",index = 1)
    @ColumnWidth(25)
    private String warehouseName;

    /**
     * 商品ID
     */
    @ExcelProperty(value = "商品组ID",index = 2)
    @ColumnWidth(25)
    private String goodsNo;

    /**
     * 计量单位名称
     * 20230928蛋品-赵翔宇-盘点单
     */
    @ExcelProperty(value = "主计量单位名称",index = 3)
    @ColumnWidth(10)
    private String calculationUnitName;

}
