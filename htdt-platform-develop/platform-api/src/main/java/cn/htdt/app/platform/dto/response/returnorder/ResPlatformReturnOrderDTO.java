package cn.htdt.app.platform.dto.response.returnorder;

import cn.htdt.common.dto.BigDecimalSerialize;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 售后订单数据
 * <AUTHOR>
 */
@Data
public class ResPlatformReturnOrderDTO implements Serializable {

    @ApiModelProperty(value = "退单申请时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime applyTime;

    @ApiModelProperty(value = "售后状态")
    private String returnStatus;

    @ApiModelProperty(value = "退款单状态")
    private String refundmentStatus;

    @ApiModelProperty(value = "售后编号")
    private String soReturnNo;

    @ApiModelProperty(value = "订单编号")
    private String orderNo;

    @ApiModelProperty(value = "售后类型")
    private String returnType;

    @ApiModelProperty(value = "订单类型")
    private String orderType;

    /**
     * 订单渠道来源 字典ORDER_CHANNEL_SOURCE
     */
    private String orderChannelSource;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "用户申请退款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal applyReturnAmount;

    @ApiModelProperty(value = "申请来源")
    private String source;

    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    @ApiModelProperty(value = "实际退款金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal actualReturnAmount;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
