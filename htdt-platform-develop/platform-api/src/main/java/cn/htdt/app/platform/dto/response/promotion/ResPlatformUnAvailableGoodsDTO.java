package cn.htdt.app.platform.dto.response.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalTime;

/**
 * 活动场次请求参数
 *
 * <AUTHOR>
 * @date 2021-11-30
 */
@Data
public class ResPlatformUnAvailableGoodsDTO implements Serializable {

    private static final long serialVersionUID = -6932480086332483907L;

    @ApiModelProperty(value = "时间段编号")
    private String periodNo;

    @ApiModelProperty(value = "活动编号")
    private String promotionNo;

    @ApiModelProperty(value = "活动场次下是否存在互斥商品的标识，1-否，2-是")
    private Integer unAvailableFlag;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
