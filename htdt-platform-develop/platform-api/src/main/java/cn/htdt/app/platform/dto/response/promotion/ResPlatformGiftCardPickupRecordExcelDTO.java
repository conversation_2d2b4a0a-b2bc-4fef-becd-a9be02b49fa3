package cn.htdt.app.platform.dto.response.promotion;

import cn.htdt.app.platform.converter.*;
import cn.htdt.app.platform.converter.base.LocalDateTimeConverter;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 礼品卡提货记录excel导出
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-20
 */
@Data
public class ResPlatformGiftCardPickupRecordExcelDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ColumnWidth(40)
    @ExcelProperty(value = "礼品卡名称", index = 0)
    private String promotionName;

    @ColumnWidth(40)
    @ExcelProperty(value = "卡密", index = 1)
    private String secretKey;

    @ColumnWidth(40)
    @ExcelProperty(value = "订单编码", index = 2)
    private String orderNo;

    @ExcelProperty(value = "下单时间", index = 3, converter = LocalDateTimeConverter.class)
    @ColumnWidth(20)
    private LocalDateTime orderCreateTime;

    @ExcelProperty(value = "商品名称", index = 4)
    @ColumnWidth(40)
    private String goodsNameConcat;

    @ExcelProperty(value = "下单人昵称", index = 5)
    @ColumnWidth(20)
    private String buyerName;

    @ExcelProperty(value = "下单人手机号", index = 6)
    @ColumnWidth(20)
    private String buyerMobile;

    @ExcelProperty(value = "订单状态", index = 7, converter = OrderStatusConverter.class)
    @ColumnWidth(20)
    private String orderStatus;

}
