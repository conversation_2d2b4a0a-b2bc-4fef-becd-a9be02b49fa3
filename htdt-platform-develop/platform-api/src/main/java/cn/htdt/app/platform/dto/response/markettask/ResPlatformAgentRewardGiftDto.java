package cn.htdt.app.platform.dto.response.markettask;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ResPlatformAgentRewardGiftDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 酬劳名称
     */
    @ApiModelProperty(value = "酬劳名称")
    private String rewardName;


    /**
     * 酬劳
     */
    @ApiModelProperty(value = "酬劳数量")
    @JSONField(serialzeFeatures= {SerializerFeature.WriteNullNumberAsZero})
    private int rewardValue;


}
