package cn.htdt.app.platform.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 产品订单合同表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-08-31
 */
@Data
public class ResPlatformPayDealsContractDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 产品订单编号
     */
    @ApiModelProperty(value="产品订单编号")
    private String dealsNo;

    /**
     * 产品唯一编号
     */
    @ApiModelProperty(value="产品唯一编号")
    private String projectKitNo;

    /**
     * 合同编码(合同系统)
     */
    @ApiModelProperty(value="合同编码(合同系统)")
    private String contractNo;

    /**
     * 流程ID（合同系统）
     */
    @ApiModelProperty(value="流程ID（合同系统）")
    private String requestId;

    /**
     * 合同链接地址
     */
    @ApiModelProperty(value="合同链接地址")
    private String contractUrl;

    /**
     * 合同类型  1:套餐 2:增值
     */
    @ApiModelProperty(value="合同类型  1:套餐 2:增值")
    private Integer contractType;

    @ApiModelProperty(value = "合同名称 ")
    private String contractName = "";

}
