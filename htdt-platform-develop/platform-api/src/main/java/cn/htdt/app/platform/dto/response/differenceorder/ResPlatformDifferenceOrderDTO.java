package cn.htdt.app.platform.dto.response.differenceorder;

import cn.htdt.common.base.annotations.Converter;
import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.dto.BigDecimalSerializeNoZero;
import cn.htdt.common.dto.request.ReqBaseDTO;
import cn.htdt.common.enums.goods.DifferenceOrderDeliveryStatusEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 差异单响应DTO
 *
 * <AUTHOR>
 * @since 2023-05-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
public class ResPlatformDifferenceOrderDTO extends ReqBaseDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 差异单号
     */
    @ApiModelProperty(value = "差异单号")
    private String differenceNo;

    /**
     * 要货单号
     */
    @ApiModelProperty(value = "要货单号")
    private String requisitionNo;

    /**
     * 差异品项数
     */
    @ApiModelProperty(value = "差异品项数")
    @JsonSerialize(using = BigDecimalSerializeNoZero.class)
    private BigDecimal differenceCount;

    /**
     * 差异金额
     */
    @ApiModelProperty(value = "差异金额")
    @JsonSerialize(using = BigDecimalSerialize.class)
    private BigDecimal differenceAmount;

    /**
     * 状态 1001-待收货 1002-已完成, 参考枚举: DifferenceOrderDeliveryStatusEnum
     */
    @ApiModelProperty(value = "状态, 1001-待收货, 1002-已完成")
    @Converter(enumClass = DifferenceOrderDeliveryStatusEnum.class, fieldName = "statusValue")
    private String status;

    @ApiModelProperty("状态值")
    private String statusValue;

    /**
     * 入库人
     */
    @ApiModelProperty(value = "入库人")
    private String inStorageUser;

    /**
     * 入库时间
     */
    @ApiModelProperty(value = "入库时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime inStorageTime;

    @ApiModelProperty(value = "商家编号")
    private String merchantNo;

    @ApiModelProperty(value = "商家名称")
    private String merchantName;

    @ApiModelProperty(value = "店铺编号")
    private String storeNo;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime modifyTime;

    @ApiModelProperty(value = "差异单商品行列表")
    private List<ResPlatformDifferenceOrderGoodsDTO> differenceOrderGoodsDTOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
