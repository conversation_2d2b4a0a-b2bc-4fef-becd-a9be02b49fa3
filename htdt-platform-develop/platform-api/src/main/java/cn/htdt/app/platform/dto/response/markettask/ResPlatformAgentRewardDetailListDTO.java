package cn.htdt.app.platform.dto.response.markettask;

import cn.htdt.common.dto.BigDecimalSerialize;
import cn.htdt.common.enums.constants.NumConstant;
import cn.htdt.common.enums.market.RewardTypeEnum;
import cn.htdt.common.utils.BigDecimalUtil;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Date 2021-2-3
 * @Description 代理人酬劳管理酬劳明细列表DTO
 **/
@Data
public class ResPlatformAgentRewardDetailListDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = 1L;

    /**
     * 代理人酬劳主键
     */
    @ApiModelProperty(value = "代理人酬劳主键")
    private String agentAwardNo;
    /**
     * 代理人的编码
     */
    @ApiModelProperty(value = "代理人的编码")
    private String agentNo;

    /**
     * 店铺编号
     */
    @ApiModelProperty(value = "店铺编号")
    private String storeNo;
    /**
     * 酬劳类型
     */
    @ApiModelProperty(value = "酬劳类型")
    private Integer rewardType;
    /**
     * 酬劳名称
     */
    @ApiModelProperty(value = "酬劳名称")
    private String rewardName;
    /**
     * 酬劳汇总
     */
    private BigDecimal rewardValue;

    @ApiModelProperty(value = "酬劳汇总 预计酬劳： 1--预估 2--冻结 6--酬劳取消 7 --冻结账户退回 8 -- 解冻后未核销 9 -- 未产生  实际获得酬劳: 3--解冻 4--待领取 5--已领取")
    private String rewardValueStr;
    /**
     * 酬劳状态标识
     */
    @ApiModelProperty(value = "酬劳状态标识")
    private Integer rewardStatusFlag;

    @ApiModelProperty(value = "酬劳状态")
    private String rewardStatusStr;
    /**
     * 任务活动唯一编号或者是商品编号
     */
    @ApiModelProperty(value = "任务活动唯一编号或者是商品编号")
    private String taskOrGoodsNo;
    /**
     * 订单编号
     */
    @ApiModelProperty(value = "订单编号")
    private String orderNo;
    /**
     * 任务名称|商品名称
     */
    @ApiModelProperty(value = "任务名称|商品名称")
    private String taskOrGoodsName;

    @ApiModelProperty(value = "订单状态")
    private String orderStatus;
    /**
     * 营销活动类型，对应枚举MarketTypeEnum
     */
    @ApiModelProperty(value = "酬劳来源 0001:完成平台任务 0500:分销本店商品 1000:分销平台商品")
    private String marketType;

    @ApiModelProperty(value = "1 - 本店 2 - 平台")
    private Integer marketSource;

    public BigDecimal getRewardValue() {
        if (this.rewardValue == null){
            return BigDecimal.ZERO;
        }
        if(!RewardTypeEnum.YJ.getCode().equals(this.rewardType)){
             rewardValue = BigDecimalUtil.setScale(rewardValue, NumConstant.ZERO);
        }else {
            rewardValue = BigDecimalUtil.setScale(rewardValue);
        }
        return rewardValue;
    }

    public void setRewardValue(BigDecimal rewardValue) {
        this.rewardValue = rewardValue;
    }
    public String getRewardValueStr() {
        if (this.rewardValue == null){
            return BigDecimal.ZERO.toString();
        }
        if(!RewardTypeEnum.YJ.getCode().equals(this.rewardType)){
            rewardValueStr = BigDecimalUtil.setScale(rewardValue, NumConstant.ZERO).toString();
        }else {
            rewardValueStr = BigDecimalUtil.setScale(rewardValue).toString();
        }
        return rewardValueStr;
    }
}