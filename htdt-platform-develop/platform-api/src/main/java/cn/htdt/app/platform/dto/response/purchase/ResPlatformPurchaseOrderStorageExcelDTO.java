package cn.htdt.app.platform.dto.response.purchase;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 *  采购单待入库导出DTO
 *
 * <AUTHOR>
 * @date 2020年9月15日
 */
@Data
public class ResPlatformPurchaseOrderStorageExcelDTO extends BaseRowModel implements Serializable {
    private static final long serialVersionUID = -2183266402894644967L;
    /**
     *采购单编码
     */
    @ExcelProperty(value = "采购单编码",index = 0)
    @ColumnWidth(15)
    private String purchaseCode;
    /**
     *采购单商品行编码
     */
    @ExcelProperty(value = "采购单商品行编码",index = 1)
    @ColumnWidth(15)
    private String purchaseGoodsCode;
    /**
     *商品编码
     */
    @ExcelProperty(value = "商品编码",index = 2)
    @ColumnWidth(15)
    private String goodsNo;
    /**
     *商品名称
     */
    @ExcelProperty(value = "商品名称",index = 3)
    @ColumnWidth(15)
    private String goodsName;
    /**
     * 计量单位名称
     */
    @ExcelProperty(value = "计量单位名称",index = 5)
    @ColumnWidth(15)
    private String calculationUnitName;
    /**
     *采购单位
     */
    @ExcelProperty(value = "采购单位",index = 6)
    @ColumnWidth(15)
    private String purchaseUnit;

    /**
     * 转换率(辅转主)
     */
    @ExcelProperty(value = "转换率",index = 7)
    @ColumnWidth(15)
    private BigDecimal conversionRate;
    /**
     * 是否入仓:1-否;2-是;
     */
    @ExcelProperty(value = "是否入仓",index = 4)
    @ColumnWidth(15)
    private String warehouseFlagStr;

    /**
     *采购数量
     */
    @ExcelProperty(value = "采购数量",index = 8)
    @ColumnWidth(15)
    private BigDecimal purchaseNum;
    /**
     * 待入库数量
     */
    @ExcelProperty(value = "待入库数量",index = 9)
    @ColumnWidth(15)
    private BigDecimal waitStorageCount;
    /**
     *已入库数量
     */
    @ExcelProperty(value = "已入库数量",index = 10)
    @ColumnWidth(15)
    private BigDecimal storageCount;
    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }


}
