package cn.htdt.app.platform.dto.response.promotion;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 开单用户积分返回dto
 *
 * <AUTHOR>
 */
@Data
public class ResPlatformUserPointDeductionDTO implements Serializable {

    /**
     * 序列化
     */
    private static final long serialVersionUID = -4826833361952907625L;

    /**
     * 粉丝编号
     */
    private String fansNo;

    //账户剩余积分
    @ApiModelProperty(value = "会员账号积分")
    private Integer accountRemainPoints;

    @ApiModelProperty(value = "可用积分")
    private BigDecimal canUsePoint;

    @ApiModelProperty(value = "可抵扣金额")
    private BigDecimal canDeductionAmount;

    /**
     *抽奖积分配置 1:启用 2:禁用
     */
    @ApiModelProperty(value = "抽奖积分配置 1:启用 2:禁用")
    private Integer pointsConfig;

    /**
     * 配置类型(1001:积分获得上限 1002:粉丝下单配置 1003:关注店铺赠积分  2001:积分兑换礼品 2002:积分兑换抽奖机会)
     */
    private String configType;

    @ApiModelProperty(value = "积分抵扣比例默认抵扣1分钱")
    private Integer deductionScale;

    @ApiModelProperty(value = "订单金额门槛：1001-不限制，1002-限制")
    private String orderAmountLimit;

    @ApiModelProperty(value = "订单应付金额（元）")
    private BigDecimal orderHandleAmount;

    @ApiModelProperty(value = "抵扣金额门槛：1001-不限制，1002-限制")
    private String deductionAmountLimit;

    @ApiModelProperty(value = "积分抵扣上限")
    private Integer pointsDeductionLimit;

    @ApiModelProperty(value = "积分适用渠道(1400:一体机开单 1200:app及pc开单 1003:汇享购网店订单)")
    private String useChannel;

}
