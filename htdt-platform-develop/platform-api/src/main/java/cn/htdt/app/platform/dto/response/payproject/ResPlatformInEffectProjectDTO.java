package cn.htdt.app.platform.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-08-31
 * @description 店铺生效套餐响应DTO
 **/
@Data
public class ResPlatformInEffectProjectDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="店铺名称")
    private String storeName;

    @ApiModelProperty(value="套餐图片url")
    private String projectImg;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
