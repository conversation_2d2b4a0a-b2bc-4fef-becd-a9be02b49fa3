package cn.htdt.app.platform.dto.response.goodsdistributionshoprelation;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 分发店铺关系请求表实体类
 *
 * <AUTHOR>
 * @date 2020年9月28日
 */
@Data
public class ResPlatformGoodsDistributionShopRelationDTO  implements Serializable {


	/**
	 * 
	 */
	private static final long serialVersionUID = 7959644595771984550L;

	@ApiModelProperty(value = "分发店铺关系编号")
    private String relationNo;

	@ApiModelProperty(value = "原始商品编号，包含普通商品、套餐商品等所有商品")
    private String goodsNo;

	@ApiModelProperty(value = "商家编号")
    private String merchantNo;

	@ApiModelProperty(value = "商家名称")
    private String merchantName;

	@ApiModelProperty(value = "门店编号，对应orgId")
    private String storeNo;

	@ApiModelProperty(value = "店铺名称，对应orgName")
    private String storeName;

	@ApiModelProperty(value = "店铺编号列表")
    private List<String> storeNoList;

    @ApiModelProperty(value = "商品展示状态1001-未上架;1002-已上架;2001-审核中;2002-审核成功;2003-审核失败;3001:未分发;3002:分发成功;3003:已失效;")
    private String goodsShowStatus;

    @ApiModelProperty(value = "商品展示状态")
    private String goodsShowStatusValue;

    @ApiModelProperty(value = "分发时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime modifyTime;

}
