package cn.htdt.app.platform.dto.response.goodsdistributionshoprelation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * 详细说明.
 * <p>
 * Copyright: Copyright (c) 2020/11/30 18:29
 * <p>
 * Company: htd
 * <p>
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class ResPlatformDistributeReturnStatusDTO  implements Serializable {
    @ApiModelProperty(value = "点击分发操作后返回 1001：分发成功 1002:打开“商品分发”弹窗")
    private String distributeOperate;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
