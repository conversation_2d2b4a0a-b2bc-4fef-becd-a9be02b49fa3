package cn.htdt.app.platform.dto.response.inventory;

import cn.htdt.goodsprocess.dto.response.inventory.ResInventoryDetailDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020-09-02
 * @Description 盘点 返回DTO
 **/
@Data
public class ResPlatformInventoryErrorDTO  implements Serializable {

    @ApiModelProperty(value = "表头错误", name = "inventoryCode")
    private String headError;

    @ApiModelProperty(value = "数据返回", name = "importDTOList")
    private List<ResInventoryDetailDTO> importDTOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
