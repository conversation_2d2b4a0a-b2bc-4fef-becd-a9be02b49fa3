package cn.htdt.app.platform.dto.response.payproject;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;

/**
 * <p>
 * 付费产品订单表详情记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2021/8/10
 */
@Data
public class ResPlatformPayProjectDealsDetailsDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value="商户类型 商户类型 1一般纳税人 2小规模纳税人 3个体工商户")
    private String traderType;

    @ApiModelProperty(value="发票类型")
    private String invoiceType;

    @ApiModelProperty(value="发票抬头")
    private String invoiceNotify;

    @ApiModelProperty(value="纳税人识别号")
    private String taxManid;

    @ApiModelProperty(value="开户行名称")
    private String bankName;

    @ApiModelProperty(value="开户行账号")
    private String bankAccount;

    @ApiModelProperty(value="发票地址")
    private String invoiceAddress;

    @ApiModelProperty(value="发票联系电话")
    private String contactPhone;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
