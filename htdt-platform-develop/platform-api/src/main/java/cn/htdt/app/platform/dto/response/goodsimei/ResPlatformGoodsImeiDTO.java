package cn.htdt.app.platform.dto.response.goodsimei;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021-04-06
 * @description 商品串码响应DTO
 **/
@Data
public class ResPlatformGoodsImeiDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "仓库编码")
    private String warehouseNo;

    @ApiModelProperty(value = "仓库名称")
    private String warehouseName;

    @ApiModelProperty(value = "1=可用；2=不可用")
    private Integer useFlag;

}
