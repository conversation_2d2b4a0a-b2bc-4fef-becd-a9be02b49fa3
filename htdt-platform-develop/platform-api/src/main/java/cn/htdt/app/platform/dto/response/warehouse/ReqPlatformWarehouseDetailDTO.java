package cn.htdt.app.platform.dto.response.warehouse;

import cn.htdt.common.dto.annon.PrivateData;
import cn.htdt.common.dto.enums.DecipherScene;
import cn.htdt.common.dto.enums.HandlerPolicy;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 仓库信息
 *
 * <AUTHOR>
 */
@Data
public class ReqPlatformWarehouseDetailDTO implements Serializable {

    /**
     * 仓库编号
     */
    private String warehouseNo;

    /**
     * 仓库编码，手动输入的
     */
    private String warehouseCode;

    /**
     * 仓库名称
     */
    private String warehouseName;

    /**
     * 仓库类型1001:平台仓 1002：商家仓 1003：店铺仓
     */
    private String warehouseType;

    /**
     * 是否自建仓库 1:否 2:是
     */
    private Integer selfWarehouseFlag;

    /**
     * 是否虚拟仓库 1-否 2-是
     */
    private Integer virtualWarehouseFlag;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 国家编码名称
     */
    private String countryName;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份编码名称
     */
    private String provinceName;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市编码名称
     */
    private String cityName;

    /**
     * 区县编码
     */
    private String districtCode;

    /**
     * 区县编码名称
     */
    private String districtName;

    /**
     * 镇编码
     */
    private String townCode;

    /**
     * 镇编码名称
     */
    private String townName;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 详细地址
     */
    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.PLATFORM_WAREHOUSE_DETAIL, fieldName = "address")
    private String dsAddress;

    /**
     * 仓库负责人
     */
    private String warehouseContacter;

    /**
     * 仓库负责人电话
     */
    private String warehouseContacterMobile;
    /**
     * 仓库负责人电话-加密
     */
//    @PrivateData(handlerPolicy = HandlerPolicy.DECIPHER, decipherScene= DecipherScene.PLATFORM_WAREHOUSE_DETAIL, fieldName = "warehouseContacterMobile")
    private String dsWarehouseContacterMobile;


    /**
     * 备注
     */
    private String warehouseRemark;

    /**
     * 是否可用:默认2，1：不可用 2：可用
     */
    private Integer disableFlag;

    /**
     * 创建人no
     */
    private String createNo;

    /**
     * 创建人名称
     */
    private String createName;

    /**
     * 修改人no
     */
    private String modifyNo;

    /**
     * 修改人名称
     */
    private String modifyName;

    /**
     * 商家编号
     */
    private String merchantNo;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 门店编号，对应orgId
     */
    private String storeNo;

    /**
     * 店铺名称，对应orgName
     */
    private String storeName;

    /**
     * 平台编号
     */
    private String companyNo;

    /**
     * 平台名称
     */
    private String companyName;

    /**
     * 分部编号
     */
    private String branchNo;

    /**
     * 分部名称
     */
    private String branchName;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }

}
