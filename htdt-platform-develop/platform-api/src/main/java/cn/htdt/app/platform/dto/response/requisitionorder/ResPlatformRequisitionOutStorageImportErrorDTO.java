package cn.htdt.app.platform.dto.response.requisitionorder;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 要货单
 * 
 * <AUTHOR>
 * @date 2023/5/30 15:40
 **/
@Data
public class ResPlatformRequisitionOutStorageImportErrorDTO implements Serializable {

    @ApiModelProperty(value = "表头错误")
    private String headError;

    @ApiModelProperty(value = "数据返回")
    private List<ResPlatformRequisitionOutStorageImportDTO> importDTOList;

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(ToStringStyle.SHORT_PREFIX_STYLE);
    }
}
