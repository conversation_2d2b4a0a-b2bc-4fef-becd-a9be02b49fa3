package cn.htdt.common.utils;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;

/**
 * 短信操作公共接口
 *
 * <AUTHOR>
 * @date 2021/7/10
 **/
@Slf4j
public class SmsUtils {
    public static final BigDecimal UNIT_PRICE = new BigDecimal("0.06");

    /**
     * 计算短信充值金额
     *
     * @param rechargeNum 充值条数
     * @return BigDecimal 充值金额
     */
    public static BigDecimal calculationSmsRechargeAmount(Integer rechargeNum) {
        //自定义充值
        return new BigDecimal(String.valueOf(rechargeNum)).multiply(UNIT_PRICE);
    }

}
    
